'use client'

import { useState } from 'react'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu'
import { MoreHorizontal, Edit, Trash, ChevronLeft, ChevronRight } from 'lucide-react'
import { formatCurrencyStandalone } from '@/lib/currency'
import { useCurrency } from '@/contexts/currency-context'

interface Customer {
  id: string
  name: string
  email: string
  totalOrders: number
  totalSpent: number
  lastOrderDate?: string
}

interface EnhancedCustomerTableProps {
  customers: Customer[]
  isMobile?: boolean
  onEditCustomer: (customer: Customer) => void
  onDeleteCustomer: (customerId: string) => void
  // Pagination props
  currentPage?: number
  pageSize?: number
  totalItems?: number
  onPageChange?: (page: number) => void
  onPageSizeChange?: (size: number) => void
}

export function EnhancedCustomerTable({
  customers,
  isMobile = false,
  onEditCustomer,
  onDeleteCustomer,
  // Pagination props with defaults
  currentPage = 1,
  pageSize = 10,
  totalItems = 0,
  onPageChange = () => {},
  onPageSizeChange = () => {}
}: EnhancedCustomerTableProps) {
  const { formatCurrency } = useCurrency()
  const totalPages = Math.ceil(totalItems / pageSize)

  return (
    <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
      <Table>
        <TableHeader className="bg-slate-50 dark:bg-gray-800">
          <TableRow>
            <TableHead className="text-left uppercase text-xs font-semibold text-gray-900 dark:text-gray-100 p-3">
              Customer Name
            </TableHead>
            <TableHead className="text-left uppercase text-xs font-semibold text-gray-900 dark:text-gray-100 p-3">
              Email
            </TableHead>
            <TableHead className="text-left uppercase text-xs font-semibold text-gray-900 dark:text-gray-100 p-3">
              Total Orders
            </TableHead>
            <TableHead className="text-left uppercase text-xs font-semibold text-gray-900 dark:text-gray-100 p-3">
              Total Spent
            </TableHead>
            <TableHead className="text-left uppercase text-xs font-semibold text-gray-900 dark:text-gray-100 p-3">
              Actions
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {customers.map((customer) => (
            <TableRow 
              key={customer.id} 
              className="border-b border-gray-200 dark:border-gray-700 hover:bg-slate-50 dark:hover:bg-gray-800 transition"
            >
              <TableCell className="p-3">
                <div className="font-medium text-gray-900 dark:text-gray-100 text-xs">
                  {customer.name}
                </div>
              </TableCell>
              <TableCell className="p-3">
                <div className="text-gray-700 dark:text-gray-300 text-xs">
                  {customer.email}
                </div>
              </TableCell>
              <TableCell className="p-3">
                <div className="text-gray-900 dark:text-gray-100 text-xs">
                  {customer.totalOrders}
                </div>
              </TableCell>
              <TableCell className="p-3">
                <div className="text-gray-900 dark:text-gray-100 text-xs">
                  {formatCurrency(customer.totalSpent)}
                </div>
              </TableCell>
              <TableCell className="p-3">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                      <MoreHorizontal className="h-3.5 w-3.5" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem 
                      onClick={() => onEditCustomer(customer)}
                      className="flex items-center gap-2 text-xs py-2"
                    >
                      <Edit className="h-3.5 w-3.5" />
                      Edit
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      onClick={() => onDeleteCustomer(customer.id)}
                      className="flex items-center gap-2 text-red-600 focus:text-red-600 text-xs py-2"
                    >
                      <Trash className="h-3.5 w-3.5" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      
      {customers.length === 0 && (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="text-gray-500 mb-2">
              <h3 className="text-lg font-medium">No customers found</h3>
            </div>
            <p className="text-muted-foreground text-sm">
              Try adjusting your search or filter criteria
            </p>
          </div>
        </div>
      )}
      
      {/* Pagination */}
      {totalItems > 0 && (
        <div className="flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 p-4 border border-gray-200 border-t-0 rounded-b-lg bg-gray-50">
          {/* Left: Items per page selector */}
          <div className="flex items-center gap-2 text-xs text-gray-600">
            <span>Show</span>
            <select
              value={pageSize}
              onChange={(e) => onPageSizeChange(Number(e.target.value))}
              className="rounded border border-gray-200 px-2 py-1 text-xs h-7"
            >
              <option value={5}>5</option>
              <option value={10}>10</option>
              <option value={20}>20</option>
              <option value={50}>50</option>
            </select>
            <span>per page</span>
          </div>
          
          {/* Right: Pagination Controls and Stats */}
          <div className="flex items-center w-full md:w-auto justify-end">
            {/* Item Range and Navigation */}
            <div className="flex items-center gap-2">
              <p className="text-xs text-gray-600">
                <span className="hidden sm:inline">Showing </span>
                <span className="font-medium">{Math.min((currentPage - 1) * pageSize + 1, totalItems)}-{Math.min(currentPage * pageSize, totalItems)}</span>
                <span className="hidden sm:inline"> of </span>
                <span className="sm:hidden">/</span>
                <span className="font-medium">{totalItems}</span>
              </p>
              
              <div className="flex items-center rounded-md border border-gray-200 overflow-hidden">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onPageChange(currentPage - 1)}
                  disabled={currentPage <= 1}
                  className="h-7 w-7 p-0 rounded-none border-r border-gray-200"
                >
                  <ChevronLeft className="h-3.5 w-3.5 text-gray-600" />
                </Button>
                
                <div className="hidden sm:flex">
                  {Array.from({ length: Math.min(3, totalPages) }, (_, i) => {
                    const page = Math.max(1, currentPage - 1) + i
                    if (page > totalPages) return null
                    
                    return (
                      <Button
                        key={page}
                        variant={page === currentPage ? "default" : "ghost"}
                        size="sm"
                        onClick={() => onPageChange(page)}
                        className={`h-7 w-7 p-0 rounded-none border-r border-gray-200 text-xs ${
                          page === currentPage 
                            ? "bg-blue-600 hover:bg-blue-700 text-white" 
                            : "text-gray-700"
                        }`}
                      >
                        {page}
                      </Button>
                    )
                  })}
                </div>
                
                <div className="sm:hidden flex items-center justify-center h-7 w-7 text-xs font-medium border-r border-gray-200">
                  {currentPage}
                </div>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onPageChange(currentPage + 1)}
                  disabled={currentPage >= totalPages}
                  className="h-7 w-7 p-0 rounded-none"
                >
                  <ChevronRight className="h-3.5 w-3.5 text-gray-600" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}