-- =============================================
-- ORGANIZATION PREFERENCES TABLE
-- =============================================

-- Create organization_preferences table
create table if not exists organization_preferences (
  organization_id uuid references organizations(id) on delete cascade primary key,
  email_notifications boolean default true,
  push_notifications boolean default true,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create trigger for organization_preferences updates
create or replace function handle_organization_preferences_updated_at()
returns trigger as $$
begin
  new.updated_at = timezone('utc'::text, now());
  return new;
end;
$$ language plpgsql;

create trigger organization_preferences_updated_at before update on organization_preferences
  for each row execute procedure handle_organization_preferences_updated_at();

-- Enable RLS on organization_preferences table
alter table organization_preferences enable row level security;

-- Organization preferences policies
create policy "Organization members can view preferences" on organization_preferences
  for select using (
    organization_id in (
      select organization_id 
      from organization_members 
      where user_id = auth.uid()
    )
  );

create policy "Organization admins can update preferences" on organization_preferences
  for update using (
    organization_id in (
      select organization_id 
      from organization_members 
      where user_id = auth.uid() and role = 'admin'
    )
  );

create policy "Organization admins can insert preferences" on organization_preferences
  for insert with check (
    organization_id in (
      select organization_id 
      from organization_members 
      where user_id = auth.uid() and role = 'admin'
    )
  );

-- Create indexes for better query performance
create index if not exists idx_organization_preferences_org_id on organization_preferences(organization_id);

-- Grant necessary permissions
grant all on organization_preferences to authenticated;