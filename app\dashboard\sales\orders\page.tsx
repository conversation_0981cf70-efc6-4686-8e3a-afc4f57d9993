'use client'

import { useState, useEffect } from 'react'
import { OrdersTab } from '@/components/sales/orders/OrdersTab'
import { NewOrderModal } from '@/components/sales/orders/NewOrderModal'
import { useToast } from '@/components/ui/use-toast'
import { Button } from '@/components/ui/button'
import { FileSpreadsheet } from 'lucide-react'
import { useAllProducts, type ExtendedProductRow } from '@/hooks/use-products'
import { useAuth } from '@/contexts/auth-context'
import { PageHeader } from '@/components/ui/page-header'

export default function OrdersPage() {
  const [isMobile, setIsMobile] = useState(false)
  const [isAddOrderModalOpen, setIsAddOrderModalOpen] = useState(false)
  const [isExporting, setIsExporting] = useState(false)
  const [filteredOrders, setFilteredOrders] = useState<any[]>([])
  const { toast } = useToast()
  const { user, organizationId } = useAuth()
  const { data: productsData = [], isLoading: isLoadingProducts } = useAllProducts(organizationId || undefined)
  const products = productsData as ExtendedProductRow[]

  // Mobile detection
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Export to CSV function (placeholder)
  const exportToCSV = () => {
    setIsExporting(true)
    
    try {
      // TODO: Implement actual CSV export logic when orders data is available
      toast({
        title: "Export Not Available",
        description: "CSV export will be available when orders are implemented.",
      })
    } catch (error) {
      console.error('CSV export error:', error)
      toast({
        title: "Export Failed",
        description: "Failed to export orders. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsExporting(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Page Header with Title and Actions */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <PageHeader 
          title="Orders"
          description="Manage customer orders"
        />
        
        <div className="flex flex-col sm:flex-row sm:items-center gap-2">
          {/* Export Button */}
          <Button 
            variant="outline" 
            size="sm" 
            className="flex items-center gap-1.5 h-8 px-3"
            onClick={exportToCSV}
            disabled={isExporting || filteredOrders.length === 0}
          >
            <FileSpreadsheet className="h-4 w-4" />
            <span>Export CSV</span>
          </Button>
          
          {/* Add Order Button */}
          <Button
            className="flex items-center gap-1.5 h-8 px-3 bg-blue-600 hover:bg-blue-700"
            onClick={() => setIsAddOrderModalOpen(true)}
          >
            <span>+ New Order</span>
          </Button>
        </div>
      </div>
      
      {/* Main Content - Orders Tab */}
      <OrdersTab 
        isMobile={isMobile} 
      />
      
      {/* New Order Modal */}
      <NewOrderModal
        isOpen={isAddOrderModalOpen}
        onClose={() => setIsAddOrderModalOpen(false)}
        products={products}
        isLoadingProducts={isLoadingProducts}
      />
    </div>
  )
}