import React, { useState } from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { Switch } from '@/components/ui/switch'
import { ChevronDown, ChevronRight, Package, DollarSign, BarChart3, Settings, X } from 'lucide-react'
import { useProductForm } from '../../context/ProductFormContext'

interface VariantConfig {
  id: string
  attributes: { [key: string]: string }
  sku: string
  enabled: boolean
  pricing: {
    regular_price: string
    sale_price: string
    cost_price: string
  }
  inventory: {
    manage_stock: boolean
    stock_quantity: string
    low_stock_threshold: string
  }
  shipping: {
    weight: string
    dimensions: {
      length: string
      width: string
      height: string
    }
  }
}

interface Props {
  generatedVariants?: Array<{
    id: string
    attributes: { [key: string]: string }
    sku: string
    enabled: boolean
  }>
}

export function VariantConfigurationSection({ generatedVariants = [] }: Props) {
  const { formData, updateFormData, savedVariants, setSavedVariants } = useProductForm()
  const [variants, setVariants] = useState<VariantConfig[]>([])
  const [expandedVariants, setExpandedVariants] = useState<Set<string>>(new Set())
  const [bulkEditMode, setBulkEditMode] = useState(false)

  // Initialize variants from generated combinations
  React.useEffect(() => {
    if (generatedVariants.length > 0) {
      const initialVariants: VariantConfig[] = generatedVariants.map(variant => ({
        ...variant,
        pricing: {
          regular_price: '',
          sale_price: '',
          cost_price: ''
        },
        inventory: {
          manage_stock: false,
          stock_quantity: '',
          low_stock_threshold: '5'
        },
        shipping: {
          weight: '',
          dimensions: {
            length: '',
            width: '',
            height: ''
          }
        }
      }))
      setVariants(initialVariants)
    }
  }, [generatedVariants])

  // Toggle variant expansion
  const toggleVariant = (variantId: string) => {
    const newExpanded = new Set(expandedVariants)
    if (newExpanded.has(variantId)) {
      newExpanded.delete(variantId)
    } else {
      newExpanded.add(variantId)
    }
    setExpandedVariants(newExpanded)
  }

  // Update variant configuration
  const updateVariant = (variantId: string, updates: Partial<VariantConfig>) => {
    setVariants(prev => prev.map(variant =>
      variant.id === variantId
        ? { ...variant, ...updates }
        : variant
    ))
  }

  // Remove variant
  const removeVariant = (variantId: string) => {
    setVariants(prev => prev.filter(variant => variant.id !== variantId))
    setExpandedVariants(prev => {
      const newExpanded = new Set(prev)
      newExpanded.delete(variantId)
      return newExpanded
    })
  }

  // Bulk actions
  const bulkUpdatePricing = (field: string, value: string) => {
    setVariants(prev => prev.map(variant => ({
      ...variant,
      pricing: { ...variant.pricing, [field]: value }
    })))
  }

  const bulkToggleStock = (enabled: boolean) => {
    setVariants(prev => prev.map(variant => ({
      ...variant,
      inventory: { ...variant.inventory, manage_stock: enabled }
    })))
  }

  // Generate variant display name
  const getVariantDisplayName = (attributes: { [key: string]: string }) => {
    return Object.entries(attributes)
      .map(([attr, value]) => `${attr}: ${value}`)
      .join(' • ')
  }

  return (
    <div className="space-y-6">
      {/* Bulk Actions */}
      {variants.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm">Bulk Actions</CardTitle>
              <Switch
                checked={bulkEditMode}
                onCheckedChange={setBulkEditMode}
                className="scale-75"
              />
            </div>
          </CardHeader>
          {bulkEditMode && (
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <label className="text-xs font-medium">Bulk Regular Price</label>
                  <Input
                    placeholder="0.00"
                    className="h-8 text-xs"
                    onChange={(e) => bulkUpdatePricing('regular_price', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-xs font-medium">Bulk Sale Price</label>
                  <Input
                    placeholder="0.00"
                    className="h-8 text-xs"
                    onChange={(e) => bulkUpdatePricing('sale_price', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-xs font-medium">Bulk Cost Price</label>
                  <Input
                    placeholder="0.00"
                    className="h-8 text-xs"
                    onChange={(e) => bulkUpdatePricing('cost_price', e.target.value)}
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => bulkToggleStock(true)}
                  className="text-xs"
                >
                  Enable Stock Management for All
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => bulkToggleStock(false)}
                  className="text-xs"
                >
                  Disable Stock Management for All
                </Button>
              </div>
            </CardContent>
          )}
        </Card>
      )}

      {/* Variant List */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm">Variants</CardTitle>
        </CardHeader>
        <CardContent>
          {variants.length === 0 ? (
            <div className="text-center py-8 bg-gray-50 border border-gray-200 rounded-md">
              <div className="text-gray-500 mb-2">
                <Package className="w-8 h-8 mx-auto" />
              </div>
              <h3 className="font-medium text-gray-900 text-sm mb-1">No Variants Created</h3>
              <p className="text-xs text-gray-600">
                Define attributes in the previous section to generate variants
              </p>
            </div>
          ) : (
            <div className="space-y-2">
              {variants.map((variant) => (
                <Collapsible
                  key={variant.id}
                  open={expandedVariants.has(variant.id)}
                  onOpenChange={() => toggleVariant(variant.id)}
                >
                  <CollapsibleTrigger asChild>
                    <div className="w-full flex items-center justify-between p-3 border rounded-md hover:bg-gray-50 cursor-pointer">
                      <div className="flex items-center gap-3 flex-1">
                        {expandedVariants.has(variant.id) ? (
                          <ChevronDown className="h-4 w-4 text-gray-500" />
                        ) : (
                          <ChevronRight className="h-4 w-4 text-gray-500" />
                        )}
                        <div className="flex-1">
                          <div className="font-medium text-sm">{getVariantDisplayName(variant.attributes)}</div>
                          <div className="text-xs text-gray-500 font-mono">{variant.sku}</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {variant.pricing.regular_price && (
                          <Badge variant="outline" className="text-xs">
                            ${variant.pricing.regular_price}
                          </Badge>
                        )}
                        {variant.inventory.manage_stock && (
                          <Badge variant="secondary" className="text-xs">
                            Stock: {variant.inventory.stock_quantity || '0'}
                          </Badge>
                        )}
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            removeVariant(variant.id)
                          }}
                          className="h-6 w-6 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </CollapsibleTrigger>

                  <CollapsibleContent>
                    <div className="mx-3 mb-3 p-4 bg-gray-50 border rounded-md space-y-4">
                      {/* Pricing Section */}
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <DollarSign className="h-4 w-4 text-gray-500" />
                          <h4 className="font-medium text-sm">Pricing</h4>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                          <div className="space-y-1">
                            <label className="text-xs font-medium">Regular Price</label>
                            <Input
                              value={variant.pricing.regular_price}
                              onChange={(e) => updateVariant(variant.id, {
                                pricing: { ...variant.pricing, regular_price: e.target.value }
                              })}
                              placeholder="0.00"
                              className="h-8 text-xs"
                            />
                          </div>
                          <div className="space-y-1">
                            <label className="text-xs font-medium">Sale Price</label>
                            <Input
                              value={variant.pricing.sale_price}
                              onChange={(e) => updateVariant(variant.id, {
                                pricing: { ...variant.pricing, sale_price: e.target.value }
                              })}
                              placeholder="0.00"
                              className="h-8 text-xs"
                            />
                          </div>
                          <div className="space-y-1">
                            <label className="text-xs font-medium">Cost Price</label>
                            <Input
                              value={variant.pricing.cost_price}
                              onChange={(e) => updateVariant(variant.id, {
                                pricing: { ...variant.pricing, cost_price: e.target.value }
                              })}
                              placeholder="0.00"
                              className="h-8 text-xs"
                            />
                          </div>
                        </div>
                      </div>

                      {/* Inventory Section */}
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <BarChart3 className="h-4 w-4 text-gray-500" />
                          <h4 className="font-medium text-sm">Inventory</h4>
                        </div>
                        <div className="space-y-3">
                          <div className="flex items-center gap-2">
                            <Switch
                              checked={variant.inventory.manage_stock}
                              onCheckedChange={(manage_stock) => updateVariant(variant.id, {
                                inventory: { ...variant.inventory, manage_stock }
                              })}
                              className="scale-75"
                            />
                            <label className="text-xs">Manage stock for this variant</label>
                          </div>
                          {variant.inventory.manage_stock && (
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                              <div className="space-y-1">
                                <label className="text-xs font-medium">Stock Quantity</label>
                                <Input
                                  value={variant.inventory.stock_quantity}
                                  onChange={(e) => updateVariant(variant.id, {
                                    inventory: { ...variant.inventory, stock_quantity: e.target.value }
                                  })}
                                  placeholder="0"
                                  className="h-8 text-xs"
                                />
                              </div>
                              <div className="space-y-1">
                                <label className="text-xs font-medium">Low Stock Threshold</label>
                                <Input
                                  value={variant.inventory.low_stock_threshold}
                                  onChange={(e) => updateVariant(variant.id, {
                                    inventory: { ...variant.inventory, low_stock_threshold: e.target.value }
                                  })}
                                  placeholder="5"
                                  className="h-8 text-xs"
                                />
                              </div>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Shipping Section */}
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <Settings className="h-4 w-4 text-gray-500" />
                          <h4 className="font-medium text-sm">Shipping</h4>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
                          <div className="space-y-1">
                            <label className="text-xs font-medium">Weight (kg)</label>
                            <Input
                              value={variant.shipping.weight}
                              onChange={(e) => updateVariant(variant.id, {
                                shipping: { ...variant.shipping, weight: e.target.value }
                              })}
                              placeholder="0.0"
                              className="h-8 text-xs"
                            />
                          </div>
                          <div className="space-y-1">
                            <label className="text-xs font-medium">Length (cm)</label>
                            <Input
                              value={variant.shipping.dimensions.length}
                              onChange={(e) => updateVariant(variant.id, {
                                shipping: {
                                  ...variant.shipping,
                                  dimensions: { ...variant.shipping.dimensions, length: e.target.value }
                                }
                              })}
                              placeholder="0"
                              className="h-8 text-xs"
                            />
                          </div>
                          <div className="space-y-1">
                            <label className="text-xs font-medium">Width (cm)</label>
                            <Input
                              value={variant.shipping.dimensions.width}
                              onChange={(e) => updateVariant(variant.id, {
                                shipping: {
                                  ...variant.shipping,
                                  dimensions: { ...variant.shipping.dimensions, width: e.target.value }
                                }
                              })}
                              placeholder="0"
                              className="h-8 text-xs"
                            />
                          </div>
                          <div className="space-y-1">
                            <label className="text-xs font-medium">Height (cm)</label>
                            <Input
                              value={variant.shipping.dimensions.height}
                              onChange={(e) => updateVariant(variant.id, {
                                shipping: {
                                  ...variant.shipping,
                                  dimensions: { ...variant.shipping.dimensions, height: e.target.value }
                                }
                              })}
                              placeholder="0"
                              className="h-8 text-xs"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}