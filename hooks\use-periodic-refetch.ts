import { useEffect, useRef } from 'react'
import { useQueryClient } from '@tanstack/react-query'

/**
 * Custom hook for periodic data refetching with smart scheduling
 * based on tab visibility and user activity
 */
export function usePeriodicRefetch() {
  const queryClient = useQueryClient()
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const lastActivityRef = useRef<number>(Date.now())
  const lastRefetchRef = useRef<number>(Date.now())
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Update last activity timestamp
  const updateActivity = () => {
    lastActivityRef.current = Date.now()
  }

  // Refetch all queries with better error handling
  const refetchAllQueries = async () => {
    const now = Date.now()
    const timeSinceLastRefetch = now - lastRefetchRef.current
    
    // Prevent too frequent refetching
    if (timeSinceLastRefetch < 5 * 60 * 1000) { // 5 minutes minimum
      return
    }
    
    lastRefetchRef.current = now
    
    try {
      // Only invalidate if tab is visible
      if (document.visibilityState === 'visible') {
        await queryClient.invalidateQueries()
        console.debug('Periodic refetch completed')
      }
    } catch (error) {
      console.error('Error during periodic refetch:', error)
    }
  }

  // Start periodic refetching
  const startPeriodicRefetch = () => {
    // Clear any existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }

    // Set up periodic refetching
    intervalRef.current = setInterval(async () => {
      // Only refetch if the tab is visible
      if (document.visibilityState === 'visible') {
        const now = Date.now()
        const timeSinceLastActivity = now - lastActivityRef.current
        
        // Only refetch if there's been recent activity (within last 30 minutes)
        // or if it's been a while since last refetch (30 minutes)
        const timeSinceLastRefetch = now - lastRefetchRef.current
        
        if (timeSinceLastActivity < 30 * 60 * 1000 || timeSinceLastRefetch > 30 * 60 * 1000) {
          await refetchAllQueries()
        }
      }
    }, 5 * 60 * 1000) // Check every 5 minutes instead of every minute
  }

  // Stop periodic refetching
  const stopPeriodicRefetch = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }
  }

  // Set up event listeners for activity tracking
  useEffect(() => {
    // Update activity on various user interactions
    const handleUserActivity = () => {
      updateActivity()
    }

    // Handle visibility change with debounce
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        // When tab becomes visible, update activity and consider refetching
        updateActivity()
        
        // Clear any existing timeout
        if (debounceTimeoutRef.current) {
          clearTimeout(debounceTimeoutRef.current)
        }
        
        // Use setTimeout to debounce the refetch
        debounceTimeoutRef.current = setTimeout(() => {
          refetchAllQueries()
        }, 1000)
      }
    }

    // Add event listeners
    window.addEventListener('focus', handleUserActivity)
    window.addEventListener('click', handleUserActivity)
    window.addEventListener('keypress', handleUserActivity)
    window.addEventListener('scroll', handleUserActivity)
    document.addEventListener('visibilitychange', handleVisibilityChange)

    // Start periodic refetching
    startPeriodicRefetch()

    // Clean up
    return () => {
      window.removeEventListener('focus', handleUserActivity)
      window.removeEventListener('click', handleUserActivity)
      window.removeEventListener('keypress', handleUserActivity)
      window.removeEventListener('scroll', handleUserActivity)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      stopPeriodicRefetch()
      
      // Clean up timeouts
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current)
      }
    }
  }, [])

  return {
    startPeriodicRefetch,
    stopPeriodicRefetch,
    updateActivity,
    refetchAllQueries
  }
}