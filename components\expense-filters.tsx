'use client'

import { useState, useEffect, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { 
  Search, 
  Filter, 
  X, 
  Calendar,
  DollarSign,
  Tag,
  CreditCard,
  RotateCcw,
  SlidersHorizontal
} from 'lucide-react'
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet'
import { DatePicker } from '@/components/ui/date-picker'
import { CustomDropdown, CustomDropdownOption } from '@/components/ui/custom-dropdown'
import { type ExpenseRow } from '@/lib/supabase'
import { useCurrency } from '@/lib/currency'
import { cn } from '@/lib/utils'

// Filter state interface
export interface ExpenseFilters {
  searchQuery: string
  categories: string[]
  paymentMethods: string[]
  dateRange: {
    start: Date | null
    end: Date | null
  }
  amountRange: {
    min: number | null
    max: number | null
  }
}

// Initial filter state
export const INITIAL_FILTERS: ExpenseFilters = {
  searchQuery: '',
  categories: [],
  paymentMethods: [],
  dateRange: {
    start: null,
    end: null
  },
  amountRange: {
    min: null,
    max: null
  }
}

// Quick filter presets
const QUICK_FILTERS = [
  { label: 'This Week', value: 'thisWeek' },
  { label: 'This Month', value: 'thisMonth' },
  { label: 'Last Month', value: 'lastMonth' },
  { label: 'Large Expenses (>$100)', value: 'large' },
  { label: 'Small Expenses (<$50)', value: 'small' },
] as const

type QuickFilterValue = typeof QUICK_FILTERS[number]['value']

interface ExpenseFiltersProps {
  expenses: ExpenseRow[]
  filters: ExpenseFilters
  onFiltersChange: (filters: ExpenseFilters) => void
  onFilteredExpensesChange: (filteredExpenses: ExpenseRow[]) => void
  isCollapsible?: boolean
}

export function ExpenseFilters({
  expenses,
  filters,
  onFiltersChange,
  onFilteredExpensesChange,
  isCollapsible = true
}: ExpenseFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [searchDebounce, setSearchDebounce] = useState('')
  const [isMobile, setIsMobile] = useState(false)
  const [isSheetOpen, setIsSheetOpen] = useState(false)
  
  const { formatCurrency } = useCurrency()

  // Mobile detection
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      onFiltersChange({
        ...filters,
        searchQuery: searchDebounce
      })
    }, 300)
    
    return () => clearTimeout(timer)
  }, [searchDebounce])

  // Extract unique categories and payment methods from expenses
  const availableOptions = useMemo(() => {
    const categories = new Set<string>()
    const paymentMethods = new Set<string>()
    
    expenses.forEach(expense => {
      if (expense.category) categories.add(expense.category)
      if (expense.payment_method) paymentMethods.add(expense.payment_method)
    })
    
    return {
      categories: Array.from(categories).map(cat => ({ value: cat, label: cat })),
      paymentMethods: Array.from(paymentMethods).map(pm => ({ value: pm, label: pm }))
    }
  }, [expenses])

  // Filter expenses based on current filters
  const filteredExpenses = useMemo(() => {
    let result = expenses

    // Text search
    if (filters.searchQuery.trim()) {
      const query = filters.searchQuery.toLowerCase()
      result = result.filter(expense => 
        expense.description?.toLowerCase().includes(query) ||
        expense.vendor?.toLowerCase().includes(query) ||
        expense.category?.toLowerCase().includes(query) ||
        expense.expense_id?.toLowerCase().includes(query)
      )
    }

    // Category filter
    if (filters.categories.length > 0) {
      result = result.filter(expense => 
        expense.category && filters.categories.includes(expense.category)
      )
    }

    // Payment method filter
    if (filters.paymentMethods.length > 0) {
      result = result.filter(expense => 
        expense.payment_method && filters.paymentMethods.includes(expense.payment_method)
      )
    }

    // Date range filter
    if (filters.dateRange.start || filters.dateRange.end) {
      result = result.filter(expense => {
        const expenseDate = new Date(expense.expense_date || expense.created_at)
        
        if (filters.dateRange.start && expenseDate < filters.dateRange.start) {
          return false
        }
        
        if (filters.dateRange.end && expenseDate > filters.dateRange.end) {
          return false
        }
        
        return true
      })
    }

    // Amount range filter
    if (filters.amountRange.min !== null || filters.amountRange.max !== null) {
      result = result.filter(expense => {
        if (filters.amountRange.min !== null && expense.amount < filters.amountRange.min) {
          return false
        }
        
        if (filters.amountRange.max !== null && expense.amount > filters.amountRange.max) {
          return false
        }
        
        return true
      })
    }

    return result
  }, [expenses, filters])

  // Update filtered expenses when they change
  useEffect(() => {
    onFilteredExpensesChange(filteredExpenses)
  }, [filteredExpenses, onFilteredExpensesChange])

  // Quick filter handlers
  const applyQuickFilter = (quickFilter: QuickFilterValue) => {
    const now = new Date()
    let newFilters = { ...filters }

    switch (quickFilter) {
      case 'thisWeek':
        const startOfWeek = new Date(now)
        startOfWeek.setDate(now.getDate() - now.getDay())
        const endOfWeek = new Date(startOfWeek)
        endOfWeek.setDate(startOfWeek.getDate() + 6)
        
        newFilters.dateRange = { start: startOfWeek, end: endOfWeek }
        break
        
      case 'thisMonth':
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
        const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0)
        
        newFilters.dateRange = { start: startOfMonth, end: endOfMonth }
        break
        
      case 'lastMonth':
        const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
        const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0)
        
        newFilters.dateRange = { start: startOfLastMonth, end: endOfLastMonth }
        break
        
      case 'large':
        newFilters.amountRange = { min: 100, max: null }
        break
        
      case 'small':
        newFilters.amountRange = { min: null, max: 50 }
        break
    }

    onFiltersChange(newFilters)
  }

  // Clear all filters
  const clearAllFilters = () => {
    setSearchDebounce('')
    onFiltersChange(INITIAL_FILTERS)
  }

  // Handle category selection (multi-select)
  const handleCategoryToggle = (category: string) => {
    const newCategories = filters.categories.includes(category)
      ? filters.categories.filter(c => c !== category)
      : [...filters.categories, category]
    
    onFiltersChange({
      ...filters,
      categories: newCategories
    })
  }

  // Handle payment method selection (multi-select)
  const handlePaymentMethodToggle = (paymentMethod: string) => {
    const newPaymentMethods = filters.paymentMethods.includes(paymentMethod)
      ? filters.paymentMethods.filter(pm => pm !== paymentMethod)
      : [...filters.paymentMethods, paymentMethod]
    
    onFiltersChange({
      ...filters,
      paymentMethods: newPaymentMethods
    })
  }

  // Count active filters
  const activeFilterCount = useMemo(() => {
    let count = 0
    if (filters.searchQuery.trim()) count++
    if (filters.categories.length > 0) count += filters.categories.length
    if (filters.paymentMethods.length > 0) count += filters.paymentMethods.length
    if (filters.dateRange.start || filters.dateRange.end) count++
    if (filters.amountRange.min !== null || filters.amountRange.max !== null) count++
    return count
  }, [filters])

  // Generate active filter tags
  const activeFilterTags = useMemo(() => {
    const tags = []
    
    if (filters.searchQuery.trim()) {
      tags.push({ type: 'search', label: `Search: "${filters.searchQuery}"`, value: 'searchQuery' })
    }
    
    filters.categories.forEach(category => {
      tags.push({ type: 'category', label: `Category: ${category}`, value: category })
    })
    
    filters.paymentMethods.forEach(method => {
      tags.push({ type: 'payment', label: `Payment: ${method}`, value: method })
    })
    
    if (filters.dateRange.start || filters.dateRange.end) {
      const start = filters.dateRange.start?.toLocaleDateString() || 'Start'
      const end = filters.dateRange.end?.toLocaleDateString() || 'End'
      tags.push({ type: 'date', label: `Date: ${start} - ${end}`, value: 'dateRange' })
    }
    
    if (filters.amountRange.min !== null || filters.amountRange.max !== null) {
      const min = filters.amountRange.min !== null ? formatCurrency(filters.amountRange.min) : 'Min'
      const max = filters.amountRange.max !== null ? formatCurrency(filters.amountRange.max) : 'Max'
      tags.push({ type: 'amount', label: `Amount: ${min} - ${max}`, value: 'amountRange' })
    }
    
    return tags
  }, [filters, formatCurrency])

  // Remove specific filter
  const removeFilter = (type: string, value: string) => {
    const newFilters = { ...filters }
    
    switch (type) {
      case 'search':
        newFilters.searchQuery = ''
        setSearchDebounce('')
        break
      case 'category':
        newFilters.categories = filters.categories.filter(c => c !== value)
        break
      case 'payment':
        newFilters.paymentMethods = filters.paymentMethods.filter(pm => pm !== value)
        break
      case 'date':
        newFilters.dateRange = { start: null, end: null }
        break
      case 'amount':
        newFilters.amountRange = { min: null, max: null }
        break
    }
    
    onFiltersChange(newFilters)
  }

  // Render advanced filters content
  const renderAdvancedFilters = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {/* Category Filter */}
      <div className="space-y-2">
        <Label className="flex items-center space-x-1">
          <Tag className="h-4 w-4" />
          <span>Categories</span>
        </Label>
        <div className="space-y-1 max-h-32 overflow-y-auto">
          {availableOptions.categories.map(category => (
            <label
              key={category.value}
              className="flex items-center space-x-2 text-sm cursor-pointer hover:bg-muted p-1 rounded"
            >
              <input
                type="checkbox"
                checked={filters.categories.includes(category.value)}
                onChange={() => handleCategoryToggle(category.value)}
                className="rounded border-gray-300"
              />
              <span>{category.label}</span>
            </label>
          ))}
          {availableOptions.categories.length === 0 && (
            <p className="text-xs text-muted-foreground">No categories available</p>
          )}
        </div>
      </div>

      {/* Payment Method Filter */}
      <div className="space-y-2">
        <Label className="flex items-center space-x-1">
          <CreditCard className="h-4 w-4" />
          <span>Payment Methods</span>
        </Label>
        <div className="space-y-1 max-h-32 overflow-y-auto">
          {availableOptions.paymentMethods.map(method => (
            <label
              key={method.value}
              className="flex items-center space-x-2 text-sm cursor-pointer hover:bg-muted p-1 rounded"
            >
              <input
                type="checkbox"
                checked={filters.paymentMethods.includes(method.value)}
                onChange={() => handlePaymentMethodToggle(method.value)}
                className="rounded border-gray-300"
              />
              <span>{method.label}</span>
            </label>
          ))}
          {availableOptions.paymentMethods.length === 0 && (
            <p className="text-xs text-muted-foreground">No payment methods available</p>
          )}
        </div>
      </div>

      {/* Date Range Filter */}
      <div className="space-y-2">
        <Label className="flex items-center space-x-1">
          <Calendar className="h-4 w-4" />
          <span>Date Range</span>
        </Label>
        <div className="space-y-2">
          <DatePicker
            date={filters.dateRange.start || undefined}
            onDateChange={(date) => onFiltersChange({
              ...filters,
              dateRange: { ...filters.dateRange, start: date || null }
            })}
            placeholder="Start date"
          />
          <DatePicker
            date={filters.dateRange.end || undefined}
            onDateChange={(date) => onFiltersChange({
              ...filters,
              dateRange: { ...filters.dateRange, end: date || null }
            })}
            placeholder="End date"
          />
        </div>
      </div>

      {/* Amount Range Filter */}
      <div className="space-y-2">
        <Label className="flex items-center space-x-1">
          <DollarSign className="h-4 w-4" />
          <span>Amount Range</span>
        </Label>
        <div className="space-y-2">
          <Input
            type="number"
            placeholder="Min amount"
            value={filters.amountRange.min || ''}
            onChange={(e) => onFiltersChange({
              ...filters,
              amountRange: { 
                ...filters.amountRange, 
                min: e.target.value ? parseFloat(e.target.value) : null 
              }
            })}
          />
          <Input
            type="number"
            placeholder="Max amount"
            value={filters.amountRange.max || ''}
            onChange={(e) => onFiltersChange({
              ...filters,
              amountRange: { 
                ...filters.amountRange, 
                max: e.target.value ? parseFloat(e.target.value) : null 
              }
            })}
          />
        </div>
      </div>
    </div>
  )

  return (
    <Card className="border-2">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Search className="h-5 w-5 text-muted-foreground" />
            <CardTitle className="text-lg">Search & Filter Expenses</CardTitle>
            {activeFilterCount > 0 && (
              <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                {activeFilterCount} active
              </span>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            {activeFilterCount > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={clearAllFilters}
                className="text-red-600 hover:text-red-700"
              >
                <RotateCcw className="h-4 w-4 mr-1" />
                Clear All
              </Button>
            )}
            
            {/* Mobile Filter Sheet */}
            {isMobile ? (
              <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
                <SheetTrigger asChild>
                  <Button variant="outline" size="sm">
                    <SlidersHorizontal className="h-4 w-4 mr-1" />
                    Advanced Filters
                  </Button>
                </SheetTrigger>
                <SheetContent side="bottom" className="h-[80vh] overflow-y-auto">
                  <SheetHeader>
                    <SheetTitle>Advanced Filters</SheetTitle>
                    <SheetDescription>
                      Filter your expenses by category, payment method, date range, and amount
                    </SheetDescription>
                  </SheetHeader>
                  <div className="py-6">
                    {renderAdvancedFilters()}
                  </div>
                  <div className="flex items-center space-x-2 pt-4 border-t">
                    <Button 
                      onClick={() => setIsSheetOpen(false)}
                      className="flex-1"
                    >
                      Apply Filters
                    </Button>
                    {activeFilterCount > 0 && (
                      <Button
                        variant="outline"
                        onClick={() => {
                          clearAllFilters()
                          setIsSheetOpen(false)
                        }}
                      >
                        Clear All
                      </Button>
                    )}
                  </div>
                </SheetContent>
              </Sheet>
            ) : (
              isCollapsible && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsExpanded(!isExpanded)}
                >
                  <Filter className={cn("h-4 w-4 transition-transform", isExpanded ? "rotate-180" : "")} />
                </Button>
              )
            )}
          </div>
        </div>
        
        <CardDescription>
          Search and filter your expenses • Found {filteredExpenses.length} of {expenses.length} expenses
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Search Bar - Always Visible */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search by description, vendor, category, or expense ID..."
            value={searchDebounce}
            onChange={(e) => setSearchDebounce(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Quick Filters - Always Visible */}
        <div className="flex flex-wrap gap-2">
          {QUICK_FILTERS.map(quickFilter => (
            <Button
              key={quickFilter.value}
              variant="outline"
              size="sm"
              onClick={() => applyQuickFilter(quickFilter.value)}
              className="text-xs"
            >
              {quickFilter.label}
            </Button>
          ))}
        </div>

        {/* Active Filter Tags */}
        {activeFilterTags.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {activeFilterTags.map((tag, index) => (
              <div
                key={index}
                className="inline-flex items-center gap-1 bg-blue-50 text-blue-700 text-xs font-medium px-2.5 py-1 rounded-md border border-blue-200"
              >
                <span>{tag.label}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeFilter(tag.type, tag.value)}
                  className="h-3 w-3 p-0 hover:bg-blue-100"
                >
                  <X className="h-2 w-2" />
                </Button>
              </div>
            ))}
          </div>
        )}

        {/* Desktop Expandable Advanced Filters */}
        {!isMobile && isExpanded && (
          <div className="pt-4 border-t">
            {renderAdvancedFilters()}
          </div>
        )}
      </CardContent>
    </Card>
  )
}