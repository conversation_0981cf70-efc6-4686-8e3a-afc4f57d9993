-- =============================================
-- MIGRATE PRODUCT_ATTRIBUTES TABLE TO ORGANIZATION MODEL
-- =============================================

-- Add organization_id column to product_attributes table
ALTER TABLE product_attributes 
ADD COLUMN IF NOT EXISTS organization_id uuid REFERENCES organizations(id) ON DELETE CASCADE;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_product_attributes_organization_id ON product_attributes(organization_id);

-- Backfill organization_id from user_id
UPDATE product_attributes 
SET organization_id = (
  SELECT organization_id 
  FROM organization_members 
  WHERE user_id = product_attributes.user_id 
  LIMIT 1
)
WHERE organization_id IS NULL;

-- Make organization_id NOT NULL
ALTER TABLE product_attributes 
ALTER COLUMN organization_id SET NOT NULL;

-- Remove user_id column
ALTER TABLE product_attributes 
DROP COLUMN IF EXISTS user_id;

-- Update RLS policies for organization-based access
DROP POLICY IF EXISTS "Users can view their product attributes" ON product_attributes;
DROP POLICY IF EXISTS "Users can insert product attributes" ON product_attributes;
DROP POLICY IF EXISTS "Users can update their product attributes" ON product_attributes;
DROP POLICY IF EXISTS "Users can delete their product attributes" ON product_attributes;

-- Create new organization-based policies
CREATE POLICY "Organization members can view product attributes" ON product_attributes
  FOR SELECT USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can insert product attributes" ON product_attributes
  FOR INSERT WITH CHECK (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can update product attributes" ON product_attributes
  FOR UPDATE USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can delete product attributes" ON product_attributes
  FOR DELETE USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

-- Grant necessary permissions
GRANT ALL ON product_attributes TO authenticated;