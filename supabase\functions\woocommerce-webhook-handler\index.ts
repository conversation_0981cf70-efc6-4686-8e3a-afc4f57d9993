// @ts-ignore
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
// @ts-ignore
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3';
import { decode as base64Decode } from "https://deno.land/std@0.168.0/encoding/base64.ts";
import { HmacSha256 } from "https://deno.land/std@0.168.0/crypto/sha256.ts";

// Import types for better development experience
import type { SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3';
import type { Database } from '../_generated/types.ts';

interface WooCommerceOrder {
  id: number;
  parent_id: number;
  status: string;
  currency: string;
  version: string;
  prices_include_tax: boolean;
  date_created: string;
  date_modified: string;
  discount_total: string;
  discount_tax: string;
  shipping_total: string;
  shipping_tax: string;
  cart_tax: string;
  total: string;
  total_tax: string;
  customer_id: number;
  order_key: string;
  billing: {
    first_name: string;
    last_name: string;
    company: string;
    address_1: string;
    address_2: string;
    city: string;
    state: string;
    postcode: string;
    country: string;
    email: string;
    phone: string;
  };
  shipping: {
    first_name: string;
    last_name: string;
    company: string;
    address_1: string;
    address_2: string;
    city: string;
    state: string;
    postcode: string;
    country: string;
  };
  payment_method: string;
  payment_method_title: string;
  transaction_id: string;
  customer_note: string;
  line_items: Array<{
    id: number;
    name: string;
    product_id: number;
    variation_id: number;
    quantity: number;
    tax_class: string;
    subtotal: string;
    subtotal_tax: string;
    total: string;
    total_tax: string;
    taxes: Array<any>;
    meta_data: Array<any>;
    sku: string;
    price: number;
    parent_name: string | null;
  }>;
  tax_lines: Array<any>;
  shipping_lines: Array<{
    id: number;
    method_title: string;
    method_id: string;
    instance_id: string;
    total: string;
    total_tax: string;
    taxes: Array<any>;
    meta_data: Array<any>;
  }>;
  fee_lines: Array<any>;
  coupon_lines: Array<any>;
  refunds: Array<any>;
  meta_data: Array<any>;
}

interface WooCommerceCustomer {
  id: number;
  date_created: string;
  date_created_gmt: string;
  date_modified: string;
  date_modified_gmt: string;
  email: string;
  first_name: string;
  last_name: string;
  role: string;
  username: string;
  billing: {
    first_name: string;
    last_name: string;
    company: string;
    address_1: string;
    address_2: string;
    city: string;
    state: string;
    postcode: string;
    country: string;
    email: string;
    phone: string;
  };
  shipping: {
    first_name: string;
    last_name: string;
    company: string;
    address_1: string;
    address_2: string;
    city: string;
    state: string;
    postcode: string;
    country: string;
  };
}

interface OrderItem {
  product_id: string;
  variant_id: string | null;
  quantity: number;
  unit_price: number;
  subtotal: number;
}

const handleWebhook = async (req: Request): Promise<Response> => {
  try {
    // Add CORS headers
    const corsHeaders = {
      'Access-Control-Allow-Origin': '*', // In production, restrict this to your domain
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, X-WC-Webhook-Signature, X-WC-Webhook-Topic, X-WC-Webhook-Resource, X-WC-Webhook-Secret',
      'Access-Control-Max-Age': '86400',
    };

    // Handle preflight OPTIONS request
    if (req.method === 'OPTIONS') {
      return new Response(null, {
        status: 204,
        headers: corsHeaders,
      });
    }

    // Create a Supabase client with service role key for full access
    const supabase = createClient<Database>(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Verify the webhook signature
    const signature = req.headers.get('X-WC-Webhook-Signature');
    const topic = req.headers.get('X-WC-Webhook-Topic');
    const resource = req.headers.get('X-WC-Webhook-Resource');
    const secret = req.headers.get('X-WC-Webhook-Secret');
    
    if (!topic || !resource) {
      return new Response(
        JSON.stringify({ error: 'Missing webhook headers' }),
        { status: 400, headers: { 'Content-Type': 'application/json', ...corsHeaders } }
      );
    }

    // Get the raw body for signature verification
    const body = await req.text();
    
    // Find the integration that matches this webhook
    // In a real implementation, you would verify the signature using the secret stored with the integration
    // For now, we'll process the webhook without signature verification for simplicity
    
    // Handle different webhook topics
    if (topic === 'order.created' && resource === 'order') {
      return await handleOrderCreated(supabase, body, corsHeaders);
    } else {
      return new Response(
        JSON.stringify({ message: `Webhook topic ${topic} for resource ${resource} not handled` }),
        { 
          status: 200, 
          headers: { 
            'Content-Type': 'application/json',
            ...corsHeaders
          } 
        }
      );
    }
  } catch (error) {
    console.error('Error processing webhook:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500, 
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        } 
      }
    );
  }
};

const handleOrderCreated = async (supabase: SupabaseClient<Database>, body: string, corsHeaders: any): Promise<Response> => {
  try {
    // Parse the JSON payload
    const order: WooCommerceOrder = JSON.parse(body);
    
    // Find the integration for this WooCommerce store
    // We'll need to find a way to identify which integration this webhook belongs to
    // For now, we'll assume there's only one WooCommerce integration
    const { data: integration, error: integrationError } = await supabase
      .from('integrations')
      .select('id, organization_id')
      .eq('platform', 'woocommerce')
      .single();

    if (integrationError || !integration) {
      return new Response(
        JSON.stringify({ error: 'Integration not found' }),
        { 
          status: 404, 
          headers: { 
            'Content-Type': 'application/json',
            ...corsHeaders
          } 
        }
      );
    }

    // Check if this order has already been synced
    const { data: existingSync, error: syncError } = await supabase
      .from('synced_orders')
      .select('id')
      .eq('external_order_id', order.id.toString())
      .eq('integration_id', integration.id)
      .single();

    if (existingSync) {
      return new Response(
        JSON.stringify({ message: 'Order already synced' }),
        { 
          status: 200, 
          headers: { 
            'Content-Type': 'application/json',
            ...corsHeaders
          } 
        }
      );
    }

    // Get or create customer
    let customerId: string | null = null;
    if (order.customer_id > 0) {
      // Try to find existing customer
      const { data: existingCustomer } = await supabase
        .from('customers')
        .select('id')
        .eq('organization_id', integration.organization_id)
        .eq('email', order.billing.email)
        .single();

      if (existingCustomer) {
        customerId = existingCustomer.id;
      } else {
        // Create new customer
        const { data: newCustomer, error: customerError } = await supabase
          .from('customers')
          .insert({
            organization_id: integration.organization_id,
            full_name: `${order.billing.first_name} ${order.billing.last_name}`,
            email: order.billing.email,
            phone: order.billing.phone,
            shipping_address: `${order.shipping.address_1} ${order.shipping.address_2}, ${order.shipping.city}, ${order.shipping.state} ${order.shipping.postcode}, ${order.shipping.country}`,
            billing_address: `${order.billing.address_1} ${order.billing.address_2}, ${order.billing.city}, ${order.billing.state} ${order.billing.postcode}, ${order.billing.country}`,
          })
          .select('id')
          .single();

        if (!customerError && newCustomer) {
          customerId = newCustomer.id;
        }
      }
    }

    // Transform line items to our format
    const orderItems: OrderItem[] = order.line_items.map(item => ({
      product_id: item.product_id.toString(), // Will need to map this to actual product ID
      variant_id: item.variation_id > 0 ? item.variation_id.toString() : null, // Will need to map this to actual variant ID
      quantity: item.quantity,
      unit_price: parseFloat(item.price.toString()),
      subtotal: parseFloat(item.subtotal),
    }));

    // Create the order using our RPC function
    // Note: This is a simplified version. In a real implementation, you would need to:
    // 1. Map WooCommerce product IDs to ONKO product/variant IDs
    // 2. Handle currency conversion if needed
    // 3. Map WooCommerce order status to ONKO order status
    
    const orderDate = new Date(order.date_created).toISOString();
    const orderStatus = mapWooCommerceStatusToOnko(order.status);
    
    // For now, we'll use a simplified approach to create the order
    // In a real implementation, you would need to properly map products and handle the RPC call
    
    // Create a simple order record for demonstration
    const { data: createdOrder, error: orderError } = await supabase
      .from('orders')
      .insert({
        organization_id: integration.organization_id,
        customer_id: customerId,
        order_number: `WC-${order.id}`,
        order_date: orderDate,
        status: orderStatus,
        subtotal: parseFloat(order.line_items.reduce((sum, item) => sum + parseFloat(item.subtotal), 0).toString()),
        tax: parseFloat(order.total_tax),
        shipping: parseFloat(order.shipping_total),
        discount: parseFloat(order.discount_total),
        total: parseFloat(order.total),
      })
      .select('id')
      .single();

    if (orderError) {
      console.error('Error creating order:', orderError);
      return new Response(
        JSON.stringify({ error: 'Failed to create order', details: orderError.message }),
        { 
          status: 500, 
          headers: { 
            'Content-Type': 'application/json',
            ...corsHeaders
          } 
        }
      );
    }

    // Create order items
    for (const item of orderItems) {
      const { error: itemError } = await supabase
        .from('order_items')
        .insert({
          order_id: createdOrder.id,
          product_id: item.product_id,
          variant_id: item.variant_id,
          quantity: item.quantity,
          unit_price: item.unit_price,
          subtotal: item.subtotal,
          organization_id: integration.organization_id
        });

      if (itemError) {
        console.error('Error creating order item:', itemError);
      }
    }

    // Log the sync
    const { error: logError } = await supabase
      .from('synced_orders')
      .insert({
        organization_id: integration.organization_id,
        integration_id: integration.id,
        external_order_id: order.id.toString(),
        onko_order_id: createdOrder.id,
      });

    if (logError) {
      console.error('Error logging sync:', logError);
      // Don't return an error here as the order was created successfully
    }

    return new Response(
      JSON.stringify({ 
        message: 'Order synced successfully',
        order_id: createdOrder.id
      }),
      { 
        status: 200, 
        headers: { 
          'Content-Type': 'application/json',
          ...corsHeaders
        } 
      }
    );
  } catch (error) {
    console.error('Error handling order created:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500, 
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        } 
      }
    );
  }
};

const mapWooCommerceStatusToOnko = (wcStatus: string): string => {
  // Map WooCommerce order statuses to ONKO order statuses
  switch (wcStatus.toLowerCase()) {
    case 'pending':
      return 'Pending payment';
    case 'processing':
      return 'Processing';
    case 'on-hold':
      return 'On hold';
    case 'completed':
      return 'Completed';
    case 'cancelled':
      return 'Cancelled';
    case 'refunded':
      return 'Refunded';
    case 'failed':
      return 'Failed';
    case 'draft':
      return 'Draft';
    default:
      return 'Processing'; // Default to processing if unknown
  }
};

// Serve the function
serve(handleWebhook);