// <PERSON>ript to create and configure the company logos storage bucket in Supabase
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Please set NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY in .env.local');
  process.exit(1);
}

// Create Supabase client with service role key (has admin privileges)
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function setupCompanyLogosBucket() {
  console.log('🔧 Setting up company logos storage bucket...');
  
  try {
    // Step 1: Check if bucket already exists
    const { data: buckets, error: listError } = await supabase.storage.listBuckets();
    
    if (listError) {
      throw new Error(`Error listing buckets: ${listError.message}`);
    }
    
    const companyLogosBucket = buckets.find(bucket => bucket.name === 'company-logos');
    
    // Step 2: Create bucket if it doesn't exist
    if (!companyLogosBucket) {
      console.log('Creating new company-logos bucket...');
      
      const { data, error } = await supabase.storage.createBucket('company-logos', {
        public: true, // Make the bucket publicly accessible
        fileSizeLimit: 5 * 1024 * 1024, // 5MB limit
        allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml']
      });
      
      if (error) {
        throw new Error(`Error creating bucket: ${error.message}`);
      }
      
      console.log('✅ Company logos bucket created successfully');
    } else {
      console.log('✅ Company logos bucket already exists');
      
      // Update bucket settings to ensure correct configuration
      const { error } = await supabase.storage.updateBucket('company-logos', {
        public: true,
        fileSizeLimit: 5 * 1024 * 1024,
        allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml']
      });
      
      if (error) {
        throw new Error(`Error updating bucket: ${error.message}`);
      }
      
      console.log('✅ Company logos bucket settings updated');
    }
    
    // Step 3: Set up bucket policy to allow public access for company logos
    console.log('Configuring storage policies...');
    
    // Create a policy to allow authenticated users to upload files
    try {
      await supabase.rpc('create_storage_policy', {
        bucket_name: 'company-logos',
        policy_name: 'authenticated can upload',
        definition: `(bucket_id = 'company-logos'::text) AND (auth.role() = 'authenticated'::text)`,
        policy_for: 'INSERT'
      });
    } catch (error) {
      console.log('Note: Policy may already exist or require manual creation:', error.message);
    }
    
    // Create a policy to allow public access to read files
    try {
      await supabase.rpc('create_storage_policy', {
        bucket_name: 'company-logos',
        policy_name: 'anyone can download',
        definition: `(bucket_id = 'company-logos'::text)`,
        policy_for: 'SELECT'
      });
    } catch (error) {
      console.log('Note: Policy may already exist or require manual creation:', error.message);
    }
    
    // Create a policy to allow users to update their own company logos
    try {
      await supabase.rpc('create_storage_policy', {
        bucket_name: 'company-logos',
        policy_name: 'users can update own company logos',
        definition: `(bucket_id = 'company-logos'::text) AND (auth.uid()::text = (storage.foldername(name))[1])`,
        policy_for: 'UPDATE'
      });
    } catch (error) {
      console.log('Note: Policy may already exist or require manual creation:', error.message);
    }
    
    // Create a policy to allow users to delete their own company logos
    try {
      await supabase.rpc('create_storage_policy', {
        bucket_name: 'company-logos',
        policy_name: 'users can delete own company logos',
        definition: `(bucket_id = 'company-logos'::text) AND (auth.uid()::text = (storage.foldername(name))[1])`,
        policy_for: 'DELETE'
      });
    } catch (error) {
      console.log('Note: Policy may already exist or require manual creation:', error.message);
    }
    
    console.log('✅ Storage policies configured');
    console.log('\n🎉 Company logos storage bucket setup completed successfully!');
    console.log('\nYou can now upload company logos through the application.');
    
  } catch (error) {
    console.error('❌ Error setting up company logos bucket:', error.message);
    console.error('\n💡 Manual Setup Instructions:');
    console.error('1. Go to your Supabase dashboard: https://supabase.com/dashboard');
    console.error('2. Select your project');
    console.error('3. Go to Storage > Buckets');
    console.error('4. Create a new bucket named "company-logos"');
    console.error('5. Set the bucket visibility to "Public"');
    console.error('6. Configure the following policies:');
    console.error('   - Allow authenticated users to upload');
    console.error('   - Allow public read access');
    console.error('   - Allow users to update/delete their own files');
    process.exit(1);
  }
}

setupCompanyLogosBucket();