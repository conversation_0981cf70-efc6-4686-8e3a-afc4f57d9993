import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query'
import { getAllCustomersForOrganization, createCustomer, updateCustomer, deleteCustomer } from '@/lib/supabase-customers'
import type { CustomerRow } from '@/lib/supabase-customers'

// Debounce function to prevent rapid requests
function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  return function (...args: Parameters<T>) {
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(() => func(...args), wait)
  }
}

/**
 * Custom hook to fetch all customers for an organization with React Query
 * @param organizationId - The organization ID to fetch customers for
 * @returns React Query result object with customers data
 */
export function useAllCustomers(organizationId: string | undefined) {
  return useQuery<CustomerRow[], Error>({
    queryKey: ['allCustomers', organizationId],
    queryFn: async () => {
      if (!organizationId) {
        throw new Error('Organization ID is required')
      }
      
      const data = await getAllCustomersForOrganization(organizationId)
      return data || []
    },
    enabled: !!organizationId,
    staleTime: 2 * 60 * 1000, // 2 minutes - consistent with providers
    gcTime: 5 * 60 * 1000, // 5 minutes garbage collection time
    refetchOnMount: true,
    refetchOnWindowFocus: true, // Important for tab switching
    refetchOnReconnect: true,
    refetchInterval: false,
    refetchIntervalInBackground: false,
    retry: 3, // Increased retry count
    retryDelay: (attemptIndex) => {
      return Math.min(1000 * 2 ** attemptIndex, 30000)
    },
    networkMode: 'online' // Handle offline scenarios
  })
}

/**
 * Custom hook to create a new customer with React Query mutation
 * @returns React Query mutation object for creating customers
 */
export function useCreateCustomer() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (customerData: any) => createCustomer(customerData.organizationId, customerData.customerData),
    onSuccess: () => {
      // Invalidate and refetch all customers after creating a new one
      queryClient.invalidateQueries({ queryKey: ['allCustomers'] })
    },
  })
}

/**
 * Custom hook to update an existing customer with React Query mutation
 * @returns React Query mutation object for updating customers
 */
export function useUpdateCustomer() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (params: { customerId: string; customerData: any }) => 
      updateCustomer(params.customerId, params.customerData),
    onSuccess: () => {
      // Invalidate and refetch all customers after updating
      queryClient.invalidateQueries({ queryKey: ['allCustomers'] })
    },
  })
}

/**
 * Custom hook to delete a customer with React Query mutation
 * @returns React Query mutation object for deleting customers
 */
export function useDeleteCustomer() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: deleteCustomer,
    onSuccess: () => {
      // Invalidate and refetch all customers after deleting
      queryClient.invalidateQueries({ queryKey: ['allCustomers'] })
    },
  })
}

/**
 * Custom hook to invalidate customer queries and trigger refetching with debouncing
 * @returns Object with functions to invalidate queries
 */
export function useInvalidateCustomers() {
  const queryClient = useQueryClient()
  
  // Debounced invalidate function
  const debouncedInvalidateAllCustomers = debounce(() => {
    queryClient.invalidateQueries({ queryKey: ['allCustomers'] })
  }, 1000) // 1 second debounce
  
  return {
    invalidateAllCustomers: debouncedInvalidateAllCustomers
  }
}