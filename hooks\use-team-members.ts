import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { 
  getTeamMembers, 
  inviteUserToTeam, 
  updateUserRole, 
  removeUserFromTeam,
  type UserRole
} from '@/lib/team-management'

// Define types
export interface TeamMember {
  id: string
  email: string
  display_name?: string | null
  first_name?: string | null
  last_name?: string | null
  avatar_url?: string | null
  role: UserRole
  created_at: string
}

// Fetch all team members
export const useTeamMembers = () => {
  return useQuery<TeamMember[], Error>({
    queryKey: ['teamMembers'],
    queryFn: getTeamMembers,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

// Invite user to team
export const useInviteUserToTeam = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ email, role }: { email: string; role: UserRole }) => 
      inviteUserToTeam(email, role),
    onSuccess: () => {
      // Invalidate and refetch team members
      queryClient.invalidateQueries({ queryKey: ['teamMembers'] })
    },
  })
}

// Update user role
export const useUpdateUserRole = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ userId, newRole }: { userId: string; newRole: UserRole }) => 
      updateUserRole(userId, newRole),
    onSuccess: () => {
      // Invalidate and refetch team members
      queryClient.invalidateQueries({ queryKey: ['teamMembers'] })
    },
  })
}

// Remove user from team
export const useRemoveUserFromTeam = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (userId: string) => removeUserFromTeam(userId),
    onSuccess: () => {
      // Invalidate and refetch team members
      queryClient.invalidateQueries({ queryKey: ['teamMembers'] })
    },
  })
}
