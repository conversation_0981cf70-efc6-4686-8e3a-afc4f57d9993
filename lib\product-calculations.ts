import { ProductRow, ProductVariantRow } from '@/lib/supabase'

/**
 * Calculate the total cost of a product including packaging
 * @param baseCost The base cost of the product
 * @param packagingCost The packaging cost
 * @returns The total cost
 */
export function calculateTotalCost(baseCost: number, packagingCost: number): number {
  return baseCost + packagingCost
}

/**
 * Calculate profit amount from cost and selling price
 * @param cost The total cost of the product
 * @param price The selling price
 * @returns The profit amount
 */
export function calculateProfitAmount(cost: number, price: number): number {
  return price - cost
}

/**
 * Calculate profit margin as a percentage
 * @param cost The total cost of the product
 * @param price The selling price
 * @returns The profit margin as a percentage
 */
export function calculateProfitMargin(cost: number, price: number): number {
  if (price <= 0) return 0
  return ((price - cost) / price) * 100
}

/**
 * Calculate the sale price based on cost and desired margin
 * @param cost The total cost of the product
 * @param desiredMargin The desired profit margin as a percentage
 * @returns The calculated sale price
 */
export function calculateSalePriceFromMargin(cost: number, desiredMargin: number): number {
  if (desiredMargin >= 100) return Infinity
  return cost / (1 - desiredMargin / 100)
}

/**
 * Check if a product or variant is currently on sale
 * @param startDate The sale start date
 * @param endDate The sale end date
 * @returns Whether the product is currently on sale
 */
export function isCurrentlyOnSale(startDate: string | null, endDate: string | null): boolean {
  if (!startDate || !endDate) return false
  
  const now = new Date()
  const start = new Date(startDate)
  const end = new Date(endDate)
  
  return now >= start && now <= end
}

/**
 * Get the effective price (sale price if on sale, otherwise regular price)
 * @param price The regular price
 * @param salePrice The sale price
 * @param isOnSale Whether the product is on sale
 * @returns The effective price
 */
export function getEffectivePrice(price: number, salePrice: number | null, isOnSale: boolean): number {
  return isOnSale && salePrice ? salePrice : price
}

/**
 * Calculate all product metrics at once
 * @param product The product object
 * @returns An object containing all calculated metrics
 */
export function calculateProductMetrics(product: ProductRow) {
  const totalCost = calculateTotalCost(product.base_cost || 0, product.packaging_cost || 0)
  const effectivePrice = getEffectivePrice(
    product.price || 0, 
    product.sale_price, 
    product.is_on_sale || false
  )
  const profitAmount = calculateProfitAmount(totalCost, effectivePrice)
  const profitMargin = calculateProfitMargin(totalCost, effectivePrice)
  
  return {
    totalCost,
    effectivePrice,
    profitAmount,
    profitMargin
  }
}

/**
 * Calculate all variant metrics at once
 * @param variant The product variant object
 * @returns An object containing all calculated metrics
 */
export function calculateVariantMetrics(variant: ProductVariantRow) {
  const effectivePrice = getEffectivePrice(
    variant.price, 
    variant.sale_price, 
    variant.is_on_sale || false
  )
  const totalCost = variant.cost_adjustment + (variant.price - variant.cost_adjustment) // Simplified for demo
  const profitAmount = calculateProfitAmount(totalCost, effectivePrice)
  const profitMargin = calculateProfitMargin(totalCost, effectivePrice)
  
  return {
    totalCost,
    effectivePrice,
    profitAmount,
    profitMargin
  }
}

/**
 * Format currency for display
 * @param amount The amount to format
 * @param currency The currency code (default: 'USD')
 * @returns Formatted currency string
 */
export function formatCurrency(amount: number, currency: string = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount)
}

/**
 * Format percentage for display
 * @param percentage The percentage to format
 * @param decimals Number of decimal places (default: 2)
 * @returns Formatted percentage string
 */
export function formatPercentage(percentage: number, decimals: number = 2): string {
  return `${percentage.toFixed(decimals)}%`
}