-- =============================================
-- NOTIFICATIONS TABLE FOR ORGANIZATION-BASED APP
-- =============================================

-- Create notifications table
create table if not exists notifications (
  id uuid default gen_random_uuid() primary key,
  organization_id uuid references organizations(id) on delete cascade not null,
  type text not null,
  title text not null,
  message text not null,
  related_entity_type text,
  related_entity_id uuid,
  is_read boolean default false,
  is_archived boolean default false,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create trigger for notification updates
create or replace function handle_notifications_updated_at()
returns trigger as $$
begin
  new.updated_at = timezone('utc'::text, now());
  return new;
end;
$$ language plpgsql;

create trigger notifications_updated_at before update on notifications
  for each row execute procedure handle_notifications_updated_at();

-- Enable RLS on notifications table
alter table notifications enable row level security;

-- Notification policies
create policy "Organization members can view notifications" on notifications
  for select using (
    organization_id in (
      select organization_id 
      from organization_members 
      where user_id = auth.uid()
    )
  );

create policy "Organization members can insert notifications" on notifications
  for insert with check (
    organization_id in (
      select organization_id 
      from organization_members 
      where user_id = auth.uid()
    )
  );

create policy "Organization members can update notifications" on notifications
  for update using (
    organization_id in (
      select organization_id 
      from organization_members 
      where user_id = auth.uid()
    )
  );

create policy "Organization members can delete notifications" on notifications
  for delete using (
    organization_id in (
      select organization_id 
      from organization_members 
      where user_id = auth.uid()
    )
  );

-- Create indexes for better query performance
create index if not exists idx_notifications_organization_id on notifications(organization_id);
create index if not exists idx_notifications_is_read on notifications(is_read);
create index if not exists idx_notifications_is_archived on notifications(is_archived);
create index if not exists idx_notifications_created_at on notifications(created_at);
create index if not exists idx_notifications_type on notifications(type);

-- Grant necessary permissions
grant all on notifications to authenticated;