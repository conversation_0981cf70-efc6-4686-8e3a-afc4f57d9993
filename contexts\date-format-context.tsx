'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { 
  DateFormatPreferences, 
  DEFAULT_DATE_FORMAT_PREFERENCES,
  formatUserDate,
  formatUserTime,
  formatUserDateTime,
  getFirstDayOfWeekNumber
} from '@/lib/date-format'

interface DateFormatContextType {
  preferences: DateFormatPreferences
  setPreferences: (preferences: DateFormatPreferences) => void
  formatUserDate: (date: Date) => string
  formatUserTime: (date: Date) => string
  formatUserDateTime: (date: Date) => string
  getFirstDayOfWeekNumber: () => number
}

const DateFormatContext = createContext<DateFormatContextType | undefined>(undefined)

export function useDateFormat() {
  const context = useContext(DateFormatContext)
  if (context === undefined) {
    throw new Error('useDateFormat must be used within a DateFormatProvider')
  }
  return context
}

export function DateFormatProvider({ children }: { children: React.ReactNode }) {
  const [preferences, setPreferences] = useState<DateFormatPreferences>(DEFAULT_DATE_FORMAT_PREFERENCES)

  useEffect(() => {
    // Check for saved preferences in localStorage
    const savedPreferences = localStorage.getItem('dateFormatPreferences')
    if (savedPreferences) {
      try {
        setPreferences(JSON.parse(savedPreferences))
      } catch (error) {
        console.error('Error parsing date format preferences:', error)
      }
    }
  }, [])

  useEffect(() => {
    // Save preferences to localStorage
    localStorage.setItem('dateFormatPreferences', JSON.stringify(preferences))
  }, [preferences])

  const value = {
    preferences,
    setPreferences,
    formatUserDate: (date: Date) => formatUserDate(date, preferences),
    formatUserTime: (date: Date) => formatUserTime(date, preferences),
    formatUserDateTime: (date: Date) => formatUserDateTime(date, preferences),
    getFirstDayOfWeekNumber: () => getFirstDayOfWeekNumber(preferences)
  }

  return (
    <DateFormatContext.Provider value={value}>
      {children}
    </DateFormatContext.Provider>
  )
}