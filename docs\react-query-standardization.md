# React Query Configuration Standardization

This document outlines the standardization of React Query configurations across all hooks in the application to ensure consistent behavior, especially for tab switching scenarios.

## Problem

Previously, different React Query hooks had inconsistent configurations:
- Some hooks used `staleTime: 30 * 1000` (30 seconds)
- Others used `staleTime: 2 * 60 * 1000` (2 minutes)
- Retry configurations varied between hooks
- Some hooks had different `retryDelay` implementations

This inconsistency caused unpredictable behavior when switching browser tabs and returning to the application.

## Solution

Standardized all React Query hooks to use the same configuration as defined in the Providers component:

```typescript
{
  staleTime: 2 * 60 * 1000, // 2 minutes for responsive updates
  gcTime: 5 * 60 * 1000, // 5 minutes garbage collection time
  refetchOnMount: true,
  refetchOnWindowFocus: true, // Critical for tab switching
  refetchOnReconnect: true,
  refetchInterval: false,
  refetchIntervalInBackground: false,
  retry: 3, // Increased retry count
  retryDelay: (attemptIndex) => {
    return Math.min(1000 * 2 ** attemptIndex, 30000)
  },
  networkMode: 'online' // Handle offline scenarios
}
```

## Updated Hooks

The following hooks were updated to use the standardized configuration:

### 1. `hooks/use-expenses.ts`
- Updated `staleTime` from 30 seconds to 2 minutes
- Updated `retry` from 2 to 3
- Updated `retryDelay` function
- Added `networkMode: 'online'`

### 2. `hooks/use-customers.ts`
- Updated `staleTime` from 30 seconds to 2 minutes
- Updated `retry` from 2 to 3
- Updated `retryDelay` function
- Added `networkMode: 'online'`

### 3. `hooks/use-invoices.ts`
- Updated `staleTime` from 30 seconds to 2 minutes
- Updated `retry` from 2 to 3
- Updated `retryDelay` function
- Added `networkMode: 'online'`

### 4. `hooks/use-orders.ts`
- Updated `staleTime` from 30 seconds to 2 minutes
- Updated `retry` from 2 to 3
- Updated `retryDelay` function
- Added `networkMode: 'online'`

## Hooks Already Standardized

The following hooks already had the correct configuration and required no changes:

### 1. `hooks/use-products.ts`
- Already used `staleTime: 2 * 60 * 1000`
- Already had proper retry configuration
- Already included `networkMode: 'online'`

### 2. `hooks/use-profile.ts`
- Already used `staleTime: 2 * 60 * 1000`
- Already had proper retry configuration
- Already included `networkMode: 'online'`

### 3. `hooks/use-purchase-orders.ts`
- Created with standardized configuration
- Uses `staleTime: 2 * 60 * 1000`
- Proper retry configuration
- Includes `networkMode: 'online'`

## Benefits

1. **Consistent Tab Switching Behavior**: All hooks now properly refetch data when users return to the tab after being away
2. **Predictable Performance**: Standardized caching behavior across the application
3. **Better Error Handling**: Consistent retry mechanisms with exponential backoff
4. **Offline Support**: All hooks now include `networkMode: 'online'` for better offline scenario handling
5. **Maintainability**: Single configuration standard makes future updates easier

## Testing

All updated hooks maintain the same API and functionality, with only the internal React Query configuration changing. Existing tests should continue to pass without modification.