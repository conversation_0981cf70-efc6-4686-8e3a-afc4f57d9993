'use client'

import React, { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { useToast } from '@/components/ui/use-toast'
import { createSupabaseClient } from '@/lib/supabase'
import { useAuth } from '@/contexts/auth-context'

export default function CreateOrganizationPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { user, forceSessionAndDataRefresh } = useAuth()
  const supabase = createSupabaseClient()
  const [organizationName, setOrganizationName] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const handleCreateOrganization = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!organizationName.trim()) {
      toast({
        title: 'Organization name required',
        description: 'Please enter a name for your organization.',
        variant: 'destructive',
      })
      return
    }
    
    setIsLoading(true)
    
    try {
      // Create a new organization
      const { data: organization, error: orgError } = await supabase
        .from('organizations')
        .insert({
          name: organizationName.trim(),
          owner_id: user?.id
        })
        .select()
        .single()
      
      if (orgError) {
        throw new Error(orgError.message)
      }
      
      // Create organization member record for the owner
      const { error: memberError } = await supabase
        .from('organization_members')
        .insert({
          organization_id: organization.id,
          user_id: user?.id,
          role: 'admin',
          invited_by: user?.id,
          accepted_at: new Date().toISOString()
        })
      
      if (memberError) {
        throw new Error(memberError.message)
      }
      
      // Update user's profile with the organization name
      const { error: profileError } = await supabase
        .from('profiles')
        .update({ 
          business_name: organizationName.trim()
        })
        .eq('id', user?.id)
      
      if (profileError) {
        console.warn('Could not update profile:', profileError)
      }
      
      toast({
        title: 'Organization Created',
        description: 'Your organization has been successfully created.',
      })
      
      // Refresh the session and redirect to dashboard
      await forceSessionAndDataRefresh()
      router.push('/dashboard')
    } catch (error) {
      console.error('Error creating organization:', error)
      toast({
        title: 'Creation Failed',
        description: error instanceof Error ? error.message : 'Failed to create organization. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold text-center">Create Organization</CardTitle>
          <CardDescription className="text-center">
            Create a new workspace for your business
          </CardDescription>
        </CardHeader>
        <form onSubmit={handleCreateOrganization}>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="organization-name">Organization Name</Label>
              <Input
                id="organization-name"
                placeholder="Enter your organization name"
                value={organizationName}
                onChange={(e) => setOrganizationName(e.target.value)}
                disabled={isLoading}
                className="h-10"
              />
            </div>
          </CardContent>
          <CardFooter className="flex flex-col space-y-4">
            <Button 
              type="submit" 
              className="w-full h-10"
              disabled={isLoading || !organizationName.trim()}
            >
              {isLoading ? 'Creating...' : 'Create Organization'}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  )
}