# Organization Name Fix

## Problem
When users sign up for the application, their organization is created with a default name "My Organization". When they later update their company name in the Company Settings, only the `business_name` field in the `profiles` table is updated, but the actual organization name in the `organizations` table remains as "My Organization".

This causes the accept invitation page to display "My Organization" instead of the actual organization name because it retrieves the name from the `organizations` table.

## Root Cause
The [CompanySettings](file:///Qoder/onko/components/settings/company-settings.tsx#L27-L393) component only updates the user's profile with the business name but doesn't synchronize it with the organization name in the organizations table.

## Solution
The fix involves modifying the [handleCompanyInfoUpdate](file:///Qoder/onko/components/settings/company-settings.tsx#L255-L309) function in the [CompanySettings](file:///Qoder/onko/components/settings/company-settings.tsx#L27-L393) component to also update the organization name when the business name is changed:

1. After updating the profile with the new business name, the function now:
   - Retrieves the user's organization ID from the `organization_members` table
   - Updates the organization name in the `organizations` table to match the business name
   - <PERSON><PERSON><PERSON> handles errors to ensure the profile update still succeeds even if the organization update fails

2. A SQL script [fix-organization-names.sql](file:///Qoder/onko/scripts/fix-organization-names.sql) has been created to fix existing organizations that still have the default "My Organization" name.

## Implementation Details
The fix was implemented in [components/settings/company-settings.tsx](file:///Qoder/onko/components/settings/company-settings.tsx):

- Added code to retrieve the user's organization membership
- Added code to update the organization name when the business name changes
- Added proper error handling to prevent issues if the organization update fails
- Maintained backward compatibility with existing functionality

## Testing
The fix has been verified to work correctly:

1. When a user updates their company name in settings, both the profile and organization records are updated
2. New invitations show the correct organization name
3. The accept invitation page now displays the actual organization name instead of "My Organization"

## Database Fix Script
The [fix-organization-names.sql](file:///Qoder/onko/scripts/fix-organization-names.sql) script can be run to fix existing organizations:

1. Updates organizations with "My Organization" name to use the business_name from the owner's profile
2. For organizations without a business_name, uses the email prefix as the organization name
3. Includes a verification query to check the results

To run the script:
```sql
-- Run the script in your Supabase SQL Editor
-- It will update all organizations still using the default name
```

## Future Considerations
This fix ensures that organization names stay in sync with business names. Future enhancements could include:

1. Adding a separate organization name field that can be different from the business name
2. Implementing real-time updates using Supabase Realtime
3. Adding more robust error handling and logging