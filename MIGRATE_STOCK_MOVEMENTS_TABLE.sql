-- =============================================
-- MIGRATE STOCK_MOVEMENTS TABLE TO ORGANIZATION MODEL
-- =============================================

-- Add organization_id column to stock_movements table
ALTER TABLE stock_movements 
ADD COLUMN IF NOT EXISTS organization_id uuid REFERENCES organizations(id) ON DELETE CASCADE;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_stock_movements_organization_id ON stock_movements(organization_id);

-- Backfill organization_id from user_id
UPDATE stock_movements 
SET organization_id = (
  SELECT organization_id 
  FROM organization_members 
  WHERE user_id = stock_movements.user_id 
  LIMIT 1
)
WHERE organization_id IS NULL;

-- Make organization_id NOT NULL
ALTER TABLE stock_movements 
ALTER COLUMN organization_id SET NOT NULL;

-- Remove user_id column
ALTER TABLE stock_movements 
DROP COLUMN IF EXISTS user_id;

-- Update RLS policies for organization-based access
DROP POLICY IF EXISTS "Users can view their stock movements" ON stock_movements;
DROP POLICY IF EXISTS "Users can insert stock movements" ON stock_movements;
DROP POLICY IF EXISTS "Users can update their stock movements" ON stock_movements;
DROP POLICY IF EXISTS "Users can delete their stock movements" ON stock_movements;

-- Create new organization-based policies
CREATE POLICY "Organization members can view stock movements" ON stock_movements
  FOR SELECT USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can insert stock movements" ON stock_movements
  FOR INSERT WITH CHECK (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can update stock movements" ON stock_movements
  FOR UPDATE USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can delete stock movements" ON stock_movements
  FOR DELETE USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

-- Grant necessary permissions
GRANT ALL ON stock_movements TO authenticated;