'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useAuth } from '@/contexts/auth-context'
import { createSupabaseClient } from '@/lib/supabase'
import { Badge } from '@/components/ui/badge'

export function BillingSettings() {
  const { user } = useAuth()
  const supabase = createSupabaseClient()
  const [subscriptionTier, setSubscriptionTier] = useState<string>('free')
  const [nextBillingDate, setNextBillingDate] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchSubscriptionInfo = async () => {
      if (!user) return

      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('subscription_tier')
          .eq('id', user.id)
          .single()

        if (error) {
          console.error('Error fetching subscription info:', error)
          return
        }

        if (data) {
          setSubscriptionTier(data.subscription_tier || 'free')
          
          // Calculate next billing date (assuming monthly billing)
          const today = new Date()
          const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, today.getDate())
          setNextBillingDate(nextMonth.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          }))
        }
      } catch (error) {
        console.error('Error fetching subscription info:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchSubscriptionInfo()
  }, [user])

  // Get plan name based on subscription tier
  const getPlanName = () => {
    switch (subscriptionTier) {
      case 'free':
        return 'Free Plan'
      case 'pro':
        return 'Pro Plan'
      case 'enterprise':
        return 'Enterprise Plan'
      default:
        return 'Free Plan'
    }
  }

  // Get plan features based on subscription tier
  const getPlanFeatures = () => {
    switch (subscriptionTier) {
      case 'free':
        return [
          'Up to 100 products',
          'Basic inventory tracking',
          'Email support',
          'Community access'
        ]
      case 'pro':
        return [
          'Unlimited products',
          'Advanced inventory tracking',
          'Priority email support',
          'Analytics dashboard',
          'Export to CSV/PDF'
        ]
      case 'enterprise':
        return [
          'Unlimited products',
          'Advanced inventory tracking',
          '24/7 priority support',
          'Advanced analytics',
          'Custom reporting',
          'API access',
          'Dedicated account manager'
        ]
      default:
        return [
          'Up to 100 products',
          'Basic inventory tracking',
          'Email support',
          'Community access'
        ]
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Billing Information</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Current Plan */}
          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-gray-900">Current Plan</h3>
              <p className="text-xs text-gray-500">Your current subscription details</p>
            </div>
            
            {loading ? (
              <div className="space-y-3">
                <div className="h-4 w-32 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-3 w-48 bg-gray-200 rounded animate-pulse"></div>
              </div>
            ) : (
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div>
                  <div className="flex items-center gap-2">
                    <span className="text-base font-medium text-gray-900">{getPlanName()}</span>
                    <Badge variant="secondary" className="text-xs">
                      {subscriptionTier.charAt(0).toUpperCase() + subscriptionTier.slice(1)}
                    </Badge>
                  </div>
                  {nextBillingDate && (
                    <p className="text-xs text-gray-500 mt-1">
                      Next billing date: {nextBillingDate}
                    </p>
                  )}
                </div>
                <Button 
                  variant="outline" 
                  className="h-8 px-3 text-xs"
                  disabled
                >
                  Manage Subscription
                </Button>
              </div>
            )}
          </div>

          {/* Plan Features */}
          {!loading && (
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-900">Plan Features</h3>
                <p className="text-xs text-gray-500">What's included in your plan</p>
              </div>
              
              <ul className="space-y-2">
                {getPlanFeatures().map((feature, index) => (
                  <li key={index} className="flex items-center text-xs text-gray-600">
                    <span className="w-1.5 h-1.5 bg-green-500 rounded-full mr-2"></span>
                    {feature}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Upgrade Section */}
          <div className="space-y-4 pt-2">
            <div>
              <h3 className="text-sm font-medium text-gray-900">Upgrade Your Plan</h3>
              <p className="text-xs text-gray-500">Get more features with a higher tier plan</p>
            </div>
            
            <Button 
              variant="default" 
              className="w-full h-9 text-sm"
              disabled
            >
              View All Plans
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}