'use client'

import React, { useState, useEffect, useRef, useMemo } from 'react'
import { 
  <PERSON><PERSON>, 
  Dialog<PERSON>ontent, 
  DialogHeader, 
  DialogTitle,
  DialogDescription
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { DatePicker } from '@/components/ui/date-picker'
import { getSupabaseClient } from '@/lib/supabase'
import { useToast } from '@/components/ui/use-toast'
import { useAuth } from '@/contexts/auth-context'
import { useCurrency } from '@/contexts/currency-context'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { PlusCircle, X, Search } from 'lucide-react'
import { CustomDropdown, CustomDropdownOption } from '@/components/ui/custom-dropdown'
import { formatCurrencyStandalone } from '@/lib/currency'
import { useAllCustomers, useCreateCustomer } from '@/hooks/use-customers'
import { NewCustomerModal } from '@/components/sales/customers/NewCustomerModal'
import { createOrder, mapUIStatusToDatabaseStatus } from '@/lib/supabase-orders'
import { useInvalidateOrders } from '@/hooks/use-orders'
import { type ProductRow, type ProductVariantRow } from '@/lib/supabase'
import { ProductSelector } from './ProductSelector'


interface VariantAttributes {
  size?: string
  color?: string
  material?: string
  style?: string
}

interface LineItem {
  id: string
  productId: string | null
  variantId: string | null
  productName: string
  variantName?: string
  sku: string
  quantity: number
  unitPrice: number
  subtotal: number
  variantAttributes?: VariantAttributes
  isNewProduct?: boolean
}

interface CustomerOption {
  value: string
  label: string
  isCustom?: boolean
  isDivider?: boolean
}

// Updated interface to match organization-based model
interface ProductRowOrg {
  id: string
  organization_id: string
  category_id: string | null
  name: string
  description: string | null
  brand: string | null
  supplier: string | null
  base_sku: string | null
  has_variants: boolean
  track_inventory: boolean
  is_active: boolean
  base_cost: number | null
  packaging_cost: number | null
  price: number | null
  size: string | null
  color: string | null
  stock_quantity: number | null
  low_stock_threshold: number | null
  barcode: string | null
  image_url: string | null
  sale_price: number | null
  sale_start_date: string | null
  sale_end_date: string | null
  batch_reference: string | null
  purchase_date: string | null
  notes: string | null
  total_cost: number | null
  effective_price: number | null
  is_on_sale: boolean | null
  profit_amount: number | null
  profit_margin: number | null
  created_at: string
  updated_at: string
}

interface ProductVariantRowOrg {
  id: string
  product_id: string
  organization_id: string
  sku: string
  variant_name: string | null
  size: string | null
  color: string | null
  material: string | null
  style: string | null
  weight: number | null
  dimensions: any | null
  cost_adjustment: number | null
  base_cost: number | null
  price: number | null
  sale_price: number | null
  sale_start_date: string | null
  sale_end_date: string | null
  stock_quantity: number | null
  low_stock_threshold: number | null
  reserved_quantity: number | null
  is_active: boolean
  barcode: string | null
  image_urls: string[] | null
  effective_price: number | null
  is_on_sale: boolean | null
  available_quantity: number | null
  created_at: string
  updated_at: string
}

interface NewOrderModalProps {
  isOpen: boolean
  onClose: () => void
  products: (ProductRowOrg & { variants?: ProductVariantRowOrg[] })[]
  isLoadingProducts?: boolean
}

export function NewOrderModal({ isOpen, onClose, products, isLoadingProducts }: NewOrderModalProps) {
  const { user, organizationId } = useAuth()
  const { formatCurrency } = useCurrency()
  const { data: customers = [], isLoading: isLoadingCustomers } = useAllCustomers(organizationId || undefined)
  const createCustomerMutation = useCreateCustomer()
  const invalidateOrders = useInvalidateOrders()
  const [customer, setCustomer] = useState('')
  const [orderNumber, setOrderNumber] = useState('')
  const [orderDate, setOrderDate] = useState<Date>(new Date())
  const [orderStatus, setOrderStatus] = useState<'Pending payment' | 'Processing' | 'On hold' | 'Completed' | 'Cancelled' | 'Refunded' | 'Failed' | 'Draft'>('Pending payment')
  const [lineItems, setLineItems] = useState<LineItem[]>([])
  const [errors, setErrors] = useState<{orderNumber?: string}>({})
  const [openProductDropdown, setOpenProductDropdown] = useState(false)
  const [activeItemIndex, setActiveItemIndex] = useState<string | null>(null)
  const [isAddCustomerModalOpen, setIsAddCustomerModalOpen] = useState(false)

  // Financial fields
  const [shippingCost, setShippingCost] = useState<number>(0)
  const [taxRate, setTaxRate] = useState<number>(0)
  const [discountAmount, setDiscountAmount] = useState<number>(0)
  
  // Ref to track if we've initialized line items
  const hasInitializedLineItems = useRef(false)
  
  // Memoized available customers list
  const availableCustomers = useMemo(() => {
    if (!customers) return []
    
    const customerOptions: CustomDropdownOption[] = customers.map(cust => ({
      value: cust.id,
      label: cust.full_name
    }))
    
    // Add divider and "Create New Customer" option at the bottom
    customerOptions.push(
      { value: 'divider', label: '', isDivider: true },
      { value: 'create-new', label: 'Create New Customer' }
    )
    
    return customerOptions
  }, [customers])
  
  const { toast } = useToast()
  const supabase = getSupabaseClient()

  // Calculate subtotal for all line items
  const calculateSubtotal = () => {
    return lineItems.reduce((sum, item) => sum + item.subtotal, 0)
  }

  // Calculate tax amount based on subtotal and tax rate
  const calculateTaxAmount = () => {
    const subtotal = calculateSubtotal()
    return subtotal * (taxRate / 100)
  }

  // Calculate total including tax, shipping, and discount
  const calculateTotal = () => {
    const subtotal = calculateSubtotal()
    const taxAmount = calculateTaxAmount()
    return subtotal + taxAmount + shippingCost - discountAmount
  }

  // Generate order number when component mounts
  useEffect(() => {
    if (isOpen) {
      // Reset initialization flag when modal opens
      hasInitializedLineItems.current = false
      
      // Generate order number (simplified version)
      const date = new Date()
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const random = Math.floor(1000 + Math.random() * 9000)
      const newOrderNumber = `ORD-${year}${month}${day}-${random}`
      setOrderNumber(newOrderNumber)
      
      // Reset line items when modal opens
      setLineItems([])
    }
  }, [isOpen])

  // Initialize with an empty line item
  useEffect(() => {
    if (isOpen && lineItems.length === 0 && !hasInitializedLineItems.current) {
      handleAddProduct()
      hasInitializedLineItems.current = true
    }
  }, [isOpen, lineItems.length])

  // Handle quantity change for a line item
  const handleQuantityChange = (id: string, quantity: number) => {
    setLineItems(prevItems => 
      prevItems.map(item => 
        item.id === id 
          ? { ...item, quantity: quantity, subtotal: quantity * item.unitPrice } 
          : item
      )
    )
  }

  // Handle unit price change for a line item
  const handleUnitPriceChange = (id: string, unitPrice: number) => {
    setLineItems(prevItems => 
      prevItems.map(item => 
        item.id === id 
          ? { ...item, unitPrice: unitPrice, subtotal: item.quantity * unitPrice } 
          : item
      )
    )
  }

  // Remove a line item
  const handleRemoveItem = (id: string) => {
    setLineItems(prevItems => prevItems.filter(item => item.id !== id))
  }

  // Add a new product line
  const handleAddProduct = () => {
    const newItem: LineItem = {
      id: `item-${Date.now()}`,
      productId: null,
      variantId: null,
      productName: '',
      sku: '',
      quantity: 1,
      unitPrice: 0,
      subtotal: 0
    }
    setLineItems(prev => [...prev, newItem])
  }

  // Handle product selection
  const handleProductSelect = (itemId: string, product: ProductRowOrg, variant?: ProductVariantRowOrg) => {
    setLineItems(prevItems => 
      prevItems.map(item => {
        if (item.id === itemId) {
          const unitPrice = variant ? (variant.price || 0) : (product.price || 0)
          const updatedItem = {
            ...item,
            productId: product.id,
            variantId: variant?.id || null,
            productName: product.name,
            variantName: variant?.variant_name || undefined,
            sku: variant?.sku || product.base_sku || '',
            unitPrice: unitPrice,
            subtotal: unitPrice * item.quantity,
            variantAttributes: variant ? {
              size: variant.size || undefined,
              color: variant.color || undefined,
              material: variant.material || undefined,
              style: variant.style || undefined
            } : undefined
          }
          return updatedItem
        }
        return item
      })
    )
    setOpenProductDropdown(false)
    setActiveItemIndex(null)
  }

  // Handle customer selection
  const handleCustomerSelect = (value: string) => {
    if (value === 'create-new') {
      setIsAddCustomerModalOpen(true)
    } else {
      setCustomer(value)
    }
  }

  // Handle new customer creation
  const handleCreateCustomer = async (customerData: any) => {
    if (!organizationId) return
    
    try {
      // Call the mutate function from the mutation hook
      // Structure the data correctly for the useCreateCustomer hook
      const result = await createCustomerMutation.mutateAsync({
        organizationId: organizationId,
        customerData: {
          full_name: customerData.full_name,
          email: customerData.email,
          phone: customerData.phone,
          shipping_address: customerData.shipping_address,
          billing_address: customerData.billing_address,
          notes: customerData.notes
        }
      })
      
      toast({
        title: "Customer Created",
        description: "The new customer has been created successfully."
      })
      setIsAddCustomerModalOpen(false)
      // Set the newly created customer as selected
      setCustomer(result.id)
    } catch (error: any) {
      console.error('Error creating customer:', error)
      toast({
        title: "Error",
        description: error.message || "Failed to create customer. Please try again.",
        variant: "destructive"
      })
    }
  }

  // Handle form submission
  const handleSubmit = async (saveAsDraft: boolean) => {
    // Validate required fields
    const newErrors: {orderNumber?: string} = {}
    
    if (!customer) {
      toast({
        title: "Error",
        description: "Please select a customer",
        variant: "destructive"
      })
      return
    }
    
    if (!orderNumber.trim()) {
      newErrors.orderNumber = 'Order Number is required'
    }
    
    if (lineItems.length === 0) {
      toast({
        title: "Error",
        description: "At least one line item is required",
        variant: "destructive"
      })
      return
    }
    
    // Validate line items
    for (const item of lineItems) {
      if (!item.productName.trim()) {
        toast({
          title: "Error",
          description: "Product name is required for all items",
          variant: "destructive"
        })
        return
      }
      
      if (item.quantity <= 0) {
        toast({
          title: "Error",
          description: "Quantity must be greater than zero",
          variant: "destructive"
        })
        return
      }
      
      if (item.unitPrice < 0) {
        toast({
          title: "Error",
          description: "Unit price cannot be negative",
          variant: "destructive"
        })
        return
      }
      
      if (!item.productId) {
        toast({
          title: "Error",
          description: "Product must be selected for all items",
          variant: "destructive"
        })
        return
      }
    }
    
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors)
      return
    }
    
    if (!organizationId) {
      toast({
        title: "Error",
        description: "Organization not available",
        variant: "destructive"
      })
      return
    }
    
    try {
      // Prepare order data
      const orderData = {
        customer_id: customer,
        order_number: orderNumber,
        order_date: orderDate.toISOString(),
        status: mapUIStatusToDatabaseStatus(orderStatus) as 'pending' | 'completed' | 'failed',
        subtotal: calculateSubtotal(),
        tax: calculateTaxAmount(),
        shipping: shippingCost,
        discount: discountAmount,
        total: calculateTotal(),
        items: lineItems.map(item => ({
          product_id: item.productId!,
          variant_id: item.variantId || null,
          quantity: item.quantity,
          unit_price: item.unitPrice,
          subtotal: item.subtotal
        }))
      }
      
      // Create the order
      await createOrder(organizationId, orderData)
      
      // Call the invalidate function properly
      invalidateOrders.invalidateAllOrders()
      
      toast({
        title: "Order Created",
        description: `Order has been created with status: ${orderStatus}`
      })
      
      // Close the modal and reset
      onClose()
      setLineItems([])
    } catch (error: any) {
      console.error('Error creating order:', error)
      toast({
        title: "Error",
        description: error.message || "Failed to create order. Please try again.",
        variant: "destructive"
      })
    }
  }

  // Early return if not open
  if (!isOpen) {
    return null
  }

  return (
    <>
      <Dialog open={isOpen} onOpenChange={(open) => {
        if (!open) {
          onClose();
        }
      }} modal={true}>
        <DialogContent className="max-w-5xl p-0 rounded-2xl shadow-xl border-0 bg-white dark:bg-gray-900 overflow-hidden" hideCloseButton>
          <DialogHeader className="sr-only">
            <DialogTitle>New Order</DialogTitle>
            <DialogDescription>Create a new customer order</DialogDescription>
          </DialogHeader>
          <div className="flex">
            {/* Left Column - Workspace */}
            <div className="w-3/5 p-8">
              <div className="mb-8">
                <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">New Order</h1>
                <p className="text-sm text-gray-500 mt-1">Create a new customer order</p>
              </div>
              
              {/* Line Items Section */}
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Line Items</h2>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleAddProduct}
                    className="h-9 px-3 text-sm font-medium border-gray-300 hover:bg-gray-100 transition-all duration-200"
                  >
                    <PlusCircle className="h-4 w-4 mr-2" />
                    Add Item
                  </Button>
                </div>
                
                <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                  <Table className="w-full border-collapse">
                    <TableHeader className="bg-slate-50 dark:bg-gray-800">
                      <TableRow className="border-b border-gray-200 dark:border-gray-700">
                        <TableHead className="text-left uppercase text-xs font-semibold text-gray-900 dark:text-gray-100 p-4">Product</TableHead>
                        <TableHead className="text-left uppercase text-xs font-semibold text-gray-900 dark:text-gray-100 p-4">Qty</TableHead>
                        <TableHead className="text-left uppercase text-xs font-semibold text-gray-900 dark:text-gray-100 p-4">Unit Price</TableHead>
                        <TableHead className="text-left uppercase text-xs font-semibold text-gray-900 dark:text-gray-100 p-4">Subtotal</TableHead>
                        <TableHead className="text-left uppercase text-xs font-semibold text-gray-900 dark:text-gray-100 p-4"></TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {lineItems.map((item) => (
                        <TableRow 
                          key={item.id} 
                          className="border-b border-gray-200 dark:border-gray-700 hover:bg-slate-50 dark:hover:bg-gray-800 transition"
                        >
                          <TableCell className="p-4 align-top">
                            <div className="relative">
                              <ProductSelector
                                products={products}
                                isLoading={isLoadingProducts}
                                onSelect={(product, variant) => handleProductSelect(item.id, product, variant)}
                                currentValue={{
                                  productName: item.productName,
                                  sku: item.sku
                                }}
                              />
                            </div>
                          </TableCell>
                          <TableCell className="p-4 align-top">
                            <Input
                              type="number"
                              min="1"
                              value={item.quantity}
                              onChange={(e) => handleQuantityChange(item.id, Number(e.target.value))}
                              className="w-16 text-center border-gray-200 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                            />
                          </TableCell>
                          <TableCell className="p-4 align-top">
                            <Input
                              type="number"
                              min="0"
                              step="0.01"
                              value={item.unitPrice}
                              onChange={(e) => handleUnitPriceChange(item.id, Number(e.target.value))}
                              className="w-20 text-center border-gray-200 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                            />
                          </TableCell>
                          <TableCell className="p-4 align-top">
                            <div className="text-gray-900 dark:text-gray-100">
                              {formatCurrency(item.subtotal)}
                            </div>
                          </TableCell>
                          <TableCell className="p-4 align-top">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 transition-opacity"
                              onClick={() => handleRemoveItem(item.id)}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </div>
            
            {/* Right Column - Control Panel */}
            <div className="w-2/5 bg-slate-50 dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 p-8 flex flex-col rounded-r-2xl">
              {/* Close Button */}
              <div className="flex justify-end -mt-2 -mr-2 mb-4">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                  onClick={onClose}
                >
                  <X className="h-5 w-5" />
                </Button>
              </div>
              
              {/* Customer Section */}
              <div className="mb-8">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Customer</h2>
                <CustomDropdown
                  options={availableCustomers}
                  value={customer}
                  onValueChange={handleCustomerSelect}
                  placeholder="Select customer"
                  allowCustom={false} // Disable the built-in custom option since we're providing our own
                  className="w-full h-9 text-sm border-gray-300 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200"
                />
              </div>
              
              {/* Order Details Section */}
              <div className="mb-8">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Order Details</h2>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1 block">Order Number</label>
                    <Input
                      type="text"
                      value={orderNumber}
                      onChange={(e) => {
                        setOrderNumber(e.target.value)
                        if (errors.orderNumber) {
                          setErrors(prev => ({ ...prev, orderNumber: undefined }))
                        }
                      }}
                      className={`h-9 text-sm ${errors.orderNumber ? 'border-red-500' : ''}`}
                      aria-invalid={!!errors.orderNumber}
                      aria-describedby={errors.orderNumber ? "order-number-error" : undefined}
                    />
                    {errors.orderNumber && (
                      <p id="order-number-error" className="text-xs text-red-500 mt-1">
                        {errors.orderNumber}
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1 block">Order Date</label>
                    <DatePicker
                      date={orderDate}
                      onDateChange={(date) => date && setOrderDate(date)}
                      className="h-9 text-sm"
                    />
                  </div>
                  <div className="col-span-2">
                    <label className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1 block">Order Status</label>
                    <Select value={orderStatus} onValueChange={(value: any) => setOrderStatus(value)}>
                      <SelectTrigger className="h-9 text-sm">
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Pending payment">Pending payment</SelectItem>
                        <SelectItem value="Processing">Processing</SelectItem>
                        <SelectItem value="On hold">On hold</SelectItem>
                        <SelectItem value="Completed">Completed</SelectItem>
                        <SelectItem value="Cancelled">Cancelled</SelectItem>
                        <SelectItem value="Refunded">Refunded</SelectItem>
                        <SelectItem value="Failed">Failed</SelectItem>
                        <SelectItem value="Draft">Draft</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
              
              {/* Financial Summary Section */}
              <div className="mb-8 flex-grow">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Summary</h2>
                <div className="bg-white dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 p-4">
                  <dl className="space-y-3">
                    <div className="flex justify-between items-center">
                      <dt className="text-sm text-gray-700 dark:text-gray-300">Subtotal</dt>
                      <dd className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {formatCurrency(calculateSubtotal())}
                      </dd>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <dt className="text-sm text-gray-700 dark:text-gray-300">Shipping</dt>
                      <dd>
                        <Input
                          type="number"
                          min="0"
                          step="0.01"
                          value={shippingCost || ''}
                          onChange={(e) => setShippingCost(Number(e.target.value) || 0)}
                          className="w-24 h-8 text-right text-sm border-gray-200 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500/20 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                          placeholder="0.00"
                        />
                      </dd>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <dt className="text-sm text-gray-700 dark:text-gray-300">Tax (%)</dt>
                      <dd className="flex items-center">
                        <Input
                          type="number"
                          min="0"
                          max="100"
                          step="0.01"
                          value={taxRate || ''}
                          onChange={(e) => setTaxRate(Number(e.target.value) || 0)}
                          className="w-20 h-8 text-right text-sm border-gray-200 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500/20 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                          placeholder="0"
                        />
                        <span className="ml-1 text-sm text-gray-700 dark:text-gray-300">%</span>
                      </dd>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <dt className="text-sm text-gray-700 dark:text-gray-300">Discount</dt>
                      <dd>
                        <Input
                          type="number"
                          min="0"
                          step="0.01"
                          value={discountAmount || ''}
                          onChange={(e) => setDiscountAmount(Number(e.target.value) || 0)}
                          className="w-24 h-8 text-right text-sm border-gray-200 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500/20 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                          placeholder="0.00"
                        />
                      </dd>
                    </div>
                    
                    <div className="border-t border-gray-200 dark:border-gray-600 pt-3 mt-3">
                      <div className="flex justify-between items-center">
                        <dt className="text-lg font-bold text-gray-900 dark:text-gray-100">Total</dt>
                        <dd className="text-lg font-bold text-gray-900 dark:text-gray-100">
                          {formatCurrency(calculateTotal())}
                        </dd>
                      </div>
                    </div>
                  </dl>
                </div>
              </div>
              
              {/* Footer Section */}
              <div className="border-t border-gray-200 dark:border-gray-600 pt-6 mt-auto">
                <div className="flex justify-end space-x-3">
                  <Button
                    onClick={() => handleSubmit(false)}
                    className="h-9 px-4 text-sm font-medium bg-blue-600 hover:bg-blue-700 transition-all duration-200"
                  >
                    Create Order
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
      
      {/* New Customer Modal */}
      <NewCustomerModal
        isOpen={isAddCustomerModalOpen}
        onClose={() => setIsAddCustomerModalOpen(false)}
        onCreateCustomer={handleCreateCustomer}
      />
    </>
  )
}