'use client'

import { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select'
import { 
  <PERSON>alog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle,
  DialogFooter,
  DialogDescription
} from '@/components/ui/dialog'
import { 
  Popover, 
  PopoverTrigger,
  PopoverContent 
} from '@/components/ui/popover'
import { HelpCircle } from 'lucide-react'
import { type InventoryItem } from '../inventory/types'
import { getSupabaseClient } from '@/lib/supabase'

interface AdjustStockModalProps {
  isOpen: boolean
  onClose: () => void
  selectedItem: InventoryItem | null
  onConfirm: (data: {
    adjustmentType: 'adjustBy' | 'setNewTotal'
    adjustmentValue: number
    reason: string
    notes: string
  }) => void
}

const REASON_OPTIONS = [
  { value: 'initial_stock_correction', label: 'Initial Stock Correction' },
  { value: 'shipment_received', label: 'Shipment Received' },
  { value: 'stock_take', label: 'Stock Take / Cycle Count' },
  { value: 'damaged_goods', label: 'Damaged Goods' },
  { value: 'lost_stolen', label: 'Lost / Stolen' },
  { value: 'customer_return', label: 'Customer Return' },
  { value: 'other', label: 'Other' }
]

export function AdjustStockModal({ isOpen, onClose, selectedItem, onConfirm }: AdjustStockModalProps) {
  const [newStockValue, setNewStockValue] = useState<number | null>(null);
  const [reason, setReason] = useState('')
  const [notes, setNotes] = useState('')
  const [errors, setErrors] = useState<{reason?: string, newStock?: string}>({})
  
  const inputRef = useRef<HTMLInputElement>(null)
  const confirmButtonRef = useRef<HTMLButtonElement>(null)
  
  // Reset form when selectedItem changes and focus first element
  useEffect(() => {
    if (selectedItem) {
      setNewStockValue(null);
      setReason('');
      setNotes('');
      setErrors({});
      
      // Focus the new stock input when modal opens
      setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.focus();
        }
      }, 100);
    }
  }, [selectedItem]);

  // Handle keyboard events
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
      
      // Submit with Ctrl+Enter or Cmd+Enter
      if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        handleSubmit();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, newStockValue, reason, notes]);

  // Early return if not open or no selected item
  if (!isOpen || !selectedItem) {
    return null;
  }

  const handleSubmit = () => {
    const newErrors: {reason?: string, newStock?: string} = {};
    
    if (!reason) {
      newErrors.reason = 'Reason is required';
    }
    
    // Only validate new stock if user has entered a value
    if (newStockValue !== null) {
      if (newStockValue < 0) {
        newErrors.newStock = 'Stock cannot be negative';
      }
    }
    
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }
    
    // If no new stock value entered, treat as no change
    const finalNewStockValue = newStockValue !== null ? newStockValue : selectedItem.stockOnHand;
    const adjustmentValue = finalNewStockValue - selectedItem.stockOnHand;
    
    onConfirm({
      adjustmentType: 'adjustBy',
      adjustmentValue,
      reason,
      notes
    })
  }

  // Calculate resulting stock quantity
  const resultingStock = newStockValue !== null ? newStockValue : selectedItem.stockOnHand;
  const adjustmentValue = newStockValue !== null ? newStockValue - selectedItem.stockOnHand : 0;
  
  // Check if there are changes
  const hasChanges = (newStockValue !== null && newStockValue !== selectedItem.stockOnHand) || reason !== '' || notes !== ''

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent 
        className="sm:max-w-lg rounded-lg transition-all duration-200"
        onOpenAutoFocus={(e) => e.preventDefault()}
      >
        <DialogHeader className="space-y-1">
          <DialogTitle className="text-base font-semibold text-gray-900">
            Adjust Stock
          </DialogTitle>
          <DialogDescription className="text-xs text-gray-500">
            {selectedItem.name}
          </DialogDescription>
          <div className="text-xs text-gray-500 mt-1">SKU: {selectedItem.sku}</div>
        </DialogHeader>
        
        <div className="space-y-6 py-2">
          {/* Integrated Stock Information */}
          <div className="border border-gray-200 rounded-lg p-4 space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <span className="text-xs font-medium text-gray-700">Current Stock</span>
                <div className="text-base font-semibold text-gray-900 mt-1">
                  {selectedItem.stockOnHand} units
                </div>
              </div>
              
              <div className="flex items-center">
                <svg className="text-gray-400 mx-2" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <line x1="5" y1="12" x2="19" y2="12"></line>
                  <polyline points="12 5 19 12 12 19"></polyline>
                </svg>
              </div>
              
              <div className="text-right">
                <span className="text-xs font-medium text-gray-700">New Stock</span>
                <div className="mt-1">
                  <Input
                    ref={inputRef}
                    type="number"
                    className="h-9 w-24 text-base text-center font-semibold border border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                    value={newStockValue !== null ? newStockValue : ''}
                    onChange={(e) => {
                      const value = e.target.value === '' ? null : Number(e.target.value);
                      setNewStockValue(value);
                      if (errors.newStock) {
                        setErrors(prev => ({ ...prev, newStock: undefined }));
                      }
                    }}
                    placeholder="0"
                    aria-invalid={!!errors.newStock}
                    aria-describedby={errors.newStock ? "new-stock-error" : undefined}
                  />
                </div>
              </div>
            </div>
            
            {/* Adjustment Information */}
            {newStockValue !== null && (
              <div className="pt-3 border-t border-gray-200">
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-600">
                    {adjustmentValue > 0 ? 'Adding' : adjustmentValue < 0 ? 'Removing' : 'No change'}
                  </span>
                  <span className="text-xs font-medium text-gray-900">
                    {adjustmentValue !== 0 ? `${Math.abs(adjustmentValue)} units` : 'No adjustment'}
                  </span>
                </div>
              </div>
            )}
            
            {errors.newStock && (
              <p id="new-stock-error" className="text-xs text-red-500 mt-2">
                {errors.newStock}
              </p>
            )}
          </div>
          
          {/* Reason */}
          <div className="space-y-3">
            <div className="flex items-center">
              <span className="text-xs font-medium text-gray-900">
                Reason for Adjustment
              </span>
              <span className="text-red-500 ml-1">*</span>
            </div>
            <Select 
              value={reason} 
              onValueChange={(value) => {
                setReason(value);
                if (errors.reason) {
                  setErrors(prev => ({ ...prev, reason: undefined }));
                }
              }}
            >
              <SelectTrigger 
                className={`w-full text-xs h-9 transition-all duration-150 focus:ring-2 focus:ring-offset-0 ${
                  errors.reason 
                    ? 'border-red-500 focus:ring-red-500' 
                    : 'focus:ring-blue-500'
                }`}
                aria-invalid={!!errors.reason}
                aria-describedby={errors.reason ? "reason-error" : undefined}
              >
                <SelectValue placeholder="Select a reason" />
              </SelectTrigger>
              <SelectContent className="rounded-md shadow-md animate-in fade-in-0 zoom-in-95">
                {REASON_OPTIONS.map((option) => (
                  <SelectItem 
                    key={option.value} 
                    value={option.value} 
                    className="text-xs cursor-pointer hover:bg-gray-100 transition-colors duration-150"
                  >
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.reason && (
              <p id="reason-error" className="text-xs text-red-500">
                {errors.reason}
              </p>
            )}
          </div>
          
          {/* Notes */}
          <div className="space-y-3">
            <div className="text-xs font-medium text-gray-900">
              Notes
            </div>
            <textarea
              rows={3}
              className="flex w-full rounded-md border border-input bg-background px-3 py-2 text-xs ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-0 disabled:cursor-not-allowed disabled:opacity-50 min-h-[80px] transition-all duration-150"
              placeholder="Add any additional notes here..."
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
            />
          </div>
        </div>
        
        <DialogFooter className="gap-3 sm:space-x-0 pt-2">
          <Button
            type="button"
            variant="outline"
            className="w-full sm:w-auto text-xs h-9 px-4 transition-colors duration-150"
            onClick={onClose}
          >
            Cancel
          </Button>
          <Button
            ref={confirmButtonRef}
            type="button"
            className="w-full sm:w-auto text-xs h-9 px-4 transition-colors duration-150"
            onClick={handleSubmit}
            disabled={(!hasChanges && !reason) || !!errors.reason}
          >
            Confirm Adjustment
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}