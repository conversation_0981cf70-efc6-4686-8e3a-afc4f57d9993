# React Query Patterns in ONKO Web App

This document outlines the specific React Query patterns and best practices implemented in the ONKO web application.

## 1. Custom Hook Structure

### Basic Pattern
```typescript
export function useEntity(userId: string | undefined) {
  return useQuery<EntityRow[], Error>({
    queryKey: ['entity', userId],
    queryFn: async () => {
      if (!userId) {
        throw new Error('User ID is required')
      }
      
      // Fetch data
      const { data, error } = await fetchData()
      
      if (error) {
        throw new Error(`Failed to fetch: ${error.message}`)
      }
      
      return data || []
    },
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 2,
    retryDelay: (attemptIndex) => {
      return Math.min(1000 * 2 ** attemptIndex, 30000)
    },
  })
}
```

### Invalidate Pattern
```typescript
export function useInvalidateEntity() {
  const queryClient = useQueryClient()
  
  // Debounced invalidate functions
  const debouncedInvalidate = debounce(() => {
    queryClient.invalidateQueries({ queryKey: ['entity'] })
  }, 1000) // 1 second debounce
  
  return {
    invalidateEntity: debouncedInvalidate
  }
}
```

## 2. Query Keys

### Naming Convention
- Use array format: `['entityName', identifier, ...parameters]`
- Always include user ID when applicable: `['products', userId]`
- Use specific keys for different data sets: `['allProducts', userId]` vs `['products', userId]`

### Examples
```typescript
// Products
['allProducts', userId]
['products', userId]

// Expenses
['allExpenses', userId]
```

## 3. Error Handling

### Error Types
- Network errors
- Authentication errors
- Timeout errors
- Server errors

### User-Friendly Messages
```typescript
if (errorMessage.includes("NetworkError") || errorMessage.includes("Failed to fetch")) {
  userFriendlyMessage = "Network connection error. Please check your internet connection and try again."
} else if (errorMessage.includes("ERR_INSUFFICIENT_RESOURCES")) {
  userFriendlyMessage = "System resource limit reached. Please refresh the page or try again in a few minutes."
} else if (errorMessage.includes("timeout")) {
  userFriendlyMessage = "Request timed out. Please try again."
} else if (errorMessage.includes("401") || errorMessage.includes("403")) {
  userFriendlyMessage = "Authentication error. Please sign in again."
} else if (errorMessage.includes("500")) {
  userFriendlyMessage = "Server error. Please try again later."
}
```

## 4. Performance Optimization

### Stale Time
- Set to 5 minutes for most queries to balance freshness and performance
- Prevents excessive refetching when users navigate between pages

### GC Time
- Set to 10 minutes to keep data in cache longer than stale time
- Allows for instant rendering when returning to pages

### Refetch on Window Focus
- Disabled to prevent unnecessary requests when users switch tabs

### Retry Logic
- Limited to 2 retries to prevent excessive requests
- Exponential backoff to avoid overwhelming the server

## 5. Debouncing

### Purpose
- Prevent rapid successive requests
- Reduce server load
- Improve user experience

### Implementation
```typescript
function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  return function (...args: Parameters<T>) {
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(() => func(...args), wait)
  }
}
```

## 6. Data Transformation

### In Hooks
- Transform data as close to the source as possible
- Add computed properties
- Handle complex relationships

### Example
```typescript
const transformedProducts = (data || []).map(product => {
  // Handle categories which might be an array or object
  let categoryName = null
  if (product.categories) {
    if (Array.isArray(product.categories) && product.categories.length > 0) {
      categoryName = (product.categories[0] as CategoryRow)?.name || null
    } else if (!Array.isArray(product.categories) && (product.categories as CategoryRow)?.name) {
      categoryName = (product.categories as CategoryRow).name
    }
  }
  
  return {
    ...product,
    category_name: categoryName,
    variants: product.product_variants || []
  }
})
```

## 7. Integration with Components

### Loading States
```typescript
if (isLoading) {
  return <Skeleton />
}
```

### Error States
```typescript
if (error) {
  return <ErrorComponent error={error} refetch={refetch} />
}
```

### Data Usage
```typescript
const { data: products = [], isLoading, error, refetch } = useAllProducts(user?.id)
```

## 8. Event-Driven Updates

### Custom Events
```typescript
useEffect(() => {
  const handleProductAdded = () => {
    invalidateAll()
  }

  window.addEventListener('productAdded', handleProductAdded)
  return () => {
    window.removeEventListener('productAdded', handleProductAdded)
  }
}, [invalidateAll])
```

### Real-time Subscriptions
```typescript
useEffect(() => {
  if (!user) return
  
  const supabase = getSupabaseClient()
  
  const channel = supabase
    .channel('changes')
    .on(
      'postgres_changes',
      {
        event: 'INSERT',
        schema: 'public',
        table: 'products',
        filter: `user_id=eq.${user.id}`
      },
      () => {
        invalidateAll()
      }
    )
    .subscribe()
  
  return () => {
    supabase.removeChannel(channel)
  }
}, [user])
```

## 9. Best Practices

### Do
- Always include user ID in query keys for user-specific data
- Use proper error handling with user-friendly messages
- Implement debouncing for invalidate functions
- Transform data in hooks rather than components
- Use consistent staleTime and gcTime values
- Handle loading and error states properly

### Don't
- Don't fetch data directly in components
- Don't ignore error states
- Don't create excessive real-time subscriptions
- Don't use inconsistent caching strategies
- Don't make query keys too complex

## 10. Migration Guide

### From Manual Fetching to React Query
1. Create custom hook with useQuery
2. Move data fetching logic to hook
3. Add proper error handling
4. Implement loading states
5. Add invalidate functions for updates
6. Replace manual fetch calls with hook usage
7. Add event listeners for automatic updates

### Example Migration
```typescript
// Before: Manual fetching
const [products, setProducts] = useState([])
const [loading, setLoading] = useState(false)

useEffect(() => {
  const fetchProducts = async () => {
    setLoading(true)
    try {
      const data = await fetchProductsFromAPI()
      setProducts(data)
    } finally {
      setLoading(false)
    }
  }
  fetchProducts()
}, [])

// After: React Query
const { data: products = [], isLoading, error } = useAllProducts(user?.id)
```

This pattern ensures consistent, performant, and maintainable data fetching across the application.