// Test script for Vault integration
// This script can be used to verify that the Vault integration is working correctly

import { createClient } from '@supabase/supabase-js';

// Replace with your Supabase URL and service role key
const supabaseUrl = process.env.SUPABASE_URL || 'YOUR_SUPABASE_URL';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'YOUR_SERVICE_ROLE_KEY';

const supabase = createClient(supabaseUrl, supabaseKey);

async function testVaultIntegration() {
  try {
    console.log('Testing Vault integration...');
    
    // Test the connect_vault RPC function
    const { data, error } = await supabase.rpc('connect_vault', {
      vault_url: 'https://api.vault.com',
      api_key: 'test_api_key',
      account_id: 'test_account_id'
    });
    
    if (error) {
      console.error('Error calling connect_vault function:', error);
      return;
    }
    
    console.log('Vault integration response:', data);
    
    if (data.success) {
      console.log('✅ Vault integration test passed!');
      console.log('Integration ID:', data.integration_id);
    } else {
      console.error('❌ Vault integration test failed:', data.error);
    }
  } catch (error) {
    console.error('Unexpected error during Vault integration test:', error);
  }
}

// Run the test
testVaultIntegration();