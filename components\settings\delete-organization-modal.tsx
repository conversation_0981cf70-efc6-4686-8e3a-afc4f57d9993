'use client'

import React, { useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useToast } from '@/components/ui/use-toast'
import { createSupabaseClient } from '@/lib/supabase'
import { useAuth } from '@/contexts/auth-context'

interface DeleteOrganizationModalProps {
  open: boolean
  onClose: () => void
  organizationName: string
  organizationId: string
}

export function DeleteOrganizationModal({
  open,
  onClose,
  organizationName,
  organizationId
}: DeleteOrganizationModalProps) {
  const { toast, dismiss } = useToast()
  const { signOut } = useAuth()
  const supabase = createSupabaseClient()
  const [confirmText, setConfirmText] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const handleDelete = async () => {
    if (confirmText !== organizationName) return
    
    setIsLoading(true)
    
    try {
      // Call the delete_organization function in Supabase
      const { error } = await supabase.rpc('delete_organization', {
        org_id: organizationId
      })
      
      if (error) {
        throw new Error(error.message)
      }
      
      toast({
        title: 'Organization Deleted',
        description: 'Your organization has been successfully deleted.',
      })
      
      // Sign out the user after deletion
      setTimeout(async () => {
        await signOut()
      }, 2000)
    } catch (error) {
      console.error('Error deleting organization:', error)
      toast({
        title: 'Deletion Failed',
        description: error instanceof Error ? error.message : 'Failed to delete organization. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
      setConfirmText('')
      onClose()
    }
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Delete Organization</DialogTitle>
          <DialogDescription>
            This action cannot be undone. This will permanently delete your organization and remove all associated data.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <p className="text-sm text-gray-500">
            To confirm, please type the full name of your organization: <strong>{organizationName}</strong>
          </p>
          
          <div className="space-y-2">
            <Label htmlFor="confirm-name" className="text-sm font-medium text-gray-700">
              Organization Name
            </Label>
            <Input
              id="confirm-name"
              value={confirmText}
              onChange={(e) => setConfirmText(e.target.value)}
              placeholder="Type organization name to confirm"
              className="text-sm h-9"
            />
          </div>
        </div>
        
        <DialogFooter>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={isLoading || confirmText !== organizationName}
            className="h-9 px-3 text-sm"
          >
            {isLoading ? 'Deleting...' : 'Delete Organization'}
          </Button>
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
            className="h-9 px-3 text-sm"
          >
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}