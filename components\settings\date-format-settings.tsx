'use client'

import { useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select'
import { useDateFormat } from '@/contexts/date-format-context'

export function DateFormatSettings() {
  const { preferences, setPreferences } = useDateFormat()

  // Example date for preview
  const exampleDate = new Date(2025, 11, 25, 14, 30, 0)

  // Format date based on selected format
  const formatExampleDate = () => {
    switch (preferences.dateFormat) {
      case 'MM/dd/yyyy':
        return '12/25/2025'
      case 'dd/MM/yyyy':
        return '25/12/2025'
      case 'yyyy-MM-dd':
        return '2025-12-25'
      case 'MMMM d, yyyy':
        return 'December 25, 2025'
      default:
        return '12/25/2025'
    }
  }

  // Format time based on selected format
  const formatExampleTime = () => {
    if (preferences.timeFormat === '12-hour') {
      return '2:30 PM'
    } else {
      return '14:30'
    }
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200">
      <div className="px-4 py-3 border-b border-gray-200">
        <h3 className="text-base font-medium text-gray-900">Date & Time Format</h3>
      </div>
      <div className="p-4 space-y-5">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Date Format</label>
            <Select 
              value={preferences.dateFormat} 
              onValueChange={(value) => setPreferences({ ...preferences, dateFormat: value as any })}
            >
              <SelectTrigger className="text-sm h-9">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="MM/dd/yyyy">MM/DD/YYYY</SelectItem>
                <SelectItem value="dd/MM/yyyy">DD/MM/YYYY</SelectItem>
                <SelectItem value="yyyy-MM-dd">YYYY-MM-DD</SelectItem>
                <SelectItem value="MMMM d, yyyy">Month DD, YYYY</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Time Format</label>
            <Select 
              value={preferences.timeFormat} 
              onValueChange={(value) => setPreferences({ ...preferences, timeFormat: value as any })}
            >
              <SelectTrigger className="text-sm h-9">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="12-hour">12-hour</SelectItem>
                <SelectItem value="24-hour">24-hour</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">First Day</label>
            <Select 
              value={preferences.firstDayOfWeek} 
              onValueChange={(value) => setPreferences({ ...preferences, firstDayOfWeek: value as any })}
            >
              <SelectTrigger className="text-sm h-9">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="sunday">Sunday</SelectItem>
                <SelectItem value="monday">Monday</SelectItem>
                <SelectItem value="tuesday">Tuesday</SelectItem>
                <SelectItem value="wednesday">Wednesday</SelectItem>
                <SelectItem value="thursday">Thursday</SelectItem>
                <SelectItem value="friday">Friday</SelectItem>
                <SelectItem value="saturday">Saturday</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="p-3 bg-gray-50 rounded-md">
          <h4 className="text-sm font-medium text-gray-900 mb-1">Preview</h4>
          <p className="text-sm text-gray-700">
            {formatExampleDate()} at {formatExampleTime()}
          </p>
        </div>
      </div>
    </div>
  )
}