import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query'
import {
  getAllProductsForOrganization,
  getProductsForOrganization,
  getBestSellingItems,
  getSlowMovingInventoryCount,
} from '@/lib/supabase'

// Define the ProductVariantRow type to match the actual data structure
type ProductVariantRow = {
  id: string
  product_id: string
  organization_id: string
  sku: string
  variant_name: string | null
  size: string | null
  color: string | null
  material: string | null
  style: string | null
  weight: number | null
  dimensions: string | null
  cost_adjustment: number | null
  base_cost: number | null
  price: number | null
  sale_price: number | null
  sale_start_date: string | null
  sale_end_date: string | null
  stock_quantity: number | null
  low_stock_threshold: number | null
  reserved_quantity: number | null
  is_active: boolean
  barcode: string | null
  image_urls: string[] | null
  effective_price: number | null
  is_on_sale: boolean
  available_quantity: number | null
  created_at: string
  updated_at: string
}

// Define the ExtendedProductRow type to match the actual data structure
export type ExtendedProductRow = {
  id: string
  organization_id: string
  category_id: string | null
  name: string
  description: string | null
  brand: string | null
  supplier: string | null
  base_sku: string | null
  has_variants: boolean
  track_inventory: boolean
  is_active: boolean
  base_cost: number | null
  packaging_cost: number | null
  price: number | null
  size: string | null
  color: string | null
  stock_quantity: number | null
  low_stock_threshold: number | null
  barcode: string | null
  image_url: string | null
  sale_price: number | null
  sale_start_date: string | null
  sale_end_date: string | null
  batch_reference: string | null
  purchase_date: string | null
  notes: string | null
  total_cost: number | null
  effective_price: number | null
  is_on_sale: boolean | null
  profit_amount: number | null
  profit_margin: number | null
  created_at: string
  updated_at: string
  categories?:
    | {
        name: string
      }
    | {
        name: string
      }[]
    | null
  product_variants?: ProductVariantRow[]
  category_name?: string | null
  variants?: ProductVariantRow[]
}

// Debounce function to prevent rapid requests
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  return function (...args: Parameters<T>) {
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(() => func(...args), wait)
  }
}

/**
 * Custom hook to fetch all products for an organization with React Query
 * @param organizationId - The organization ID to fetch products for
 * @returns React Query result object with products data
 */
export function useAllProducts(organizationId: string | undefined) {
  return useQuery<ExtendedProductRow[], Error>({
    queryKey: ['allProducts', organizationId],
    queryFn: async () => {
      if (!organizationId) {
        throw new Error('Organization ID is required')
      }

      try {
        const data = await getAllProductsForOrganization(organizationId)

        // Transform data to include category name and process variants
        const transformedProducts = (data || []).map((product) => {
          return {
            ...product,
            category_name: product.category_name || null,
            variants: product.product_variants || [],
          }
        })

        return transformedProducts
      } catch (error) {
        console.error('Error in useAllProducts query function:', error)
        // Re-throw the error so React Query can handle it properly
        throw error
      }
    },
    enabled: !!organizationId,
    staleTime: 2 * 60 * 1000, // Reduced to 2 minutes for more responsive updates
    gcTime: 5 * 60 * 1000, // Reduced garbage collection time
    refetchOnMount: true,
    refetchOnWindowFocus: true, // Re-enable this - it's important for tab switching
    refetchOnReconnect: true,
    refetchInterval: false,
    refetchIntervalInBackground: false,
    retry: 3, // Increased retry count
    retryDelay: (attemptIndex) => {
      // More aggressive retry timing
      return Math.min(500 * Math.pow(2, attemptIndex), 5000)
    },
    // Add network mode to handle offline scenarios
    networkMode: 'online',
  })
}

/**
 * Custom hook to fetch active products for an organization with React Query
 * @param organizationId - The organization ID to fetch products for
 * @returns React Query result object with products data
 */
export function useProducts(organizationId: string | undefined) {
  return useQuery<ExtendedProductRow[], Error>({
    queryKey: ['products', organizationId],
    queryFn: async () => {
      if (!organizationId) {
        throw new Error('Organization ID is required')
      }

      try {
        const data = await getProductsForOrganization(organizationId)

        // Transform data to include category name and process variants
        const transformedProducts = (data || []).map((product) => {
          return {
            ...product,
            category_name: product.category_name || null,
            variants: product.product_variants || [],
          }
        })

        return transformedProducts
      } catch (error) {
        console.error('Error in useProducts query function:', error)
        // Re-throw the error so React Query can handle it properly
        throw error
      }
    },
    enabled: !!organizationId,
    staleTime: 2 * 60 * 1000, // Reduced to 2 minutes
    gcTime: 5 * 60 * 1000, // Reduced garbage collection time
    refetchOnMount: true,
    refetchOnWindowFocus: true, // Re-enable this
    refetchOnReconnect: true,
    refetchInterval: false,
    refetchIntervalInBackground: false,
    retry: 3, // Increased retry count
    retryDelay: (attemptIndex) => {
      return Math.min(500 * Math.pow(2, attemptIndex), 5000)
    },
    networkMode: 'online',
  })
}

/**
 * Custom hook to fetch best selling items with React Query
 * @param organizationId - The organization ID to fetch data for
 * @returns React Query result object with best selling items data
 */
export function useBestSellingItems(
  organizationId: string | undefined,
  limit: number = 5,
  dateRange: '30d' | '90d' | '1y' | 'all' = '30d'
) {
  return useQuery({
    queryKey: ['bestSellingItems', organizationId, limit, dateRange],
    queryFn: async () => {
      if (!organizationId) {
        throw new Error('Organization ID is required')
      }

      try {
        const data = await getBestSellingItems(organizationId, limit, dateRange)
        return data
      } catch (error) {
        console.error('Error in useBestSellingItems query function:', error)
        throw error
      }
    },
    enabled: !!organizationId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Custom hook to fetch slow moving inventory count with React Query
 * @param organizationId - The organization ID to fetch data for
 * @returns React Query result object with slow moving inventory count
 */
export function useSlowMovingInventory(organizationId: string | undefined) {
  return useQuery({
    queryKey: ['slowMovingInventory', organizationId],
    queryFn: async () => {
      if (!organizationId) {
        throw new Error('Organization ID is required')
      }

      try {
        const count = await getSlowMovingInventoryCount(organizationId)
        return count
      } catch (error) {
        console.error('Error in useSlowMovingInventory query function:', error)
        throw error
      }
    },
    enabled: !!organizationId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Custom hook to invalidate product queries and trigger refetching with debouncing
 * @returns Object with functions to invalidate queries
 */
export function useInvalidateProducts() {
  const queryClient = useQueryClient()

  // Debounced invalidate functions
  const debouncedInvalidateAllProducts = debounce(() => {
    queryClient.invalidateQueries({ queryKey: ['allProducts'] })
  }, 1000) // 1 second debounce

  const debouncedInvalidateProducts = debounce(() => {
    queryClient.invalidateQueries({ queryKey: ['products'] })
  }, 1000) // 1 second debounce

  const debouncedInvalidateAll = debounce(() => {
    queryClient.invalidateQueries({ queryKey: ['allProducts'] })
    queryClient.invalidateQueries({ queryKey: ['products'] })
  }, 1000) // 1 second debounce

  return {
    invalidateAllProducts: debouncedInvalidateAllProducts,
    invalidateProducts: debouncedInvalidateProducts,
    invalidateAll: debouncedInvalidateAll,
  }
}
