import { getSupabaseClient } from '@/lib/supabase'

// Define types
export type UserRole = 'admin' | 'staff'

export interface TeamMember {
  id: string
  email: string
  display_name?: string | null
  first_name?: string | null
  last_name?: string | null
  avatar_url?: string | null
  role: UserRole
  created_at: string
}

export interface PendingInvitation {
  id: string
  email: string
  role: string
  created_at: string
}

interface OrganizationMember {
  role: string
  user_id: string
  users: {
    id: string
    email: string
    display_name: string | null
    first_name: string | null
    last_name: string | null
    avatar_url: string | null
  }
}

// Get the user's organization ID
async function getUserOrganizationId() {
  const supabase = getSupabaseClient()
  
  // Get the current user
  const { data: { user }, error: userError } = await supabase.auth.getUser()
  if (userError || !user) {
    throw new Error(userError?.message || 'User not authenticated')
  }
  
  // Get the user's organization
  const { data: orgData, error: orgError } = await supabase
    .from('organization_members')
    .select('organization_id')
    .eq('user_id', user.id)
    .single()
  
  // If user doesn't have an organization, create one
  if (orgError || !orgData) {
    // Get user's business name from profile
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('business_name')
      .eq('id', user.id)
      .single();
    
    // Use the business_name from profile, or fallback to email prefix, or default
    const organizationName = profileData?.business_name || 
                            (user.email ? user.email.split('@')[0] : '') || 
                            'My Organization';
    
    // Create organization for user
    const { data: newOrg, error: createOrgError } = await supabase
      .from('organizations')
      .insert({
        name: organizationName,
        owner_id: user.id
      })
      .select()
      .single()
    
    if (createOrgError) {
      throw new Error(createOrgError.message)
    }
    
    // Add user as admin member
    const { error: addMemberError } = await supabase
      .from('organization_members')
      .insert({
        organization_id: newOrg.id,
        user_id: user.id,
        role: 'admin',
        invited_by: user.id
      })
    
    if (addMemberError) {
      throw new Error(addMemberError.message)
    }
    
    return newOrg.id
  }
  
  return orgData.organization_id
}

// Invite user to team
export async function inviteUserToTeam(email: string, role: UserRole) {
  const supabase = getSupabaseClient()
  
  try {
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      throw new Error(userError?.message || 'User not authenticated')
    }
    
    // Get user's organization
    const organizationId = await getUserOrganizationId()
    
    // Create an invitation record
    const { error: insertError } = await supabase
      .from('invitations')
      .insert({
        email,
        role,
        organization_id: organizationId,
        invited_by: user.id,
        created_at: new Date().toISOString(),
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
        status: 'pending'
      })
    
    if (insertError) {
      throw new Error(insertError.message)
    }
    
    // Get the invitation ID to send email
    const { data: invitationData, error: fetchError } = await supabase
      .from('invitations')
      .select('id')
      .eq('email', email)
      .eq('organization_id', organizationId)
      .eq('status', 'pending')
      .order('created_at', { ascending: false })
      .limit(1)
      .single();
    
    if (fetchError) {
      console.error('Error fetching invitation for email sending:', fetchError);
    } else {
      // Send invitation email
      await sendInvitationEmail(invitationData.id);
    }
    
    return { success: true, message: 'Invitation sent successfully' }
  } catch (error) {
    console.error('Error inviting user to team:', error)
    throw error
  }
}

// Update user role
export async function updateUserRole(userId: string, newRole: UserRole) {
  const supabase = getSupabaseClient()
  
  try {
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      throw new Error(userError?.message || 'User not authenticated')
    }
    
    // Get user's organization
    const organizationId = await getUserOrganizationId()
    
    // Update the user's role in the organization
    const { error: updateError } = await supabase
      .from('organization_members')
      .update({ role: newRole, updated_at: new Date().toISOString() })
      .eq('user_id', userId)
      .eq('organization_id', organizationId)
    
    if (updateError) {
      throw new Error(updateError.message)
    }
    
    return { success: true, message: 'User role updated successfully' }
  } catch (error) {
    console.error('Error updating user role:', error)
    throw error
  }
}

// Remove user from team
export async function removeUserFromTeam(userId: string) {
  const supabase = getSupabaseClient()
  
  try {
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      throw new Error(userError?.message || 'User not authenticated')
    }
    
    // Get user's organization
    const organizationId = await getUserOrganizationId()
    
    // Remove the user from the organization
    const { error: deleteError } = await supabase
      .from('organization_members')
      .delete()
      .eq('user_id', userId)
      .eq('organization_id', organizationId)
    
    if (deleteError) {
      throw new Error(deleteError.message)
    }
    
    return { success: true, message: 'User removed from team successfully' }
  } catch (error) {
    console.error('Error removing user from team:', error)
    throw error
  }
}

// Get all team members
export async function getTeamMembers(): Promise<TeamMember[]> {
  const supabase = getSupabaseClient()
  
  try {
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      throw new Error(userError?.message || 'User not authenticated')
    }
    
    // Get user's organization
    const organizationId = await getUserOrganizationId()
    
    // Get all members of the organization by joining organization_members with profiles
    // We need to do this as two separate queries since there's no direct FK relationship
    const { data: orgMembers, error: orgMembersError } = await supabase
      .from('organization_members')
      .select('role, user_id')
      .eq('organization_id', organizationId)
      .neq('user_id', user.id) // Exclude the current user
    
    if (orgMembersError) {
      throw new Error(orgMembersError.message)
    }
    
    // If no members, return empty array
    if (!orgMembers || orgMembers.length === 0) {
      return []
    }
    
    // Get the user IDs
    const userIds = orgMembers.map(member => member.user_id)
    
    // Get the profile information for these users
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, email, display_name, first_name, last_name, avatar_url')
      .in('id', userIds)
    
    if (profilesError) {
      throw new Error(profilesError.message)
    }
    
    // Create a map of profiles by user ID for easy lookup
    const profileMap = new Map(profiles.map(profile => [profile.id, profile]))
    
    // Combine the organization member data with profile data
    const teamMembers: TeamMember[] = orgMembers.map(member => {
      const profile = profileMap.get(member.user_id)
      
      return {
        id: member.user_id,
        email: profile?.email || '',
        display_name: profile?.display_name || null,
        first_name: profile?.first_name || null,
        last_name: profile?.last_name || null,
        avatar_url: profile?.avatar_url || null,
        role: member.role as UserRole,
        created_at: new Date().toISOString()
      }
    })
    
    return teamMembers
  } catch (error) {
    console.error('Error fetching team members:', error)
    throw error
  }
}

// Get pending invitations
export async function getPendingInvitations(): Promise<any[]> {
  const supabase = getSupabaseClient()
  
  try {
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      throw new Error(userError?.message || 'User not authenticated')
    }
    
    // Get user's organization
    const organizationId = await getUserOrganizationId()
    
    // Get all pending invitations for this organization
    const { data: invitations, error: invitationsError } = await supabase
      .from('invitations')
      .select('id, email, role, created_at')
      .eq('organization_id', organizationId)
      .eq('status', 'pending')
      .order('created_at', { ascending: false })
    
    if (invitationsError) {
      throw new Error(invitationsError.message)
    }
    
    return invitations || []
  } catch (error) {
    console.error('Error fetching pending invitations:', error)
    throw error
  }
}

// Resend invitation
export async function resendInvitation(invitationId: string) {
  const supabase = getSupabaseClient()
  
  try {
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      throw new Error(userError?.message || 'User not authenticated')
    }
    
    // Get the invitation to resend
    const { data: invitation, error: fetchError } = await supabase
      .from('invitations')
      .select('email, role, organization_id')
      .eq('id', invitationId)
      .single()
    
    if (fetchError) {
      throw new Error(fetchError.message)
    }
    
    // Update the expiration date to 7 days from now
    const { error: updateError } = await supabase
      .from('invitations')
      .update({
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', invitationId)
    
    if (updateError) {
      throw new Error(updateError.message)
    }
    
    // Send invitation email
    await sendInvitationEmail(invitationId);
    
    return { success: true, message: 'Invitation resent successfully' }
  } catch (error) {
    console.error('Error resending invitation:', error)
    throw error
  }
}

// Cancel invitation
export async function cancelInvitation(invitationId: string) {
  const supabase = getSupabaseClient()
  
  try {
    // Delete the invitation record
    const { error: deleteError } = await supabase
      .from('invitations')
      .delete()
      .eq('id', invitationId)
    
    if (deleteError) {
      throw new Error(deleteError.message)
    }
    
    return { success: true, message: 'Invitation cancelled successfully' }
  } catch (error) {
    console.error('Error cancelling invitation:', error)
    throw error
  }
}

// Send invitation email
export async function sendInvitationEmail(invitationId: string) {
  const supabase = getSupabaseClient()
  
  try {
    // Get invitation details
    const { data: invitation, error: invitationError } = await supabase
      .from('invitations')
      .select(`
        id,
        email,
        role,
        organization_id,
        created_at,
        organizations (name)
      `)
      .eq('id', invitationId)
      .single()

    if (invitationError) {
      throw new Error(invitationError.message)
    }

    if (!invitation) {
      throw new Error('Invitation not found')
    }

    // Get organization name
    const orgData = invitation.organizations as { name: string }[] | null;
    const organizationName = (orgData && orgData.length > 0) ? orgData[0].name : 'Your Organization';

    // In a real implementation, you would send an actual email here
    // For example, using SendGrid, Mailgun, or another email service
    // This is where you would integrate with your email provider
    
    console.log(`Sending invitation email to ${invitation.email}`)
    console.log(`Organization: ${organizationName}`)
    console.log(`Role: ${invitation.role}`)
    console.log(`Invitation ID: ${invitation.id}`)
    
    // For demonstration purposes, we're just logging the email content
    // In a production environment, you would use an email service provider
    
    const emailContent = `
      Subject: You've been invited to join ${organizationName}
      
      Hello,
      
      You've been invited to join ${organizationName} as a ${invitation.role}.
      
      To accept this invitation, please click on the following link:
      ${typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000'}/accept-invitation?token=${invitation.id}
      
      This invitation will expire in 7 days.
      
      If you did not expect this invitation, you can safely ignore this email.
      
      Best regards,
      The ${organizationName} Team
    `
    
    console.log('Email content:', emailContent)

    return { success: true, message: 'Invitation email sent successfully' }
  } catch (error) {
    console.error('Error sending invitation email:', error)
    throw error
  }
}