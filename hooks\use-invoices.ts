import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query'
import { getAllInvoicesForOrganization, getInvoiceById, createInvoice, updateInvoice, deleteInvoice, generateInvoiceFromOrder, updateInvoiceStatus } from '@/lib/supabase-invoices'
import type { InvoiceRow } from '@/lib/supabase-invoices'
import { useToast } from '@/components/ui/use-toast'
import { getSupabaseClient } from '@/lib/supabase'

// Debounce function to prevent rapid requests
function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  return function (...args: Parameters<T>) {
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(() => func(...args), wait)
  }
}

/**
 * Custom hook to fetch all invoices for an organization with React Query
 * @param organizationId - The organization ID to fetch invoices for
 * @returns React Query result object with invoices data
 */
export function useAllInvoices(organizationId: string | undefined) {
  return useQuery<InvoiceRow[], Error>({
    queryKey: ['allInvoices', organizationId],
    queryFn: async () => {
      if (!organizationId) {
        throw new Error('Organization ID is required')
      }
      
      const data = await getAllInvoicesForOrganization(organizationId)
      return data || []
    },
    enabled: !!organizationId,
    staleTime: 2 * 60 * 1000, // 2 minutes - consistent with providers
    gcTime: 5 * 60 * 1000, // 5 minutes garbage collection time
    refetchOnMount: true,
    refetchOnWindowFocus: true, // Important for tab switching
    refetchOnReconnect: true,
    refetchInterval: false,
    refetchIntervalInBackground: false,
    retry: 3, // Increased retry count
    retryDelay: (attemptIndex) => {
      return Math.min(1000 * 2 ** attemptIndex, 30000)
    },
    networkMode: 'online' // Handle offline scenarios
  })
}

/**
 * Custom hook to fetch a specific invoice by ID with React Query
 * @param invoiceId - The ID of the invoice to fetch
 * @returns React Query result object with invoice data
 */
export function useInvoice(invoiceId: string | undefined) {
  return useQuery<InvoiceRow, Error>({
    queryKey: ['invoice', invoiceId],
    queryFn: async () => {
      if (!invoiceId) {
        throw new Error('Invoice ID is required')
      }
      
      const data = await getInvoiceById(invoiceId)
      return data
    },
    enabled: !!invoiceId,
    staleTime: 2 * 60 * 1000, // 2 minutes - consistent with providers
    gcTime: 5 * 60 * 1000, // 5 minutes garbage collection time
    refetchOnMount: true,
    refetchOnWindowFocus: true, // Important for tab switching
    refetchOnReconnect: true,
    refetchInterval: false,
    refetchIntervalInBackground: false,
    retry: 3, // Increased retry count
    retryDelay: (attemptIndex) => {
      return Math.min(1000 * 2 ** attemptIndex, 30000)
    },
    networkMode: 'online' // Handle offline scenarios
  })
}

/**
 * Custom hook to create a new invoice with React Query mutation
 * @returns React Query mutation object for creating invoices
 */
export function useCreateInvoice() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (params: { organizationId: string; invoiceData: any }) => 
      createInvoice(params.organizationId, params.invoiceData),
    onSuccess: () => {
      // Invalidate and refetch all invoices after creating a new one
      queryClient.invalidateQueries({ queryKey: ['allInvoices'] })
    },
  })
}

/**
 * Custom hook to update an existing invoice with React Query mutation
 * @returns React Query mutation object for updating invoices
 */
export function useUpdateInvoice() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (params: { invoiceId: string; invoiceData: any }) => 
      updateInvoice(params.invoiceId, params.invoiceData),
    onSuccess: () => {
      // Invalidate and refetch all invoices after updating
      queryClient.invalidateQueries({ queryKey: ['allInvoices'] })
    },
  })
}

/**
 * Custom hook to update an invoice status with React Query mutation
 * @returns React Query mutation object for updating invoice status
 */
export function useUpdateInvoiceStatus() {
  const queryClient = useQueryClient()
  const { toast } = useToast()
  
  return useMutation({
    mutationFn: (params: { 
      invoiceId: string; 
      newStatus: 'Draft' | 'Sent' | 'Paid' | 'Overdue'; 
      additionalData?: Partial<any> 
    }) => updateInvoiceStatus(params.invoiceId, params.newStatus, params.additionalData),
    onSuccess: () => {
      // Invalidate and refetch all invoices after updating status
      queryClient.invalidateQueries({ queryKey: ['allInvoices'] })
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to update invoice status: ${error.message}`,
        variant: "destructive",
      })
    },
  })
}

/**
 * Custom hook to delete an invoice with React Query mutation
 * @returns React Query mutation object for deleting invoices
 */
export function useDeleteInvoice() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: deleteInvoice,
    onSuccess: () => {
      // Invalidate and refetch all invoices after deleting
      queryClient.invalidateQueries({ queryKey: ['allInvoices'] })
    },
  })
}

/**
 * Custom hook to generate an invoice from an order with React Query mutation
 * @returns React Query mutation object for generating invoices from orders
 */
export function useGenerateInvoice() {
  const queryClient = useQueryClient()
  const { toast } = useToast()
  
  return useMutation({
    mutationFn: async (params: { orderId: string; dueDate?: string; markAsSent?: boolean }) => {
      // Get the Supabase client
      const supabase = getSupabaseClient()
      
      // Prepare RPC parameters
      const rpcParams: any = {
        p_order_id: params.orderId
      }
      
      // Add due date if provided
      if (params.dueDate) {
        rpcParams.p_due_date = params.dueDate
      }
      
      // Call the database function directly via RPC
      const { data, error } = await supabase.rpc('generate_invoice_from_order', rpcParams)
      
      if (error) {
        throw new Error(error.message)
      }
      
      // If markAsSent is true, update the invoice status
      if (params.markAsSent && data) {
        // Get the invoice to update its status
        const { data: invoiceData, error: invoiceError } = await supabase
          .from('invoices')
          .select('id')
          .eq('id', data)
          .single()
        
        if (!invoiceError && invoiceData) {
          // Update the invoice status to 'Sent'
          const { error: updateError } = await supabase
            .from('invoices')
            .update({ status: 'Sent', updated_at: new Date().toISOString() })
            .eq('id', invoiceData.id)
          
          if (updateError) {
            console.warn('Failed to mark invoice as sent:', updateError.message)
          }
        }
      }
      
      return data
    },
    onSuccess: () => {
      // Invalidate and refetch orders and invoices after generating an invoice
      queryClient.invalidateQueries({ queryKey: ['allOrders'] })
      queryClient.invalidateQueries({ queryKey: ['allInvoices'] })
      
      toast({
        title: "Invoice Generated",
        description: "The invoice has been successfully generated.",
      })
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to generate invoice: ${error.message}`,
        variant: "destructive",
      })
    },
  })
}

/**
 * Custom hook to invalidate invoice queries and trigger refetching with debouncing
 * @returns Object with functions to invalidate queries
 */
export function useInvalidateInvoices() {
  const queryClient = useQueryClient()
  
  // Debounced invalidate function
  const debouncedInvalidateAllInvoices = debounce(() => {
    queryClient.invalidateQueries({ queryKey: ['allInvoices'] })
  }, 1000) // 1 second debounce
  
  return {
    invalidateAllInvoices: debouncedInvalidateAllInvoices
  }
}