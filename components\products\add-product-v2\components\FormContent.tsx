import React from 'react'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useProductForm } from '../context/ProductFormContext'
import { getSectionById, getNextSection, getPreviousSection } from '../config/sections'

// Section components (we'll create these next)
import { ProductTypeSection } from './sections/ProductTypeSection'
import { ProductDetailsSection } from './sections/ProductDetailsSection'
import { PricingSection } from './sections/PricingSection'
import { InventorySection } from './sections/InventorySection'
import { SupplierSection } from './sections/SupplierSection'
import { AdditionalInfoSection } from './sections/AdditionalInfoSection'

function SectionHeader() {
  const { currentSection } = useProductForm()
  const section = getSectionById(currentSection)

  return (
    <div className="flex items-center justify-between">
      <div>
        <h2 className="text-lg font-semibold text-gray-900">
          {section?.label || 'Unknown Section'}
        </h2>
        {section?.description && (
          <p className="text-sm text-gray-600 mt-0.5">
            {section.description}
          </p>
        )}
      </div>
      <div className="flex items-center gap-2">
        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
        <span className="text-xs text-gray-500 uppercase tracking-wide">Active</span>
      </div>
    </div>
  )
}

function SectionContent() {
  const { currentSection } = useProductForm()
  
  switch (currentSection) {
    case 'type':
      return <ProductTypeSection />

    case 'details':
      return <ProductDetailsSection />
    case 'pricing':
      return <PricingSection />
    case 'inventory':
      return <InventorySection />
    case 'supplier':
      return <SupplierSection />
    case 'additional':
      return <AdditionalInfoSection />
    default:
      return (
        <div className="text-center py-12">
          <p className="text-gray-500">Section not found</p>
        </div>
      )
  }
}

function ActionButtons() {
  const { formData, resetForm, isSubmitting } = useProductForm()

  const handleSave = async () => {
    // TODO: Implement save functionality
    console.log('Saving product:', formData)
    alert('Save functionality will be implemented next!')
  }

  return (
    <div className="flex items-center justify-end gap-2">
      <Button
        type="button"
        variant="outline"
        onClick={resetForm}
        disabled={isSubmitting}
        className="h-8 px-3 text-xs"
      >
        Reset
      </Button>

      <Button
        type="button"
        onClick={handleSave}
        disabled={isSubmitting || !formData.name || !formData.base_sku}
        className="h-8 px-3 text-xs bg-green-600 hover:bg-green-700"
      >
        {isSubmitting ? 'Creating...' : 'Create Product'}
      </Button>
    </div>
  )
}

export function FormContent() {
  return (
    <div className="flex-1 flex flex-col bg-white overflow-hidden">
      {/* Top divider aligned with left column - adjusted padding for proper alignment */}
      <div className="px-6 pt-9 pb-3 border-b border-gray-200 bg-white flex-shrink-0 pr-12">
        <div className="h-0"></div>
      </div>

      {/* Scrollable content */}
      <div className="flex-1 overflow-y-auto px-6 py-4">
        <SectionContent />
      </div>

      {/* Action buttons footer */}
      <div className="border-t border-gray-200 px-6 py-3 bg-gray-50 flex-shrink-0">
        <ActionButtons />
      </div>
    </div>
  )
}