class DashboardTemplate {
  generate(route = '/dashboard') {
    const isExpensesPage = route === '/dashboard/expenses';
    
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${isExpensesPage ? 'Expenses' : 'Dashboard'} - ONKO</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="flex h-screen">
        <!-- Sidebar -->
        ${this.generateSidebar(isExpensesPage)}
        
        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Header -->
            ${this.generateHeader(isExpensesPage)}
            
            <!-- Content -->
            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6">
                ${isExpensesPage ? this.generateExpensesContent() : this.generateDashboardContent()}
            </main>
        </div>
    </div>
    
    ${this.generateScripts(isExpensesPage)}
</body>
</html>
    `;
  }

  generateSidebar(isExpensesPage) {
    return `
        <div class="w-64 bg-gray-900 text-white">
            <div class="p-6">
                <h1 class="text-2xl font-bold">ONKO</h1>
                <p class="text-sm text-gray-400 mt-1">Business Management</p>
            </div>
            <nav class="mt-6">
                <a href="/dashboard" class="block px-6 py-3 ${!isExpensesPage ? 'bg-blue-600 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white'}">
                    📊 Dashboard
                </a>
                <a href="/dashboard/expenses" class="block px-6 py-3 ${isExpensesPage ? 'bg-blue-600 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white'}">
                    💰 Expenses
                </a>
                <a href="#" class="block px-6 py-3 text-gray-300 hover:bg-gray-700 hover:text-white">
                    ➕ Add Products
                </a>
                <a href="#" class="block px-6 py-3 text-gray-300 hover:bg-gray-700 hover:text-white">
                    📦 Products List
                </a>
                <a href="#" class="block px-6 py-3 text-gray-300 hover:bg-gray-700 hover:text-white">
                    📋 Stock Management
                </a>
                <a href="#" class="block px-6 py-3 text-gray-300 hover:bg-gray-700 hover:text-white">
                    📈 Reporting
                </a>
                <a href="#" class="block px-6 py-3 text-gray-300 hover:bg-gray-700 hover:text-white">
                    👥 Customers
                </a>
                <a href="#" class="block px-6 py-3 text-gray-300 hover:bg-gray-700 hover:text-white">
                    ⚙️ Settings
                </a>
            </nav>
            <div class="absolute bottom-0 w-64 p-6">
                <button onclick="signOut()" class="w-full text-left text-gray-300 hover:text-white">
                    🚪 Sign Out
                </button>
            </div>
        </div>
    `;
  }

  generateHeader(isExpensesPage) {
    return `
        <header class="bg-white shadow-sm border-b border-gray-200">
            <div class="px-6 py-4">
                <h2 class="text-2xl font-bold text-gray-900">
                    ${isExpensesPage ? 'Expenses Management' : 'Dashboard Overview'}
                </h2>
                <p class="text-gray-600 mt-1">
                    ${isExpensesPage ? 'Track and manage your business expenses' : 'Monitor your business performance'}
                </p>
            </div>
        </header>
    `;
  }

  generateDashboardContent() {
    return `
        <div class="space-y-6">
            <!-- Metrics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <span class="text-2xl">💰</span>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Expenses</p>
                            <p id="totalExpenses" class="text-2xl font-bold text-gray-900">Loading...</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <span class="text-2xl">📅</span>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Monthly Expenses</p>
                            <p id="monthlyExpenses" class="text-2xl font-bold text-gray-900">Loading...</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <span class="text-2xl">📦</span>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Products</p>
                            <p id="totalProducts" class="text-2xl font-bold text-gray-900">0</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <span class="text-2xl">📈</span>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Monthly Revenue</p>
                            <p id="monthlyRevenue" class="text-2xl font-bold text-gray-900">$0.00</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Recent Expenses</h3>
                </div>
                <div class="p-6">
                    <div id="recentExpenses" class="space-y-3">
                        <p class="text-gray-500">Loading recent expenses...</p>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Quick Actions</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <a href="/dashboard/expenses" class="bg-blue-50 hover:bg-blue-100 p-4 rounded-lg text-center transition-colors">
                            <span class="text-2xl block mb-2">💰</span>
                            <span class="text-sm font-medium text-blue-900">Add Expense</span>
                        </a>
                        <button class="bg-green-50 hover:bg-green-100 p-4 rounded-lg text-center transition-colors" disabled>
                            <span class="text-2xl block mb-2">📦</span>
                            <span class="text-sm font-medium text-green-900">Add Product</span>
                        </button>
                        <button class="bg-purple-50 hover:bg-purple-100 p-4 rounded-lg text-center transition-colors" disabled>
                            <span class="text-2xl block mb-2">📊</span>
                            <span class="text-sm font-medium text-purple-900">View Reports</span>
                        </button>
                        <button class="bg-orange-50 hover:bg-orange-100 p-4 rounded-lg text-center transition-colors" disabled>
                            <span class="text-2xl block mb-2">⚙️</span>
                            <span class="text-sm font-medium text-orange-900">Settings</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
  }

  generateExpensesContent() {
    return `
        <div class="space-y-6">
            <!-- Add Expense Form -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Add New Expense</h3>
                    <p class="text-sm text-gray-600">Record a new business expense to your Supabase database</p>
                </div>
                <div class="p-6">
                    <form id="expenseForm" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Amount ($)</label>
                            <input id="amount" type="number" step="0.01" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="0.00" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Description</label>
                            <input id="description" type="text" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="Office supplies, travel, etc." required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Category</label>
                            <input id="category" type="text" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="Office, Travel, Marketing">
                        </div>
                        <div class="flex items-end">
                            <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition-colors">
                                Add Expense
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Expenses Table -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Recent Expenses</h3>
                    <p class="text-sm text-gray-600">View and manage your recorded expenses</p>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                            </tr>
                        </thead>
                        <tbody id="expensesTableBody" class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td colspan="4" class="px-6 py-4 text-center text-gray-500">Loading expenses...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Quick Test Expenses -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Quick Test Expenses</h3>
                    <p class="text-sm text-gray-600">Click these buttons to quickly add test expense data</p>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <button onclick="fillTestExpense('25.99', 'Office supplies', 'Office')" class="bg-gray-50 hover:bg-gray-100 p-4 rounded-lg text-center transition-colors">
                            📝 Office Supplies ($25.99)
                        </button>
                        <button onclick="fillTestExpense('120.00', 'Business lunch', 'Meals')" class="bg-gray-50 hover:bg-gray-100 p-4 rounded-lg text-center transition-colors">
                            🍽️ Business Lunch ($120.00)
                        </button>
                        <button onclick="fillTestExpense('89.50', 'Software subscription', 'Software')" class="bg-gray-50 hover:bg-gray-100 p-4 rounded-lg text-center transition-colors">
                            💻 Software ($89.50)
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
  }

  generateScripts(isExpensesPage) {
    return `
    <script>
        // Global functions
        function signOut() {
            alert('Signing out...');
            window.location.href = '/auth/signin';
        }

        ${isExpensesPage ? this.generateExpensesScripts() : this.generateDashboardScripts()}
    </script>
    `;
  }

  generateDashboardScripts() {
    return `
        // Load dashboard metrics
        async function loadDashboardMetrics() {
            try {
                const token = localStorage.getItem('onko_auth_token');
                if (!token) {
                    alert('Please sign in again.');
                    window.location.href = '/auth/signin';
                    return;
                }
                
                const response = await fetch('/api/dashboard/metrics', {
                    headers: {
                        'Authorization': 'Bearer ' + token
                    }
                });
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        const data = result.data;
                        document.getElementById('totalExpenses').textContent = '$' + data.totalExpenses;
                        document.getElementById('monthlyExpenses').textContent = '$' + data.monthlyExpenses;
                        document.getElementById('totalProducts').textContent = data.totalProducts;
                        document.getElementById('monthlyRevenue').textContent = '$' + data.monthlyRevenue;
                        
                        // Update recent expenses
                        const recentExpensesDiv = document.getElementById('recentExpenses');
                        if (data.recentExpenses && data.recentExpenses.length > 0) {
                            recentExpensesDiv.innerHTML = data.recentExpenses.map(expense => 
                                '<div class="flex justify-between items-center py-2 border-b last:border-b-0">' +
                                '<div>' +
                                '<p class="font-medium">' + expense.description + '</p>' +
                                '<p class="text-sm text-gray-500">' + expense.category + '</p>' +
                                '</div>' +
                                '<span class="font-bold text-red-600">$' + parseFloat(expense.amount).toFixed(2) + '</span>' +
                                '</div>'
                            ).join('');
                        } else {
                            recentExpensesDiv.innerHTML = '<p class="text-gray-500">No expenses recorded yet.</p>';
                        }
                    }
                }
            } catch (error) {
                console.error('Error loading dashboard metrics:', error);
            }
        }

        // Load data when page loads
        document.addEventListener('DOMContentLoaded', loadDashboardMetrics);
    `;
  }

  generateExpensesScripts() {
    return `
        // Fill test expense data
        function fillTestExpense(amount, description, category) {
            document.getElementById('amount').value = amount;
            document.getElementById('description').value = description;
            document.getElementById('category').value = category;
        }

        // Load expenses table
        async function loadExpenses() {
            try {
                const token = localStorage.getItem('onko_auth_token');
                if (!token) {
                    alert('Please sign in again.');
                    window.location.href = '/auth/signin';
                    return;
                }
                
                const response = await fetch('/api/expenses', {
                    headers: {
                        'Authorization': 'Bearer ' + token
                    }
                });
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        const tableBody = document.getElementById('expensesTableBody');
                        if (result.data && result.data.length > 0) {
                            tableBody.innerHTML = result.data.map(expense => 
                                '<tr>' +
                                '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">' + 
                                new Date(expense.created_at).toLocaleDateString() + '</td>' +
                                '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">' + expense.description + '</td>' +
                                '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + (expense.category || 'General') + '</td>' +
                                '<td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600">$' + parseFloat(expense.amount).toFixed(2) + '</td>' +
                                '</tr>'
                            ).join('');
                        } else {
                            tableBody.innerHTML = '<tr><td colspan="4" class="px-6 py-4 text-center text-gray-500">No expenses found</td></tr>';
                        }
                    }
                }
            } catch (error) {
                console.error('Error loading expenses:', error);
                document.getElementById('expensesTableBody').innerHTML = 
                    '<tr><td colspan="4" class="px-6 py-4 text-center text-red-500">Error loading expenses</td></tr>';
            }
        }

        // Handle expense form submission
        document.getElementById('expenseForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const amount = document.getElementById('amount').value;
            const description = document.getElementById('description').value;
            const category = document.getElementById('category').value;
            
            try {
                const token = localStorage.getItem('onko_auth_token');
                if (!token) {
                    alert('Please sign in again.');
                    window.location.href = '/auth/signin';
                    return;
                }
                
                const response = await fetch('/api/expenses', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + token
                    },
                    body: JSON.stringify({
                        amount: parseFloat(amount),
                        description: description,
                        category: category || 'General'
                    })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        alert('Expense recorded successfully!');
                        document.getElementById('expenseForm').reset();
                        loadExpenses(); // Reload the table
                    } else {
                        alert('Error: ' + result.error);
                    }
                } else {
                    alert('Server error: ' + response.status);
                }
            } catch (error) {
                console.error('Error adding expense:', error);
                alert('Network error occurred');
            }
        });

        // Load expenses when page loads
        document.addEventListener('DOMContentLoaded', loadExpenses);
    `;
  }
}

module.exports = new DashboardTemplate();