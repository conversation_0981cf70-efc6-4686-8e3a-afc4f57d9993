'use client'

import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useToast } from '@/components/ui/use-toast'
import { createSupabaseClient } from '@/lib/supabase'
import { useAuth } from '@/contexts/auth-context'
import { useCurrency, SUPPORTED_CURRENCIES, type CurrencyCode } from '@/lib/currency'
import { CustomDropdown } from '@/components/ui/custom-dropdown'

interface GeneralSettingsProps {
  initialBusinessName: string
  initialCurrency: string
  onBusinessNameUpdate: (name: string) => void
  onCurrencyUpdate: (currency: string) => void
}

export function GeneralSettings({ 
  initialBusinessName, 
  initialCurrency,
  onBusinessNameUpdate,
  onCurrencyUpdate
}: GeneralSettingsProps) {
  const { user } = useAuth()
  const { userCurrency, formatCurrency, updateUserCurrency } = useCurrency()
  const { toast } = useToast()
  const supabase = createSupabaseClient()
  
  // Convert currencies to dropdown options dynamically
  const CURRENCY_OPTIONS = Object.entries(SUPPORTED_CURRENCIES).map(
    ([code, info]) => ({
      value: code,
      label: `${code} - ${info.name} (${info.symbol})`,
    })
  )
  
  // Form states
  const [businessName, setBusinessName] = useState(initialBusinessName)
  const [selectedCurrency, setSelectedCurrency] = useState(initialCurrency)
  const [isLoading, setIsLoading] = useState(false)
  const [isCurrencyLoading, setIsCurrencyLoading] = useState(false)

  const handleBusinessNameUpdate = async () => {
    setIsLoading(true)
    try {
      const { error } = await supabase
        .from('profiles')
        .update({ business_name: businessName.trim() })
        .eq('id', user?.id)

      if (error) {
        toast({
          title: "Update Failed",
          description: `Failed to update business name: ${error.message}`,
          variant: "destructive",
        })
      } else {
        onBusinessNameUpdate(businessName)
        toast({
          title: "Business Name Updated",
          description: "Your business name has been updated successfully.",
        })
      }
    } catch (error) {
      toast({
        title: "Unexpected Error",
        description: "Something went wrong. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleCurrencyUpdate = async () => {
    if (selectedCurrency === userCurrency) return

    setIsCurrencyLoading(true)
    try {
      const success = await updateUserCurrency(selectedCurrency as CurrencyCode)
      
      if (success) {
        onCurrencyUpdate(selectedCurrency)
        toast({
          title: "Currency Updated",
          description: `Currency changed to ${SUPPORTED_CURRENCIES[selectedCurrency as CurrencyCode].name}`,
        })
      } else {
        toast({
          title: "Update Failed",
          description: "Failed to update currency. Please try again.",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Unexpected Error",
        description: "Something went wrong. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsCurrencyLoading(false)
    }
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {/* Business Information */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="px-4 py-3 border-b border-gray-200">
          <h3 className="text-base font-medium text-gray-900">Business Information</h3>
        </div>
        <div className="p-4 space-y-4">
          <div className="space-y-2">
            <Label htmlFor="business-name" className="text-sm font-medium text-gray-700">Business Name</Label>
            <Input
              id="business-name"
              value={businessName}
              onChange={(e) => setBusinessName(e.target.value)}
              placeholder="Your Business Name"
              className="text-sm h-9"
            />
          </div>
          
          <Button 
            onClick={handleBusinessNameUpdate}
            disabled={isLoading}
            className="h-9 px-3 text-sm"
          >
            {isLoading ? 'Updating...' : 'Update Business Name'}
          </Button>
        </div>
      </div>

      {/* Currency Settings */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="px-4 py-3 border-b border-gray-200">
          <h3 className="text-base font-medium text-gray-900">Currency Settings</h3>
        </div>
        <div className="p-4 space-y-4">
          <div className="space-y-2">
            <Label htmlFor="currency" className="text-sm font-medium text-gray-700">Default Currency</Label>
            <CustomDropdown
              options={CURRENCY_OPTIONS}
              value={selectedCurrency}
              onValueChange={setSelectedCurrency}
              placeholder="Select your currency"
              allowCustom={false}
            />
          </div>
          
          {selectedCurrency && (
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-xs text-gray-500 mb-1">Preview:</p>
              <p className="text-sm font-semibold text-gray-900">
                {formatCurrency(1234.56, selectedCurrency as CurrencyCode)}
              </p>
            </div>
          )}
          
          <Button 
            onClick={handleCurrencyUpdate}
            disabled={isCurrencyLoading || selectedCurrency === userCurrency}
            className="h-9 px-3 text-sm"
          >
            {isCurrencyLoading ? 'Updating...' : 'Update Currency'}
          </Button>
          
          {selectedCurrency !== userCurrency && (
            <p className="text-xs text-amber-600">
              You have unsaved currency changes
            </p>
          )}
        </div>
      </div>
    </div>
  )
}