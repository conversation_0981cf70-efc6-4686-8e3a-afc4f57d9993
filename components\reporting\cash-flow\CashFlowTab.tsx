'use client'

import { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { DateRangeSelector } from '@/components/ui/date-range-selector'
import { DateRange } from 'react-day-picker'
import { useCashFlow } from '@/hooks/use-reports'
import { 
  Collapsible, 
  CollapsibleContent, 
  CollapsibleTrigger 
} from '@/components/ui/collapsible'
import { format } from 'date-fns'
import { ChevronDown, ChevronRight } from 'lucide-react'
import { PageHeader } from '@/components/ui/page-header'

export function CashFlowTab() {
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(new Date().getFullYear(), 0, 1),
    to: new Date(),
  })

  // State for collapsible sections
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({
    operating: false,
    investing: false,
    financing: false
  })

  const toggleSection = (section: string) => {
    setOpenSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  const { data: cashFlowData, isLoading, error } = useCashFlow({
    from: dateRange?.from,
    to: dateRange?.to
  })

  const handleExport = (format: 'pdf' | 'csv') => {
    console.log(`Exporting as ${format}`)
    // Export functionality will be implemented later
  }

  // Calculate net change in cash
  const netChangeInCash = cashFlowData 
    ? cashFlowData.cash_from_operating.total + 
      cashFlowData.cash_from_investing.total + 
      cashFlowData.cash_from_financing.total
    : 0

  return (
    <div className="space-y-6">
      {/* Header Controls */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        {/* Removed duplicate PageHeader component to prevent title duplication */}
        
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={() => handleExport('pdf')}
            disabled={isLoading}
          >
            Export PDF
          </Button>
          <Button 
            variant="outline" 
            onClick={() => handleExport('csv')}
            disabled={isLoading}
          >
            Export CSV
          </Button>
        </div>
      </div>

      {/* Date Range Selector */}
      <DateRangeSelector dateRange={dateRange} onDateChange={setDateRange} />

      {/* Loading State */}
      {isLoading && (
        <div className="flex justify-center items-center h-64">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary mb-2"></div>
            <p>Generating Cash Flow Report...</p>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4">
          <p className="text-destructive font-medium">Error loading report: {error.message}</p>
        </div>
      )}

      {/* Financial Statement Collapsible Sections */}
      {cashFlowData && !isLoading && (
        <Card>
          <CardHeader>
            {/* Removed duplicate CardTitle component to prevent title duplication */}
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Cash from Operating Activities */}
            <Collapsible 
              open={openSections.operating} 
              onOpenChange={() => toggleSection('operating')}
            >
              <CollapsibleTrigger asChild>
                <div className="flex justify-between items-center w-full p-4 border rounded-md hover:bg-gray-50 cursor-pointer">
                  <span className="font-medium">Cash from Operating Activities</span>
                  <div className="flex items-center gap-2">
                    <span className={cashFlowData.cash_from_operating.total >= 0 ? 'text-green-600' : 'text-red-600'}>
                      ${cashFlowData.cash_from_operating.total.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                    </span>
                    {openSections.operating ? 
                      <ChevronDown className="h-4 w-4 text-gray-500" /> : 
                      <ChevronRight className="h-4 w-4 text-gray-500" />
                    }
                  </div>
                </div>
              </CollapsibleTrigger>
              
              <CollapsibleContent>
                <div className="mt-2 p-4 bg-gray-50 border rounded-md space-y-6">
                  {/* Paid Invoices Table */}
                  <div>
                    <h4 className="text-sm font-medium mb-2">Paid Invoices</h4>
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left py-2 text-xs uppercase text-slate-500 font-normal">Invoice #</th>
                          <th className="text-left py-2 text-xs uppercase text-slate-500 font-normal">Customer</th>
                          <th className="text-left py-2 text-xs uppercase text-slate-500 font-normal">Date Paid</th>
                          <th className="text-right py-2 text-xs uppercase text-slate-500 font-normal">Amount</th>
                        </tr>
                      </thead>
                      <tbody>
                        {cashFlowData.cash_from_operating.paid_invoices.map((invoice) => (
                          <tr key={invoice.id} className="border-b last:border-0">
                            <td className="py-2 text-sm">{invoice.invoice_number}</td>
                            <td className="py-2 text-sm">{invoice.customer_name}</td>
                            <td className="py-2 text-sm">{format(new Date(invoice.issue_date), 'MMM d, yyyy')}</td>
                            <td className="py-2 text-right text-sm">${invoice.total.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</td>
                          </tr>
                        ))}
                        {cashFlowData.cash_from_operating.paid_invoices.length === 0 && (
                          <tr>
                            <td colSpan={4} className="py-4 text-center text-sm text-slate-500">
                              No paid invoices found
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>

                  {/* Paid Expenses Table */}
                  <div>
                    <h4 className="text-sm font-medium mb-2">Paid Expenses</h4>
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left py-2 text-xs uppercase text-slate-500 font-normal">Description</th>
                          <th className="text-left py-2 text-xs uppercase text-slate-500 font-normal">Category</th>
                          <th className="text-left py-2 text-xs uppercase text-slate-500 font-normal">Date Paid</th>
                          <th className="text-right py-2 text-xs uppercase text-slate-500 font-normal">Amount</th>
                        </tr>
                      </thead>
                      <tbody>
                        {cashFlowData.cash_from_operating.paid_expenses.map((expense) => (
                          <tr key={expense.id} className="border-b last:border-0">
                            <td className="py-2 text-sm">{expense.description}</td>
                            <td className="py-2 text-sm">{expense.category}</td>
                            <td className="py-2 text-sm">{format(new Date(expense.expense_date), 'MMM d, yyyy')}</td>
                            <td className="py-2 text-right text-sm">${expense.amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</td>
                          </tr>
                        ))}
                        {cashFlowData.cash_from_operating.paid_expenses.length === 0 && (
                          <tr>
                            <td colSpan={4} className="py-4 text-center text-sm text-slate-500">
                              No paid expenses found
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              </CollapsibleContent>
            </Collapsible>

            {/* Cash from Investing Activities */}
            <Collapsible 
              open={openSections.investing} 
              onOpenChange={() => toggleSection('investing')}
            >
              <CollapsibleTrigger asChild>
                <div className="flex justify-between items-center w-full p-4 border rounded-md hover:bg-gray-50 cursor-pointer">
                  <span className="font-medium">Cash from Investing Activities</span>
                  <div className="flex items-center gap-2">
                    <span className={cashFlowData.cash_from_investing.total >= 0 ? 'text-green-600' : 'text-red-600'}>
                      ${cashFlowData.cash_from_investing.total.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                    </span>
                    {openSections.investing ? 
                      <ChevronDown className="h-4 w-4 text-gray-500" /> : 
                      <ChevronRight className="h-4 w-4 text-gray-500" />
                    }
                  </div>
                </div>
              </CollapsibleTrigger>
              
              <CollapsibleContent>
                <div className="mt-2 p-4 bg-gray-50 border rounded-md">
                  <div className="text-sm text-slate-500">
                    No investing activities recorded
                  </div>
                </div>
              </CollapsibleContent>
            </Collapsible>

            {/* Cash from Financing Activities */}
            <Collapsible 
              open={openSections.financing} 
              onOpenChange={() => toggleSection('financing')}
            >
              <CollapsibleTrigger asChild>
                <div className="flex justify-between items-center w-full p-4 border rounded-md hover:bg-gray-50 cursor-pointer">
                  <span className="font-medium">Cash from Financing Activities</span>
                  <div className="flex items-center gap-2">
                    <span className={cashFlowData.cash_from_financing.total >= 0 ? 'text-green-600' : 'text-red-600'}>
                      ${cashFlowData.cash_from_financing.total.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                    </span>
                    {openSections.financing ? 
                      <ChevronDown className="h-4 w-4 text-gray-500" /> : 
                      <ChevronRight className="h-4 w-4 text-gray-500" />
                    }
                  </div>
                </div>
              </CollapsibleTrigger>
              
              <CollapsibleContent>
                <div className="mt-2 p-4 bg-gray-50 border rounded-md">
                  <div className="text-sm text-slate-500">
                    No financing activities recorded
                  </div>
                </div>
              </CollapsibleContent>
            </Collapsible>

            {/* Net Change in Cash Summary */}
            <div className="mt-6 pt-4 border-t border-slate-200">
              <div className="flex justify-between items-center">
                <span className="font-medium">Net Change in Cash</span>
                <span className={`font-medium ${netChangeInCash >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  ${netChangeInCash.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}