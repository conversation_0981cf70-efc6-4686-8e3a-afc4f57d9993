# WooCommerce Integration Setup Guide

This guide will help you properly set up the WooCommerce integration with your Onko account.

## Prerequisites

1. A WooCommerce store with REST API enabled
2. Administrator access to your WooCommerce store
3. SSL certificate (HTTPS) for your store

## Step 1: Enable REST API in WooCommerce

1. Log in to your WordPress admin panel
2. Go to WooCommerce > Settings > Advanced > REST API
3. Make sure the REST API is enabled

## Step 2: Create API Credentials

1. In your WordPress admin panel, go to WooCommerce > Settings > Advanced > REST API
2. Click "Add Key"
3. Fill in the details:
   - Description: `Onko Integration`
   - User: Select an admin user
   - Permissions: `Read/Write`
4. Click "Generate API Key"
5. Save the Consumer Key and Consumer Secret in a secure location

## Step 3: Configure Onko Integration

1. In Onko, go to Settings > Integrations
2. Find the WooCommerce integration card
3. Click "Connect"
4. Fill in the form:
   - Store URL: Your WooCommerce store URL (e.g., `https://yourstore.com`)
   - Consumer Key: The key you generated in Step 2
   - Consumer Secret: The secret you generated in Step 2
5. Click "Test Connection" to verify your credentials
6. If successful, click "Connect Store"

## Step 4: Register Webhook

After connecting your store:
1. Click "Register Webhook" on the WooCommerce integration card
2. This will set up automatic order syncing when new orders are created in WooCommerce

## Troubleshooting

### Common Issues

1. **400 Bad Request Error**: This typically means there's an issue with your credentials or store URL
   - Verify your store URL is correct and accessible
   - Confirm your API credentials are correct and have Read/Write permissions
   - Check that your store has SSL (HTTPS)

2. **Webhook Registration Failed**: 
   - Ensure your store URL is publicly accessible
   - Check that your WooCommerce REST API is properly configured

3. **Orders Not Syncing**:
   - Verify the webhook is properly registered
   - Check the webhook status in your WooCommerce settings

### Checking Webhook Status

1. In your WordPress admin panel, go to WooCommerce > Settings > Advanced > Webhooks
2. Look for the "ONKO Order Created" webhook
3. Ensure its status is "Active"
4. Check the delivery logs for any errors

## Security Notes

- Never share your API credentials
- Use Read/Write permissions only when necessary
- Regularly rotate your API keys for security
- The Consumer Secret is stored securely and never displayed in the UI

## Support

If you continue to experience issues, please contact support with:
- Your store URL
- Error messages you're seeing
- Steps you've already taken to troubleshoot