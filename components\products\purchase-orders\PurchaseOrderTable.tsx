'use client'

import { useState } from 'react'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import { MoreHorizontal, Eye, Edit, Trash2 } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { PurchaseOrder } from './types'
import { useCurrency } from '@/contexts/currency-context'

interface PurchaseOrderTableProps {
  purchaseOrders: PurchaseOrder[]
  onDelete: (id: string) => void
}

export function PurchaseOrderTable({ purchaseOrders, onDelete }: PurchaseOrderTableProps) {
  const router = useRouter()
  const { userCurrency, formatCurrency } = useCurrency()
  const [openMenuId, setOpenMenuId] = useState<string | null>(null)

  const handleView = (id: string) => {
    router.push(`/dashboard/products/purchase-orders/${id}`)
  }

  const handleEdit = (id: string) => {
    // For now, we'll just view the PO since editing might be complex
    router.push(`/dashboard/products/purchase-orders/${id}`)
  }

  const handleDelete = (id: string) => {
    onDelete(id)
    setOpenMenuId(null)
  }

  return (
    <div className="border border-gray-200 rounded-lg overflow-hidden">
      <div className="overflow-x-auto">
        <Table>
          <TableHeader className="bg-gray-50">
            <TableRow>
              <TableHead className="text-xs font-medium text-gray-900 py-3 px-6">
                PO Number
              </TableHead>
              <TableHead className="text-xs font-medium text-gray-900 py-3 px-6">
                Supplier
              </TableHead>
              <TableHead className="text-xs font-medium text-gray-900 py-3 px-6">
                Status
              </TableHead>
              <TableHead className="text-xs font-medium text-gray-900 py-3 px-6">
                Items
              </TableHead>
              <TableHead className="text-xs font-medium text-gray-900 py-3 px-6">
                Date Issued
              </TableHead>
              <TableHead className="text-xs font-medium text-gray-900 py-3 px-6">
                Expected Arrival
              </TableHead>
              <TableHead className="text-xs font-medium text-gray-900 py-3 px-6 text-right">
                Total Value
              </TableHead>
              <TableHead className="text-center py-3 px-6 font-medium text-gray-900 w-32 text-xs">
                Actions
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody className="bg-white divide-y divide-gray-200">
            {purchaseOrders.map((po) => (
              <TableRow key={po.id} className="hover:bg-gray-50">
                <TableCell className="py-3 px-6 text-xs">
                  <button 
                    onClick={() => handleView(po.id)}
                    className="font-medium text-blue-600 hover:text-blue-800 hover:underline"
                  >
                    {po.po_number}
                  </button>
                </TableCell>
                <TableCell className="py-3 px-6 text-xs">
                  {po.supplier}
                </TableCell>
                <TableCell className="py-3 px-6 text-xs">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${po.status_color}`}>
                    {po.status_display}
                  </span>
                </TableCell>
                <TableCell className="py-3 px-6 text-xs">
                  {po.item_count || 0}
                </TableCell>
                <TableCell className="py-3 px-6 text-xs">
                  {po.formatted_issue_date}
                </TableCell>
                <TableCell className="py-3 px-6 text-xs">
                  {po.formatted_expected_arrival_date || '-'}
                </TableCell>
                <TableCell className="py-3 px-6 text-xs text-right">
                  {formatCurrency(po.total_value, userCurrency)}
                </TableCell>
                <TableCell className="py-3 px-6 text-center">
                  <DropdownMenu 
                    open={openMenuId === po.id} 
                    onOpenChange={(isOpen) => setOpenMenuId(isOpen ? po.id : null)}
                  >
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-48">
                      <DropdownMenuItem 
                        onClick={() => handleView(po.id)}
                        className="text-xs flex items-center gap-2"
                      >
                        <Eye className="h-4 w-4" />
                        View Details
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        onClick={() => handleEdit(po.id)}
                        className="text-xs flex items-center gap-2"
                      >
                        <Edit className="h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        onClick={() => handleDelete(po.id)}
                        className="text-xs flex items-center gap-2 text-red-600 focus:text-red-600"
                      >
                        <Trash2 className="h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
            {purchaseOrders.length === 0 && (
              <TableRow>
                <TableCell colSpan={8} className="py-8 px-6 text-center text-xs text-gray-500">
                  No purchase orders found. Create your first purchase order to get started.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}