'use client';

import { useState } from 'react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, XCircle, RefreshCw } from 'lucide-react';

interface WooCommerceStatusCardProps {
  integration: {
    id: string;
    store_url: string | null;
    updated_at?: string;
    consumer_key?: string | null;
    consumer_secret?: string | null;
    consumer_secret_ref?: string | null;
  };
  onDisconnect: () => void;
}

export default function WooCommerceStatusCard({ integration, onDisconnect }: WooCommerceStatusCardProps) {
  const { toast } = useToast();
  const [isDisconnectModalOpen, setIsDisconnectModalOpen] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const [webhookStatus, setWebhookStatus] = useState<{registered: boolean; message: string} | null>(null);
  const supabase = createClientComponentClient();

  const checkWebhookStatus = async () => {
    try {
      // Get the access token
      const { data: { session } } = await supabase.auth.getSession();
      const token = session?.access_token;
      
      if (!token) {
        throw new Error('User not authenticated');
      }
      
      // Validate integration data before checking webhook
      if (!integration.store_url || integration.store_url === 'https://example-store.com') {
        setWebhookStatus({
          registered: false,
          message: 'Invalid store URL. Please update your WooCommerce store URL in the integration settings.'
        });
        return;
      }
      
      if (!integration.consumer_key || !integration.consumer_secret || 
          integration.consumer_key === 'ck_testkey123' || 
          integration.consumer_secret === 'cs_testsecret123') {
        setWebhookStatus({
          registered: false,
          message: 'Invalid API credentials. Please update your WooCommerce API credentials in the integration settings.'
        });
        return;
      }
      
      // Call the edge function to check webhook status
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/v1/register-woocommerce-webhook`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            integration_id: integration.id,
            check_status: true // Custom parameter to check status instead of registering
          })
        }
      );
      
      const result = await response.json();
      
      if (response.ok) {
        setWebhookStatus({
          registered: true,
          message: result.message || 'Webhook is properly registered with WooCommerce'
        });
      } else {
        console.error('Webhook status check failed:', result);
        // Provide more specific error messages
        if (result.error && result.error.includes('not found')) {
          setWebhookStatus({
            registered: false,
            message: 'Webhook not registered. Please click "Register Webhook" to set it up.'
          });
        } else {
          setWebhookStatus({
            registered: false,
            message: result.error || result.message || 'Webhook is not registered or has issues'
          });
        }
      }
    } catch (error: any) {
      console.error('Error checking webhook status:', error);
      setWebhookStatus({
        registered: false,
        message: `Failed to check webhook status: ${error.message || 'Unknown error'}`
      });
    }
  };

  const handleSyncHistoricalData = async () => {
    setIsSyncing(true);
    try {
      // In a real implementation, this would trigger a function to sync historical data
      // For now, we'll simulate this with a timeout
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      toast({
        title: 'Sync Initiated',
        description: 'Historical data sync has been initiated. This may take a few minutes.',
      });
    } catch (error: any) {
      toast({
        title: 'Sync Failed',
        description: error.message || 'Failed to initiate historical data sync',
        variant: 'destructive',
      });
    } finally {
      setIsSyncing(false);
    }
  };

  const handleRegisterWebhook = async () => {
    try {
      // Get the access token
      const { data: { session } } = await supabase.auth.getSession();
      const token = session?.access_token;
      
      if (!token) {
        throw new Error('User not authenticated');
      }
      
      // Validate integration data before registering webhook
      if (!integration.store_url || integration.store_url === 'https://example-store.com') {
        toast({
          title: 'Invalid Store URL',
          description: 'Please update your WooCommerce store URL in the integration settings before registering the webhook.',
          variant: 'destructive',
        });
        return;
      }
      
      if (!integration.consumer_key || !integration.consumer_secret || 
          integration.consumer_key === 'ck_testkey123' || 
          integration.consumer_secret === 'cs_testsecret123') {
        toast({
          title: 'Invalid Credentials',
          description: 'Please update your WooCommerce API credentials in the integration settings before registering the webhook.',
          variant: 'destructive',
        });
        return;
      }
      
      // Show loading state
      setWebhookStatus({
        registered: false,
        message: 'Registering webhook...'
      });
      
      // Call the edge function to register the webhook
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/v1/register-woocommerce-webhook`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            integration_id: integration.id
          })
        }
      );
      
      const result = await response.json();
      
      if (response.ok) {
        toast({
          title: 'Success',
          description: result.message || 'Webhook registered successfully',
        });
        // Refresh webhook status
        checkWebhookStatus();
      } else {
        console.error('Webhook registration failed:', result);
        toast({
          title: 'Registration Failed',
          description: result.error || result.message || 'Failed to register webhook. Please check your store URL and API credentials.',
          variant: 'destructive',
        });
        // Update status to show error
        setWebhookStatus({
          registered: false,
          message: result.error || result.message || 'Webhook registration failed'
        });
      }
    } catch (error: any) {
      console.error('Error registering webhook:', error);
      toast({
        title: 'Registration Failed',
        description: error.message || 'Failed to register webhook. Please check your network connection and try again.',
        variant: 'destructive',
      });
      // Update status to show error
      setWebhookStatus({
        registered: false,
        message: `Failed to register webhook: ${error.message || 'Unknown error'}`
      });
    }
  };

  const handleConfirmDisconnect = async () => {
    try {
      // Delete the integration
      const { error } = await supabase
        .from('integrations')
        .delete()
        .eq('id', integration.id);

      if (error) throw error;

      toast({
        title: 'Disconnected',
        description: 'WooCommerce store has been successfully disconnected.',
      });

      setIsDisconnectModalOpen(false);
      onDisconnect();
    } catch (error: any) {
      toast({
        title: 'Disconnect Failed',
        description: error.message || 'Failed to disconnect WooCommerce store',
        variant: 'destructive',
      });
    }
  };

  // Format the last sync time
  const formatLastSync = (dateString?: string) => {
    if (!dateString) return 'Never';
    
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    return `${Math.floor(diffInSeconds / 86400)} days ago`;
  };

  // Check if credentials are properly set
  const hasValidCredentials = integration.consumer_key && integration.consumer_secret;

  return (
    <>
      <Card className="flex flex-col">
        <CardHeader>
          <div className="flex items-center space-x-3">
            <div className="bg-purple-100 p-2 rounded-md">
              <div className="bg-purple-500 text-white p-2 rounded">
                {/* WooCommerce Icon Placeholder */}
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M21 12.5c0 1.4-1.1 2.5-2.5 2.5H10V9h8.5c1.4 0 2.5 1.1 2.5 2.5z"/>
                  <path d="M16 9h2c1.7 0 3 1.3 3 3v3c0 1.7-1.3 3-3 3h-2"/>
                  <path d="M3 3v18h18"/>
                  <path d="M3 9h4c1.7 0 3 1.3 3 3v3c0 1.7-1.3 3-3 3H3"/>
                </svg>
              </div>
            </div>
            <div>
              <CardTitle>WooCommerce</CardTitle>
              <Badge variant={hasValidCredentials ? "success" : "destructive"} className="mt-1">
                <span className="flex items-center">
                  {hasValidCredentials ? (
                    <>
                      <span className="w-2 h-2 rounded-full bg-green-500 mr-1"></span>
                      Connected
                    </>
                  ) : (
                    <>
                      <span className="w-2 h-2 rounded-full bg-red-500 mr-1"></span>
                      Incomplete Setup
                    </>
                  )}
                </span>
              </Badge>
            </div>
          </div>
          <CardDescription>
            Sync your orders and inventory automatically from your WooCommerce store
          </CardDescription>
        </CardHeader>
        <CardContent className="flex-grow space-y-4">
          <div>
            <h4 className="text-sm font-medium text-muted-foreground">Store URL</h4>
            <p className="text-sm">{integration.store_url || 'Not available'}</p>
          </div>
          
          <div>
            <h4 className="text-sm font-medium text-muted-foreground">Connection Status</h4>
            {hasValidCredentials ? (
              <div className="flex items-center text-sm text-green-600">
                <CheckCircle className="h-4 w-4 mr-1" />
                Credentials verified
              </div>
            ) : (
              <div className="flex items-center text-sm text-red-600">
                <XCircle className="h-4 w-4 mr-1" />
                Missing credentials
              </div>
            )}
          </div>
          
          <div>
            <div className="flex justify-between items-center">
              <h4 className="text-sm font-medium text-muted-foreground">Webhook Status</h4>
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={checkWebhookStatus}
                className="h-6 px-2 text-xs"
              >
                <RefreshCw className="h-3 w-3 mr-1" />
                Check
              </Button>
            </div>
            {webhookStatus ? (
              <Alert variant={webhookStatus.registered ? "default" : "destructive"} className="mt-1 py-2">
                {webhookStatus.registered ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  <XCircle className="h-4 w-4" />
                )}
                <AlertDescription className="text-xs">
                  {webhookStatus.message}
                </AlertDescription>
              </Alert>
            ) : (
              <p className="text-sm text-muted-foreground">Click "Check" to verify webhook status</p>
            )}
          </div>
          
          <div>
            <h4 className="text-sm font-medium text-muted-foreground">Last Sync</h4>
            <p className="text-sm">{formatLastSync(integration.updated_at)}</p>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button 
            onClick={handleRegisterWebhook}
            disabled={!hasValidCredentials || isSyncing}
          >
            Register Webhook
          </Button>
          <Button 
            variant="destructive" 
            onClick={() => setIsDisconnectModalOpen(true)}
          >
            Disconnect
          </Button>
        </CardFooter>
      </Card>

      <Dialog open={isDisconnectModalOpen} onOpenChange={setIsDisconnectModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Disconnect WooCommerce Store</DialogTitle>
            <DialogDescription>
              Are you sure you want to disconnect your WooCommerce store? This will:
              <div className="mt-2 space-y-1">
                <ul className="list-disc list-inside space-y-1">
                  <li>Stop automatic syncing of orders and products</li>
                  <li>Remove all integration credentials</li>
                  <li>Preserve existing synced data in Onko</li>
                </ul>
              </div>
              <div className="mt-3 font-medium">This action cannot be undone.</div>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDisconnectModalOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleConfirmDisconnect}>
              Disconnect Store
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}