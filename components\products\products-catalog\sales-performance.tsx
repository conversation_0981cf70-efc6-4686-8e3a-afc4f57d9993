'use client'

import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import {
  useBestSellingItems,
} from '@/hooks/use-products'
import { useAuth } from '@/contexts/auth-context'
import { cn } from '@/lib/utils'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { useState } from 'react'

interface BestSellingItem {
  id: string
  name: string
  sku: string
  quantitySold: number
  isVariant: boolean
}

interface BestSellingItemsProps {
  className?: string
}

export function BestSellingItems({ className }: BestSellingItemsProps) {
  const { organizationId } = useAuth()
  const [dateRange, setDateRange] = useState<'30d' | '90d' | '1y' | 'all'>('30d')
  
  // Wrapper function to handle type conversion
  const handleDateRangeChange = (value: string) => {
    setDateRange(value as '30d' | '90d' | '1y' | 'all')
  }
  
  const {
    data: bestSellingItems = [],
    isLoading,
    error,
  } = useBestSellingItems(organizationId || undefined, 5, dateRange)

  // Map date range values to display labels
  const dateRangeLabels = {
    '30d': '30 days',
    '90d': '90 days',
    '1y': '1 year',
    'all': 'All time'
  }

  if (isLoading) {
    return (
      <Card className={cn('bg-white border border-gray-200 rounded-lg shadow-sm', className)}>
        <CardHeader className="p-4 border-b border-gray-200 space-y-0.5">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Top 5 products by quantity sold ({dateRangeLabels[dateRange]})</CardTitle>
            <Select value={dateRange} onValueChange={handleDateRangeChange}>
              <SelectTrigger className="w-[100px] h-7 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="30d" className="text-xs">30 days</SelectItem>
                <SelectItem value="90d" className="text-xs">90 days</SelectItem>
                <SelectItem value="1y" className="text-xs">1 year</SelectItem>
                <SelectItem value="all" className="text-xs">All time</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent className="p-4">
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center justify-between">
                <div className="space-y-1">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-3 w-16" />
                </div>
                <Skeleton className="h-4 w-8" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={cn('bg-white border border-gray-200 rounded-lg shadow-sm', className)}>
        <CardHeader className="p-4 border-b border-gray-200 space-y-0.5">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Top 5 products by quantity sold ({dateRangeLabels[dateRange]})</CardTitle>
            <Select value={dateRange} onValueChange={handleDateRangeChange}>
              <SelectTrigger className="w-[100px] h-7 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="30d" className="text-xs">30 days</SelectItem>
                <SelectItem value="90d" className="text-xs">90 days</SelectItem>
                <SelectItem value="1y" className="text-xs">1 year</SelectItem>
                <SelectItem value="all" className="text-xs">All time</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent className="p-4">
          <div className="text-center text-xs text-gray-500 py-4">
            Error loading data
          </div>
        </CardContent>
      </Card>
    )
  }

  // Calculate the maximum quantity for progress bar scaling
  const maxQuantity = bestSellingItems[0]?.quantitySold || 0

  return (
    <Card className={cn('bg-white border border-gray-200 rounded-lg shadow-sm', className)}>
      <CardHeader className="p-4 border-b border-gray-200 space-y-0.5">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Top 5 products by quantity sold ({dateRangeLabels[dateRange]})</CardTitle>
          <Select value={dateRange} onValueChange={handleDateRangeChange}>
            <SelectTrigger className="w-[100px] h-7 text-xs">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="30d" className="text-xs">30 days</SelectItem>
              <SelectItem value="90d" className="text-xs">90 days</SelectItem>
              <SelectItem value="1y" className="text-xs">1 year</SelectItem>
              <SelectItem value="all" className="text-xs">All time</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent className="p-4">
        {bestSellingItems.length === 0 ? (
          <div className="text-center text-xs text-gray-500 py-4">
            No sales data available
          </div>
        ) : (
          <div className="space-y-4">
            {bestSellingItems.map((item: BestSellingItem, index: number) => (
              <div key={item.id}>
                <div className="flex justify-between items-center mb-1">
                  <span className={`text-xs ${index === 0 ? 'text-slate-800 font-medium' : 'text-slate-600'}`}>
                    {item.name}
                  </span>
                  <span className="text-xs font-semibold text-slate-500">
                    {item.quantitySold} units
                  </span>
                </div>
                <div className="w-full bg-slate-200 rounded-full h-1.5">
                  <div 
                    className="bg-blue-500 h-1.5 rounded-full" 
                    style={{ width: maxQuantity > 0 ? `${(item.quantitySold / maxQuantity) * 100}%` : '0%' }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}