import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase'
import { useAuth } from '@/contexts/auth-context'

// Common currencies with their details
export const SUPPORTED_CURRENCIES = {
  USD: { symbol: '$', name: 'US Dollar', locale: 'en-US' },
  EUR: { symbol: '€', name: 'Euro', locale: 'en-EU' },
  GBP: { symbol: '£', name: 'British Pound', locale: 'en-GB' },
  CAD: { symbol: 'C$', name: 'Canadian Dollar', locale: 'en-CA' },
  AUD: { symbol: 'A$', name: 'Australian Dollar', locale: 'en-AU' },
  JPY: { symbol: '¥', name: 'Japanese Yen', locale: 'ja-JP' },
  CHF: { symbol: 'CHF', name: 'Swiss Franc', locale: 'de-CH' },
  CNY: { symbol: '¥', name: 'Chinese Yuan', locale: 'zh-CN' },
  INR: { symbol: '₹', name: 'Indian Rupee', locale: 'en-IN' },
  BDT: { symbol: '৳', name: 'Bangladeshi Taka', locale: 'bn-BD' },
  SGD: { symbol: 'S$', name: 'Singapore Dollar', locale: 'en-SG' },
  PKR: { symbol: 'Rs', name: 'Pakistani Rupee', locale: 'en-PK' },
  LKR: { symbol: 'Rs', name: 'Sri Lankan Rupee', locale: 'en-LK' },
  MYR: { symbol: 'RM', name: 'Malaysian Ringgit', locale: 'ms-MY' },
  THB: { symbol: '฿', name: 'Thai Baht', locale: 'th-TH' },
}

export type CurrencyCode = keyof typeof SUPPORTED_CURRENCIES

export interface CurrencyInfo {
  symbol: string
  name: string
  locale: string
}

// Re-export the hook from the context
export { useCurrency } from '@/contexts/currency-context'

// Standalone currency formatter for non-hook contexts
export const formatCurrencyStandalone = (
  amount: number, 
  currency: CurrencyCode = 'USD'
) => {
  const currencyInfo = SUPPORTED_CURRENCIES[currency]
  return new Intl.NumberFormat(currencyInfo.locale, {
    style: 'currency',
    currency: currency,
  }).format(amount)
}
