// Supabase Edge Function to send invitation emails
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from '@supabase/supabase-js';

console.log("Send invitation function started");

serve(async (req: Request) => {
  try {
    // Add CORS headers
    if (req.method === 'OPTIONS') {
      return new Response(null, {
        status: 204,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type',
          'Access-Control-Max-Age': '86400',
        },
      });
    }

    // Get the request body
    const { invitationId } = await req.json();
    
    if (!invitationId) {
      return new Response(JSON.stringify({ error: 'Missing invitationId' }), {
        status: 400,
        headers: { 
          "Content-Type": "application/json",
          'Access-Control-Allow-Origin': '*',
        }
      });
    }

    // Create a Supabase client with the service role key for full access
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Get invitation details
    const { data: invitation, error: invitationError } = await supabase
      .from('invitations')
      .select(`
        id,
        email,
        role,
        organization_id,
        created_at,
        organizations (name)
      `)
      .eq('id', invitationId)
      .single();

    if (invitationError) {
      console.error('Error fetching invitation:', invitationError);
      return new Response(JSON.stringify({ error: invitationError.message }), {
        status: 500,
        headers: { 
          "Content-Type": "application/json",
          'Access-Control-Allow-Origin': '*',
        }
      });
    }

    if (!invitation) {
      return new Response(JSON.stringify({ error: 'Invitation not found' }), {
        status: 404,
        headers: { 
          "Content-Type": "application/json",
          'Access-Control-Allow-Origin': '*',
        }
      });
    }

    // Get organization name
    const organizationName = invitation.organizations?.name || 'Your Organization';

    // In a real implementation, you would send an actual email here
    // For example, using SendGrid, Mailgun, or another email service
    // This is where you would integrate with your email provider
    
    console.log(`Sending invitation email to ${invitation.email}`);
    console.log(`Organization: ${organizationName}`);
    console.log(`Role: ${invitation.role}`);
    console.log(`Invitation ID: ${invitation.id}`);
    
    // For demonstration purposes, we're just logging the email content
    // In a production environment, you would use an email service provider
    
    const emailContent = `
      Subject: You've been invited to join ${organizationName}
      
      Hello,
      
      You've been invited to join ${organizationName} as a ${invitation.role}.
      
      To accept this invitation, please click on the following link:
      ${Deno.env.get('SITE_URL') || 'http://localhost:3000'}/accept-invitation?token=${invitation.id}
      
      This invitation will expire in 7 days.
      
      If you did not expect this invitation, you can safely ignore this email.
      
      Best regards,
      The ${organizationName} Team
    `;
    
    console.log('Email content:', emailContent);

    return new Response(JSON.stringify({ 
      message: 'Invitation email sent successfully',
      invitationId: invitation.id
    }), {
      status: 200,
      headers: { 
        "Content-Type": "application/json",
        'Access-Control-Allow-Origin': '*',
      }
    });
  } catch (error) {
    console.error('Error in send invitation function:', error);
    return new Response(JSON.stringify({ error: (error as Error).message }), {
      status: 500,
      headers: { 
        "Content-Type": "application/json",
        'Access-Control-Allow-Origin': '*',
      }
    });
  }
});