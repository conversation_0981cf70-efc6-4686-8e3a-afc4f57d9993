'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Check } from 'lucide-react'

export function ThemeSettings({ 
  onThemeChange,
  currentTheme
}: { 
  onThemeChange: (theme: 'light' | 'dark') => void,
  currentTheme: 'light' | 'dark'
}) {
  const [mounted, setMounted] = useState(false)

  // Ensure component is mounted before rendering to avoid hydration issues
  useEffect(() => {
    setMounted(true)
  }, [])

  const handleThemeChange = (newTheme: 'light' | 'dark') => {
    onThemeChange(newTheme)
  }

  if (!mounted) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Appearance</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="h-20 bg-gray-200 rounded-lg animate-pulse"></div>
            <div className="h-20 bg-gray-200 rounded-lg animate-pulse"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Appearance</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <p className="text-xs text-gray-500">
            Select your preferred theme
          </p>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {/* Light Theme Option */}
            <button
              onClick={() => handleThemeChange('light')}
              className={`
                relative rounded-lg border-2 p-4 text-left transition-all
                ${currentTheme === 'light' 
                  ? 'border-blue-500 bg-blue-50' 
                  : 'border-gray-200 hover:border-gray-300'}
              `}
            >
              {currentTheme === 'light' && (
                <div className="absolute top-2 right-2 w-5 h-5 rounded-full bg-blue-500 flex items-center justify-center">
                  <Check className="w-3 h-3 text-white" />
                </div>
              )}
              
              <div className="flex items-center gap-3">
                <div className="flex flex-col gap-1">
                  <div className="w-10 h-6 rounded bg-white border border-gray-200 flex items-center justify-center">
                    <div className="w-6 h-1 bg-gray-200 rounded"></div>
                  </div>
                  <div className="w-10 h-1 bg-gray-100 rounded"></div>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-gray-900">Light</h3>
                  <p className="text-xs text-gray-500">Clean and bright</p>
                </div>
              </div>
            </button>

            {/* Dark Theme Option */}
            <button
              onClick={() => handleThemeChange('dark')}
              className={`
                relative rounded-lg border-2 p-4 text-left transition-all
                ${currentTheme === 'dark' 
                  ? 'border-blue-500 bg-blue-950' 
                  : 'border-gray-700 hover:border-gray-600'}
              `}
            >
              {currentTheme === 'dark' && (
                <div className="absolute top-2 right-2 w-5 h-5 rounded-full bg-blue-500 flex items-center justify-center">
                  <Check className="w-3 h-3 text-white" />
                </div>
              )}
              
              <div className="flex items-center gap-3">
                <div className="flex flex-col gap-1">
                  <div className="w-10 h-6 rounded bg-gray-800 border border-gray-700 flex items-center justify-center">
                    <div className="w-6 h-1 bg-gray-600 rounded"></div>
                  </div>
                  <div className="w-10 h-1 bg-gray-800 rounded"></div>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-white">Dark</h3>
                  <p className="text-xs text-gray-400">Easy on the eyes</p>
                </div>
              </div>
            </button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}