const supabaseConfig = require('../config/supabase');

class ExpensesController {
  async addExpense(expenseData, userToken) {
    try {
      console.log('Expenses: Adding expense:', {
        amount: expenseData.amount,
        description: expenseData.description,
        category: expenseData.category,
        vendor: expenseData.vendor,
        payment_method: expenseData.payment_method,
        expense_date: expenseData.expense_date,
        userToken: userToken ? 'Provided' : 'Missing'
      });
      
      // Extract user ID from JWT token
      let userId = null;
      if (userToken) {
        try {
          // Decode JWT token to get user ID (simple base64 decode of payload)
          const payload = JSON.parse(Buffer.from(userToken.split('.')[1], 'base64').toString());
          userId = payload.sub; // 'sub' is the standard JWT claim for user ID
          console.log('Expenses: Extracted user ID from token:', userId);
        } catch (tokenError) {
          console.error('Expenses: Failed to extract user ID from token:', tokenError.message);
          throw new Error('Invalid authentication token');
        }
      }
      
      if (!userId) {
        throw new Error('User ID is required for expense creation');
      }
      
      // Prepare expense payload with new fields
      const expensePayload = {
        user_id: userId, // Explicitly include user_id for RLS policy
        amount: parseFloat(expenseData.amount),
        description: expenseData.description || null,
        category: expenseData.category || 'General',
        vendor: expenseData.vendor || null, // New field
        payment_method: expenseData.payment_method || null, // New field
        expense_date: expenseData.expense_date || new Date().toISOString().split('T')[0] // Format as YYYY-MM-DD
      };
      
      console.log('Expenses: Sending payload to Supabase:', expensePayload);
      
      const result = await supabaseConfig.makeRequest('expenses', {
        method: 'POST',
        userToken: userToken,
        body: JSON.stringify(expensePayload)
      });
      
      console.log('Expenses: Successfully created expense:', result);
      return result[0];
    } catch (error) {
      console.error('Error adding expense to Supabase:', error);
      console.error('Error details:', {
        message: error.message,
        expenseData: expenseData,
        userToken: userToken ? 'Provided' : 'Missing'
      });
      throw error;
    }
  }

  async getExpenses(userToken) {
    try {
      console.log('Expenses: Getting expenses for user token:', userToken ? userToken.substring(0, 20) + '...' : 'null');
      
      const result = await supabaseConfig.makeRequest('expenses?order=created_at.desc', {
        userToken: userToken
      });
      
      console.log('Expenses: Retrieved', result.length, 'expenses from Supabase');
      return result;
    } catch (error) {
      console.error('Error fetching expenses from Supabase:', error);
      console.error('Error details:', {
        message: error.message,
        userToken: userToken ? 'Provided' : 'Missing'
      });
      throw error;
    }
  }

  async deleteExpense(expenseId, userToken) {
    try {
      const result = await supabaseConfig.makeRequest(`expenses?id=eq.${expenseId}`, {
        method: 'DELETE',
        userToken: userToken
      });
      return result;
    } catch (error) {
      console.error('Error deleting expense from Supabase:', error);
      throw error;
    }
  }

  async updateExpense(expenseId, updateData, userToken) {
    try {
      const result = await supabaseConfig.makeRequest(`expenses?id=eq.${expenseId}`, {
        method: 'PATCH',
        userToken: userToken,
        body: JSON.stringify(updateData)
      });
      return result[0];
    } catch (error) {
      console.error('Error updating expense in Supabase:', error);
      throw error;
    }
  }
}

module.exports = new ExpensesController();