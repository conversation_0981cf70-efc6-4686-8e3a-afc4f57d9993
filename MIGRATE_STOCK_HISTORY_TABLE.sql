-- =============================================
-- MIGRATE STOCK_HISTORY TABLE TO ORGANIZATION MODEL
-- =============================================

-- Add organization_id column to stock_history table
ALTER TABLE stock_history 
ADD COLUMN IF NOT EXISTS organization_id uuid REFERENCES organizations(id) ON DELETE CASCADE;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_stock_history_organization_id ON stock_history(organization_id);

-- Backfill organization_id from user_id
UPDATE stock_history 
SET organization_id = (
  SELECT organization_id 
  FROM organization_members 
  WHERE user_id = stock_history.user_id 
  LIMIT 1
)
WHERE organization_id IS NULL;

-- Make organization_id NOT NULL
ALTER TABLE stock_history 
ALTER COLUMN organization_id SET NOT NULL;

-- Remove user_id column
ALTER TABLE stock_history 
DROP COLUMN IF EXISTS user_id;

-- Update RLS policies for organization-based access
DROP POLICY IF EXISTS "Users can view their stock history" ON stock_history;
DROP POLICY IF EXISTS "Users can insert stock history" ON stock_history;
DROP POLICY IF EXISTS "Users can update their stock history" ON stock_history;
DROP POLICY IF EXISTS "Users can delete their stock history" ON stock_history;

-- Create new organization-based policies
CREATE POLICY "Organization members can view stock history" ON stock_history
  FOR SELECT USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can insert stock history" ON stock_history
  FOR INSERT WITH CHECK (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can update stock history" ON stock_history
  FOR UPDATE USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can delete stock history" ON stock_history
  FOR DELETE USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

-- Grant necessary permissions
GRANT ALL ON stock_history TO authenticated;