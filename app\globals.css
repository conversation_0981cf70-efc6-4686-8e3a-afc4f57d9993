@tailwind base;
@tailwind components;
@tailwind utilities;

/* Hide spinner controls on number inputs */
input[type=number]::-webkit-outer-spin-button,
input[type=number]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type=number] {
  -moz-appearance: textfield;
}

/* ==========================================
   SIDEBAR COLOR SYSTEM
   ==========================================

   This section centralizes all sidebar color definitions.
   Modify these CSS variables to change sidebar colors throughout the application.
   
   Color Variables:
   --sidebar-bg: Background color of the entire sidebar
   --sidebar-text: Default text color for sidebar items
   --sidebar-text-hover: Text color when hovering over sidebar items
   --sidebar-hover-bg: Background color when hovering over sidebar items
   --sidebar-active-bg: Background color for active parent menu items
   --sidebar-active-text: Text color for active parent menu items
   --sidebar-submenu-active-bg: Background color for active submenu items
   --sidebar-submenu-active-text: Text color for active submenu items
   --sidebar-border: Border color for sidebar elements
   --sidebar-icon-active: Color for icons in active menu items
*/

:root {
  /* Light mode sidebar colors */
  --sidebar-bg: 0 0% 100%;
  --sidebar-text: 0 0% 30%;
  --sidebar-text-hover: 0 0% 15%;
  --sidebar-hover-bg: 0 0% 95%;
  --sidebar-active-bg: 216 83% 43%;
  --sidebar-active-text: 0 0% 100%;
  --sidebar-submenu-active-bg: 216 83% 65%;
  --sidebar-submenu-active-text: 0 0% 100%;
  --sidebar-border: 0 0% 85%;
  --sidebar-icon-active: 0 0% 100%;
}

.dark {
  /* Dark mode sidebar colors */
  --sidebar-bg: 0 0% 3.9%;
  --sidebar-text: 0 0% 70%;
  --sidebar-text-hover: 0 0% 95%;
  --sidebar-hover-bg: 0 0% 15%;
  --sidebar-active-bg: 216 83% 43%;
  --sidebar-active-text: 0 0% 100%;
  --sidebar-submenu-active-bg: 216 83% 43%;
  --sidebar-submenu-active-text: 0 0% 100%;
  --sidebar-border: 0 0% 15%;
  --sidebar-icon-active: 216 83% 43%;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
 
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
 
    --primary: 216 83% 43%;
    --primary-foreground: 0 0% 98%;
 
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
 
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
 
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
 
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
 
    --radius: 0.5rem;
 
    --chart-1: 12 76% 61%;
 
    --chart-2: 173 58% 39%;
 
    --chart-3: 197 37% 24%;
 
    --chart-4: 43 74% 66%;
 
    --chart-5: 27 87% 67%;
    
    --sidebar-active: 184 40% 15%;
  }
 
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
 
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
 
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
 
    --primary: 216 83% 43%;
    --primary-foreground: 0 0% 98%;
 
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
 
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
 
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
 
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
 
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    
    --sidebar-active: 216 83% 43%;
  }
}

/* Add custom sidebar classes */
@layer base {
  .bg-sidebar-submenu-active {
    background-color: hsl(var(--sidebar-submenu-active-bg));
  }
  
  .text-sidebar-submenu-text {
    color: hsl(var(--sidebar-submenu-active-text));
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Typography System */
@layer utilities {
  /* Headings */
  .heading-1 {
    @apply text-3xl font-bold leading-tight;
  }
  
  .heading-2 {
    @apply text-2xl font-semibold leading-tight;
  }
  
  .heading-3 {
    @apply text-xl font-semibold leading-snug;
  }
  
  .heading-4 {
    @apply text-lg font-semibold leading-normal;
  }
  
  /* Body Text */
  .body-large {
    @apply text-lg leading-relaxed;
  }
  
  .body {
    @apply text-base leading-relaxed;
  }
  
  .body-small {
    @apply text-sm leading-relaxed;
  }
  
  .caption {
    @apply text-xs leading-relaxed text-muted-foreground;
  }
  
  /* Font Weights */
  .font-primary {
    @apply font-medium;
  }
  
  .font-secondary {
    @apply font-normal;
  }
  
  /* Special Text Styles */
  .text-primary {
    @apply text-foreground;
  }
  
  .text-secondary {
    @apply text-muted-foreground;
  }
  
  .text-accent {
    @apply text-primary;
  }
}

/* Component Styles */
@layer components {
  /* Card Styles */
  .card-base {
    @apply rounded-lg border bg-card text-card-foreground shadow-sm;
  }
  
  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }
  
  .card-title {
    @apply text-lg font-semibold leading-none tracking-tight;
  }
  
  .card-description {
    @apply text-sm text-muted-foreground;
  }
  
  .card-content {
    @apply p-6 pt-0;
  }
  
  .card-footer {
    @apply flex items-center p-6 pt-0;
  }
  
  /* Button Styles */
  .btn-primary {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2;
  }
  
  .btn-secondary {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-secondary text-secondary-foreground hover:bg-secondary/80 h-10 px-4 py-2;
  }
  
  .btn-ghost {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2;
  }
  
  /* Tab Styles */
  .tab-base {
    @apply flex items-center space-x-2 py-3 text-sm font-medium transition-all duration-200 border-b-2;
  }
  
  .tab-active {
    @apply border-primary text-foreground shadow-sm;
  }
  
  .tab-inactive {
    @apply border-transparent text-muted-foreground hover:text-foreground;
  }
  
  /* Mobile Tab Styles */
  .mobile-tab-base {
    @apply flex flex-col h-auto py-2 px-3 text-xs whitespace-nowrap min-w-[70px] flex-shrink-0 rounded-lg snap-center touch-manipulation select-none transition-all duration-200;
  }
  
  .mobile-tab-active {
    @apply bg-primary text-primary-foreground shadow-sm;
  }
  
  .mobile-tab-inactive {
    @apply hover:bg-accent hover:text-accent-foreground active:bg-accent/80;
  }
}

/* ==========================================
   NOTIFICATION ANIMATIONS
   ==========================================

   Custom animations for notification components
   to enhance user experience with smooth transitions
   and micro-interactions.
*/

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

/* Utility classes for notification animations */
.animate-fade-in-up {
  animation: fadeInUp 0.3s ease-out forwards;
}

.animate-fade-in-down {
  animation: fadeInDown 0.3s ease-out forwards;
}

.animate-slide-in-right {
  animation: slideInRight 0.3s ease-out forwards;
}

.animate-notification-pulse {
  animation: pulse 2s infinite;
}

.animate-notification-bounce {
  animation: bounce 0.6s ease-out;
}

/* Staggered animation delays */
.animate-delay-100 {
  animation-delay: 100ms;
}

.animate-delay-200 {
  animation-delay: 200ms;
}

.animate-delay-300 {
  animation-delay: 300ms;
}

.animate-delay-400 {
  animation-delay: 400ms;
}

.animate-delay-500 {
  animation-delay: 500ms;
}

/* Hover effects for notification cards */
.notification-card-hover {
  transition: all 0.2s ease-out;
}

.notification-card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Loading skeleton animations */
.skeleton-pulse {
  animation: pulse 1.5s ease-in-out infinite;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}