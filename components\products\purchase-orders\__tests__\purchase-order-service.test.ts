import { 
  createPurchaseOrder,
  getPurchaseOrders,
  getPurchaseOrderById,
  updatePurchaseOrderItemReceived,
  deletePurchaseOrder,
  updatePurchaseOrderStatus
} from '../purchase-order-service'
import { PurchaseOrderFormData } from '../types'

// Mock Supabase client
jest.mock('@/lib/supabase', () => ({
  getSupabaseClient: () => ({
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    ilike: jest.fn().mockReturnThis(),
    or: jest.fn().mockReturnThis(),
    gte: jest.fn().mockReturnThis(),
    lte: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    single: jest.fn().mockReturnThis(),
  })
}))

describe('Purchase Order Service', () => {
  const organizationId = 'test-organization-id'
  const mockFormData: PurchaseOrderFormData = {
    supplier: 'Test Supplier',
    issue_date: new Date(),
    expected_arrival_date: new Date(),
    notes: 'Test notes',
    reference_number: 'REF-001',
    items: [
      {
        productId: 'product-1',
        variantId: null,
        productName: 'Test Product',
        sku: 'TEST-001',
        quantity: 10,
        unitCost: 5.99,
        subtotal: 59.90
      }
    ]
  }

  describe('createPurchaseOrder', () => {
    it('should create a purchase order with valid data', async () => {
      const result = await createPurchaseOrder(organizationId, mockFormData)
      expect(result).toBeDefined()
    })

    it('should throw an error if supplier is empty', async () => {
      const invalidFormData = { ...mockFormData, supplier: '' }
      await expect(createPurchaseOrder(organizationId, invalidFormData))
        .rejects
        .toThrow('Supplier is required')
    })

    it('should throw an error if no items are provided', async () => {
      const invalidFormData = { ...mockFormData, items: [] }
      await expect(createPurchaseOrder(organizationId, invalidFormData))
        .rejects
        .toThrow('At least one item is required')
    })
  })

  describe('updatePurchaseOrderItemReceived', () => {
    it('should update received quantity with valid data', async () => {
      const result = await updatePurchaseOrderItemReceived('item-1', organizationId, 5)
      expect(result).toBeDefined()
    })

    it('should throw an error if quantity is negative', async () => {
      await expect(updatePurchaseOrderItemReceived('item-1', organizationId, -1))
        .rejects
        .toThrow('Quantity received cannot be negative')
    })
  })

  describe('updatePurchaseOrderStatus', () => {
    it('should update purchase order status', async () => {
      const result = await updatePurchaseOrderStatus('po-1', organizationId, 'received')
      expect(result).toBeDefined()
      expect(result.status).toBe('received')
    })
  })
})