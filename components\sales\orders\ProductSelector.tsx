'use client'

import * as React from 'react'
import { useState } from 'react'
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  DialogDescription,
  DialogTitle,
  DialogTrigger 
} from '@/components/ui/dialog'
import { Search, ChevronRight, ChevronDown } from 'lucide-react'
import type { ProductRow, ProductVariantRow } from '@/lib/supabase'

// Updated interfaces to match organization-based model
interface ProductRowOrg {
  id: string
  organization_id: string
  category_id: string | null
  name: string
  description: string | null
  brand: string | null
  supplier: string | null
  base_sku: string | null
  has_variants: boolean
  track_inventory: boolean
  is_active: boolean
  base_cost: number | null
  packaging_cost: number | null
  price: number | null
  size: string | null
  color: string | null
  stock_quantity: number | null
  low_stock_threshold: number | null
  barcode: string | null
  image_url: string | null
  sale_price: number | null
  sale_start_date: string | null
  sale_end_date: string | null
  batch_reference: string | null
  purchase_date: string | null
  notes: string | null
  total_cost: number | null
  effective_price: number | null
  is_on_sale: boolean | null
  profit_amount: number | null
  profit_margin: number | null
  created_at: string
  updated_at: string
}

interface ProductVariantRowOrg {
  id: string
  product_id: string
  organization_id: string
  sku: string
  variant_name: string | null
  size: string | null
  color: string | null
  material: string | null
  style: string | null
  weight: number | null
  dimensions: any | null
  cost_adjustment: number | null
  base_cost: number | null
  price: number | null
  sale_price: number | null
  sale_start_date: string | null
  sale_end_date: string | null
  stock_quantity: number | null
  low_stock_threshold: number | null
  reserved_quantity: number | null
  is_active: boolean
  barcode: string | null
  image_urls: string[] | null
  effective_price: number | null
  is_on_sale: boolean | null
  available_quantity: number | null
  created_at: string
  updated_at: string
}

interface ProductSelectorProps {
  products: (ProductRowOrg & { variants?: ProductVariantRowOrg[] })[]
  onSelect: (product: ProductRowOrg, variant?: ProductVariantRowOrg) => void
  currentValue?: {
    productName: string
    sku: string
  }
  isLoading?: boolean
}

export function ProductSelector({ products, onSelect, currentValue, isLoading }: ProductSelectorProps) {
  const [open, setOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [expandedProducts, setExpandedProducts] = useState<Record<string, boolean>>({})

  // Filter products based on search term
  const filteredProducts = React.useMemo(() => {
    if (!products) return []
    if (!searchTerm) return products
    
    return products.filter(product => 
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (product.base_sku && product.base_sku.toLowerCase().includes(searchTerm.toLowerCase()))
    )
  }, [products, searchTerm])

  // Toggle product expansion
  const toggleProductExpansion = (productId: string) => {
    setExpandedProducts(prev => ({
      ...prev,
      [productId]: !prev[productId]
    }))
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <div 
          className="flex items-center justify-between w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 cursor-pointer"
        >
          {currentValue?.productName ? (
            <div className="flex flex-col">
              <div className="font-medium text-gray-900 dark:text-gray-100">{currentValue.productName}</div>
              <div className="text-xs text-slate-500 dark:text-gray-400">{currentValue.sku || 'No SKU'}</div>
            </div>
          ) : (
            <div className="flex items-center text-muted-foreground">
              <Search className="h-4 w-4 mr-2" />
              <span>
                {isLoading 
                  ? 'Loading products...' 
                  : products && products.length > 0 
                    ? 'Search products...' 
                    : 'No products available'
                }
              </span>
            </div>
          )}
        </div>
      </DialogTrigger>
      <DialogContent className="max-w-md p-0 rounded-lg overflow-hidden">
        <DialogTitle className="sr-only">Search Products</DialogTitle>
        <DialogDescription className="sr-only">
          Search and select products or product variants to add to your order
        </DialogDescription>
        <div className="p-4">
          <div className="relative mb-2">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <input
              type="text"
              placeholder="Search products..."
              className="flex h-9 w-full rounded-md border border-input bg-background px-3 py-2 pl-8 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          {isLoading ? (
            <div className="py-2 text-muted-foreground">Loading products...</div>
          ) : filteredProducts && filteredProducts.length > 0 ? (
            <div className="space-y-1 max-h-60 overflow-y-auto">
              {filteredProducts.map((product) => (
                <div key={product.id}>
                  <div
                    className={`p-2 cursor-pointer hover:bg-accent hover:text-accent-foreground rounded flex justify-between items-center ${
                      product.variants && product.variants.length > 0 ? '' : ''
                    }`}
                    onClick={
                      product.variants && product.variants.length > 0
                        ? () => toggleProductExpansion(product.id)
                        : () => {
                            onSelect(product)
                            setOpen(false)
                          }
                    }
                  >
                    <div>
                      <div className="text-sm font-medium">{product.name}</div>
                      <div className="text-xs text-gray-500">{product.base_sku || 'No SKU'}</div>
                    </div>
                    {product.variants && product.variants.length > 0 && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          toggleProductExpansion(product.id)
                        }}
                        className="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700"
                      >
                        {expandedProducts[product.id] ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                      </button>
                    )}
                  </div>
                  {product.variants && product.variants.length > 0 && expandedProducts[product.id] && (
                    <div className="pl-4">
                      {product.variants.map((variant) => (
                        <div
                          key={variant.id}
                          onClick={() => {
                            onSelect(product, variant)
                            setOpen(false)
                          }}
                          className="p-2 pl-6 cursor-pointer hover:bg-accent hover:text-accent-foreground rounded"
                        >
                          <div className="text-sm font-medium">
                            {product.name} - {variant.variant_name}
                            {variant.size && ` (${variant.size})`}
                          </div>
                          <div className="text-xs text-gray-500">
                            {variant.sku || product.base_sku || 'No SKU'}
                            {variant.color && ` | Color: ${variant.color}`}
                            {variant.material && ` | Material: ${variant.material}`}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="py-2 text-muted-foreground text-center">
              {searchTerm ? 'No products found matching your search' : 'No products available'}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}