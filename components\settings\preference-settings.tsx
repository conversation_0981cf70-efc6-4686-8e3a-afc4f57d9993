'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { useTheme } from '@/contexts/theme-context'
import { useToast } from '@/components/ui/use-toast'
import { ThemeSettings } from '@/components/settings/theme-settings'
import { DateFormatSettings } from '@/components/settings/date-format-settings'
import { NotificationSettings } from '@/components/settings/notification-settings'

export function PreferenceSettings() {
  const { theme, setTheme } = useTheme()
  const { toast } = useToast()
  const [mounted, setMounted] = useState(false)
  const [unsavedTheme, setUnsavedTheme] = useState<'light' | 'dark' | null>(null)
  const [isSaving, setIsSaving] = useState(false)
  const [hasUnsavedNotificationChanges, setHasUnsavedNotificationChanges] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  useEffect(() => {
    const handleNotificationChanges = () => {
      setHasUnsavedNotificationChanges(true)
    }

    const handleSaveNotificationPreferences = () => {
      // Trigger save in NotificationSettings component
      window.dispatchEvent(new CustomEvent('saveNotificationPreferences'))
    }

    window.addEventListener('notificationPreferencesChanged', handleNotificationChanges)
    window.addEventListener('saveNotificationPreferences', handleSaveNotificationPreferences)
    
    return () => {
      window.removeEventListener('notificationPreferencesChanged', handleNotificationChanges)
      window.removeEventListener('saveNotificationPreferences', handleSaveNotificationPreferences)
    }
  }, [])

  const handleThemeChange = (newTheme: 'light' | 'dark') => {
    setUnsavedTheme(newTheme)
  }

  const hasUnsavedChanges = () => {
    return unsavedTheme !== null && unsavedTheme !== theme
  }

  const handleSave = async () => {
    setIsSaving(true)
    
    if (unsavedTheme !== null && unsavedTheme !== theme) {
      setTheme(unsavedTheme)
    }
    
    // Save notification preferences by triggering save in the NotificationSettings component
    // This is a simplified approach - in a real app, you might want to use a more sophisticated state management
    window.dispatchEvent(new CustomEvent('saveNotificationPreferences'))
    
    // Reset unsaved states
    setUnsavedTheme(null)
    setHasUnsavedNotificationChanges(false)
    
    // Show success message
    toast({
      title: "Preferences Saved",
      description: "Your preferences have been saved successfully.",
    })
    
    setIsSaving(false)
  }

  if (!mounted) {
    return (
      <div className="space-y-4">
        <div className="bg-white rounded-lg border border-gray-200">
          <div className="px-4 py-3 border-b border-gray-200">
            <h3 className="text-base font-medium text-gray-900">Theme Settings</h3>
          </div>
          <div className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-sm font-medium text-gray-900">Theme</h3>
                <p className="text-xs text-gray-500">Select your preferred theme</p>
              </div>
              <div className="w-24 h-8 bg-gray-200 rounded-md animate-pulse"></div>
            </div>
          </div>
        </div>
        <DateFormatSettings />
        <div className="flex justify-end">
          <Button 
            variant="outline"
            size="sm"
            disabled
          >
            Save Preferences
          </Button>
        </div>
      </div>
    )
  }

  const hasAnyUnsavedChanges = () => {
    return hasUnsavedChanges() || hasUnsavedNotificationChanges
  }

  return (
    <div className="space-y-4">
      <ThemeSettings 
        onThemeChange={handleThemeChange} 
        currentTheme={unsavedTheme || theme} 
      />
      <DateFormatSettings />
      <NotificationSettings />
      <div className="flex justify-end">
        <Button 
          variant={hasAnyUnsavedChanges() ? "default" : "outline"}
          size="sm"
          onClick={handleSave}
          disabled={!hasAnyUnsavedChanges() || isSaving}
        >
          {isSaving ? 'Saving...' : 'Save Preferences'}
        </Button>
      </div>
    </div>
  )
}