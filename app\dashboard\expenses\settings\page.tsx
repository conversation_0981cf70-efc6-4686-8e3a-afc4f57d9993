'use client'

import { useState, useEffect } from 'react'
import { SettingsTab } from '@/components/expenses/settings-tab'
import { usePersistedState } from '@/lib/use-persisted-state'
import { type ExpenseFilters, INITIAL_FILTERS } from '@/components/expenses/expense-table-header'

export default function ExpensesSettingsPage() {
  const [isMobile, setIsMobile] = useState(false)
  
  // Shared state for filters - with persistence
  const [globalFilters, setGlobalFilters] = usePersistedState<ExpenseFilters>(
    'expenses-filters', 
    INITIAL_FILTERS
  )
  const [filteredExpenseIds, setFilteredExpenseIds] = useState<string[]>([])

  // Mobile detection
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  return (
    <div className="flex flex-col gap-4">
      <SettingsTab 
        isMobile={isMobile} 
        contextualFilters={globalFilters}
        filteredExpenseIds={filteredExpenseIds}
      />
    </div>
  )
}