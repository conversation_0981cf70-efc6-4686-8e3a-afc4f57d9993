import React, { useState } from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Plus, X, RefreshCw } from 'lucide-react'
import { useProductForm } from '../../context/ProductFormContext'
import { SectionHeader } from '../shared/SectionHeader'
import { FormField } from '../shared/FormField'
import { useCurrency } from '@/lib/currency'
import { ProductVariant, CurrentVariant } from '../../types'

interface VariantAttribute {
  name: string
  value: string
}

interface Variant {
  id: string
  attributes: VariantAttribute[]
  sku: string
  price: number | null
  base_cost: number | null
  packaging_cost: number | null
  quantity: number
  low_stock_threshold: number
}

// Generate SKU for variant
function generateVariantSKU(baseSKU: string, attributes: VariantAttribute[]): string {
  const attributeCode = attributes
    .map(attr => attr.value.substring(0, 2).toUpperCase())
    .join('')
  
  return `${baseSKU}-${attributeCode}`
}

export function VariantManagementSection() {
  const { formData, updateFormData, savedVariants, setSavedVariants, currentVariant, setCurrentVariant } = useProductForm()
  const { formatCurrency } = useCurrency()
  
  const [newAttribute, setNewAttribute] = useState({ name: '', value: '' })
  
  // Add attribute to current variant
  const addAttribute = () => {
    if (newAttribute.name && newAttribute.value) {
      setCurrentVariant((prev: CurrentVariant) => ({
        ...prev,
        attributes: [...prev.attributes, { ...newAttribute }],
        sku: generateVariantSKU(formData.base_sku, [...prev.attributes, { ...newAttribute }])
      }))
      setNewAttribute({ name: '', value: '' })
    }
  }
  
  // Remove attribute from current variant
  const removeAttribute = (index: number) => {
    setCurrentVariant((prev: CurrentVariant) => {
      const newAttributes = prev.attributes.filter((_: VariantAttribute, i: number) => i !== index)
      return {
        ...prev,
        attributes: newAttributes,
        sku: generateVariantSKU(formData.base_sku, newAttributes)
      }
    })
  }
  
  // Save current variant
  const saveVariant = () => {
    if (currentVariant.attributes.length === 0) return
    
    const variant: Variant = {
      id: Date.now().toString(),
      attributes: currentVariant.attributes,
      sku: currentVariant.sku,
      price: currentVariant.price,
      base_cost: currentVariant.base_cost,
      packaging_cost: currentVariant.packaging_cost,
      quantity: currentVariant.quantity,
      low_stock_threshold: currentVariant.low_stock_threshold
    }
    
    setSavedVariants((prev: ProductVariant[]) => [...prev, variant])
    
    // Reset current variant
    setCurrentVariant({
      attributes: [],
      price: null,
      base_cost: null,
      packaging_cost: null,
      quantity: 0,
      sku: '',
      low_stock_threshold: 10
    })
  }
  
  // Remove saved variant
  const removeVariant = (id: string) => {
    setSavedVariants((prev: ProductVariant[]) => prev.filter(v => v.id !== id))
  }
  
  return (
    <div className="max-w-4xl">
      <SectionHeader
        title="Variants"
      />
      
      <div className="space-y-4">
        {/* Variant Builder */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Create New Variant</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Add Attributes */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-2 items-end">
              <FormField label="Attribute Name">
                <Input
                  value={newAttribute.name}
                  onChange={(e) => setNewAttribute(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="e.g., Size, Color"
                  className="h-8 text-xs placeholder:text-xs"
                />
              </FormField>
              
              <FormField label="Attribute Value">
                <Input
                  value={newAttribute.value}
                  onChange={(e) => setNewAttribute(prev => ({ ...prev, value: e.target.value }))}
                  placeholder="e.g., Large, Gold"
                  className="h-8 text-xs placeholder:text-xs"
                />
              </FormField>
              
              <Button
                type="button"
                onClick={addAttribute}
                disabled={!newAttribute.name || !newAttribute.value}
                className="h-8 px-3 text-xs"
              >
                <Plus className="h-3 w-3 mr-1" />
                Add
              </Button>
            </div>
            
            {/* Current Attributes */}
            {currentVariant.attributes.length > 0 && (
              <div className="space-y-2">
                <label className="text-xs font-medium text-gray-900">Variant Attributes:</label>
                <div className="flex flex-wrap gap-2">
                  {currentVariant.attributes.map((attr, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {attr.name}: {attr.value}
                      <button
                        onClick={() => removeAttribute(index)}
                        className="ml-1 hover:text-red-600"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              </div>
            )}
            
            {/* Variant Details */}
            {currentVariant.attributes.length > 0 && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t">
                <FormField label="SKU">
                  <Input
                    value={currentVariant.sku}
                    onChange={(e) => setCurrentVariant((prev: CurrentVariant) => ({ ...prev, sku: e.target.value }))}
                    className="h-8 font-mono text-xs"
                  />
                </FormField>
                
                <FormField label="Price">
                  <Input
                    type="number"
                    step="0.01"
                    value={currentVariant.price || ''}
                    onChange={(e) => setCurrentVariant((prev: CurrentVariant) => ({ ...prev, price: parseFloat(e.target.value) || null }))}
                    placeholder="0.00"
                    className="h-8 text-xs placeholder:text-xs"
                  />
                </FormField>
                
                <FormField label="Base Cost">
                  <Input
                    type="number"
                    step="0.01"
                    value={currentVariant.base_cost || ''}
                    onChange={(e) => setCurrentVariant((prev: CurrentVariant) => ({ ...prev, base_cost: parseFloat(e.target.value) || null }))}
                    placeholder="0.00"
                    className="h-8 text-xs placeholder:text-xs"
                  />
                </FormField>
                
                <FormField label="Packaging Cost">
                  <Input
                    type="number"
                    step="0.01"
                    value={currentVariant.packaging_cost || ''}
                    onChange={(e) => setCurrentVariant((prev: CurrentVariant) => ({ ...prev, packaging_cost: parseFloat(e.target.value) || null }))}
                    placeholder="0.00"
                    className="h-8 text-xs placeholder:text-xs"
                  />
                </FormField>
                
                <FormField label="Stock Quantity">
                  <Input
                    type="number"
                    value={currentVariant.quantity || ''}
                    onChange={(e) => setCurrentVariant((prev: CurrentVariant) => ({ ...prev, quantity: parseInt(e.target.value) || 0 }))}
                    placeholder="0"
                    className="h-8 text-xs placeholder:text-xs"
                  />
                </FormField>
                
                <FormField label="Low Stock Threshold">
                  <Input
                    type="number"
                    value={currentVariant.low_stock_threshold || ''}
                    onChange={(e) => setCurrentVariant((prev: CurrentVariant) => ({ ...prev, low_stock_threshold: parseInt(e.target.value) || 10 }))}
                    placeholder="10"
                    className="h-8 text-xs placeholder:text-xs"
                  />
                </FormField>
              </div>
            )}
            
            {/* Save Variant Button */}
            {currentVariant.attributes.length > 0 && (
              <div className="pt-4 border-t">
                <Button
                  type="button"
                  onClick={saveVariant}
                  className="h-8 px-4 text-xs bg-blue-600 hover:bg-blue-700"
                >
                  Save Variant
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
        
        {/* Saved Variants Table */}
        {savedVariants.length > 0 && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Created Variants ({savedVariants.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-xs">Variant</TableHead>
                    <TableHead className="text-xs">SKU</TableHead>
                    <TableHead className="text-xs">Price</TableHead>
                    <TableHead className="text-xs">Cost</TableHead>
                    <TableHead className="text-xs">Stock</TableHead>
                    <TableHead className="text-xs">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {savedVariants.map((variant) => (
                    <TableRow key={variant.id}>
                      <TableCell className="text-xs">
                        <div className="flex flex-wrap gap-1">
                          {variant.attributes.map((attr, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {attr.name}: {attr.value}
                            </Badge>
                          ))}
                        </div>
                      </TableCell>
                      <TableCell className="text-xs font-mono">{variant.sku}</TableCell>
                      <TableCell className="text-xs">{variant.price ? formatCurrency(variant.price) : '—'}</TableCell>
                      <TableCell className="text-xs">
                        {variant.base_cost ? formatCurrency(variant.base_cost) : '—'}
                      </TableCell>
                      <TableCell className="text-xs">{variant.quantity}</TableCell>
                      <TableCell>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeVariant(variant.id)}
                          className="h-6 w-6 p-0 text-red-600 hover:text-red-700"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        )}
        
        {/* Guidance */}
        {savedVariants.length === 0 && (
          <div className="text-center py-8 bg-blue-50 border border-blue-200 rounded-md">
            <div className="text-blue-500 mb-2">
              <svg className="w-8 h-8 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <h3 className="font-medium text-blue-900 text-sm mb-1">Create Your First Variant</h3>
            <p className="text-xs text-blue-700 mb-4">
              Add attributes like Size, Color, Material to create product variants with individual pricing and inventory.
            </p>
            <div className="text-xs text-blue-600 bg-blue-100 rounded px-3 py-2 inline-block">
              💡 Example: Size: Large + Color: Gold = "Large Gold" variant
            </div>
          </div>
        )}
      </div>
    </div>
  )
}