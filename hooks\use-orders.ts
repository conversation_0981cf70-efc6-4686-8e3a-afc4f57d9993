import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query'
import { getAllOrdersForOrganization, getOrderById, updateOrder, deleteOrder, createOrder, updateOrderStatus } from '@/lib/supabase-orders'
import type { OrderRow, OrderStatus } from '@/lib/supabase-orders'

// Debounce function to prevent rapid requests
function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  return function (...args: Parameters<T>) {
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(() => func(...args), wait)
  }
}

/**
 * Custom hook to fetch all orders for an organization with React Query
 * @param organizationId - The organization ID to fetch orders for
 * @returns React Query result object with orders data
 */
export function useAllOrders(organizationId: string | undefined) {
  return useQuery<OrderRow[], Error>({
    queryKey: ['allOrders', organizationId],
    queryFn: async () => {
      if (!organizationId) {
        throw new Error('Organization ID is required')
      }
      
      const data = await getAllOrdersForOrganization(organizationId)
      return data || []
    },
    enabled: !!organizationId,
    staleTime: 2 * 60 * 1000, // 2 minutes - consistent with providers
    gcTime: 5 * 60 * 1000, // 5 minutes garbage collection time
    refetchOnMount: true,
    refetchOnWindowFocus: true, // Important for tab switching
    refetchOnReconnect: true,
    refetchInterval: false,
    refetchIntervalInBackground: false,
    retry: 3, // Increased retry count
    retryDelay: (attemptIndex) => {
      return Math.min(1000 * 2 ** attemptIndex, 30000)
    },
    networkMode: 'online' // Handle offline scenarios
  })
}

/**
 * Custom hook to fetch a specific order by ID with React Query
 * @param orderId - The ID of the order to fetch
 * @returns React Query result object with order data
 */
export function useOrder(orderId: string | undefined) {
  return useQuery<OrderRow, Error>({
    queryKey: ['order', orderId],
    queryFn: async () => {
      if (!orderId) {
        throw new Error('Order ID is required')
      }
      
      const data = await getOrderById(orderId)
      return data
    },
    enabled: !!orderId,
    staleTime: 2 * 60 * 1000, // 2 minutes - consistent with providers
    gcTime: 5 * 60 * 1000, // 5 minutes garbage collection time
    refetchOnMount: true,
    refetchOnWindowFocus: true, // Important for tab switching
    refetchOnReconnect: true,
    refetchInterval: false,
    refetchIntervalInBackground: false,
    retry: 3, // Increased retry count
    retryDelay: (attemptIndex) => {
      return Math.min(1000 * 2 ** attemptIndex, 30000)
    },
    networkMode: 'online' // Handle offline scenarios
  })
}

/**
 * Custom hook to create a new order with React Query mutation
 * @returns React Query mutation object for creating orders
 */
export function useCreateOrder() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (params: { organizationId: string; orderData: any }) => 
      createOrder(params.organizationId, params.orderData),
    onSuccess: () => {
      // Invalidate and refetch all orders after creating a new one
      queryClient.invalidateQueries({ queryKey: ['allOrders'] })
    },
  })
}

/**
 * Custom hook to update an existing order with React Query mutation
 * @returns React Query mutation object for updating orders
 */
export function useUpdateOrder() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (params: { orderId: string; orderData: any }) => 
      updateOrder(params.orderId, params.orderData),
    onSuccess: () => {
      // Invalidate and refetch all orders after updating
      queryClient.invalidateQueries({ queryKey: ['allOrders'] })
    },
  })
}

/**
 * Custom hook to delete an order with React Query mutation
 * @returns React Query mutation object for deleting orders
 */
export function useDeleteOrder() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: deleteOrder,
    onSuccess: () => {
      // Invalidate and refetch all orders after deleting
      queryClient.invalidateQueries({ queryKey: ['allOrders'] })
    },
  })
}

/**
 * Custom hook to update an order status with React Query mutation
 * @returns React Query mutation object for updating order status
 */
export function useUpdateOrderStatus() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (params: { orderId: string; newStatus: any }) => 
      updateOrderStatus(params.orderId, params.newStatus),
    onSuccess: () => {
      // Invalidate and refetch all orders after updating status
      queryClient.invalidateQueries({ queryKey: ['allOrders'] })
    },
  })
}

/**
 * Custom hook to invalidate order queries and trigger refetching with debouncing
 * @returns Object with functions to invalidate queries
 */
export function useInvalidateOrders() {
  const queryClient = useQueryClient()
  
  // Debounced invalidate function
  const debouncedInvalidateAllOrders = debounce(() => {
    queryClient.invalidateQueries({ queryKey: ['allOrders'] })
  }, 1000) // 1 second debounce
  
  return {
    invalidateAllOrders: debouncedInvalidateAllOrders
  }
}