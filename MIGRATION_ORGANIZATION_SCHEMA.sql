-- =============================================
-- MIGRATION: USER-CENTRIC TO ORGANIZATION-BASED SCHEMA
-- =============================================

-- Add organization_id column to products table
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS organization_id uuid REFERENCES organizations(id) ON DELETE CASCADE;

-- Add organization_id column to product_variants table
ALTER TABLE product_variants 
ADD COLUMN IF NOT EXISTS organization_id uuid REFERENCES organizations(id) ON DELETE CASCADE;

-- Add organization_id column to categories table
ALTER TABLE categories 
ADD COLUMN IF NOT EXISTS organization_id uuid REFERENCES organizations(id) ON DELETE CASCADE;

-- Add organization_id column to expenses table
ALTER TABLE expenses 
ADD COLUMN IF NOT EXISTS organization_id uuid REFERENCES organizations(id) ON DELETE CASCADE;

-- Add organization_id column to customers table
ALTER TABLE customers 
ADD COLUMN IF NOT EXISTS organization_id uuid REFERENCES organizations(id) ON DELETE CASCADE;

-- Add organization_id column to sales table
ALTER TABLE sales 
ADD COLUMN IF NOT EXISTS organization_id uuid REFERENCES organizations(id) ON DELETE CASCADE;

-- Add organization_id column to stock_movements table
ALTER TABLE stock_movements 
ADD COLUMN IF NOT EXISTS organization_id uuid REFERENCES organizations(id) ON DELETE CASCADE;

-- Add organization_id column to stock_history table
ALTER TABLE stock_history 
ADD COLUMN IF NOT EXISTS organization_id uuid REFERENCES organizations(id) ON DELETE CASCADE;

-- Populate organization_id for existing records
-- This assumes each user belongs to exactly one organization (which is typical for this migration)
UPDATE products 
SET organization_id = (
  SELECT organization_id 
  FROM organization_members 
  WHERE user_id = products.user_id 
  LIMIT 1
) 
WHERE organization_id IS NULL;

UPDATE product_variants 
SET organization_id = (
  SELECT organization_id 
  FROM organization_members 
  WHERE user_id = product_variants.user_id 
  LIMIT 1
) 
WHERE organization_id IS NULL;

UPDATE categories 
SET organization_id = (
  SELECT organization_id 
  FROM organization_members 
  WHERE user_id = categories.user_id 
  LIMIT 1
) 
WHERE organization_id IS NULL;

UPDATE expenses 
SET organization_id = (
  SELECT organization_id 
  FROM organization_members 
  WHERE user_id = expenses.user_id 
  LIMIT 1
) 
WHERE organization_id IS NULL;

UPDATE customers 
SET organization_id = (
  SELECT organization_id 
  FROM organization_members 
  WHERE user_id = customers.user_id 
  LIMIT 1
) 
WHERE organization_id IS NULL;

UPDATE sales 
SET organization_id = (
  SELECT organization_id 
  FROM organization_members 
  WHERE user_id = sales.user_id 
  LIMIT 1
) 
WHERE organization_id IS NULL;

UPDATE stock_movements 
SET organization_id = (
  SELECT organization_id 
  FROM organization_members 
  WHERE user_id = stock_movements.user_id 
  LIMIT 1
) 
WHERE organization_id IS NULL;

UPDATE stock_history 
SET organization_id = (
  SELECT organization_id 
  FROM organization_members 
  WHERE user_id = stock_history.user_id 
  LIMIT 1
) 
WHERE organization_id IS NULL;

-- Make organization_id NOT NULL for all tables
ALTER TABLE products ALTER COLUMN organization_id SET NOT NULL;
ALTER TABLE product_variants ALTER COLUMN organization_id SET NOT NULL;
ALTER TABLE categories ALTER COLUMN organization_id SET NOT NULL;
ALTER TABLE expenses ALTER COLUMN organization_id SET NOT NULL;
ALTER TABLE customers ALTER COLUMN organization_id SET NOT NULL;
ALTER TABLE sales ALTER COLUMN organization_id SET NOT NULL;
ALTER TABLE stock_movements ALTER COLUMN organization_id SET NOT NULL;
ALTER TABLE stock_history ALTER COLUMN organization_id SET NOT NULL;

-- Update unique constraints to use organization_id instead of user_id
ALTER TABLE products DROP CONSTRAINT IF EXISTS products_user_id_base_sku_key;
ALTER TABLE products ADD CONSTRAINT products_organization_id_base_sku_key UNIQUE(organization_id, base_sku);

ALTER TABLE product_variants DROP CONSTRAINT IF EXISTS product_variants_user_id_sku_key;
ALTER TABLE product_variants ADD CONSTRAINT product_variants_organization_id_sku_key UNIQUE(organization_id, sku);

ALTER TABLE categories DROP CONSTRAINT IF EXISTS categories_user_id_name_key;
ALTER TABLE categories ADD CONSTRAINT categories_organization_id_name_key UNIQUE(organization_id, name);

-- Update indexes to use organization_id instead of user_id
DROP INDEX IF EXISTS idx_products_user_id;
DROP INDEX IF EXISTS idx_product_variants_user_id;
DROP INDEX IF EXISTS idx_categories_user_id;
DROP INDEX IF EXISTS idx_expenses_user_id;
DROP INDEX IF EXISTS idx_customers_user_id;
DROP INDEX IF EXISTS idx_sales_user_id;
DROP INDEX IF EXISTS idx_stock_movements_user_id;
DROP INDEX IF EXISTS idx_stock_history_user_id;

CREATE INDEX IF NOT EXISTS idx_products_organization_id ON products(organization_id);
CREATE INDEX IF NOT EXISTS idx_product_variants_organization_id ON product_variants(organization_id);
CREATE INDEX IF NOT EXISTS idx_categories_organization_id ON categories(organization_id);
CREATE INDEX IF NOT EXISTS idx_expenses_organization_id ON expenses(organization_id);
CREATE INDEX IF NOT EXISTS idx_customers_organization_id ON customers(organization_id);
CREATE INDEX IF NOT EXISTS idx_sales_organization_id ON sales(organization_id);
CREATE INDEX IF NOT EXISTS idx_stock_movements_organization_id ON stock_movements(organization_id);
CREATE INDEX IF NOT EXISTS idx_stock_history_organization_id ON stock_history(organization_id);

-- Update RLS policies to use organization_id
-- Products policies
DROP POLICY IF EXISTS "Users can view own products" ON products;
DROP POLICY IF EXISTS "Users can create own products" ON products;
DROP POLICY IF EXISTS "Users can update own products" ON products;
DROP POLICY IF EXISTS "Users can delete own products" ON products;

CREATE POLICY "Organization members can view products" ON products
  FOR SELECT USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can create products" ON products
  FOR INSERT WITH CHECK (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can update products" ON products
  FOR UPDATE USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can delete products" ON products
  FOR DELETE USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

-- Product variants policies
DROP POLICY IF EXISTS "Users can view own product variants" ON product_variants;
DROP POLICY IF EXISTS "Users can create own product variants" ON product_variants;
DROP POLICY IF EXISTS "Users can update own product variants" ON product_variants;
DROP POLICY IF EXISTS "Users can delete own product variants" ON product_variants;

CREATE POLICY "Organization members can view product variants" ON product_variants
  FOR SELECT USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can create product variants" ON product_variants
  FOR INSERT WITH CHECK (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can update product variants" ON product_variants
  FOR UPDATE USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can delete product variants" ON product_variants
  FOR DELETE USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

-- Categories policies
DROP POLICY IF EXISTS "Users can view own categories" ON categories;
DROP POLICY IF EXISTS "Users can create own categories" ON categories;
DROP POLICY IF EXISTS "Users can update own categories" ON categories;
DROP POLICY IF EXISTS "Users can delete own categories" ON categories;

CREATE POLICY "Organization members can view categories" ON categories
  FOR SELECT USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can create categories" ON categories
  FOR INSERT WITH CHECK (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can update categories" ON categories
  FOR UPDATE USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can delete categories" ON categories
  FOR DELETE USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

-- Expenses policies
DROP POLICY IF EXISTS "Users can view own expenses" ON expenses;
DROP POLICY IF EXISTS "Users can create own expenses" ON expenses;
DROP POLICY IF EXISTS "Users can update own expenses" ON expenses;
DROP POLICY IF EXISTS "Users can delete own expenses" ON expenses;

CREATE POLICY "Organization members can view expenses" ON expenses
  FOR SELECT USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can create expenses" ON expenses
  FOR INSERT WITH CHECK (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can update expenses" ON expenses
  FOR UPDATE USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can delete expenses" ON expenses
  FOR DELETE USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

-- Customers policies
DROP POLICY IF EXISTS "Users can view own customers" ON customers;
DROP POLICY IF EXISTS "Users can create own customers" ON customers;
DROP POLICY IF EXISTS "Users can update own customers" ON customers;
DROP POLICY IF EXISTS "Users can delete own customers" ON customers;

CREATE POLICY "Organization members can view customers" ON customers
  FOR SELECT USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can create customers" ON customers
  FOR INSERT WITH CHECK (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can update customers" ON customers
  FOR UPDATE USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can delete customers" ON customers
  FOR DELETE USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

-- Sales policies
DROP POLICY IF EXISTS "Users can view own sales" ON sales;
DROP POLICY IF EXISTS "Users can create own sales" ON sales;
DROP POLICY IF EXISTS "Users can update own sales" ON sales;
DROP POLICY IF EXISTS "Users can delete own sales" ON sales;

CREATE POLICY "Organization members can view sales" ON sales
  FOR SELECT USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can create sales" ON sales
  FOR INSERT WITH CHECK (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can update sales" ON sales
  FOR UPDATE USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can delete sales" ON sales
  FOR DELETE USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

-- Stock movements policies
DROP POLICY IF EXISTS "Users can view own stock movements" ON stock_movements;
DROP POLICY IF EXISTS "Users can create own stock movements" ON stock_movements;
DROP POLICY IF EXISTS "Users can update own stock movements" ON stock_movements;
DROP POLICY IF EXISTS "Users can delete own stock movements" ON stock_movements;

CREATE POLICY "Organization members can view stock movements" ON stock_movements
  FOR SELECT USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can create stock movements" ON stock_movements
  FOR INSERT WITH CHECK (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can update stock movements" ON stock_movements
  FOR UPDATE USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can delete stock movements" ON stock_movements
  FOR DELETE USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

-- Stock history policies (already updated in the original file, but including for completeness)
DROP POLICY IF EXISTS "Users can view own stock history" ON stock_history;
DROP POLICY IF EXISTS "Users can create own stock history" ON stock_history;
DROP POLICY IF EXISTS "Users can update own stock history" ON stock_history;
DROP POLICY IF EXISTS "Users can delete own stock history" ON stock_history;

CREATE POLICY "Organization members can view stock history" ON stock_history
  FOR SELECT USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can create stock history" ON stock_history
  FOR INSERT WITH CHECK (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can update stock history" ON stock_history
  FOR UPDATE USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can delete stock history" ON stock_history
  FOR DELETE USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

-- Remove user_id columns as they're no longer needed
ALTER TABLE products DROP COLUMN IF EXISTS user_id;
ALTER TABLE product_variants DROP COLUMN IF EXISTS user_id;
ALTER TABLE categories DROP COLUMN IF EXISTS user_id;
ALTER TABLE expenses DROP COLUMN IF EXISTS user_id;
ALTER TABLE customers DROP COLUMN IF EXISTS user_id;
ALTER TABLE sales DROP COLUMN IF EXISTS user_id;
ALTER TABLE stock_movements DROP COLUMN IF EXISTS user_id;
ALTER TABLE stock_history DROP COLUMN IF EXISTS user_id;