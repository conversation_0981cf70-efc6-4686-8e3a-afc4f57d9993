'use client'

import { useState, useEffect, useRef } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { getSupabaseClient } from '@/lib/supabase'
import { NotificationService } from '@/lib/notification-service'
import { centralizedNotificationService } from '@/lib/centralized-notification-service'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { ToastAction } from '@/components/ui/toast'
import { toast } from '@/components/ui/use-toast'
import { useToast } from '@/components/ui/use-toast'
import {
  CheckCircle,
  AlertTriangle,
  Info,
  Archive,
  CheckCheck,
  Trash2,
  EyeOff,
  Search,
  ChevronLeft,
  ChevronRight,
  SlidersHorizontal,
  RotateCcw,
  X,
  Filter,
  Clock,
  Calendar,
  ChevronDown,
  ChevronUp,
  ExternalLink,
  Bell,
  Settings
} from 'lucide-react'
import { format, formatDistanceToNow, parseISO, isToday, isYesterday, isThisWeek, isThisYear } from 'date-fns'
import { cn } from '@/lib/utils'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { useAuth } from '@/contexts/auth-context'
import { PageHeader } from '@/components/ui/page-header'
import { NotificationCard } from '@/components/ui/notification-card'
import { NotificationDetailModal } from '@/components/ui/notification-detail-modal'

interface Notification {
  id: string
  title: string
  message: string
  type: string
  created_at: string
  is_read: boolean
  is_archived: boolean
  priority?: 'low' | 'medium' | 'high' | 'urgent'
  category?: string
  related_entity_type?: string
  related_entity_id?: string
}

interface NotificationGroup {
  title: string
  notifications: Notification[]
  count: number
}

// Define notification types based on the database schema
type NotificationType = 'stock_adjustment' | 'low_stock' | 'out_of_stock' | 'purchase_order' | 'stock_history' | 'product_deleted' | 'all'

// Define undo action types
type UndoAction = {
  type: 'archive' | 'delete'
  notifications: Notification[]
}

export default function NotificationsPage() {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [filteredNotifications, setFilteredNotifications] = useState<Notification[]>([])
  const [loading, setLoading] = useState(true)
  const [readFilter, setReadFilter] = useState<'all' | 'unread' | 'read' | 'archived'>('all')
  const [typeFilter, setTypeFilter] = useState<NotificationType>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalNotifications, setTotalNotifications] = useState(0)
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([])
  const [expandedNotifications, setExpandedNotifications] = useState<string[]>([])
  const [highlightedNotification, setHighlightedNotification] = useState<string | null>(null)
  const [viewMode, setViewMode] = useState<'list' | 'cards' | 'grouped'>('cards')
  const [sortBy, setSortBy] = useState<'date' | 'priority' | 'type'>('date')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [showFilters, setShowFilters] = useState(false)
  const [selectedNotificationForDetail, setSelectedNotificationForDetail] = useState<Notification | null>(null)
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false)
  const notificationsPerPage = 12

  const router = useRouter()
  const searchParams = useSearchParams()
  const { toast: showToast } = useToast()
  const undoActionRef = useRef<UndoAction | null>(null)
  const lastVisibilityChangeRef = useRef<number>(Date.now())
  const { organizationId } = useAuth()

  const supabase = getSupabaseClient()
  const notificationService = new NotificationService()

  // Handle URL parameters for highlighting and opening specific notifications
  useEffect(() => {
    const highlight = searchParams.get('highlight')
    const notificationId = searchParams.get('notification')

    if (highlight) {
      setHighlightedNotification(highlight)
      // Auto-expand the highlighted notification
      setExpandedNotifications(prev => [...prev, highlight])
      // Scroll to notification after a brief delay
      setTimeout(() => {
        const element = document.getElementById(`notification-${highlight}`)
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'center' })
        }
      }, 500)
    }

    // Auto-open notification detail modal if notification ID is in URL
    if (notificationId && notifications.length > 0) {
      const notification = notifications.find(n => n.id === notificationId)
      if (notification) {
        setSelectedNotificationForDetail(notification)
        setIsDetailModalOpen(true)
        // Also highlight the notification
        setHighlightedNotification(notificationId)
      }
    }
  }, [searchParams, notifications])

  // Initialize filters from URL parameters
  useEffect(() => {
    const urlReadFilter = searchParams.get('status')
    const urlTypeFilter = searchParams.get('type')
    const urlSearchQuery = searchParams.get('search')
    const urlViewMode = searchParams.get('view')
    const urlSortBy = searchParams.get('sort')
    const urlSortOrder = searchParams.get('order')

    if (urlReadFilter && ['all', 'unread', 'read', 'archived'].includes(urlReadFilter)) {
      setReadFilter(urlReadFilter as any)
    }
    if (urlTypeFilter) {
      setTypeFilter(urlTypeFilter as any)
    }
    if (urlSearchQuery) {
      setSearchQuery(urlSearchQuery)
    }
    if (urlViewMode && ['list', 'cards', 'grouped'].includes(urlViewMode)) {
      setViewMode(urlViewMode as any)
    }
    if (urlSortBy && ['date', 'priority', 'type'].includes(urlSortBy)) {
      setSortBy(urlSortBy as any)
    }
    if (urlSortOrder && ['asc', 'desc'].includes(urlSortOrder)) {
      setSortOrder(urlSortOrder as any)
    }
  }, [])

  // Update URL when filters change
  useEffect(() => {
    const url = new URL(window.location.href)

    // Update filter parameters
    if (readFilter !== 'all') {
      url.searchParams.set('status', readFilter)
    } else {
      url.searchParams.delete('status')
    }

    if (typeFilter !== 'all') {
      url.searchParams.set('type', typeFilter)
    } else {
      url.searchParams.delete('type')
    }

    if (searchQuery) {
      url.searchParams.set('search', searchQuery)
    } else {
      url.searchParams.delete('search')
    }

    if (viewMode !== 'cards') {
      url.searchParams.set('view', viewMode)
    } else {
      url.searchParams.delete('view')
    }

    if (sortBy !== 'date') {
      url.searchParams.set('sort', sortBy)
    } else {
      url.searchParams.delete('sort')
    }

    if (sortOrder !== 'desc') {
      url.searchParams.set('order', sortOrder)
    } else {
      url.searchParams.delete('order')
    }

    // Update URL without triggering a page reload
    window.history.replaceState({}, '', url.toString())
  }, [readFilter, typeFilter, searchQuery, viewMode, sortBy, sortOrder])

  // Load notifications
  useEffect(() => {
    loadNotifications()

    // Set up subscription using centralized service
    let unsubscribe: (() => void) | null = null
    if (organizationId) {
      unsubscribe = centralizedNotificationService.subscribe(
        organizationId,
        (newNotification: any) => {
          // Add new notification to the top of the list only if it doesn't already exist
          setNotifications(prev => {
            // Check if notification already exists
            const exists = prev.some(notification => notification.id === newNotification.id)
            if (!exists) {
              return [newNotification, ...prev]
            }
            return prev
          })
          // Reset to first page when new notification arrives
          setCurrentPage(1)
        },
        (updatedNotification: any) => {
          // Update notification in the list
          setNotifications(prev =>
            prev.map(notification =>
              notification.id === updatedNotification.id
                ? updatedNotification
                : notification
            )
          )
        },
        () => {} // No delete handler needed for this component
      )
    }

    // Handle visibility change to refresh when tab becomes visible
    const handleVisibilityChange = () => {
      // Check if we're in the browser environment
      if (typeof document === 'undefined') return;
      
      const now = Date.now()
      // Debounce visibility changes
      if (now - lastVisibilityChangeRef.current < 1000) {
        return
      }
      
      lastVisibilityChangeRef.current = now
      
      if (document.visibilityState === 'visible') {
        console.debug('NotificationsPage: Tab became visible, refreshing data')
        // When tab becomes visible, refresh the data
        loadNotifications()
        // Force reconnection of notification service
        if (organizationId) {
          centralizedNotificationService.reconnect(organizationId)
        }
      }
    }

    // Handle window focus
    const handleFocus = () => {
      // Check if we're in the browser environment
      if (typeof window === 'undefined') return;
      
      console.debug('NotificationsPage: Window focused, refreshing data')
      loadNotifications()
      // Force reconnection of notification service
      if (organizationId) {
        centralizedNotificationService.reconnect(organizationId)
      }
    }

    // Only add event listeners if we're in the browser environment
    if (typeof document !== 'undefined' && typeof window !== 'undefined') {
      document.addEventListener('visibilitychange', handleVisibilityChange)
      window.addEventListener('focus', handleFocus)
    }

    return () => {
      // Only remove event listeners if we're in the browser environment
      if (typeof document !== 'undefined' && typeof window !== 'undefined') {
        if (unsubscribe) {
          unsubscribe()
        }
        document.removeEventListener('visibilitychange', handleVisibilityChange)
        window.removeEventListener('focus', handleFocus)
      }
    }
  }, [organizationId])

  // Apply filters when notifications or filters change
  useEffect(() => {
    let result = [...notifications]
    
    // Apply read status filter
    if (readFilter === 'unread') {
      result = result.filter(notification => !notification.is_read)
    } else if (readFilter === 'read') {
      result = result.filter(notification => notification.is_read)
    } else if (readFilter === 'archived') {
      result = result.filter(notification => notification.is_archived)
    } else {
      // For 'all', show non-archived by default
      result = result.filter(notification => !notification.is_archived)
    }
    
    // Apply type filter
    if (typeFilter !== 'all') {
      result = result.filter(notification => notification.type === typeFilter)
    }
    
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      result = result.filter(notification => 
        notification.title.toLowerCase().includes(query) ||
        notification.message.toLowerCase().includes(query) ||
        notification.type.toLowerCase().includes(query)
      )
    }
    
    // Apply sorting - always newest first
    result.sort((a, b) => {
      const dateA = new Date(a.created_at).getTime()
      const dateB = new Date(b.created_at).getTime()
      return dateB - dateA // Newest first
    })
    
    setFilteredNotifications(result)
    setTotalNotifications(result.length)
    
    // Reset to first page when filters change
    setCurrentPage(1)
  }, [notifications, readFilter, typeFilter, searchQuery])

  const loadNotifications = async () => {
    try {
      setLoading(true)
      if (organizationId) {
        // For now, we'll load all notifications (in a real app, you might want to implement server-side pagination)
        const notifications = await notificationService.getNotifications(organizationId, 1000)
        setNotifications(notifications)
        setTotalNotifications(notifications.length)
      }
    } catch (error) {
      console.error('Error loading notifications:', error)
    } finally {
      setLoading(false)
    }
  }

  // Group notifications by time periods
  const groupNotifications = (notifications: Notification[]): NotificationGroup[] => {
    const groups: NotificationGroup[] = []
    const today: Notification[] = []
    const yesterday: Notification[] = []
    const thisWeek: Notification[] = []
    const older: Notification[] = []

    notifications.forEach(notification => {
      const date = new Date(notification.created_at)
      if (isToday(date)) {
        today.push(notification)
      } else if (isYesterday(date)) {
        yesterday.push(notification)
      } else if (Date.now() - date.getTime() < 7 * 24 * 60 * 60 * 1000) {
        thisWeek.push(notification)
      } else {
        older.push(notification)
      }
    })

    if (today.length > 0) groups.push({ title: 'Today', notifications: today, count: today.length })
    if (yesterday.length > 0) groups.push({ title: 'Yesterday', notifications: yesterday, count: yesterday.length })
    if (thisWeek.length > 0) groups.push({ title: 'This Week', notifications: thisWeek, count: thisWeek.length })
    if (older.length > 0) groups.push({ title: 'Older', notifications: older, count: older.length })

    return groups
  }

  // Enhanced sorting function
  const sortNotifications = (notifications: Notification[]): Notification[] => {
    return [...notifications].sort((a, b) => {
      let comparison = 0

      switch (sortBy) {
        case 'date':
          comparison = new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
          break
        case 'priority':
          const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 }
          const aPriority = priorityOrder[a.priority as keyof typeof priorityOrder] || 0
          const bPriority = priorityOrder[b.priority as keyof typeof priorityOrder] || 0
          comparison = aPriority - bPriority
          break
        case 'type':
          comparison = a.type.localeCompare(b.type)
          break
      }

      return sortOrder === 'desc' ? -comparison : comparison
    })
  }

  // Toggle notification expansion
  const toggleNotificationExpansion = (notificationId: string) => {
    setExpandedNotifications(prev =>
      prev.includes(notificationId)
        ? prev.filter(id => id !== notificationId)
        : [...prev, notificationId]
    )
  }

  // Open notification detail modal
  const openNotificationDetail = (notification: Notification) => {
    setSelectedNotificationForDetail(notification)
    setIsDetailModalOpen(true)

    // Update URL with notification ID for deep-linking
    const url = new URL(window.location.href)
    url.searchParams.set('notification', notification.id)
    window.history.pushState({}, '', url.toString())

    // Mark as read when opening detail
    if (!notification.is_read) {
      markAsRead(notification.id)
    }
  }

  // Close notification detail modal
  const closeNotificationDetail = () => {
    setIsDetailModalOpen(false)
    setSelectedNotificationForDetail(null)

    // Remove notification ID from URL
    const url = new URL(window.location.href)
    url.searchParams.delete('notification')
    url.searchParams.delete('highlight')
    window.history.pushState({}, '', url.toString())
  }

  // Generate shareable URL for a specific notification
  const generateNotificationUrl = (notificationId: string, highlight: boolean = false) => {
    const url = new URL(window.location.origin + '/dashboard/notifications')
    if (highlight) {
      url.searchParams.set('highlight', notificationId)
    } else {
      url.searchParams.set('notification', notificationId)
    }
    return url.toString()
  }

  // Navigate to notification with highlighting
  const navigateToNotification = (notificationId: string) => {
    const url = generateNotificationUrl(notificationId, true)
    router.push(url)
  }

  const markAsRead = async (id: string) => {
    try {
      // Update database
      await notificationService.markAsRead(id)
      
      // Update local state immediately for instant feedback
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === id 
            ? { ...notification, is_read: true } 
            : notification
        )
      )
    } catch (error) {
      console.error('Error marking notification as read:', error)
    }
  }

  const markAsUnread = async (id: string) => {
    try {
      // Update database
      await notificationService.markAsUnread(id)
      
      // Update local state immediately for instant feedback
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === id 
            ? { ...notification, is_read: false } 
            : notification
        )
      )
    } catch (error) {
      console.error('Error marking notification as unread:', error)
    }
  }

  const archiveNotification = async (id: string) => {
    try {
      // Update database
      await notificationService.archiveNotification(id)
      
      // Update local state immediately for instant feedback
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === id 
            ? { ...notification, is_archived: true } 
            : notification
        )
      )
      
      // Show undo toast
      toast({
        title: "Notification Archived",
        description: "The notification has been archived.",
        action: (
          <ToastAction 
            altText="Undo" 
            onClick={async () => {
              try {
                await notificationService.unarchiveNotification(id)
                
                // Update local state
                setNotifications(prev => 
                  prev.map(notification => 
                    notification.id === id 
                      ? { ...notification, is_archived: false } 
                      : notification
                  )
                )
                
                showToast({
                  title: "Notification Restored",
                  description: "The notification has been restored."
                })
              } catch (error) {
                console.error('Error unarchiving notification:', error)
                showToast({
                  title: "Error",
                  description: "Failed to restore notification. Please try again.",
                  variant: "destructive"
                })
              }
            }}
          >
            Undo
          </ToastAction>
        )
      })
    } catch (error) {
      console.error('Error archiving notification:', error)
      showToast({
        title: "Error",
        description: "Failed to archive notification. Please try again.",
        variant: "destructive"
      })
    }
  }

  const deleteNotification = async (id: string) => {
    try {
      // Store notification for potential undo
      const notificationToDelete = notifications.find(n => n.id === id)
      if (notificationToDelete) {
        undoActionRef.current = {
          type: 'delete',
          notifications: [notificationToDelete]
        }
      }
      
      // Update database
      await notificationService.deleteNotification(id)
      
      // Update local state immediately for instant feedback
      setNotifications(prev => prev.filter(notification => notification.id !== id))
      
      // Show undo toast
      toast({
        title: "Notification Deleted",
        description: "The notification has been deleted.",
        action: (
          <ToastAction 
            altText="Undo" 
            onClick={async () => {
              try {
                // In a real implementation, you would restore the notification
                // For now, we'll just show a message
                showToast({
                  title: "Delete Undone",
                  description: "In a real implementation, this would restore the notification."
                })
              } catch (error) {
                console.error('Error undoing delete:', error)
                showToast({
                  title: "Error",
                  description: "Failed to undo delete. Please try again.",
                  variant: "destructive"
                })
              }
            }}
          >
            Undo
          </ToastAction>
        )
      })
    } catch (error) {
      console.error('Error deleting notification:', error)
      showToast({
        title: "Error",
        description: "Failed to delete notification. Please try again.",
        variant: "destructive"
      })
    }
  }

  const markAllAsRead = async () => {
    try {
      if (organizationId) {
        // Update database
        await notificationService.markAllAsRead(organizationId)
        
        // Update local state immediately for instant feedback
        setNotifications(prev => 
          prev.map(notification => ({ ...notification, is_read: true }))
        )
        
        showToast({
          title: "All Notifications Marked as Read",
          description: "All your notifications have been marked as read."
        })
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error)
      showToast({
        title: "Error",
        description: "Failed to mark all notifications as read. Please try again.",
        variant: "destructive"
      })
    }
  }

  const archiveSelected = async () => {
    try {
      // Store notifications for potential undo
      const notificationsToArchive = notifications.filter(n => selectedNotifications.includes(n.id))
      undoActionRef.current = {
        type: 'archive',
        notifications: notificationsToArchive
      }
      
      // Update database for each selected notification
      await Promise.all(
        selectedNotifications.map(id => notificationService.archiveNotification(id))
      )
      
      // Update local state immediately for instant feedback
      setNotifications(prev => 
        prev.map(notification => 
          selectedNotifications.includes(notification.id) 
            ? { ...notification, is_archived: true } 
            : notification
        )
      )
      
      // Clear selection
      setSelectedNotifications([])
      
      // Show undo toast
      toast({
        title: `${selectedNotifications.length} Notification${selectedNotifications.length > 1 ? 's' : ''} Archived`,
        description: `${selectedNotifications.length} notification${selectedNotifications.length > 1 ? 's have' : ' has'} been archived.`,
        action: (
          <ToastAction 
            altText="Undo" 
            onClick={async () => {
              try {
                // In a real implementation, you would restore the notifications
                // For now, we'll just show a message
                showToast({
                  title: "Archive Undone",
                  description: "In a real implementation, this would restore the notifications."
                })
              } catch (error) {
                console.error('Error undoing archive:', error)
                showToast({
                  title: "Error",
                  description: "Failed to undo archive. Please try again.",
                  variant: "destructive"
                })
              }
            }}
          >
            Undo
          </ToastAction>
        )
      })
    } catch (error) {
      console.error('Error archiving selected notifications:', error)
      showToast({
        title: "Error",
        description: "Failed to archive selected notifications. Please try again.",
        variant: "destructive"
      })
    }
  }

  const deleteSelected = async () => {
    try {
      // Store notifications for potential undo
      const notificationsToDelete = notifications.filter(n => selectedNotifications.includes(n.id))
      undoActionRef.current = {
        type: 'delete',
        notifications: notificationsToDelete
      }
      
      // Update database for each selected notification
      await Promise.all(
        selectedNotifications.map(id => notificationService.deleteNotification(id))
      )
      
      // Update local state immediately for instant feedback
      setNotifications(prev => 
        prev.filter(notification => !selectedNotifications.includes(notification.id))
      )
      
      // Clear selection
      setSelectedNotifications([])
      
      // Show undo toast
      toast({
        title: `${selectedNotifications.length} Notification${selectedNotifications.length > 1 ? 's' : ''} Deleted`,
        description: `${selectedNotifications.length} notification${selectedNotifications.length > 1 ? 's have' : ' has'} been deleted.`,
        action: (
          <ToastAction 
            altText="Undo" 
            onClick={async () => {
              try {
                // In a real implementation, you would restore the notifications
                // For now, we'll just show a message
                showToast({
                  title: "Delete Undone",
                  description: "In a real implementation, this would restore the notifications."
                })
              } catch (error) {
                console.error('Error undoing delete:', error)
                showToast({
                  title: "Error",
                  description: "Failed to undo delete. Please try again.",
                  variant: "destructive"
                })
              }
            }}
          >
            Undo
          </ToastAction>
        )
      })
    } catch (error) {
      console.error('Error deleting selected notifications:', error)
      showToast({
        title: "Error",
        description: "Failed to delete selected notifications. Please try again.",
        variant: "destructive"
      })
    }
  }

  const toggleNotificationSelection = (id: string) => {
    setSelectedNotifications(prev => 
      prev.includes(id) 
        ? prev.filter(notificationId => notificationId !== id) 
        : [...prev, id]
    )
  }

  const toggleSelectAll = () => {
    if (selectedNotifications.length === currentNotifications.length) {
      setSelectedNotifications([])
    } else {
      setSelectedNotifications(currentNotifications.map(notification => notification.id))
    }
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case 'product_deleted':
        return <X className="h-4 w-4 text-red-500" />
      case 'overdue_invoice':
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      case 'new_order':
        return <Info className="h-4 w-4 text-blue-500" />
      case 'info':
      default:
        return <Info className="h-4 w-4 text-blue-500" />
    }
  }

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'success':
        return 'border-l-green-500'
      case 'warning':
        return 'border-l-yellow-500'
      case 'product_deleted':
        return 'border-l-red-500'
      case 'overdue_invoice':
        return 'border-l-red-500'
      case 'new_order':
        return 'border-l-blue-500'
      case 'info':
      default:
        return 'border-l-blue-500'
    }
  }

  // Get enhanced notification icon with background
  const getNotificationIconProps = (type: string) => {
    switch (type) {
      case 'success':
        return {
          Icon: CheckCircle,
          bgClass: 'bg-green-100',
          iconClass: 'text-green-600',
          borderClass: 'border-green-200'
        }
      case 'warning':
        return {
          Icon: AlertTriangle,
          bgClass: 'bg-yellow-100',
          iconClass: 'text-yellow-600',
          borderClass: 'border-yellow-200'
        }
      case 'product_deleted':
        return {
          Icon: X,
          bgClass: 'bg-red-100',
          iconClass: 'text-red-600',
          borderClass: 'border-red-200'
        }
      case 'overdue_invoice':
        return {
          Icon: AlertTriangle,
          bgClass: 'bg-red-100',
          iconClass: 'text-red-600',
          borderClass: 'border-red-200'
        }
      case 'new_order':
        return {
          Icon: Info,
          bgClass: 'bg-blue-100',
          iconClass: 'text-blue-600',
          borderClass: 'border-blue-200'
        }
      case 'info':
      default:
        return {
          Icon: Info,
          bgClass: 'bg-blue-100',
          iconClass: 'text-blue-600',
          borderClass: 'border-blue-200'
        }
    }
  }

  // Get priority badge styling
  const getPriorityBadge = (priority?: string) => {
    if (!priority) return null

    const variants = {
      low: { variant: 'secondary' as const, text: 'Low', color: 'text-gray-600' },
      medium: { variant: 'default' as const, text: 'Medium', color: 'text-blue-600' },
      high: { variant: 'destructive' as const, text: 'High', color: 'text-orange-600' },
      urgent: { variant: 'destructive' as const, text: 'Urgent', color: 'text-red-600' }
    }

    const config = variants[priority as keyof typeof variants]
    if (!config) return null

    return (
      <Badge variant={config.variant} className={cn("text-xs", config.color)}>
        {config.text}
      </Badge>
    )
  }

  // Calculate pagination
  const indexOfLastNotification = currentPage * notificationsPerPage
  const indexOfFirstNotification = indexOfLastNotification - notificationsPerPage
  const currentNotifications = filteredNotifications.slice(indexOfFirstNotification, indexOfLastNotification)
  const totalPages = Math.ceil(filteredNotifications.length / notificationsPerPage)

  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(prev => prev + 1)
    }
  }

  const goToPrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(prev => prev - 1)
    }
  }

  const formatDate = (dateString: string) => {
    const date = parseISO(dateString)
    
    if (isToday(date)) {
      return format(date, 'h:mm a')
    } else if (isYesterday(date)) {
      return 'Yesterday'
    } else if (isThisWeek(date)) {
      return format(date, 'EEEE')
    } else if (isThisYear(date)) {
      return format(date, 'MMM d')
    } else {
      return format(date, 'MMM d, yyyy')
    }
  }

  const formatFullDate = (dateString: string) => {
    const date = parseISO(dateString)
    return format(date, 'MMMM d, yyyy h:mm a')
  }

  // Loading state with skeleton
  if (loading) {
    return (
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <div className="h-8 w-48 bg-gray-200 rounded animate-pulse" />
            <div className="h-4 w-32 bg-gray-200 rounded animate-pulse" />
          </div>
          <div className="h-10 w-32 bg-gray-200 rounded animate-pulse" />
        </div>

        <div className="grid gap-4">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="p-6 animate-fade-in-up" style={{ animationDelay: `${i * 100}ms` }}>
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 skeleton-pulse rounded-full" />
                <div className="flex-1 space-y-3">
                  <div className="h-5 skeleton-pulse rounded" />
                  <div className="h-4 skeleton-pulse rounded w-3/4" />
                  <div className="h-3 skeleton-pulse rounded w-1/2" />
                  <div className="flex items-center gap-2 mt-3">
                    <div className="h-6 w-16 skeleton-pulse rounded-full" />
                    <div className="h-6 w-20 skeleton-pulse rounded-full" />
                  </div>
                </div>
                <div className="flex gap-1">
                  <div className="w-8 h-8 skeleton-pulse rounded" />
                  <div className="w-8 h-8 skeleton-pulse rounded" />
                  <div className="w-8 h-8 skeleton-pulse rounded" />
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  // Process notifications for display
  const sortedNotifications = sortNotifications(filteredNotifications)
  const groupedNotifications = viewMode === 'grouped' ? groupNotifications(sortedNotifications) : []
  const currentNotifications = viewMode === 'grouped'
    ? sortedNotifications
    : sortedNotifications.slice(indexOfFirstNotification, indexOfLastNotification)

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Enhanced Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div className="space-y-1">
          <div className="flex items-center gap-3">
            <h1 className="text-2xl font-bold text-gray-900">Notifications</h1>
            {notifications.filter(n => !n.is_read).length > 0 && (
              <Badge variant="secondary" className="text-sm">
                {notifications.filter(n => !n.is_read).length} unread
              </Badge>
            )}
          </div>
          <p className="text-sm text-gray-600">
            {totalNotifications > 0
              ? `${totalNotifications} total notification${totalNotifications > 1 ? 's' : ''}`
              : 'No notifications to display'}
          </p>
        </div>

        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2"
          >
            <Filter className="h-4 w-4" />
            Filters
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={markAllAsRead}
            disabled={notifications.every(n => n.is_read)}
            className="flex items-center gap-2"
          >
            <CheckCheck className="h-4 w-4" />
            Mark all read
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/dashboard/settings?tab=notifications')}
            className="flex items-center gap-2"
          >
            <Settings className="h-4 w-4" />
            Settings
          </Button>
        </div>
      </div>

      {/* Enhanced Filters */}
      <Card className={cn(
        "transition-all duration-200",
        showFilters ? "shadow-md" : "shadow-sm"
      )}>
        <CardContent className="p-6">
          {/* Search Bar */}
          <div className="relative mb-4">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <Input
              type="text"
              placeholder="Search notifications by title, message, or type..."
              className="pl-10 h-12 text-base"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          {/* Collapsible Filters */}
          {showFilters && (
            <div className="space-y-4 pt-4 border-t border-gray-100">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Status</Label>
                  <Select value={readFilter} onValueChange={(value: any) => setReadFilter(value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="All statuses" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All notifications</SelectItem>
                      <SelectItem value="unread">Unread only</SelectItem>
                      <SelectItem value="read">Read only</SelectItem>
                      <SelectItem value="archived">Archived</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium">Type</Label>
                  <Select value={typeFilter} onValueChange={(value: any) => setTypeFilter(value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="All types" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All types</SelectItem>
                      <SelectItem value="stock_adjustment">Stock Adjustment</SelectItem>
                      <SelectItem value="low_stock">Low Stock</SelectItem>
                      <SelectItem value="out_of_stock">Out of Stock</SelectItem>
                      <SelectItem value="purchase_order">Purchase Order</SelectItem>
                      <SelectItem value="new_order">New Order</SelectItem>
                      <SelectItem value="product_deleted">Product Deleted</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium">Sort by</Label>
                  <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="date">Date</SelectItem>
                      <SelectItem value="priority">Priority</SelectItem>
                      <SelectItem value="type">Type</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium">View</Label>
                  <Select value={viewMode} onValueChange={(value: any) => setViewMode(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="cards">Cards</SelectItem>
                      <SelectItem value="list">List</SelectItem>
                      <SelectItem value="grouped">Grouped</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex items-center justify-between pt-2">
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setReadFilter('all')
                      setTypeFilter('all')
                      setSearchQuery('')
                      setSortBy('date')
                      setSortOrder('desc')
                    }}
                    className="text-gray-600"
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Reset filters
                  </Button>
                </div>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                  className="flex items-center gap-2"
                >
                  {sortOrder === 'desc' ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronUp className="h-4 w-4" />
                  )}
                  {sortOrder === 'desc' ? 'Newest first' : 'Oldest first'}
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Enhanced Bulk Actions */}
      {selectedNotifications.length > 0 && (
        <Card className="border-blue-200 bg-blue-50/50">
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="flex items-center gap-3">
                <Checkbox
                  checked={selectedNotifications.length === currentNotifications.length && currentNotifications.length > 0}
                  onCheckedChange={toggleSelectAll}
                />
                <div className="flex items-center gap-2">
                  <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                    {selectedNotifications.length}
                  </Badge>
                  <span className="text-sm font-medium text-blue-900">
                    notification{selectedNotifications.length > 1 ? 's' : ''} selected
                  </span>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={archiveSelected}
                  className="border-blue-300 text-blue-700 hover:bg-blue-100"
                >
                  <Archive className="h-4 w-4 mr-2" />
                  Archive
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={deleteSelected}
                  className="border-red-300 text-red-700 hover:bg-red-100"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSelectedNotifications([])}
                  className="text-gray-600"
                >
                  <X className="h-4 w-4 mr-2" />
                  Clear
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Modern Notifications Display */}
      {currentNotifications.length === 0 ? (
        <Card className="border-dashed border-2 border-gray-200">
          <CardContent className="p-12 text-center">
            <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
              <Bell className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No notifications</h3>
            <p className="text-gray-600 mb-4">
              {searchQuery || readFilter !== 'all' || typeFilter !== 'all'
                ? 'No notifications match your current filters.'
                : 'You\'re all caught up! Check back later for new notifications.'}
            </p>
            {(searchQuery || readFilter !== 'all' || typeFilter !== 'all') && (
              <Button
                variant="outline"
                onClick={() => {
                  setSearchQuery('')
                  setReadFilter('all')
                  setTypeFilter('all')
                }}
                className="mt-2"
              >
                Clear filters
              </Button>
            )}
          </CardContent>
        </Card>
      ) : viewMode === 'grouped' ? (
        // Grouped View
        <div className="space-y-6">
          {groupedNotifications.map((group, groupIndex) => (
            <div key={group.title}>
              <div className="flex items-center gap-3 mb-4">
                <h3 className="text-lg font-semibold text-gray-900">{group.title}</h3>
                <Badge variant="secondary" className="text-xs">
                  {group.count}
                </Badge>
              </div>
              <div className="grid gap-4">
                {group.notifications.map((notification) => (
                  <NotificationCard
                    key={notification.id}
                    notification={notification}
                    isSelected={selectedNotifications.includes(notification.id)}
                    isExpanded={expandedNotifications.includes(notification.id)}
                    isHighlighted={highlightedNotification === notification.id}
                    onToggleSelection={() => toggleNotificationSelection(notification.id)}
                    onToggleExpansion={() => toggleNotificationExpansion(notification.id)}
                    onMarkAsRead={() => markAsRead(notification.id)}
                    onArchive={() => archiveNotification(notification.id)}
                    onDelete={() => deleteNotification(notification.id)}
                    onOpenDetail={() => openNotificationDetail(notification)}
                  />
                ))}
              </div>
            </div>
          ))}
        </div>
      ) : (
        // Cards/List View
        <div className={cn(
          viewMode === 'cards'
            ? "grid gap-4 md:grid-cols-1 lg:grid-cols-1"
            : "space-y-2"
        )}>
          {currentNotifications.map((notification, index) => (
            <div
              key={notification.id}
              className="animate-fade-in-up"
              style={{ animationDelay: `${index * 50}ms` }}
            >
              <NotificationCard
                notification={notification}
                isSelected={selectedNotifications.includes(notification.id)}
                isExpanded={expandedNotifications.includes(notification.id)}
                isHighlighted={highlightedNotification === notification.id}
                onToggleSelection={() => toggleNotificationSelection(notification.id)}
                onToggleExpansion={() => toggleNotificationExpansion(notification.id)}
                onMarkAsRead={() => markAsRead(notification.id)}
                onArchive={() => archiveNotification(notification.id)}
                onDelete={() => deleteNotification(notification.id)}
                onOpenDetail={() => openNotificationDetail(notification)}
                viewMode={viewMode}
              />
            </div>
          ))}
        </div>
      )}

      {/* Modern Pagination */}
      {viewMode !== 'grouped' && totalPages > 1 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="text-sm text-gray-700">
                Showing <span className="font-medium">{indexOfFirstNotification + 1}</span> to{' '}
                <span className="font-medium">
                  {Math.min(indexOfLastNotification, filteredNotifications.length)}
                </span>{' '}
                of <span className="font-medium">{filteredNotifications.length}</span> notifications
              </div>

              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToPrevPage}
                  disabled={currentPage === 1}
                  className="flex items-center gap-2"
                >
                  <ChevronLeft className="h-4 w-4" />
                  Previous
                </Button>

                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                    let page
                    if (totalPages <= 5) {
                      page = i + 1
                    } else if (currentPage <= 3) {
                      page = i + 1
                    } else if (currentPage >= totalPages - 2) {
                      page = totalPages - 4 + i
                    } else {
                      page = currentPage - 2 + i
                    }

                    return (
                      <Button
                        key={page}
                        variant={currentPage === page ? "default" : "outline"}
                        size="sm"
                        className="w-10 h-10 p-0"
                        onClick={() => setCurrentPage(page)}
                      >
                        {page}
                      </Button>
                    )
                  })}
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToNextPage}
                  disabled={currentPage === totalPages}
                  className="flex items-center gap-2"
                >
                  Next
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Notification Detail Modal */}
      <NotificationDetailModal
        notification={selectedNotificationForDetail}
        isOpen={isDetailModalOpen}
        onClose={closeNotificationDetail}
        onMarkAsRead={markAsRead}
        onArchive={archiveNotification}
        onDelete={deleteNotification}
      />
    </div>
  )
}