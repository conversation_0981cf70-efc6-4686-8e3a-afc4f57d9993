'use client'

import { useState, useEffect, useRef } from 'react'
import { getSupabaseClient } from '@/lib/supabase'
import { NotificationService } from '@/lib/notification-service'
import { centralizedNotificationService } from '@/lib/centralized-notification-service'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Checkbox } from '@/components/ui/checkbox'
import { ToastAction } from '@/components/ui/toast'
import { toast } from '@/components/ui/use-toast'
import { useToast } from '@/components/ui/use-toast'
import { 
  CheckCircle, 
  AlertTriangle, 
  Info, 
  Archive, 
  CheckCheck,
  Trash2,
  EyeOff,
  Search,
  ChevronLeft,
  ChevronRight,
  SlidersHorizontal,
  RotateCcw,
  X
} from 'lucide-react'
import { format, formatDistanceToNow, parseISO, isToday, isYesterday, isThisWeek, isThisYear } from 'date-fns'
import { cn } from '@/lib/utils'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { useAuth } from '@/contexts/auth-context'
import { PageHeader } from '@/components/ui/page-header'

interface Notification {
  id: string
  title: string
  message: string
  type: string
  created_at: string
  is_read: boolean
  is_archived: boolean
}

// Define notification types based on the database schema
type NotificationType = 'stock_adjustment' | 'low_stock' | 'out_of_stock' | 'purchase_order' | 'stock_history' | 'product_deleted' | 'all'

// Define undo action types
type UndoAction = {
  type: 'archive' | 'delete'
  notifications: Notification[]
}

export default function NotificationsPage() {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [filteredNotifications, setFilteredNotifications] = useState<Notification[]>([])
  const [loading, setLoading] = useState(true)
  const [readFilter, setReadFilter] = useState<'all' | 'unread' | 'read' | 'archived'>('all')
  const [typeFilter, setTypeFilter] = useState<NotificationType>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalNotifications, setTotalNotifications] = useState(0)
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([])
  const [isExpanded, setIsExpanded] = useState(false)
  const notificationsPerPage = 10
  
  const { toast: showToast } = useToast()
  const undoActionRef = useRef<UndoAction | null>(null)
  const lastVisibilityChangeRef = useRef<number>(Date.now())
  const { organizationId } = useAuth()
  
  const supabase = getSupabaseClient()
  const notificationService = new NotificationService()

  // Load notifications
  useEffect(() => {
    loadNotifications()

    // Set up subscription using centralized service
    let unsubscribe: (() => void) | null = null
    if (organizationId) {
      unsubscribe = centralizedNotificationService.subscribe(
        organizationId,
        (newNotification: any) => {
          // Add new notification to the top of the list only if it doesn't already exist
          setNotifications(prev => {
            // Check if notification already exists
            const exists = prev.some(notification => notification.id === newNotification.id)
            if (!exists) {
              return [newNotification, ...prev]
            }
            return prev
          })
          // Reset to first page when new notification arrives
          setCurrentPage(1)
        },
        (updatedNotification: any) => {
          // Update notification in the list
          setNotifications(prev => 
            prev.map(notification => 
              notification.id === updatedNotification.id 
                ? updatedNotification 
                : notification
            )
          )
        },
        () => {} // No delete handler needed for this component
      )
    }

    // Handle visibility change to refresh when tab becomes visible
    const handleVisibilityChange = () => {
      // Check if we're in the browser environment
      if (typeof document === 'undefined') return;
      
      const now = Date.now()
      // Debounce visibility changes
      if (now - lastVisibilityChangeRef.current < 1000) {
        return
      }
      
      lastVisibilityChangeRef.current = now
      
      if (document.visibilityState === 'visible') {
        console.debug('NotificationsPage: Tab became visible, refreshing data')
        // When tab becomes visible, refresh the data
        loadNotifications()
        // Force reconnection of notification service
        if (organizationId) {
          centralizedNotificationService.reconnect(organizationId)
        }
      }
    }

    // Handle window focus
    const handleFocus = () => {
      // Check if we're in the browser environment
      if (typeof window === 'undefined') return;
      
      console.debug('NotificationsPage: Window focused, refreshing data')
      loadNotifications()
      // Force reconnection of notification service
      if (organizationId) {
        centralizedNotificationService.reconnect(organizationId)
      }
    }

    // Only add event listeners if we're in the browser environment
    if (typeof document !== 'undefined' && typeof window !== 'undefined') {
      document.addEventListener('visibilitychange', handleVisibilityChange)
      window.addEventListener('focus', handleFocus)
    }

    return () => {
      // Only remove event listeners if we're in the browser environment
      if (typeof document !== 'undefined' && typeof window !== 'undefined') {
        if (unsubscribe) {
          unsubscribe()
        }
        document.removeEventListener('visibilitychange', handleVisibilityChange)
        window.removeEventListener('focus', handleFocus)
      }
    }
  }, [organizationId])

  // Apply filters when notifications or filters change
  useEffect(() => {
    let result = [...notifications]
    
    // Apply read status filter
    if (readFilter === 'unread') {
      result = result.filter(notification => !notification.is_read)
    } else if (readFilter === 'read') {
      result = result.filter(notification => notification.is_read)
    } else if (readFilter === 'archived') {
      result = result.filter(notification => notification.is_archived)
    } else {
      // For 'all', show non-archived by default
      result = result.filter(notification => !notification.is_archived)
    }
    
    // Apply type filter
    if (typeFilter !== 'all') {
      result = result.filter(notification => notification.type === typeFilter)
    }
    
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      result = result.filter(notification => 
        notification.title.toLowerCase().includes(query) ||
        notification.message.toLowerCase().includes(query) ||
        notification.type.toLowerCase().includes(query)
      )
    }
    
    // Apply sorting - always newest first
    result.sort((a, b) => {
      const dateA = new Date(a.created_at).getTime()
      const dateB = new Date(b.created_at).getTime()
      return dateB - dateA // Newest first
    })
    
    setFilteredNotifications(result)
    setTotalNotifications(result.length)
    
    // Reset to first page when filters change
    setCurrentPage(1)
  }, [notifications, readFilter, typeFilter, searchQuery])

  const loadNotifications = async () => {
    try {
      setLoading(true)
      if (organizationId) {
        // For now, we'll load all notifications (in a real app, you might want to implement server-side pagination)
        const notifications = await notificationService.getNotifications(organizationId, 1000)
        setNotifications(notifications)
        setTotalNotifications(notifications.length)
      }
    } catch (error) {
      console.error('Error loading notifications:', error)
    } finally {
      setLoading(false)
    }
  }

  const markAsRead = async (id: string) => {
    try {
      // Update database
      await notificationService.markAsRead(id)
      
      // Update local state immediately for instant feedback
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === id 
            ? { ...notification, is_read: true } 
            : notification
        )
      )
    } catch (error) {
      console.error('Error marking notification as read:', error)
    }
  }

  const markAsUnread = async (id: string) => {
    try {
      // Update database
      await notificationService.markAsUnread(id)
      
      // Update local state immediately for instant feedback
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === id 
            ? { ...notification, is_read: false } 
            : notification
        )
      )
    } catch (error) {
      console.error('Error marking notification as unread:', error)
    }
  }

  const archiveNotification = async (id: string) => {
    try {
      // Update database
      await notificationService.archiveNotification(id)
      
      // Update local state immediately for instant feedback
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === id 
            ? { ...notification, is_archived: true } 
            : notification
        )
      )
      
      // Show undo toast
      toast({
        title: "Notification Archived",
        description: "The notification has been archived.",
        action: (
          <ToastAction 
            altText="Undo" 
            onClick={async () => {
              try {
                await notificationService.unarchiveNotification(id)
                
                // Update local state
                setNotifications(prev => 
                  prev.map(notification => 
                    notification.id === id 
                      ? { ...notification, is_archived: false } 
                      : notification
                  )
                )
                
                showToast({
                  title: "Notification Restored",
                  description: "The notification has been restored."
                })
              } catch (error) {
                console.error('Error unarchiving notification:', error)
                showToast({
                  title: "Error",
                  description: "Failed to restore notification. Please try again.",
                  variant: "destructive"
                })
              }
            }}
          >
            Undo
          </ToastAction>
        )
      })
    } catch (error) {
      console.error('Error archiving notification:', error)
      showToast({
        title: "Error",
        description: "Failed to archive notification. Please try again.",
        variant: "destructive"
      })
    }
  }

  const deleteNotification = async (id: string) => {
    try {
      // Store notification for potential undo
      const notificationToDelete = notifications.find(n => n.id === id)
      if (notificationToDelete) {
        undoActionRef.current = {
          type: 'delete',
          notifications: [notificationToDelete]
        }
      }
      
      // Update database
      await notificationService.deleteNotification(id)
      
      // Update local state immediately for instant feedback
      setNotifications(prev => prev.filter(notification => notification.id !== id))
      
      // Show undo toast
      toast({
        title: "Notification Deleted",
        description: "The notification has been deleted.",
        action: (
          <ToastAction 
            altText="Undo" 
            onClick={async () => {
              try {
                // In a real implementation, you would restore the notification
                // For now, we'll just show a message
                showToast({
                  title: "Delete Undone",
                  description: "In a real implementation, this would restore the notification."
                })
              } catch (error) {
                console.error('Error undoing delete:', error)
                showToast({
                  title: "Error",
                  description: "Failed to undo delete. Please try again.",
                  variant: "destructive"
                })
              }
            }}
          >
            Undo
          </ToastAction>
        )
      })
    } catch (error) {
      console.error('Error deleting notification:', error)
      showToast({
        title: "Error",
        description: "Failed to delete notification. Please try again.",
        variant: "destructive"
      })
    }
  }

  const markAllAsRead = async () => {
    try {
      if (organizationId) {
        // Update database
        await notificationService.markAllAsRead(organizationId)
        
        // Update local state immediately for instant feedback
        setNotifications(prev => 
          prev.map(notification => ({ ...notification, is_read: true }))
        )
        
        showToast({
          title: "All Notifications Marked as Read",
          description: "All your notifications have been marked as read."
        })
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error)
      showToast({
        title: "Error",
        description: "Failed to mark all notifications as read. Please try again.",
        variant: "destructive"
      })
    }
  }

  const archiveSelected = async () => {
    try {
      // Store notifications for potential undo
      const notificationsToArchive = notifications.filter(n => selectedNotifications.includes(n.id))
      undoActionRef.current = {
        type: 'archive',
        notifications: notificationsToArchive
      }
      
      // Update database for each selected notification
      await Promise.all(
        selectedNotifications.map(id => notificationService.archiveNotification(id))
      )
      
      // Update local state immediately for instant feedback
      setNotifications(prev => 
        prev.map(notification => 
          selectedNotifications.includes(notification.id) 
            ? { ...notification, is_archived: true } 
            : notification
        )
      )
      
      // Clear selection
      setSelectedNotifications([])
      
      // Show undo toast
      toast({
        title: `${selectedNotifications.length} Notification${selectedNotifications.length > 1 ? 's' : ''} Archived`,
        description: `${selectedNotifications.length} notification${selectedNotifications.length > 1 ? 's have' : ' has'} been archived.`,
        action: (
          <ToastAction 
            altText="Undo" 
            onClick={async () => {
              try {
                // In a real implementation, you would restore the notifications
                // For now, we'll just show a message
                showToast({
                  title: "Archive Undone",
                  description: "In a real implementation, this would restore the notifications."
                })
              } catch (error) {
                console.error('Error undoing archive:', error)
                showToast({
                  title: "Error",
                  description: "Failed to undo archive. Please try again.",
                  variant: "destructive"
                })
              }
            }}
          >
            Undo
          </ToastAction>
        )
      })
    } catch (error) {
      console.error('Error archiving selected notifications:', error)
      showToast({
        title: "Error",
        description: "Failed to archive selected notifications. Please try again.",
        variant: "destructive"
      })
    }
  }

  const deleteSelected = async () => {
    try {
      // Store notifications for potential undo
      const notificationsToDelete = notifications.filter(n => selectedNotifications.includes(n.id))
      undoActionRef.current = {
        type: 'delete',
        notifications: notificationsToDelete
      }
      
      // Update database for each selected notification
      await Promise.all(
        selectedNotifications.map(id => notificationService.deleteNotification(id))
      )
      
      // Update local state immediately for instant feedback
      setNotifications(prev => 
        prev.filter(notification => !selectedNotifications.includes(notification.id))
      )
      
      // Clear selection
      setSelectedNotifications([])
      
      // Show undo toast
      toast({
        title: `${selectedNotifications.length} Notification${selectedNotifications.length > 1 ? 's' : ''} Deleted`,
        description: `${selectedNotifications.length} notification${selectedNotifications.length > 1 ? 's have' : ' has'} been deleted.`,
        action: (
          <ToastAction 
            altText="Undo" 
            onClick={async () => {
              try {
                // In a real implementation, you would restore the notifications
                // For now, we'll just show a message
                showToast({
                  title: "Delete Undone",
                  description: "In a real implementation, this would restore the notifications."
                })
              } catch (error) {
                console.error('Error undoing delete:', error)
                showToast({
                  title: "Error",
                  description: "Failed to undo delete. Please try again.",
                  variant: "destructive"
                })
              }
            }}
          >
            Undo
          </ToastAction>
        )
      })
    } catch (error) {
      console.error('Error deleting selected notifications:', error)
      showToast({
        title: "Error",
        description: "Failed to delete selected notifications. Please try again.",
        variant: "destructive"
      })
    }
  }

  const toggleNotificationSelection = (id: string) => {
    setSelectedNotifications(prev => 
      prev.includes(id) 
        ? prev.filter(notificationId => notificationId !== id) 
        : [...prev, id]
    )
  }

  const toggleSelectAll = () => {
    if (selectedNotifications.length === currentNotifications.length) {
      setSelectedNotifications([])
    } else {
      setSelectedNotifications(currentNotifications.map(notification => notification.id))
    }
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case 'product_deleted':
        return <X className="h-4 w-4 text-red-500" />
      case 'overdue_invoice':
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      case 'new_order':
        return <Info className="h-4 w-4 text-blue-500" />
      case 'info':
      default:
        return <Info className="h-4 w-4 text-blue-500" />
    }
  }

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'success':
        return 'border-l-green-500'
      case 'warning':
        return 'border-l-yellow-500'
      case 'product_deleted':
        return 'border-l-red-500'
      case 'overdue_invoice':
        return 'border-l-red-500'
      case 'new_order':
        return 'border-l-blue-500'
      case 'info':
      default:
        return 'border-l-blue-500'
    }
  }

  // Calculate pagination
  const indexOfLastNotification = currentPage * notificationsPerPage
  const indexOfFirstNotification = indexOfLastNotification - notificationsPerPage
  const currentNotifications = filteredNotifications.slice(indexOfFirstNotification, indexOfLastNotification)
  const totalPages = Math.ceil(filteredNotifications.length / notificationsPerPage)

  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(prev => prev + 1)
    }
  }

  const goToPrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(prev => prev - 1)
    }
  }

  const formatDate = (dateString: string) => {
    const date = parseISO(dateString)
    
    if (isToday(date)) {
      return format(date, 'h:mm a')
    } else if (isYesterday(date)) {
      return 'Yesterday'
    } else if (isThisWeek(date)) {
      return format(date, 'EEEE')
    } else if (isThisYear(date)) {
      return format(date, 'MMM d')
    } else {
      return format(date, 'MMM d, yyyy')
    }
  }

  const formatFullDate = (dateString: string) => {
    const date = parseISO(dateString)
    return format(date, 'MMMM d, yyyy h:mm a')
  }

  // Loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mb-4"></div>
          <p className="text-gray-600">Loading notifications...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <PageHeader 
          title="Notifications"
          description={totalNotifications > 0 
            ? `${totalNotifications} notification${totalNotifications > 1 ? 's' : ''}` 
            : 'No notifications'}
        />
        <div className="mt-4 md:mt-0">
          <Button 
            variant="outline" 
            size="sm"
            onClick={markAllAsRead}
            disabled={notifications.every(n => n.is_read)}
          >
            <CheckCheck className="h-4 w-4 mr-2" />
            Mark all as read
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                type="text"
                placeholder="Search notifications..."
                className="pl-10 w-full"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>
          <div className="flex flex-wrap gap-2">
            <Select value={readFilter} onValueChange={(value: any) => setReadFilter(value)}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Read status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All notifications</SelectItem>
                <SelectItem value="unread">Unread</SelectItem>
                <SelectItem value="read">Read</SelectItem>
                <SelectItem value="archived">Archived</SelectItem>
              </SelectContent>
            </Select>
            <Select value={typeFilter} onValueChange={(value: any) => setTypeFilter(value)}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Notification type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All types</SelectItem>
                <SelectItem value="stock_adjustment">Stock Adjustment</SelectItem>
                <SelectItem value="low_stock">Low Stock</SelectItem>
                <SelectItem value="out_of_stock">Out of Stock</SelectItem>
                <SelectItem value="purchase_order">Purchase Order</SelectItem>
                <SelectItem value="stock_history">Stock History</SelectItem>
                <SelectItem value="product_deleted">Product Deleted</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* Bulk actions */}
      {selectedNotifications.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center">
            <Checkbox
              checked={selectedNotifications.length === currentNotifications.length && currentNotifications.length > 0}
              onCheckedChange={toggleSelectAll}
              className="mr-2"
            />
            <span className="text-sm text-blue-800">
              {selectedNotifications.length} notification{selectedNotifications.length > 1 ? 's' : ''} selected
            </span>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={archiveSelected}
              className="border-blue-300 text-blue-700 hover:bg-blue-100"
            >
              <Archive className="h-4 w-4 mr-2" />
              Archive
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={deleteSelected}
              className="border-red-300 text-red-700 hover:bg-red-100"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </Button>
          </div>
        </div>
      )}

      {/* Notifications list */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        {currentNotifications.length === 0 ? (
          <div className="text-center py-12">
            <div className="mx-auto h-12 w-12 text-gray-400">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
              </svg>
            </div>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No notifications</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchQuery || readFilter !== 'all' || typeFilter !== 'all'
                ? 'No notifications match your filters.'
                : 'You\'re all caught up! Check back later for new notifications.'}
            </p>
          </div>
        ) : (
          <ul className="divide-y divide-gray-200">
            {currentNotifications.map((notification) => (
              <li 
                key={notification.id}
                className={cn(
                  "p-4 hover:bg-gray-50 transition-colors",
                  !notification.is_read ? 'bg-blue-50' : '',
                  getNotificationColor(notification.type)
                )}
              >
                <div className="flex items-start">
                  <div className="flex items-center h-5 pt-0.5">
                    <Checkbox
                      checked={selectedNotifications.includes(notification.id)}
                      onCheckedChange={() => toggleNotificationSelection(notification.id)}
                      className="mr-3"
                    />
                  </div>
                  <div className="flex-shrink-0 pt-0.5">
                    {getNotificationIcon(notification.type)}
                  </div>
                  <div className="ml-3 flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {notification.title}
                      </p>
                      <div className="flex items-center space-x-2 ml-2">
                        <span className="text-xs text-gray-500 whitespace-nowrap">
                          {formatDate(notification.created_at)}
                        </span>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0 text-gray-400 hover:text-gray-600"
                          onClick={() => toggleNotificationSelection(notification.id)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="mt-1">
                      <p className="text-sm text-gray-500">
                        {notification.message}
                      </p>
                    </div>
                    <div className="mt-2 flex items-center text-xs text-gray-400">
                      <span title={formatFullDate(notification.created_at)}>
                        {formatDistanceToNow(new Date(notification.created_at), { addSuffix: true })}
                      </span>
                    </div>
                  </div>
                  <div className="ml-4 flex flex-shrink-0 space-x-2">
                    {!notification.is_read && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 text-gray-400 hover:text-gray-600"
                        onClick={() => markAsRead(notification.id)}
                        title="Mark as read"
                      >
                        <EyeOff className="h-4 w-4" />
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 text-gray-400 hover:text-gray-600"
                      onClick={() => archiveNotification(notification.id)}
                      title="Archive"
                    >
                      <Archive className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 text-gray-400 hover:text-gray-600"
                      onClick={() => deleteNotification(notification.id)}
                      title="Delete"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6 rounded-b-lg">
          <div className="flex flex-1 justify-between sm:hidden">
            <Button
              variant="outline"
              size="sm"
              onClick={goToPrevPage}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={goToNextPage}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
          <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing <span className="font-medium">{indexOfFirstNotification + 1}</span> to{' '}
                <span className="font-medium">
                  {Math.min(indexOfLastNotification, filteredNotifications.length)}
                </span>{' '}
                of <span className="font-medium">{filteredNotifications.length}</span> results
              </p>
            </div>
            <div>
              <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                <Button
                  variant="outline"
                  size="sm"
                  className="relative inline-flex items-center rounded-l-md px-2 py-2"
                  onClick={goToPrevPage}
                  disabled={currentPage === 1}
                >
                  <span className="sr-only">Previous</span>
                  <ChevronLeft className="h-5 w-5" aria-hidden="true" />
                </Button>
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <Button
                    key={page}
                    variant={currentPage === page ? "default" : "outline"}
                    size="sm"
                    className={cn(
                      "relative inline-flex items-center px-4 py-2",
                      currentPage === page ? "z-10" : ""
                    )}
                    onClick={() => setCurrentPage(page)}
                  >
                    {page}
                  </Button>
                ))}
                <Button
                  variant="outline"
                  size="sm"
                  className="relative inline-flex items-center rounded-r-md px-2 py-2"
                  onClick={goToNextPage}
                  disabled={currentPage === totalPages}
                >
                  <span className="sr-only">Next</span>
                  <ChevronRight className="h-5 w-5" aria-hidden="true" />
                </Button>
              </nav>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}