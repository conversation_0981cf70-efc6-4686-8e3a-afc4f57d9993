const fs = require('fs');
const path = require('path');

class SupabaseConfig {
  constructor() {
    this.SUPABASE_URL = null;
    this.SUPABASE_SERVICE_KEY = null;
    this.SUPABASE_ANON_KEY = null;
    this.loadConfig();
  }

  loadConfig() {
    try {
      const envPath = path.join(__dirname, '..', '..', '.env.local');
      const envContent = fs.readFileSync(envPath, 'utf8');
      const envLines = envContent.split('\n');
      
      envLines.forEach(line => {
        if (line.includes('NEXT_PUBLIC_SUPABASE_URL=')) {
          this.SUPABASE_URL = line.split('=')[1].trim();
        }
        if (line.includes('SUPABASE_SERVICE_ROLE_KEY=')) {
          this.SUPABASE_SERVICE_KEY = line.split('=')[1].trim();
        }
        if (line.includes('NEXT_PUBLIC_SUPABASE_ANON_KEY=')) {
          this.SUPABASE_ANON_KEY = line.split('=')[1].trim();
        }
      });
      
      if (!this.SUPABASE_URL || !this.SUPABASE_SERVICE_KEY) {
        throw new Error('Missing required Supabase credentials in .env.local');
      }
      
      console.log('✅ Supabase configuration loaded');
      console.log('✅ Database URL:', this.SUPABASE_URL.substring(0, 30) + '...');
      console.log('✅ Database: Supabase Live Database Only');
      
    } catch (error) {
      console.error('❌ Error loading Supabase configuration:', error.message);
      console.error('❌ Cannot proceed without Supabase - this is a live system only!');
      process.exit(1);
    }
  }

  getHeaders(userToken = null) {
    if (userToken) {
      // For user-authenticated requests, use ANON key + user token
      return {
        'apikey': this.SUPABASE_ANON_KEY,
        'Content-Type': 'application/json',
        'Prefer': 'return=representation',
        'Authorization': `Bearer ${userToken}`
      };
    } else {
      // For service-level requests, use SERVICE key
      return {
        'apikey': this.SUPABASE_SERVICE_KEY,
        'Content-Type': 'application/json',
        'Prefer': 'return=representation',
        'Authorization': `Bearer ${this.SUPABASE_SERVICE_KEY}`
      };
    }
  }

  getApiUrl(endpoint) {
    return `${this.SUPABASE_URL}/rest/v1/${endpoint}`;
  }

  async makeRequest(endpoint, options = {}) {
    const url = this.getApiUrl(endpoint);
    const headers = this.getHeaders(options.userToken);
    
    const response = await fetch(url, {
      ...options,
      headers: {
        ...headers,
        ...options.headers
      }
    });
    
    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Supabase API error: ${response.status} ${error}`);
    }
    
    return response.json();
  }
}

module.exports = new SupabaseConfig();