'use client'

import { useState } from 'react'
import { usePresence, PresenceUser } from '@/hooks/use-presence'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { cn } from '@/lib/utils'

export function PresenceAvatars() {
  const { onlineUsers } = usePresence()
  const [showAll, setShowAll] = useState(false)

  // Function to get user's display name
  const getUserDisplayName = (user: PresenceUser) => {
    if (user.display_name) return user.display_name
    if (user.first_name && user.last_name) return `${user.first_name} ${user.last_name}`
    if (user.first_name) return user.first_name
    return user.email?.split('@')[0] || 'Unknown User'
  }

  // Function to get user initials for avatar fallback
  const getUserInitials = (user: PresenceUser) => {
    if (user.first_name && user.last_name) {
      return `${user.first_name.charAt(0)}${user.last_name.charAt(0)}`.toUpperCase()
    }
    if (user.first_name) {
      return user.first_name.charAt(0).toUpperCase()
    }
    if (user.display_name) {
      return user.display_name.charAt(0).toUpperCase()
    }
    return user.email?.charAt(0).toUpperCase() || 'U'
  }

  // Render avatars with smart overflow logic
  const renderAvatars = () => {
    const maxVisibleAvatars = 4
    const shouldShowOverflow = onlineUsers.length > maxVisibleAvatars
    const visibleUsers = showAll ? onlineUsers : onlineUsers.slice(0, shouldShowOverflow ? 3 : maxVisibleAvatars)
    const overflowCount = onlineUsers.length - 3

    return (
      <div className="flex items-center">
        {visibleUsers.map((user, index) => (
          <div 
            key={user.id} 
            className={cn(
              "border-2 border-white rounded-full",
              index > 0 && "-ml-2"
            )}
            style={{ zIndex: visibleUsers.length - index }}
          >
            <Avatar className="w-8 h-8">
              {user.avatar_url ? (
                <AvatarImage src={user.avatar_url} alt={getUserDisplayName(user)} />
              ) : (
                <AvatarFallback className="bg-gray-200 text-gray-800 text-xs font-medium">
                  {getUserInitials(user)}
                </AvatarFallback>
              )}
            </Avatar>
          </div>
        ))}
        
        {shouldShowOverflow && !showAll && (
          <div 
            className="border-2 border-white rounded-full -ml-2 flex items-center justify-center bg-slate-200 cursor-pointer"
            style={{ zIndex: 1 }}
            onClick={() => setShowAll(true)}
          >
            <div className="w-8 h-8 flex items-center justify-center text-xs font-bold text-gray-800">
              +{overflowCount}
            </div>
          </div>
        )}
      </div>
    )
  }

  // Generate tooltip content with all user names
  const renderTooltipContent = () => {
    return (
      <div className="flex flex-col space-y-1">
        <p className="font-medium text-sm">Online Users</p>
        {onlineUsers.map((user) => (
          <p key={user.id} className="text-xs">
            {getUserDisplayName(user)}
            {user.job_title && (
              <span className="text-gray-400"> • {user.job_title}</span>
            )}
          </p>
        ))}
      </div>
    )
  }

  if (onlineUsers.length === 0) {
    return null
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="cursor-pointer">
            {renderAvatars()}
          </div>
        </TooltipTrigger>
        <TooltipContent side="bottom" className="bg-gray-800 text-white p-3">
          {renderTooltipContent()}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}