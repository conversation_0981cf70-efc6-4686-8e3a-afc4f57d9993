# React Query Implementation for Purchase Orders

This document outlines the implementation of React Query for the purchase orders functionality to resolve issues with browser tab switching.

## Problem

The purchase order pages were using basic `useState` and `useEffect` hooks for data fetching, which caused issues when switching browser tabs. The data would not refresh properly when returning to the tab, leading to stale information.

## Solution

Implemented React Query hooks to handle data fetching, caching, and automatic refetching based on browser events.

## Files Modified

### 1. New Hook: `hooks/use-purchase-orders.ts`

Created a new hook with the following functions:

- `usePurchaseOrders` - Fetches a list of purchase orders with filtering
- `usePurchaseOrder` - Fetches a single purchase order with its items
- `useDeletePurchaseOrder` - Mutation hook for deleting purchase orders
- `useUpdatePurchaseOrderItemReceived` - Mutation hook for updating received quantities
- `useInvalidatePurchaseOrders` - Helper hook for manual cache invalidation

### 2. Updated Component: `app/dashboard/products/purchase-orders/page.tsx`

Replaced `useState` and `useEffect` data fetching with React Query:

- Uses `usePurchaseOrders` for fetching the list of purchase orders
- Uses `useDeletePurchaseOrder` mutation for deleting orders
- Maintains real-time subscriptions with Supabase but integrates with React Query cache invalidation
- Proper loading and error states

### 3. Updated Component: `app/dashboard/products/purchase-orders/[id]/page.tsx`

Replaced `useState` and `useEffect` data fetching with React Query:

- Uses `usePurchaseOrder` for fetching individual purchase order details
- Uses `useUpdatePurchaseOrderItemReceived` mutation for updating received quantities
- Maintains real-time subscriptions with Supabase but integrates with React Query cache invalidation
- Proper loading and error states with retry functionality

## Key Features

### 1. Consistent Configuration

All React Query hooks use the same configuration parameters:

```typescript
{
  staleTime: 2 * 60 * 1000, // 2 minutes
  gcTime: 5 * 60 * 1000, // 5 minutes
  refetchOnWindowFocus: true, // Critical for tab switching
  refetchOnMount: true,
  refetchOnReconnect: true,
  refetchInterval: false,
  refetchIntervalInBackground: false,
  retry: 3,
  networkMode: 'online'
}
```

### 2. Tab Switching Support

The `refetchOnWindowFocus: true` setting ensures that when users switch back to the tab, the data is automatically refreshed if it's stale.

### 3. Real-time Integration

Maintained Supabase real-time subscriptions but integrated them with React Query cache invalidation for optimal performance:

```typescript
// When real-time events are received, invalidate the cache
.invalidateQueries({ queryKey: ['purchaseOrders'] })
```

### 4. Error Handling

Proper error handling with user-friendly messages and retry functionality.

### 5. Type Safety

Full TypeScript support with proper typing for all data structures.

## Benefits

1. **Automatic Refetching**: Data automatically refreshes when users return to the tab
2. **Consistent UX**: Loading states, error states, and retry functionality
3. **Performance**: Intelligent caching reduces unnecessary network requests
4. **Maintainability**: Centralized data fetching logic in reusable hooks
5. **Offline Support**: Network mode configuration for better offline handling

## Testing

Added unit tests in `hooks/__tests__/use-purchase-orders.test.ts` to verify:

- Successful data fetching
- Error handling
- Proper parameter passing to service functions