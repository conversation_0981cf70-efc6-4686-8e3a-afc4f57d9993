'use client'

import { useState, useEffect, useRef } from 'react'
import { QueryClient, QueryClientProvider, useQueryClient } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { AuthProvider } from '@/contexts/auth-context'
import { ThemeProvider } from '@/contexts/theme-context'
import { DateFormatProvider } from '@/contexts/date-format-context'
import { CurrencyProvider } from '@/contexts/currency-context'
import { usePeriodicRefetch } from '@/hooks/use-periodic-refetch'

export function Providers({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 30 * 1000, // Reduced to 30 seconds for more responsive updates
            gcTime: 5 * 60 * 1000, // Reduced garbage collection time
            refetchOnWindowFocus: true, // Re-enable automatic refetching on focus - important for tab switching
            refetchOnMount: true,
            refetchOnReconnect: true,
            retry: 3, // Increased retry count
            retryDelay: (attemptIndex) => {
              return Math.min(1000 * 2 ** attemptIndex, 30000)
            },
            // Additional options for better tab switching handling
            refetchInterval: false, // Disable automatic refetching
            refetchIntervalInBackground: false, // Don't refetch in background
            placeholderData: (previousData: unknown) => previousData, // Keep previous data while fetching
            networkMode: 'online' // Add network mode to handle offline scenarios
          },
        },
      })
  )

  return (
    <QueryClientProvider client={queryClient}>
      <ProvidersWithClient queryClient={queryClient}>
        {children}
      </ProvidersWithClient>
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  )
}

function ProvidersWithClient({ children, queryClient }: { children: React.ReactNode, queryClient: QueryClient }) {
  const isTabActiveRef = useRef(true)
  const lastVisibilityChangeRef = useRef<number>(Date.now())
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Handle visibility change to trigger refetch when tab becomes visible
  const handleVisibilityChange = () => {
    const now = Date.now()
    const timeSinceLastChange = now - lastVisibilityChangeRef.current
    
    // Debounce visibility changes to prevent excessive refetching
    if (timeSinceLastChange < 2000) {
      return
    }
    
    lastVisibilityChangeRef.current = now
    
    if (document.visibilityState === 'visible') {
      isTabActiveRef.current = true
      // When tab becomes visible, invalidate all queries to trigger refetch
      console.debug('ProvidersWithClient: Tab became visible, invalidating queries')
      
      // Clear any existing timeout
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current)
      }
      
      // Use a debounced approach to prevent multiple invalidations
      debounceTimeoutRef.current = setTimeout(() => {
        queryClient.invalidateQueries({ type: 'all' })
      }, 1000)
    } else {
      isTabActiveRef.current = false
      console.debug('ProvidersWithClient: Tab became hidden')
    }
  }

  // Handle window focus to trigger refetch when user returns to the tab
  const handleFocus = () => {
    // Only refetch if the tab was actually inactive
    if (!isTabActiveRef.current) {
      isTabActiveRef.current = true
      console.debug('ProvidersWithClient: Window focused, invalidating queries')
      
      // Clear any existing timeout
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current)
      }
      
      // When window gains focus, invalidate all queries to trigger refetch
      // Use a debounced approach to prevent multiple invalidations
      debounceTimeoutRef.current = setTimeout(() => {
        queryClient.invalidateQueries({ type: 'all' })
      }, 1000)
    }
  }

  // Handle window blur to track when user leaves the tab
  const handleBlur = () => {
    isTabActiveRef.current = false
    // Optional: Perform any cleanup or tracking when user leaves the tab
    console.debug('ProvidersWithClient: User left the tab')
  }

  // Handle online/offline events
  const handleOnline = () => {
    console.debug('ProvidersWithClient: Browser went online, invalidating queries')
    
    // Clear any existing timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current)
    }
    
    debounceTimeoutRef.current = setTimeout(() => {
      queryClient.invalidateQueries({ type: 'all' })
    }, 1000)
  }

  const handleOffline = () => {
    console.debug('ProvidersWithClient: Browser went offline')
  }

  // Add event listeners for page lifecycle events
  useEffect(() => {
    // Remove any existing listeners to prevent duplicates
    document.removeEventListener('visibilitychange', handleVisibilityChange)
    window.removeEventListener('focus', handleFocus)
    window.removeEventListener('blur', handleBlur)
    window.removeEventListener('online', handleOnline)
    window.removeEventListener('offline', handleOffline)
    
    // Add the listeners
    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('focus', handleFocus)
    window.addEventListener('blur', handleBlur)
    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('focus', handleFocus)
      window.removeEventListener('blur', handleBlur)
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
      
      // Clean up timeout
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current)
      }
    }
  }, [])

  // Set up periodic refetching
  usePeriodicRefetch()

  return (
    <AuthProvider queryClient={queryClient}>
      <ThemeProvider>
        <DateFormatProvider>
          <CurrencyProvider>
            {children}
          </CurrencyProvider>
        </DateFormatProvider>
      </ThemeProvider>
    </AuthProvider>
  )
}