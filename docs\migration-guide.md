# Migration Guide: Fixing Supabase Relationship Syntax Issues

This guide explains how to migrate from problematic Supabase relationship syntax to a more robust data fetching approach.

## Background

We encountered issues with Supabase relationship syntax causing schema cache problems, particularly with queries that used syntax like:
```javascript
.select(`
  id,
  customers (full_name)
`)
```

These queries sometimes failed with errors like:
- "column customers_1.name does not exist"
- "Could not find a relationship between 'sale_items' and 'products' in the schema cache"

## Solution Overview

We've implemented a new approach that:
1. Fetches main data with simple `.select()` calls
2. Fetches related data in separate queries
3. Combines the data in application code using utility functions

## Files Changed

### 1. Core Utility Functions
- [lib/supabase-enrichment.ts](file:///X:/Qoder/onko/lib/supabase-enrichment.ts) - New comprehensive service for data enrichment
- [lib/supabase-utils.ts](file:///X:/Qoder/onko/lib/supabase-utils.ts) - Updated to use the new service

### 2. Data Fetching Functions
- [lib/supabase.ts](file:///X:/Qoder/onko/lib/supabase.ts) - Updated `getAllProductsForOrganization` and `getProductsForOrganization` functions
- [lib/supabase-orders.ts](file:///X:/Qoder/onko/lib/supabase-orders.ts) - Already using the correct approach

### 3. React Hooks
- [hooks/use-products.ts](file:///X:/Qoder/onko/hooks/use-products.ts) - Updated to work with new data structure

### 4. Supabase Edge Functions
- [supabase/functions/check-low-stock/index.ts](file:///X:/Qoder/onko/supabase/functions/check-low-stock/index.ts) - Updated to avoid relationship syntax

## Migration Steps

### 1. Identify Problematic Code

Look for `.select()` calls that use relationship syntax:
```javascript
// Problematic
.select(`
  id,
  categories(
    name
  ),
  product_variants(
    id
  )
`)
```

### 2. Replace with Simple Select

Replace with simple field selection:
```javascript
// Fixed
.select(`
  id,
  category_id
`)
```

### 3. Use Utility Functions for Enrichment

Use the utility functions to enrich the data:
```javascript
import { enrichProductsWithRelatedData } from '@/lib/supabase-utils'

// Fetch main data
const { data, error } = await supabase
  .from('products')
  .select('id, category_id, name')

// Enrich with related data
const enrichedProducts = await enrichProductsWithRelatedData(data)
```

### 4. Update Data Consumption

Update components/hooks that consume the data to handle the new structure:
```javascript
// Before
product.categories?.[0]?.name

// After
product.category_name
```

## Example Migration

### Before (Problematic)
```javascript
// In lib/supabase.ts
export async function getAllProductsForOrganization(organizationId: string) {
  const { data, error } = await supabase
    .from('products')
    .select(`
      id,
      categories(
        name
      ),
      product_variants(
        id,
        variant_name
      )
    `)
    .eq('organization_id', organizationId)
  return data
}
```

### After (Fixed)
```javascript
// In lib/supabase.ts
export async function getAllProductsForOrganization(organizationId: string) {
  const { data, error } = await supabase
    .from('products')
    .select(`
      id,
      category_id,
      name
    `)
    .eq('organization_id', organizationId)

  // Enrich with related data using utility function
  const enrichedProducts = await enrichProductsWithRelatedData(data)
  return enrichedProducts
}
```

## Testing

After migration, test thoroughly:
1. Verify data is correctly fetched and displayed
2. Check that related data is properly enriched
3. Ensure no regressions in functionality
4. Test error handling paths

## Best Practices Going Forward

1. **Avoid relationship syntax** in `.select()` calls
2. **Use utility functions** for common enrichment patterns
3. **Document data fetching patterns** for future reference
4. **Test thoroughly** after making changes
5. **Monitor for schema cache issues** in production

## Rollback Plan

If issues are encountered:
1. Revert to the previous implementation
2. Document the specific error encountered
3. Investigate alternative solutions
4. Consult the Supabase documentation or support

## Questions or Issues

If you encounter any issues with this migration, please:
1. Check the documentation in [supabase-data-fetching-best-practices.md](file:///X:/Qoder/onko/docs/supabase-data-fetching-best-practices.md)
2. Review the updated utility functions in [supabase-utils.ts](file:///X:/Qoder/onko/lib/supabase-utils.ts)
3. Contact the development team for assistance