import { ProductRow, ProductVariantRow, StockMovementRow } from '@/lib/supabase'
import { formatCurrency } from './product-calculations'

/**
 * Calculate total inventory value
 * @param products Array of products
 * @param variants Array of product variants
 * @returns Total inventory value
 */
export function calculateTotalInventoryValue(
  products: ProductRow[], 
  variants: ProductVariantRow[]
): number {
  let totalValue = 0
  
  // Calculate value for master products
  products.forEach(product => {
    const totalCost = (product.base_cost || 0) + (product.packaging_cost || 0)
    totalValue += totalCost * (product.stock_quantity || 0)
  })
  
  // Calculate value for variants
  variants.forEach(variant => {
    totalValue += variant.cost_adjustment * (variant.stock_quantity || 0)
  })
  
  return totalValue
}

/**
 * Get low stock products
 * @param products Array of products
 * @param variants Array of product variants
 * @returns Array of low stock items
 */
export function getLowStockItems(
  products: ProductRow[], 
  variants: ProductVariantRow[]
): (ProductRow | ProductVariantRow)[] {
  const lowStockItems: (ProductRow | ProductVariantRow)[] = []
  
  // Check master products
  products.forEach(product => {
    if (
      product.stock_quantity !== undefined && 
      product.stock_quantity <= (product.low_stock_threshold || 0)
    ) {
      lowStockItems.push(product)
    }
  })
  
  // Check variants
  variants.forEach(variant => {
    if (
      variant.stock_quantity !== undefined && 
      variant.stock_quantity <= (variant.low_stock_threshold || 0)
    ) {
      lowStockItems.push(variant)
    }
  })
  
  return lowStockItems
}

/**
 * Calculate stock turnover rate
 * @param movements Array of stock movements
 * @param periodInDays The period in days to calculate turnover (default: 30)
 * @returns Stock turnover rate
 */
export function calculateStockTurnover(
  movements: StockMovementRow[], 
  periodInDays: number = 30
): number {
  const periodStart = new Date()
  periodStart.setDate(periodStart.getDate() - periodInDays)
  
  // Filter movements in the period
  const periodMovements = movements.filter(
    movement => new Date(movement.created_at) >= periodStart
  )
  
  // Calculate total sold quantity
  const totalSold = periodMovements
    .filter(m => m.movement_type === 'sale')
    .reduce((sum, m) => sum + Math.abs(m.quantity_change), 0)
  
  // Calculate average inventory
  const inventoryValues = periodMovements.map(m => m.new_quantity)
  const averageInventory = inventoryValues.length > 0 
    ? inventoryValues.reduce((sum, val) => sum + val, 0) / inventoryValues.length 
    : 0
  
  // Calculate turnover rate
  return averageInventory > 0 ? totalSold / averageInventory : 0
}

/**
 * Get stock status distribution
 * @param products Array of products
 * @param variants Array of product variants
 * @returns Object with stock status counts
 */
export function getStockStatusDistribution(
  products: ProductRow[], 
  variants: ProductVariantRow[]
): { 
  inStock: number, 
  lowStock: number, 
  outOfStock: number 
} {
  let inStock = 0
  let lowStock = 0
  let outOfStock = 0
  
  // Check master products
  products.forEach(product => {
    const quantity = product.stock_quantity || 0
    const threshold = product.low_stock_threshold || 0
    
    if (quantity === 0) {
      outOfStock++
    } else if (quantity <= threshold) {
      lowStock++
    } else {
      inStock++
    }
  })
  
  // Check variants
  variants.forEach(variant => {
    const quantity = variant.stock_quantity || 0
    const threshold = variant.low_stock_threshold || 0
    
    if (quantity === 0) {
      outOfStock++
    } else if (quantity <= threshold) {
      lowStock++
    } else {
      inStock++
    }
  })
  
  return { inStock, lowStock, outOfStock }
}

/**
 * Get top products by inventory value
 * @param products Array of products
 * @param variants Array of product variants
 * @param limit Number of top products to return (default: 10)
 * @returns Array of top products by value
 */
export function getTopProductsByValue(
  products: ProductRow[], 
  variants: ProductVariantRow[],
  limit: number = 10
): { 
  name: string, 
  sku: string, 
  value: number, 
  quantity: number 
}[] {
  const productValues: { 
    name: string, 
    sku: string, 
    value: number, 
    quantity: number 
  }[] = []
  
  // Process master products
  products.forEach(product => {
    const totalCost = (product.base_cost || 0) + (product.packaging_cost || 0)
    const value = totalCost * (product.stock_quantity || 0)
    
    if (value > 0) {
      productValues.push({
        name: product.name,
        sku: product.base_sku || '',
        value,
        quantity: product.stock_quantity || 0
      })
    }
  })
  
  // Process variants
  variants.forEach(variant => {
    const value = variant.cost_adjustment * (variant.stock_quantity || 0)
    
    if (value > 0) {
      productValues.push({
        name: `${variant.variant_name || 'Variant'} (${variant.sku})`,
        sku: variant.sku,
        value,
        quantity: variant.stock_quantity || 0
      })
    }
  })
  
  // Sort by value and limit
  return productValues
    .sort((a, b) => b.value - a.value)
    .slice(0, limit)
}

/**
 * Generate stock movement summary
 * @param movements Array of stock movements
 * @returns Summary of stock movements by type
 */
export function getStockMovementSummary(
  movements: StockMovementRow[]
): Record<string, { count: number, quantity: number }> {
  const summary: Record<string, { count: number, quantity: number }> = {}
  
  movements.forEach(movement => {
    const type = movement.movement_type
    if (!summary[type]) {
      summary[type] = { count: 0, quantity: 0 }
    }
    
    summary[type].count += 1
    summary[type].quantity += Math.abs(movement.quantity_change)
  })
  
  return summary
}

/**
 * Get inventory aging report
 * @param products Array of products
 * @param variants Array of product variants
 * @returns Aging report data
 */
export function getInventoryAgingReport(
  products: ProductRow[], 
  variants: ProductVariantRow[]
): { 
  category: string, 
  items: number, 
  value: number 
}[] {
  // For demo purposes, we'll categorize by age groups
  // In a real implementation, you would track purchase dates
  const agingData = [
    { category: '0-30 days', items: 0, value: 0 },
    { category: '31-60 days', items: 0, value: 0 },
    { category: '61-90 days', items: 0, value: 0 },
    { category: '90+ days', items: 0, value: 0 }
  ]
  
  // This is a simplified implementation
  // A real implementation would need purchase date tracking
  products.forEach(product => {
    const quantity = product.stock_quantity || 0
    const totalCost = (product.base_cost || 0) + (product.packaging_cost || 0)
    const value = quantity * totalCost
    
    // For demo, we'll put all items in the first category
    if (quantity > 0) {
      agingData[0].items += 1
      agingData[0].value += value
    }
  })
  
  return agingData
}

/**
 * Get variant-specific analytics
 * @param variants Array of product variants
 * @returns Analytics data for variants
 */
export function getVariantAnalytics(
  variants: ProductVariantRow[]
): {
  totalVariants: number,
  averageStockPerVariant: number,
  lowStockVariants: number,
  outOfStockVariants: number,
  totalVariantValue: number,
  variantStockDistribution: { range: string; count: number }[]
} {
  const totalVariants = variants.length
  const totalStock = variants.reduce((sum, variant) => sum + (variant.stock_quantity || 0), 0)
  const averageStockPerVariant = totalVariants > 0 ? totalStock / totalVariants : 0
  
  const lowStockVariants = variants.filter(variant => 
    variant.stock_quantity !== undefined && 
    variant.stock_quantity > 0 &&
    variant.stock_quantity <= (variant.low_stock_threshold || 0)
  ).length
  
  const outOfStockVariants = variants.filter(variant => 
    variant.stock_quantity !== undefined && 
    variant.stock_quantity === 0
  ).length
  
  const totalVariantValue = variants.reduce((sum, variant) => {
    return sum + (variant.cost_adjustment * (variant.stock_quantity || 0))
  }, 0)
  
  // Stock distribution analysis
  const variantStockDistribution = [
    { range: '0', count: 0 },
    { range: '1-10', count: 0 },
    { range: '11-50', count: 0 },
    { range: '51-100', count: 0 },
    { range: '100+', count: 0 }
  ]
  
  variants.forEach(variant => {
    const stock = variant.stock_quantity || 0
    if (stock === 0) {
      variantStockDistribution[0].count++
    } else if (stock <= 10) {
      variantStockDistribution[1].count++
    } else if (stock <= 50) {
      variantStockDistribution[2].count++
    } else if (stock <= 100) {
      variantStockDistribution[3].count++
    } else {
      variantStockDistribution[4].count++
    }
  })
  
  return {
    totalVariants,
    averageStockPerVariant,
    lowStockVariants,
    outOfStockVariants,
    totalVariantValue,
    variantStockDistribution
  }
}

/**
 * Get attribute-based analytics for variants
 * @param variants Array of product variants
 * @returns Analytics data grouped by variant attributes
 */
export function getAttributeAnalytics(
  variants: ProductVariantRow[]
): {
  bySize: Record<string, { count: number; totalStock: number }>,
  byColor: Record<string, { count: number; totalStock: number }>,
  byMaterial: Record<string, { count: number; totalStock: number }>,
  byStyle: Record<string, { count: number; totalStock: number }>
} {
  const bySize: Record<string, { count: number; totalStock: number }> = {}
  const byColor: Record<string, { count: number; totalStock: number }> = {}
  const byMaterial: Record<string, { count: number; totalStock: number }> = {}
  const byStyle: Record<string, { count: number; totalStock: number }> = {}
  
  variants.forEach(variant => {
    // Size analytics
    if (variant.size) {
      if (!bySize[variant.size]) {
        bySize[variant.size] = { count: 0, totalStock: 0 }
      }
      bySize[variant.size].count++
      bySize[variant.size].totalStock += variant.stock_quantity || 0
    }
    
    // Color analytics
    if (variant.color) {
      if (!byColor[variant.color]) {
        byColor[variant.color] = { count: 0, totalStock: 0 }
      }
      byColor[variant.color].count++
      byColor[variant.color].totalStock += variant.stock_quantity || 0
    }
    
    // Material analytics
    if (variant.material) {
      if (!byMaterial[variant.material]) {
        byMaterial[variant.material] = { count: 0, totalStock: 0 }
      }
      byMaterial[variant.material].count++
      byMaterial[variant.material].totalStock += variant.stock_quantity || 0
    }
    
    // Style analytics
    if (variant.style) {
      if (!byStyle[variant.style]) {
        byStyle[variant.style] = { count: 0, totalStock: 0 }
      }
      byStyle[variant.style].count++
      byStyle[variant.style].totalStock += variant.stock_quantity || 0
    }
  })
  
  return { bySize, byColor, byMaterial, byStyle }
}

/**
 * Get variant performance metrics
 * @param variants Array of product variants
 * @param movements Array of stock movements
 * @returns Performance metrics for variants
 */
export function getVariantPerformanceMetrics(
  variants: ProductVariantRow[],
  movements: StockMovementRow[]
): {
  fastMovingVariants: { variant: ProductVariantRow; turnover: number }[],
  slowMovingVariants: { variant: ProductVariantRow; turnover: number }[],
  topValueVariants: ProductVariantRow[]
} {
  // Calculate turnover for each variant
  const variantTurnover: Record<string, number> = {}
  
  // Filter movements for variants only
  const variantMovements = movements.filter(m => m.variant_id)
  
  // Calculate turnover rate for each variant
  variants.forEach(variant => {
    const variantId = variant.id
    const variantMovementsForId = variantMovements.filter(m => m.variant_id === variantId)
    
    // Calculate total sold quantity for this variant in the period
    const totalSold = variantMovementsForId
      .filter(m => m.movement_type === 'sale')
      .reduce((sum, m) => sum + Math.abs(m.quantity_change), 0)
    
    // Calculate average inventory for this variant
    const inventoryValues = variantMovementsForId.map(m => m.new_quantity)
    const averageInventory = inventoryValues.length > 0 
      ? inventoryValues.reduce((sum, val) => sum + val, 0) / inventoryValues.length 
      : variant.stock_quantity || 0
    
    // Calculate turnover rate
    variantTurnover[variantId] = averageInventory > 0 ? totalSold / averageInventory : 0
  })
  
  // Sort variants by turnover rate
  const variantsWithTurnover = variants.map(variant => ({
    variant,
    turnover: variantTurnover[variant.id] || 0
  }))
  
  const fastMovingVariants = [...variantsWithTurnover]
    .sort((a, b) => b.turnover - a.turnover)
    .slice(0, 10)
  
  const slowMovingVariants = [...variantsWithTurnover]
    .sort((a, b) => a.turnover - b.turnover)
    .slice(0, 10)
  
  // Top value variants
  const topValueVariants = [...variants]
    .sort((a, b) => {
      const aValue = (a.cost_adjustment * (a.stock_quantity || 0))
      const bValue = (b.cost_adjustment * (b.stock_quantity || 0))
      return bValue - aValue
    })
    .slice(0, 10)
  
  return {
    fastMovingVariants,
    slowMovingVariants,
    topValueVariants
  }
}

/**
 * Format analytics data for display
 * @param data The analytics data to format
 * @returns Formatted data
 */
export function formatAnalyticsData(data: any): any {
  if (typeof data === 'number') {
    // If it's a currency value
    if (data > 1000) {
      return formatCurrency(data)
    }
    // If it's a percentage or small number
    return data.toFixed(2)
  }
  
  if (Array.isArray(data)) {
    return data.map(item => formatAnalyticsData(item))
  }
  
  if (typeof data === 'object' && data !== null) {
    const formatted: any = {}
    for (const key in data) {
      formatted[key] = formatAnalyticsData(data[key])
    }
    return formatted
  }
  
  return data
}