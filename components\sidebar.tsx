'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { 
  LayoutDashboard, 
  Package, 
  Receipt, 
  BarChart3, 
  Users,
  Settings,
  Menu,
  X,
  ChevronDown,
  ChevronRight,
  Bell
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { PermissionGuard } from '@/components/auth/PermissionGuard'

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },
  { 
    name: 'Products', 
    href: '/dashboard/products', 
    icon: Package,
    children: [
      { name: 'Products Catalog', href: '/dashboard/products' },
      { name: 'Inventory', href: '/dashboard/products/inventory' },
      { name: 'Purchase Orders', href: '/dashboard/products/purchase-orders' },
      { name: 'Analytics', href: '/dashboard/products/analytics' },
      { name: 'Setting<PERSON>', href: '/dashboard/products/settings' },
    ]
  },
  { 
    name: 'Expenses', 
    href: '/dashboard/expenses', 
    icon: Receipt,
    children: [
      { name: 'Expenses', href: '/dashboard/expenses' },
      { name: 'Analytics', href: '/dashboard/expenses/analytics' },
      { name: 'Settings', href: '/dashboard/expenses/settings' },
    ]
  },
  { 
    name: 'Sales', 
    href: '/dashboard/sales/orders', 
    icon: Receipt,
    children: [
      { name: 'Orders', href: '/dashboard/sales/orders' },
      { name: 'Invoices', href: '/dashboard/sales/invoices' },
      { name: 'Customers', href: '/dashboard/sales/customers' },
      { name: 'Analytics', href: '/dashboard/sales/analytics' },
    ]
  },
  { 
    name: 'Reporting', 
    href: '/dashboard/reporting', 
    icon: BarChart3,
    children: [
      { name: 'Profit & Loss', href: '/dashboard/reporting/profit-loss' },
      { name: 'Cash Flow', href: '/dashboard/reporting/cash-flow' },
    ],
    requiresPermission: true
  },
  { name: 'Settings', href: '/dashboard/settings', icon: Settings, requiresPermission: true },
]

export function Sidebar() {
  const [isOpen, setIsOpen] = useState(false)
  const [expandedMenus, setExpandedMenus] = useState<Record<string, boolean>>({
    products: false,
    expenses: false,
    sales: false,
    reporting: false,
  })
  const pathname = usePathname()

  // Automatically manage expanded state based on current route
  useEffect(() => {
    const newExpandedMenus = { ...expandedMenus }
    
    // Check if we're on any products sub-page
    if (pathname.startsWith('/dashboard/products')) {
      newExpandedMenus.products = true
    } else {
      // Collapse products menu when navigating away
      newExpandedMenus.products = false
    }
    
    // Check if we're on any expenses sub-page
    if (pathname.startsWith('/dashboard/expenses')) {
      newExpandedMenus.expenses = true
    } else {
      // Collapse expenses menu when navigating away
      newExpandedMenus.expenses = false
    }
    
    // Check if we're on any sales sub-page
    if (pathname.startsWith('/dashboard/sales')) {
      newExpandedMenus.sales = true
    } else {
      // Collapse sales menu when navigating away
      newExpandedMenus.sales = false
    }
    
    // Check if we're on any reporting sub-page
    if (pathname.startsWith('/dashboard/reporting')) {
      newExpandedMenus.reporting = true
    } else {
      // Collapse reporting menu when navigating away
      newExpandedMenus.reporting = false
    }
    
    setExpandedMenus(newExpandedMenus)
  }, [pathname])

  const toggleMenu = (menuName: string) => {
    setExpandedMenus(prev => ({
      ...prev,
      [menuName]: !prev[menuName]
    }))
  }

  const renderNavItem = (item: any) => {
    const Icon = item.icon
    const isActive = pathname === item.href || 
      (item.href !== '/dashboard' && pathname.startsWith(item.href))
    
    // Check if this is an item with children
    if (item.children) {
      const isExpanded = expandedMenus[item.name.toLowerCase()]
      const hasActiveChild = item.children.some((child: any) => {
        return pathname === child.href || pathname.startsWith(child.href)
      })
      
      return (
        <div key={item.name}>
          {item.requiresPermission ? (
            <PermissionGuard allowedRoles={['admin', 'owner']}>
              <button
                onClick={() => toggleMenu(item.name.toLowerCase())}
                className={cn(
                  "flex items-center justify-between w-full px-3 py-2 text-xs rounded-lg transition-colors",
                  isActive || hasActiveChild
                    ? "bg-[hsl(var(--sidebar-active-bg))] text-[hsl(var(--sidebar-active-text))]"
                    : "text-[hsl(var(--sidebar-text))] hover:bg-[hsl(var(--sidebar-hover-bg))] hover:text-[hsl(var(--sidebar-text-hover))]"
                )}
              >
                <div className="flex items-center">
                  <Icon className={cn("mr-2 h-4 w-4", isActive || hasActiveChild ? "text-[hsl(var(--sidebar-icon-active))]" : "")} />
                  <span>{item.name}</span>
                </div>
                {isExpanded ? (
                  <ChevronDown className={cn("h-4 w-4", isActive || hasActiveChild ? "text-[hsl(var(--sidebar-icon-active))]" : "")} />
                ) : (
                  <ChevronRight className={cn("h-4 w-4", isActive || hasActiveChild ? "text-[hsl(var(--sidebar-icon-active))]" : "")} />
                )}
              </button>
              
              {isExpanded && (
                <div className="ml-4 mt-1 space-y-1 border-l border-[hsl(var(--sidebar-border))] pl-4">
                  {item.children.map((child: any) => {
                    const isChildActive = pathname === child.href
                    
                    return (
                      <Link
                        key={child.name}
                        href={child.href}
                        className={cn(
                          "flex items-center px-3 py-2 text-xs rounded-lg transition-colors",
                          isChildActive
                            ? "bg-[hsl(var(--sidebar-submenu-active-bg))] text-[hsl(var(--sidebar-submenu-active-text))]"
                            : "text-[hsl(var(--sidebar-text))] hover:bg-[hsl(var(--sidebar-hover-bg))] hover:text-[hsl(var(--sidebar-text-hover))]"
                        )}
                        onClick={(e) => {
                          e.stopPropagation()
                          setIsOpen(false)
                        }}
                      >
                        <span>{child.name}</span>
                      </Link>
                    )
                  })}
                </div>
              )}
            </PermissionGuard>
          ) : (
            <>
              <button
                onClick={() => toggleMenu(item.name.toLowerCase())}
                className={cn(
                  "flex items-center justify-between w-full px-3 py-2 text-xs rounded-lg transition-colors",
                  isActive || hasActiveChild
                    ? "bg-[hsl(var(--sidebar-active-bg))] text-[hsl(var(--sidebar-active-text))]"
                    : "text-[hsl(var(--sidebar-text))] hover:bg-[hsl(var(--sidebar-hover-bg))] hover:text-[hsl(var(--sidebar-text-hover))]"
                )}
              >
                <div className="flex items-center">
                  <Icon className={cn("mr-2 h-4 w-4", isActive || hasActiveChild ? "text-[hsl(var(--sidebar-icon-active))]" : "")} />
                  <span>{item.name}</span>
                </div>
                {isExpanded ? (
                  <ChevronDown className={cn("h-4 w-4", isActive || hasActiveChild ? "text-[hsl(var(--sidebar-icon-active))]" : "")} />
                ) : (
                  <ChevronRight className={cn("h-4 w-4", isActive || hasActiveChild ? "text-[hsl(var(--sidebar-icon-active))]" : "")} />
                )}
              </button>
              
              {isExpanded && (
                <div className="ml-4 mt-1 space-y-1 border-l border-[hsl(var(--sidebar-border))] pl-4">
                  {item.children.map((child: any) => {
                    const isChildActive = pathname === child.href
                    
                    return (
                      <Link
                        key={child.name}
                        href={child.href}
                        className={cn(
                          "flex items-center px-3 py-2 text-xs rounded-lg transition-colors",
                          isChildActive
                            ? "bg-[hsl(var(--sidebar-submenu-active-bg))] text-[hsl(var(--sidebar-submenu-active-text))]"
                            : "text-[hsl(var(--sidebar-text))] hover:bg-[hsl(var(--sidebar-hover-bg))] hover:text-[hsl(var(--sidebar-text-hover))]"
                        )}
                        onClick={(e) => {
                          e.stopPropagation()
                          setIsOpen(false)
                        }}
                      >
                        <span>{child.name}</span>
                      </Link>
                    )
                  })}
                </div>
              )}
            </>
          )}
        </div>
      )
    }
    
    // Regular menu item (no children)
    if (item.requiresPermission) {
      return (
        <PermissionGuard key={item.name} allowedRoles={['admin', 'owner']}>
          <Link
            href={item.href}
            className={cn(
              "flex items-center px-3 py-2 text-xs rounded-lg transition-colors",
              isActive
                ? "bg-[hsl(var(--sidebar-active-bg))] text-[hsl(var(--sidebar-active-text))]"
                : "text-[hsl(var(--sidebar-text))] hover:bg-[hsl(var(--sidebar-hover-bg))] hover:text-[hsl(var(--sidebar-text-hover))]"
            )}
            onClick={() => setIsOpen(false)}
          >
            <Icon className={cn("mr-2 h-4 w-4", isActive ? "text-[hsl(var(--sidebar-icon-active))]" : "")} />
            <span>{item.name}</span>
          </Link>
        </PermissionGuard>
      )
    }
    
    return (
      <Link
        key={item.name}
        href={item.href}
        className={cn(
          "flex items-center px-3 py-2 text-xs rounded-lg transition-colors",
          isActive
            ? "bg-[hsl(var(--sidebar-active-bg))] text-[hsl(var(--sidebar-active-text))]"
            : "text-[hsl(var(--sidebar-text))] hover:bg-[hsl(var(--sidebar-hover-bg))] hover:text-[hsl(var(--sidebar-text-hover))]"
        )}
        onClick={() => setIsOpen(false)}
      >
        <Icon className={cn("mr-2 h-4 w-4", isActive ? "text-[hsl(var(--sidebar-icon-active))]" : "")} />
        <span>{item.name}</span>
      </Link>
    )
  }

  return (
    <>
      {/* Mobile menu button */}
      <Button
        variant="ghost"
        size="icon"
        className="md:hidden fixed top-4 left-4 z-50 text-white"
        onClick={() => setIsOpen(!isOpen)}
      >
        {isOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
      </Button>

      {/* Sidebar */}
      <div className={cn(
        "fixed inset-y-0 left-0 z-50 w-56 text-[hsl(var(--sidebar-text))] shadow-md transform transition-transform duration-300 ease-in-out md:translate-x-0 md:static md:inset-0 md:z-auto border-r border-[hsl(var(--sidebar-border))]",
        isOpen ? "translate-x-0" : "-translate-x-full",
        "bg-[hsl(var(--sidebar-bg))]"
      )}>
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center h-14 px-4 border-b border-[hsl(var(--sidebar-border))]">
            <h1 className="text-xl font-bold text-[hsl(var(--sidebar-text))]">ONKO</h1>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 pt-3 pb-4">
            <div className="space-y-1">
              {navigation.map(renderNavItem)}
            </div>
          </nav>
        </div>
      </div>

      {/* Overlay for mobile */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 md:hidden"
          onClick={() => setIsOpen(false)}
        />
      )}
    </>
  )
}