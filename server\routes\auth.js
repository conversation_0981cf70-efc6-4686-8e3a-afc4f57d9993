const authController = require('../controllers/auth');
const authMiddleware = require('../middleware/auth');

class AuthRoutes {
  async handleRequest(req, res, pathname) {
    console.log('🔐 Auth routes - handling request:', pathname);
    
    // Setup middleware
    if (!authMiddleware.setupApiMiddleware(req, res)) {
      console.log('🔐 Auth routes - middleware handled request (likely OPTIONS)');
      return; // Request already handled (e.g., OPTIONS)
    }

    console.log('🔐 Auth routes - routing to handler for:', pathname);
    
    // Route to appropriate handler
    if (pathname === '/api/auth/signup') {
      console.log('🔐 Auth routes - calling handleSignUp');
      await this.handleSignUp(req, res);
    } else if (pathname === '/api/auth/signin') {
      console.log('🔐 Auth routes - calling handleSignIn');
      await this.handleSignIn(req, res);
    } else if (pathname === '/api/auth/signout') {
      await this.handleSignOut(req, res);
    } else if (pathname === '/api/auth/validate') {
      await this.handleValidateToken(req, res);
    } else if (pathname === '/api/auth/refresh') {
      await this.handleRefreshToken(req, res);
    } else {
      console.log('🔐 Auth routes - no handler found for:', pathname);
      this.handleNotFound(res);
    }
  }

  async handleSignUp(req, res) {
    console.log('🔐 handleSignUp - method:', req.method);
    
    if (req.method !== 'POST') {
      console.log('🔐 handleSignUp - method not allowed');
      this.handleMethodNotAllowed(res, ['POST']);
      return;
    }

    console.log('🔐 handleSignUp - reading request body');
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
      console.log('🔐 handleSignUp - received chunk, body length:', body.length);
    });

    req.on('end', async () => {
      try {
        console.log('🔐 handleSignUp - body complete:', body);
        const { email, password, businessName } = JSON.parse(body);
        console.log('🔐 handleSignUp - parsed data:', { email, businessName, password: '***' });
        
        // Basic validation
        if (!email || !password) {
          console.log('🔐 handleSignUp - validation failed: missing email or password');
          this.sendError(res, 400, 'Email and password are required');
          return;
        }

        if (password.length < 6) {
          console.log('🔐 handleSignUp - validation failed: password too short');
          this.sendError(res, 400, 'Password must be at least 6 characters long');
          return;
        }

        console.log('🔐 handleSignUp - calling authController.signUp');
        const result = await authController.signUp(email, password, { businessName });
        console.log('🔐 handleSignUp - authController result:', result);
        this.sendSuccess(res, result, 201);
      } catch (error) {
        console.error('🔐 Auth Error - Sign Up:', error);
        this.sendError(res, 400, `Registration error: ${error.message}`);
      }
    });
  }

  async handleSignIn(req, res) {
    if (req.method !== 'POST') {
      this.handleMethodNotAllowed(res, ['POST']);
      return;
    }

    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });

    req.on('end', async () => {
      try {
        const { email, password } = JSON.parse(body);
        
        // Basic validation
        if (!email || !password) {
          this.sendError(res, 400, 'Email and password are required');
          return;
        }

        const result = await authController.signIn(email, password);
        this.sendSuccess(res, result);
      } catch (error) {
        console.error('Auth Error - Sign In:', error);
        this.sendError(res, 401, `Authentication error: ${error.message}`);
      }
    });
  }

  async handleSignOut(req, res) {
    if (req.method !== 'POST') {
      this.handleMethodNotAllowed(res, ['POST']);
      return;
    }

    try {
      const token = authMiddleware.extractToken(req);
      const result = await authController.signOut(token);
      this.sendSuccess(res, result);
    } catch (error) {
      console.error('Auth Error - Sign Out:', error);
      this.sendError(res, 400, `Sign out error: ${error.message}`);
    }
  }

  async handleValidateToken(req, res) {
    if (req.method !== 'POST') {
      this.handleMethodNotAllowed(res, ['POST']);
      return;
    }

    try {
      const token = authMiddleware.extractToken(req);
      
      if (!token) {
        this.sendError(res, 400, 'Token is required');
        return;
      }

      const result = await authController.validateToken(token);
      this.sendSuccess(res, result);
    } catch (error) {
      console.error('Auth Error - Validate Token:', error);
      this.sendError(res, 400, `Token validation error: ${error.message}`);
    }
  }

  async handleRefreshToken(req, res) {
    if (req.method !== 'POST') {
      this.handleMethodNotAllowed(res, ['POST']);
      return;
    }

    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });

    req.on('end', async () => {
      try {
        const { refreshToken } = JSON.parse(body);
        
        if (!refreshToken) {
          this.sendError(res, 400, 'Refresh token is required');
          return;
        }

        const result = await authController.refreshToken(refreshToken);
        this.sendSuccess(res, result);
      } catch (error) {
        console.error('Auth Error - Refresh Token:', error);
        this.sendError(res, 400, `Token refresh error: ${error.message}`);
      }
    });
  }

  // Helper methods
  sendSuccess(res, data, statusCode = 200) {
    res.writeHead(statusCode, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ 
      success: true, 
      data: data,
      timestamp: new Date().toISOString()
    }));
  }

  sendError(res, statusCode, message) {
    res.writeHead(statusCode, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ 
      success: false, 
      error: message,
      timestamp: new Date().toISOString()
    }));
  }

  handleNotFound(res) {
    this.sendError(res, 404, 'Auth endpoint not found');
  }

  handleMethodNotAllowed(res, allowedMethods) {
    res.setHeader('Allow', allowedMethods.join(', '));
    this.sendError(res, 405, `Method not allowed. Allowed methods: ${allowedMethods.join(', ')}`);
  }
}

module.exports = new AuthRoutes();