/**
 * Period calculation utilities for analytics
 * Handles date range calculations, period comparisons, and trend data generation
 */

export type PeriodType = 'month' | 'quarter' | 'year'

export interface DateRange {
  start: Date
  end: Date
}

export interface PeriodRanges {
  current: DateRange
  previous: DateRange
  currentQuarter: DateRange
  currentYear: DateRange
}

/**
 * Generate date ranges for a specific period type
 */
export function getPeriodRanges(periodType: PeriodType, referenceDate = new Date()): PeriodRanges {
  const year = referenceDate.getFullYear()
  const month = referenceDate.getMonth()

  switch (periodType) {
    case 'month':
      return {
        current: {
          start: new Date(year, month, 1),
          end: new Date(year, month + 1, 0)
        },
        previous: {
          start: new Date(year, month - 1, 1),
          end: new Date(year, month, 0)
        },
        currentQuarter: {
          start: new Date(year, Math.floor(month / 3) * 3, 1),
          end: new Date(year, Math.floor(month / 3) * 3 + 3, 0)
        },
        currentYear: {
          start: new Date(year, 0, 1),
          end: new Date(year, 11, 31)
        }
      }

    case 'quarter':
      const currentQuarterStart = Math.floor(month / 3) * 3
      const prevQuarterStart = currentQuarterStart - 3
      const prevQuarterYear = prevQuarterStart < 0 ? year - 1 : year
      const adjustedPrevQuarterStart = prevQuarterStart < 0 ? 9 : prevQuarterStart

      return {
        current: {
          start: new Date(year, currentQuarterStart, 1),
          end: new Date(year, currentQuarterStart + 3, 0)
        },
        previous: {
          start: new Date(prevQuarterYear, adjustedPrevQuarterStart, 1),
          end: new Date(prevQuarterYear, adjustedPrevQuarterStart + 3, 0)
        },
        currentQuarter: {
          start: new Date(year, currentQuarterStart, 1),
          end: new Date(year, currentQuarterStart + 3, 0)
        },
        currentYear: {
          start: new Date(year, 0, 1),
          end: new Date(year, 11, 31)
        }
      }

    case 'year':
      return {
        current: {
          start: new Date(year, 0, 1),
          end: new Date(year, 11, 31)
        },
        previous: {
          start: new Date(year - 1, 0, 1),
          end: new Date(year - 1, 11, 31)
        },
        currentQuarter: {
          start: new Date(year, Math.floor(month / 3) * 3, 1),
          end: new Date(year, Math.floor(month / 3) * 3 + 3, 0)
        },
        currentYear: {
          start: new Date(year, 0, 1),
          end: new Date(year, 11, 31)
        }
      }
  }
}

/**
 * Generate period labels based on type
 */
export function getPeriodLabels(periodType: PeriodType): { current: string; previous: string } {
  switch (periodType) {
    case 'month':
      return { current: 'This Month', previous: 'Last Month' }
    case 'quarter':
      return { current: 'This Quarter', previous: 'Last Quarter' }
    case 'year':
      return { current: 'This Year', previous: 'Last Year' }
  }
}

/**
 * Generate trend data based on period type
 */
export function generateTrendData(
  periodType: PeriodType, 
  expenses: any[], 
  referenceDate = new Date()
): Array<{ period: string; amount: number; count: number }> {
  const trendData = []
  const year = referenceDate.getFullYear()
  const month = referenceDate.getMonth()

  switch (periodType) {
    case 'month':
      // Last 6 months
      for (let i = 5; i >= 0; i--) {
        const date = new Date(year, month - i, 1)
        const periodStart = new Date(date.getFullYear(), date.getMonth(), 1)
        const periodEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0)
        const periodExpenses = filterExpensesByDateRange(expenses, periodStart, periodEnd)
        
        trendData.push({
          period: date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
          amount: periodExpenses.reduce((sum, exp) => sum + exp.amount, 0),
          count: periodExpenses.length
        })
      }
      break

    case 'quarter':
      // Last 4 quarters
      for (let i = 3; i >= 0; i--) {
        const quarterStart = Math.floor(month / 3) * 3 - (i * 3)
        let quarterYear = year
        let adjustedQuarterStart = quarterStart

        if (quarterStart < 0) {
          quarterYear = year - Math.ceil(Math.abs(quarterStart) / 12)
          adjustedQuarterStart = 12 + (quarterStart % 12)
        }

        const periodStart = new Date(quarterYear, adjustedQuarterStart, 1)
        const periodEnd = new Date(quarterYear, adjustedQuarterStart + 3, 0)
        const periodExpenses = filterExpensesByDateRange(expenses, periodStart, periodEnd)
        
        const quarterNumber = Math.floor(adjustedQuarterStart / 3) + 1
        trendData.push({
          period: `Q${quarterNumber} ${quarterYear}`,
          amount: periodExpenses.reduce((sum, exp) => sum + exp.amount, 0),
          count: periodExpenses.length
        })
      }
      break

    case 'year':
      // Last 5 years
      for (let i = 4; i >= 0; i--) {
        const yearValue = year - i
        const periodStart = new Date(yearValue, 0, 1)
        const periodEnd = new Date(yearValue, 11, 31)
        const periodExpenses = filterExpensesByDateRange(expenses, periodStart, periodEnd)
        
        trendData.push({
          period: yearValue.toString(),
          amount: periodExpenses.reduce((sum, exp) => sum + exp.amount, 0),
          count: periodExpenses.length
        })
      }
      break
  }

  return trendData
}

/**
 * Filter expenses by date range
 */
export function filterExpensesByDateRange(expenses: any[], start: Date, end: Date): any[] {
  return expenses.filter(expense => {
    const expenseDate = new Date(expense.expense_date || expense.created_at)
    return expenseDate >= start && expenseDate <= end
  })
}

/**
 * Calculate percentage change between two values
 */
export function calculatePercentageChange(current: number, previous: number): number {
  if (previous === 0) return current > 0 ? 100 : 0
  return ((current - previous) / previous) * 100
}

/**
 * Get trend chart title based on period type
 */
export function getTrendChartTitle(periodType: PeriodType): string {
  switch (periodType) {
    case 'month':
      return '6-Month Expense Trend'
    case 'quarter':
      return '4-Quarter Expense Trend'
    case 'year':
      return '5-Year Expense Trend'
  }
}

/**
 * Get category breakdown title based on period type
 */
export function getCategoryBreakdownTitle(periodType: PeriodType): string {
  const labels = getPeriodLabels(periodType)
  return `Category Breakdown - ${labels.current}`
}