'use client'

import { useState, useEffect, useMemo } from 'react'
import { Button } from '@/components/ui/button'
import { useToast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import { getSupabaseClient } from '@/lib/supabase'
import { useAuth } from '@/contexts/auth-context'
import { AdjustStockModal } from '../modals/AdjustStockModal'
import { StockHistoryModal } from '../modals/StockHistoryModal'
import { PurchaseOrderModal } from '../modals/PurchaseOrderModal'
import { InventoryFilterBar } from './InventoryFilterBar'
import { ActiveFiltersDisplay } from './ActiveFiltersDisplay'
import { InventoryAdvancedFilters } from './InventoryAdvancedFilters'
import { InventoryTable } from './InventoryTable'
import { InventoryTablePagination } from './InventoryTablePagination'
import { useInventoryFilters } from './useInventoryFilters'
import { useInventoryData } from './useInventoryData'
import { useModalManagement } from './useModalManagement'
import { exportToCSV } from './exportToCSV'
import { checkItemStockHistory } from './checkItemStockHistory'
import { RefreshCw } from 'lucide-react'
import { type ExtendedProductRow } from './types'
import { getProductsForOrganization } from '@/lib/supabase'

interface EnhancedInventoryTableProps {
  inventoryItems: any[]
  isMobile?: boolean
  className?: string
  onInventoryItemsChange?: (items: any[]) => void
}

export function EnhancedInventoryTable({
  inventoryItems,
  isMobile = false,
  className,
  onInventoryItemsChange
}: EnhancedInventoryTableProps) {
  const { user, organizationId } = useAuth()
  const [isFilterSheetOpen, setIsFilterSheetOpen] = useState(false)
  const [isExpanded, setIsExpanded] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(10)
  const [openMenuId, setOpenMenuId] = useState<string | null>(null)
  const [expandedProducts, setExpandedProducts] = useState<Record<string, boolean>>({})
  const [itemsWithHistory, setItemsWithHistory] = useState<Record<string, boolean>>({})
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [products, setProducts] = useState<ExtendedProductRow[]>([])

  const { toast } = useToast()
  // Use getSupabaseClient instead of createSupabaseClient to ensure we get a fresh client
  const supabase = getSupabaseClient()

  // Fetch products data for variable product handling
  useEffect(() => {
    const fetchProducts = async () => {
      if (!organizationId) return
      
      try {
        const data = await getProductsForOrganization(organizationId)
        
        // Transform data to include category name and process variants
        const transformedProducts = (data || []).map(product => {
          // Handle categories which might be an array or object
          let categoryName = null
          if (product.categories) {
            if (Array.isArray(product.categories) && product.categories.length > 0) {
              categoryName = (product.categories[0] as any)?.name || null
            } else if (!Array.isArray(product.categories) && (product.categories as any)?.name) {
              categoryName = (product.categories as any).name
            }
          }
          
          return {
            ...product,
            category_name: categoryName,
            variants: product.product_variants || []
          }
        })
        
        setProducts(transformedProducts)
      } catch (error) {
        console.error('Error fetching products:', error)
      }
    }
    
    fetchProducts()
  }, [organizationId])

  // Use our custom hooks
  const {
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    activeFilterCount,
    clearAllFilters,
    handleStockStatusToggle
  } = useInventoryFilters()

  // Since we're now passing pre-processed inventoryItems, we need to filter them
  const {
    filteredInventoryItems,
    availableOptions
  } = useInventoryData(inventoryItems, searchQuery, filters)

  const {
    isAdjustStockModalOpen,
    setIsAdjustStockModalOpen,
    isStockHistoryModalOpen,
    setIsStockHistoryModalOpen,
    isPurchaseOrderModalOpen,
    setIsPurchaseOrderModalOpen,
    selectedItem,
    setSelectedItem
  } = useModalManagement()

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Check if click is outside any dropdown menu
      const menus = document.querySelectorAll('.dropdown-menu');
      let clickedInsideMenu = false;
      
      menus.forEach(menu => {
        if (menu.contains(event.target as Node)) {
          clickedInsideMenu = true;
        }
      });
      
      if (!clickedInsideMenu) {
        setOpenMenuId(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Listen for export event
  useEffect(() => {
    const handleExportInventoryCSV = () => {
      exportToCSV([], inventoryItems, searchQuery, filters)
    }

    window.addEventListener('exportInventoryCSV', handleExportInventoryCSV)
    return () => {
      window.removeEventListener('exportInventoryCSV', handleExportInventoryCSV)
    }
  }, [inventoryItems, searchQuery, filters])

  // Listen for refresh event
  useEffect(() => {
    const handleRefreshInventoryData = () => {
      setIsRefreshing(true)
      // Simulate a small delay to show the refresh indicator
      setTimeout(() => {
        setIsRefreshing(false)
      }, 500)
    }

    window.addEventListener('refreshInventoryData', handleRefreshInventoryData)
    return () => {
      window.removeEventListener('refreshInventoryData', handleRefreshInventoryData)
    }
  }, [])

  // Pagination logic
  const paginatedItems = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage
    return filteredInventoryItems.slice(startIndex, startIndex + itemsPerPage)
  }, [filteredInventoryItems, currentPage, itemsPerPage])

  // Calculate total pages
  const totalPages = Math.ceil(filteredInventoryItems.length / itemsPerPage)

  // Notify parent of filtered items
  useEffect(() => {
    if (onInventoryItemsChange) {
      onInventoryItemsChange(filteredInventoryItems)
    }
  }, [filteredInventoryItems, onInventoryItemsChange])

  // Add user permission verification function
  const verifyUserPermissions = async (supabase: any, userId: string) => {
    try {
      // Test query to verify user can access their own data
      const { data, error } = await supabase
        .from('products')
        .select('id')
        .eq('organization_id', organizationId)
        .eq('is_active', true)  // Only check active products
        .limit(1);
      
      if (error) {
        console.error('Permission verification failed:', error);
        // Check if it's a permission error
        if (error.code === '42501' || error.message?.includes('permission denied')) {
          return { success: false, error: 'Permission denied. Please check your database policies.' };
        }
        return { success: false, error: error.message || 'Unknown error' };
      }
      
      return { success: true, error: null };
    } catch (error: any) {
      console.error('Error verifying permissions:', error);
      return { success: false, error: error.message || 'Failed to verify permissions' };
    }
  };

  return (
    <div className={className}>
      {/* Top tier: Always visible filters */}
      <InventoryFilterBar
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        filters={filters}
        setFilters={setFilters}
        isMobile={isMobile}
        isFilterSheetOpen={isFilterSheetOpen}
        setIsFilterSheetOpen={setIsFilterSheetOpen}
        isExpanded={isExpanded}
        setIsExpanded={setIsExpanded}
        activeFilterCount={activeFilterCount}
        clearAllFilters={clearAllFilters}
        availableOptions={availableOptions}
        handleStockStatusToggle={handleStockStatusToggle}
      />
      
      {/* Active filters indicator */}
      <ActiveFiltersDisplay
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        filters={filters}
        setFilters={setFilters}
      />
      
      {/* Second tier: Expandable advanced filters */}
      {!isMobile && isExpanded && (
        <InventoryAdvancedFilters
          filters={filters}
          setFilters={setFilters}
          activeFilterCount={activeFilterCount}
          clearAllFilters={clearAllFilters}
          availableOptions={availableOptions}
          setIsExpanded={setIsExpanded}
        />
      )}
      
      {/* Inventory Table */}
      {/* Table and Pagination Container */}
      <div>
        {/* Table Container */}
        <InventoryTable
          paginatedItems={paginatedItems}
          products={products} // Pass the actual products data
          expandedProducts={expandedProducts}
          setExpandedProducts={setExpandedProducts}
          openMenuId={openMenuId}
          setOpenMenuId={setOpenMenuId}
          checkItemStockHistory={(item: any) => organizationId ? checkItemStockHistory(item, itemsWithHistory, setItemsWithHistory, organizationId) : Promise.resolve(false)}
          itemsWithHistory={itemsWithHistory}
          setSelectedItem={setSelectedItem}
          setIsAdjustStockModalOpen={setIsAdjustStockModalOpen}
          setIsStockHistoryModalOpen={setIsStockHistoryModalOpen}
          setIsPurchaseOrderModalOpen={setIsPurchaseOrderModalOpen}
        />
        
        {/* Pagination */}
        <InventoryTablePagination
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
          itemsPerPage={itemsPerPage}
          setItemsPerPage={setItemsPerPage}
          filteredInventoryItemsCount={filteredInventoryItems.length}
          totalPages={totalPages}
        />
      </div>
      
      <AdjustStockModal
        key={selectedItem?.id || 'no-selection'}
        isOpen={isAdjustStockModalOpen}
        onClose={() => setIsAdjustStockModalOpen(false)}
        selectedItem={selectedItem}
        onConfirm={async (data) => {
          if (!selectedItem) return;
          
          try {
            // Get a fresh Supabase client to ensure we have the current session
            const supabase = getSupabaseClient();
            
            // Calculate the new stock quantity based on adjustment type
            let newStockQuantity: number;
            if (data.adjustmentType === 'adjustBy') {
              newStockQuantity = selectedItem.stockOnHand + data.adjustmentValue;
            } else {
              newStockQuantity = data.adjustmentValue;
            }
            
            // Ensure stock quantity is not negative
            newStockQuantity = Math.max(0, newStockQuantity);
            
            console.log('Attempting to update stock quantity to:', newStockQuantity);
            
            // Get the current user ID from the Supabase auth session
            const { data: { user: sessionUser }, error: authError } = await supabase.auth.getUser();
            
            if (authError) {
              console.error('Authentication error:', authError);
              throw new Error('Authentication failed. Please sign in and try again.');
            }
            
            const userId = sessionUser?.id;
            
            // If we don't have a user ID, fail safely
            if (!userId) {
              console.error('Could not determine user ID for this operation');
              throw new Error('Authentication required. Please sign in and try again.');
            }
            
            // Verify that we have the correct item IDs
            if (selectedItem.type === 'product' && !selectedItem.productId) {
              throw new Error('Invalid product ID. Cannot update stock.');
            }
            
            if (selectedItem.type === 'variant' && !selectedItem.variantId) {
              throw new Error('Invalid variant ID. Cannot update stock.');
            }
            
            // Verify user permissions
            const permissionCheck = await verifyUserPermissions(supabase, userId);
            if (!permissionCheck.success) {
              throw new Error(permissionCheck.error || 'Permission denied. You may not have access to modify inventory.');
            }
            
            // Update the database based on item type
            let updateError = null;
            let updateData = null;
            
            if (selectedItem.type === 'product' && !selectedItem.variantId) {
              // Update simple product
              console.log('Updating product:', selectedItem.productId);
              const result = await supabase
                .from('products')
                .update({ 
                  stock_quantity: newStockQuantity,
                  updated_at: new Date().toISOString()
                })
                .eq('id', selectedItem.productId)
                .eq('organization_id', organizationId)
                .select();
              
              updateError = result.error;
              updateData = result.data;
              
              if (updateError) {
                console.error('Error updating product:', updateError);
                if (updateError.code === '42501' || updateError.code === 'PGRST116' || updateError.message?.includes('permission denied')) {
                  throw new Error('Permission denied. You may not have access to modify this product.');
                } else if (updateError.message?.includes('no product was found') || updateError.message?.includes('not found')) {
                  throw new Error('Product not found. It may have been deleted or you do not have access to it.');
                } else {
                  throw updateError;
                }
              }
              console.log('Product update successful:', updateData);
            } else if (selectedItem.type === 'variant' && selectedItem.variantId) {
              // Update product variant
              console.log('Updating variant:', selectedItem.variantId);
              const result = await supabase
                .from('product_variants')
                .update({ 
                  stock_quantity: newStockQuantity,
                  updated_at: new Date().toISOString()
                })
                .eq('id', selectedItem.variantId)
                .eq('organization_id', organizationId)
                .select();
              
              updateError = result.error;
              updateData = result.data;
              
              if (updateError) {
                console.error('Error updating variant:', updateError);
                if (updateError.code === '42501' || updateError.code === 'PGRST116' || updateError.message?.includes('permission denied')) {
                  throw new Error('Permission denied. You may not have access to modify this variant.');
                } else if (updateError.message?.includes('no variant was found') || updateError.message?.includes('not found')) {
                  throw new Error('Variant not found. It may have been deleted or you do not have access to it.');
                } else {
                  throw updateError;
                }
              }
              console.log('Variant update successful:', updateData);
            }
            
            console.log('Stock quantity update completed, now recording history');
            
            // Record the stock history
            try {
              const { error: historyError } = await supabase
                .rpc('log_stock_adjustment', {
                  p_organization_id: organizationId,
                  p_product_id: selectedItem.type === 'product' ? selectedItem.productId : null,
                  p_variant_id: selectedItem.type === 'variant' ? selectedItem.variantId : null,
                  p_previous_quantity: selectedItem.stockOnHand,
                  p_new_quantity: newStockQuantity,
                  p_change_reason: data.reason,
                  p_change_notes: data.notes,
                  p_source_type: 'manual',
                  p_created_by: userId,
                  p_created_by_name: sessionUser?.email || 'user'
                });

              if (historyError) {
                console.error('Error recording stock history:', historyError);
                // We'll continue without throwing here since the stock adjustment was successful
                // But we'll show a warning in the success message
                toast({
                  title: "Stock Adjusted",
                  description: `Successfully adjusted stock for ${selectedItem.name}. Note: There was an issue recording the history.`,
                });
                // Close the modal and refresh data even if history recording failed
                setIsAdjustStockModalOpen(false);
                // Only dispatch refresh event once
                setTimeout(() => {
                  window.dispatchEvent(new Event('refreshInventoryData'));
                }, 100);
                return;
              }
            } catch (historyError) {
              console.error('Error in stock history recording:', historyError);
              // Even if the history recording fails, we still want to show success for the stock adjustment
              toast({
                title: "Stock Adjusted",
                description: `Successfully adjusted stock for ${selectedItem.name}. Note: There was an issue recording the history.`,
              });
              // Close the modal and refresh data even if history recording failed
              setIsAdjustStockModalOpen(false);
              // Only dispatch refresh event once
              setTimeout(() => {
                window.dispatchEvent(new Event('refreshInventoryData'));
              }, 100);
              return;
            }
            
            // Show success message
            toast({
              title: "Stock Adjusted",
              description: `Successfully adjusted stock for ${selectedItem.name}. Change recorded in history.`,
            });
            
            // Close the modal
            setIsAdjustStockModalOpen(false);
            
            // Refresh the parent component to reload data
            setTimeout(() => {
              window.dispatchEvent(new Event('refreshInventoryData'));
            }, 100);
          } catch (error) {
            console.error('Error adjusting stock:', error);
            let errorMessage = "Failed to adjust stock. Please try again.";
            
            // More specific error messages based on error type
            if (error instanceof Error) {
              if (error.message.includes('Authentication failed') || error.message.includes('User ID not found')) {
                errorMessage = "Authentication error. Please refresh the page and try again.";
              } else if (error.message.includes('permission denied') || error.message.includes('403')) {
                errorMessage = "Permission denied. You don't have access to modify this item.";
              } else if (error.message.includes('Invalid product ID') || error.message.includes('Invalid variant ID')) {
                errorMessage = error.message;
              } else if (error.message) {
                errorMessage = error.message;
              }
            }
            
            toast({
              title: "Error",
              description: errorMessage,
              variant: "destructive",
            });
          }
        }}
      />
      <StockHistoryModal
        isOpen={isStockHistoryModalOpen}
        onClose={() => setIsStockHistoryModalOpen(false)}
        selectedItem={selectedItem}
      />
      <PurchaseOrderModal
        isOpen={isPurchaseOrderModalOpen}
        onClose={() => setIsPurchaseOrderModalOpen(false)}
        selectedItem={selectedItem}
        products={[]}
      />
    </div>
  )
}