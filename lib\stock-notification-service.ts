import { createSupabaseClient } from '@/lib/supabase';
import { getLowStockItemsForUser } from '@/lib/supabase';
import { NotificationService } from '@/lib/notification-service';

export class StockNotificationService {
  private supabase = createSupabaseClient();
  private notificationService = new NotificationService();

  /**
   * Check for low stock items and create notifications
   */
  async checkLowStockAndNotify() {
    try {
      // Note: We're not using Supabase client directly here anymore
      // The getLowStockItemsForUser function handles the client creation
      // This is kept for reference but should be updated to get user from context
      console.warn('checkLowStockAndNotify needs to be updated to receive user ID as parameter');
      
      // This is a placeholder implementation - in a real scenario, 
      // the user ID should be passed as a parameter to this function
      // For now, we'll skip the actual implementation to avoid direct Supabase calls
      
      console.log('Low stock check would be performed here using getLowStockItemsForUser');
    } catch (error) {
      console.error('Error in checkLowStockAndNotify:', error);
    }
  }

  /**
   * Create notification for stock adjustment
   * NOTE: This method is deprecated as notifications are now created via database triggers
   * This is kept for reference and backward compatibility, but should not be used for new code
   */
  async createStockAdjustmentNotification(
    userId: string,
    productName: string,
    sku: string,
    previousQuantity: number,
    newQuantity: number,
    reason: string
  ) {
    console.warn('createStockAdjustmentNotification is deprecated. Using database triggers instead.');
    // No longer creating notifications at application level
    // Stock adjustment notifications are now handled by database triggers
  }
}