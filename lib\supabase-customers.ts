import { getSupabaseClient } from '@/lib/supabase'

// Define the type for our customer data
export interface CustomerRow {
  id: string
  organization_id: string
  full_name: string
  email: string | null
  phone: string | null
  shipping_address: string | null
  billing_address: string | null
  notes: string | null
  created_at: string
  updated_at: string
  totalOrders?: number | null
  totalSpent?: number | null
}

export interface CreateCustomerData {
  full_name: string
  email: string
  phone?: string
  shipping_address?: string
  billing_address?: string
  notes?: string
}

export interface UpdateCustomerData {
  full_name?: string
  email?: string
  phone?: string
  shipping_address?: string
  billing_address?: string
  notes?: string
}

/**
 * Function to get all customers for an organization
 * @param organizationId - The organization ID to fetch customers for
 * @returns Promise with array of customers
 */
export async function getAllCustomersForOrganization(organizationId: string) {
  const supabase = getSupabaseClient()
  
  try {
    // Call the database function to get all customers for the organization
    const { data, error } = await supabase.rpc('get_all_customers_for_organization', {
      p_organization_id: organizationId
    })
    
    if (error) {
      throw new Error(error.message)
    }
    
    // Calculate totalOrders and totalSpent for each customer
    const customersWithStats = await Promise.all(data.map(async (customer: any) => {
      // Get total orders for this customer
      const { count: totalOrders, error: ordersError } = await supabase
        .from('sales')
        .select('*', { count: 'exact', head: true })
        .eq('customer_id', customer.id)
        .eq('organization_id', organizationId)
      
      // Get total spent for this customer
      const { data: ordersData, error: spentError } = await supabase
        .from('sales')
        .select('total_amount')
        .eq('customer_id', customer.id)
        .eq('organization_id', organizationId)
      
      let totalSpent = 0
      if (ordersData && ordersData.length > 0) {
        totalSpent = ordersData.reduce((sum, order) => sum + (order.total_amount || 0), 0)
      }
      
      return {
        ...customer,
        totalOrders: totalOrders || 0,
        totalSpent: totalSpent || 0
      }
    }))
    
    return customersWithStats as CustomerRow[]
  } catch (error) {
    console.error('Error fetching customers for organization:', error)
    throw error
  }
}

/**
 * Function to create a new customer
 * @param organizationId - The organization ID creating the customer
 * @param customerData - The customer data to insert
 * @returns Promise with the created customer
 */
export async function createCustomer(organizationId: string, customerData: CreateCustomerData) {
  const supabase = getSupabaseClient()
  
  try {
    // Call the database function to create a new customer
    const { data, error } = await supabase.rpc('create_customer', {
      p_organization_id: organizationId,
      p_full_name: customerData.full_name,
      p_email: customerData.email,
      p_phone: customerData.phone,
      p_shipping_address: customerData.shipping_address,
      p_billing_address: customerData.billing_address,
      p_notes: customerData.notes
    })
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data as CustomerRow
  } catch (error) {
    console.error('Error creating customer:', error)
    throw error
  }
}

/**
 * Function to update an existing customer
 * @param customerId - The ID of the customer to update
 * @param customerData - The customer data to update
 * @returns Promise with the updated customer
 */
export async function updateCustomer(customerId: string, customerData: UpdateCustomerData) {
  const supabase = getSupabaseClient()
  
  try {
    // Call the database function to update a customer
    const { data, error } = await supabase.rpc('update_customer', {
      p_customer_id: customerId,
      p_full_name: customerData.full_name,
      p_email: customerData.email,
      p_phone: customerData.phone,
      p_shipping_address: customerData.shipping_address,
      p_billing_address: customerData.billing_address,
      p_notes: customerData.notes
    })
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data as CustomerRow
  } catch (error) {
    console.error('Error updating customer:', error)
    throw error
  }
}

/**
 * Function to delete a customer
 * @param customerId - The ID of the customer to delete
 * @returns Promise with the deletion result
 */
export async function deleteCustomer(customerId: string) {
  const supabase = getSupabaseClient()
  
  try {
    // Call the database function to delete a customer
    const { error } = await supabase.rpc('delete_customer', {
      p_customer_id: customerId
    })
    
    if (error) {
      throw new Error(error.message)
    }
    
    return { success: true }
  } catch (error) {
    console.error('Error deleting customer:', error)
    throw error
  }
}