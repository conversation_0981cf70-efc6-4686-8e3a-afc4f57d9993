"use client"

import * as React from "react"
import { Check, ChevronsUpDown, Plus } from "lucide-react"

import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

interface ComboboxProps {
  value?: string
  onValueChange?: (value: string) => void
  placeholder?: string
  searchPlaceholder?: string
  emptyText?: string
  options: Array<{ value: string; label: string }>
  allowCustom?: boolean
  className?: string
  disabled?: boolean
}

export function Combobox({
  value,
  onValueChange,
  placeholder = "Select option...",
  searchPlaceholder = "Search...",
  emptyText = "No option found.",
  options,
  allowCustom = false,
  className,
  disabled = false,
}: ComboboxProps) {
  const [open, setOpen] = React.useState(false)
  const [searchValue, setSearchValue] = React.useState("")
  const [triggerWidth, setTriggerWidth] = React.useState(0)
  const triggerRef = React.useRef<HTMLButtonElement>(null)

  const handleSelect = (currentValue: string) => {
    // Handle the "add-new" option
    if (currentValue === "add-new") {
      onValueChange?.(searchValue.trim())
    } else {
      // Find the option by value since that's what we want to return
      const selectedOption = options.find(opt =>
        opt.value === currentValue
      )
      if (selectedOption) {
        onValueChange?.(selectedOption.value === value ? "" : selectedOption.value)
      } else {
        // If no option found, use the value directly (for custom entries)
        onValueChange?.(currentValue === value ? "" : currentValue)
      }
    }
    setOpen(false)
    setSearchValue("")
  }

  const filteredOptions = options.filter(option =>
    option.label.toLowerCase().includes(searchValue.toLowerCase())
  )

  const selectedOption = options.find(option => option.value === value)

  React.useEffect(() => {
    if (triggerRef.current) {
      setTriggerWidth(triggerRef.current.offsetWidth)
    }
  }, [open])

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          ref={triggerRef}
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn("justify-between text-xs", className)}
          disabled={disabled}
        >
          {selectedOption ? selectedOption.label : placeholder}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="p-0"
        style={{ width: triggerWidth > 0 ? triggerWidth : 200 }}
        align="start"
      >
        <Command shouldFilter={false}>
          <CommandInput
            placeholder={searchPlaceholder}
            value={searchValue}
            onValueChange={setSearchValue}
            className="text-xs"
          />
          <CommandList>
            {filteredOptions.map((option) => (
              <CommandItem
                key={option.value}
                value={option.value}
                onSelect={handleSelect}
                className="text-xs cursor-pointer"
              >
                <Check
                  className={cn(
                    "mr-2 h-4 w-4",
                    value === option.value ? "opacity-100" : "opacity-0"
                  )}
                />
                {option.label}
              </CommandItem>
            ))}
            {allowCustom && searchValue.trim() && !options.some(opt => opt.value === searchValue.trim()) && (
              <CommandItem
                value="add-new"
                onSelect={handleSelect}
                className="text-xs cursor-pointer"
              >
                <Plus className="mr-2 h-4 w-4" />
                Add "{searchValue}"
              </CommandItem>
            )}
            {filteredOptions.length === 0 && !searchValue.trim() && (
              <CommandEmpty>
                {emptyText}
              </CommandEmpty>
            )}
            {searchValue.trim() && options.some(opt => opt.value === searchValue.trim()) && (
              <CommandEmpty>
                No results found
              </CommandEmpty>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}