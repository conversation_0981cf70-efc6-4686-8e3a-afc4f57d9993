-- SQL function to connect Vault integration
-- This function handles the creation or update of Vault integration records

CREATE OR REPLACE FUNCTION connect_vault(
    vault_url TEXT,
    api_key TEXT,
    account_id TEXT DEFAULT NULL
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id UUID;
    org_id UUID;
    secret_name TEXT;
    integration_id UUID;
    result JSON;
BEGIN
    -- Get the current user ID
    current_user_id := auth.uid();
    
    -- Check if user is authenticated
    IF current_user_id IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', 'User not authenticated'
        );
    END IF;
    
    -- Check if user is member of an organization
    SELECT organization_id INTO org_id
    FROM organization_members
    WHERE user_id = current_user_id
    LIMIT 1;
    
    IF org_id IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', 'User is not a member of any organization'
        );
    END IF;
    
    -- Validate inputs
    IF vault_url IS NULL OR vault_url = '' THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Vault URL is required'
        );
    END IF;
    
    IF api_key IS NULL OR api_key = '' THEN
        RETURN json_build_object(
            'success', false,
            'error', 'API Key is required'
        );
    END IF;
    
    -- Validate URL format by attempting to parse it
    BEGIN
        -- This is a simple validation, in practice you might want more robust URL validation
        IF POSITION('http' IN vault_url) = 0 THEN
            RAISE EXCEPTION 'Invalid URL format';
        END IF;
    EXCEPTION WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Invalid Vault URL format'
        );
    END;
    
    -- Generate a secret reference
    secret_name := 'vault_secret_' || gen_random_uuid();
    
    -- Check if integration already exists for this organization and platform
    SELECT id INTO integration_id
    FROM integrations
    WHERE organization_id = org_id
    AND platform = 'vault'
    LIMIT 1;
    
    -- If integration exists, update it
    IF integration_id IS NOT NULL THEN
        UPDATE integrations
        SET 
            vault_url = vault_url,
            consumer_key = api_key,
            consumer_secret = account_id,
            status = 'connected',
            secret_name = secret_name,
            consumer_secret_ref = gen_random_uuid()::TEXT,
            updated_at = NOW()
        WHERE id = integration_id;
    ELSE
        -- Insert new integration
        INSERT INTO integrations (
            organization_id,
            platform,
            vault_url,
            consumer_key,
            consumer_secret,
            status,
            secret_name,
            consumer_secret_ref
        ) VALUES (
            org_id,
            'vault',
            vault_url,
            api_key,
            account_id,
            'connected',
            secret_name,
            gen_random_uuid()::TEXT
        )
        RETURNING id INTO integration_id;
    END IF;
    
    -- Return success result
    RETURN json_build_object(
        'success', true,
        'integration_id', integration_id,
        'message', 'Vault connected successfully'
    );
    
EXCEPTION WHEN OTHERS THEN
    RETURN json_build_object(
        'success', false,
        'error', SQLERRM
    );
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION connect_vault(TEXT, TEXT, TEXT) TO authenticated;