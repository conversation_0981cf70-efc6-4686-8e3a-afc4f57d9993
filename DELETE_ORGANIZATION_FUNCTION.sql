-- =============================================
-- DELETE ORGANIZATION FUNCTION
-- =============================================
-- This function performs a cascading delete of an organization and all its associated data
-- WARNING: This is a destructive operation that cannot be undone

-- Create the delete_organization function with permission checks
create or replace function delete_organization(org_id uuid)
returns void as $$
declare
  user_id uuid;
  is_owner boolean;
begin
  -- Get the current user ID
  user_id := auth.uid();
  
  -- Check if user is authenticated
  if user_id is null then
    raise exception 'Only authenticated users can delete organizations';
  end if;
  
  -- Check if the organization exists
  if not exists (select 1 from organizations where id = org_id) then
    raise exception 'Organization with ID % does not exist', org_id;
  end if;
  
  -- Check if the user is the owner of the organization
  select (owner_id = user_id) into is_owner
  from organizations
  where id = org_id;
  
  if not is_owner then
    raise exception 'Only the organization owner can delete the organization';
  end if;
  
  -- Delete all organization data in proper order to respect foreign key constraints
  -- Note: The exact order depends on your schema, this is a general approach
  
  -- Delete organization preferences
  delete from organization_preferences where organization_id = org_id;
  
  -- Delete invitations
  delete from invitations where organization_id = org_id;
  
  -- Delete organization members
  delete from organization_members where organization_id = org_id;
  
  -- Delete notifications
  delete from notifications where organization_id = org_id;
  
  -- Delete stock history
  delete from stock_history where organization_id = org_id;
  
  -- Delete stock movements
  delete from stock_movements where organization_id = org_id;
  
  -- Delete product attribute values
  delete from product_attribute_values where organization_id = org_id;
  
  -- Delete product attributes
  delete from product_attributes where organization_id = org_id;
  
  -- Delete product variants (and their related data)
  delete from product_variants where organization_id = org_id;
  
  -- Delete products
  delete from products where organization_id = org_id;
  
  -- Delete categories
  delete from categories where organization_id = org_id;
  
  -- Delete expenses
  delete from expenses where organization_id = org_id;
  
  -- Delete customers
  delete from customers where organization_id = org_id;
  
  -- Delete sales and sale items (in proper order)
  delete from sale_items where sale_id in (select id from sales where organization_id = org_id);
  delete from sales where organization_id = org_id;
  
  -- Delete purchase order items and purchase orders (in proper order)
  -- Note: These tables may not yet be migrated to the organization model
  -- Uncomment the following lines when they are migrated:
  -- delete from purchase_order_items where purchase_order_id in (select id from purchase_orders where organization_id = org_id);
  -- delete from purchase_orders where organization_id = org_id;
  
  -- Finally, delete the organization itself
  delete from organizations where id = org_id;
end;
$$ language plpgsql security definer;

-- Grant execute permission to authenticated users
-- (You might want to restrict this further based on your security requirements)
grant execute on function delete_organization(uuid) to authenticated;

-- Example usage:
-- select delete_organization('your-organization-id-here');

-- =============================================
-- NOTES ON IMPLEMENTATION
-- =============================================
-- 1. This function should only be callable by the organization owner
-- 2. You should add additional checks to ensure the caller has permission
-- 3. Consider adding a confirmation mechanism in your application layer
-- 4. You may want to log this action for audit purposes
-- 5. Add all relevant tables that are linked to organizations in your schema
-- 6. The function currently handles all known tables in the organization model
-- 7. Purchase orders are not yet migrated to the organization model, so they are commented out