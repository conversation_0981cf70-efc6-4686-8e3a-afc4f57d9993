'use client'

import { useState } from 'react'
import { PageHeader } from '@/components/ui/page-header'
import { ProfitLossTab } from '@/components/reporting/profit-loss/ProfitLossTab'
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select'
import { DateRange } from 'react-day-picker'
import { format } from 'date-fns'

export default function ProfitLossPage() {
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(new Date().getFullYear(), 0, 1),
    to: new Date(),
  })

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <PageHeader 
          title="Profit & Loss Statement"
        >
          <p className="text-sm text-gray-500 mt-1">
            Analyze your business's profitability over specific periods
          </p>
        </PageHeader>
        
        <div className="flex items-center gap-2">
          <div className="text-sm text-gray-500">
            {dateRange?.from && dateRange?.to ? (
              <>
                {format(dateRange.from, 'MMM d, yyyy')} - {format(dateRange.to, 'MMM d, yyyy')}
              </>
            ) : (
              'Select date range'
            )}
          </div>
        </div>
      </div>
      <ProfitLossTab dateRange={dateRange} />
    </div>
  )
}