#!/usr/bin/env node

/**
 * Schema validation script
 * Run this script to validate that the database schema matches application expectations
 */

import { validateAllSchemas } from '../lib/schema-validation';

async function runSchemaValidation() {
  console.log('Running schema validation...');
  
  const result = await validateAllSchemas();
  
  if (result.valid) {
    console.log('✅ Schema validation passed! All tables and functions match expectations.');
    process.exit(0);
  } else {
    console.error('❌ Schema validation failed!');
    console.error('Errors found:');
    result.errors.forEach(error => console.error(`  - ${error}`));
    process.exit(1);
  }
}

// Run the validation
runSchemaValidation().catch(err => {
  console.error('Unexpected error during schema validation:', err);
  process.exit(1);
});