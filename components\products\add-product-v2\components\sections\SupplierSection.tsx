import React, { useEffect, useState } from 'react'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useProductForm } from '../../context/ProductFormContext'
import { SectionHeader } from '../shared/SectionHeader'
import { FormField } from '../shared/FormField'
import { createSupabaseClient } from '@/lib/supabase'
import { useAuth } from '@/contexts/auth-context'

export function SupplierSection() {
  const { formData, updateFormData, errors, suppliers, setSuppliers } = useProductForm()
  const { user, organizationId } = useAuth()
  const [isLoadingSuppliers, setIsLoadingSuppliers] = useState(false)

  // Load existing suppliers
  useEffect(() => {
    const loadSuppliers = async () => {
      if (!user?.id || !organizationId) return

      setIsLoadingSuppliers(true)
      try {
        const supabase = createSupabaseClient()
        const { data, error } = await supabase
          .from('products')
          .select('supplier')
          .eq('organization_id', organizationId)
          .not('supplier', 'is', null)
          .not('supplier', 'eq', '')

        if (error) throw error

        // Get unique suppliers
        const uniqueSuppliers = Array.from(new Set(data?.map(p => p.supplier).filter(Boolean))) as string[]
        setSuppliers(uniqueSuppliers)
      } catch (error) {
        console.error('Error loading suppliers:', error)
      } finally {
        setIsLoadingSuppliers(false)
      }
    }

    loadSuppliers()
  }, [user?.id, organizationId, setSuppliers])

  return (
    <div className="max-w-4xl">
      <SectionHeader
        title="Supplier & Sourcing"
        description="Supplier information and sourcing details"
      />

      <div className="space-y-4">
        {/* Supplier Selection */}
        <FormField
          label="Supplier"
          error={errors.supplier}
        >
          {suppliers.length > 0 ? (
            <Select
              value={formData.supplier}
              onValueChange={(value) => updateFormData('supplier', value === 'custom' ? '' : value)}
            >
              <SelectTrigger className="h-8 text-xs">
                <SelectValue placeholder="Select or enter supplier" />
              </SelectTrigger>
              <SelectContent>
                {isLoadingSuppliers ? (
                  <SelectItem value="loading" disabled>Loading suppliers...</SelectItem>
                ) : (
                  <>
                    {suppliers.map((supplier) => (
                      <SelectItem key={supplier} value={supplier}>
                        {supplier}
                      </SelectItem>
                    ))}
                    <SelectItem value="custom">+ Enter new supplier</SelectItem>
                  </>
                )}
              </SelectContent>
            </Select>
          ) : (
            <Input
              value={formData.supplier}
              onChange={(e) => updateFormData('supplier', e.target.value)}
              placeholder="Enter supplier name"
              className="h-8 text-xs placeholder:text-xs"
            />
          )}
        </FormField>

        {/* Custom Supplier Input */}
        {suppliers.length > 0 && !suppliers.includes(formData.supplier) && (
          <FormField
            label="New Supplier Name"
            error={errors.supplier}
          >
            <Input
              value={formData.supplier}
              onChange={(e) => updateFormData('supplier', e.target.value)}
              placeholder="Enter new supplier name"
              className="h-8 text-xs placeholder:text-xs"
            />
          </FormField>
        )}

        {/* Purchase Date */}
        <FormField
          label="Purchase Date"
          error={errors.purchase_date}
        >
          <Input
            type="date"
            value={formData.purchase_date ? new Date(formData.purchase_date).toISOString().split('T')[0] : ''}
            onChange={(e) => updateFormData('purchase_date', e.target.value ? new Date(e.target.value) : null)}
            className="h-8 text-xs"
          />
        </FormField>

        {/* Batch Reference */}
        <FormField
          label="Batch Reference"
          error={errors.batch_reference}
        >
          <Input
            value={formData.batch_reference}
            onChange={(e) => updateFormData('batch_reference', e.target.value)}
            placeholder="Batch or lot number (optional)"
            className="h-8 text-xs placeholder:text-xs"
          />
        </FormField>

        {/* Supplier Info */}
        <div className="mt-6 p-3 bg-gray-50 border border-gray-200 rounded-md">
          <div className="flex items-start gap-2">
            <div className="text-gray-500 mt-0.5">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 text-xs mb-1">
                Supplier Information
              </h4>
              <ul className="text-xs text-gray-600 space-y-1">
                <li>• Supplier information helps track product sources</li>
                <li>• Purchase date is useful for warranty and aging tracking</li>
                <li>• Batch references help with quality control and recalls</li>
                <li>• All fields are optional but recommended for better tracking</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}