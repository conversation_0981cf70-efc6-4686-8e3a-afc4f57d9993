-- =============================================
-- ONKO SaaS Platform Database Setup
-- =============================================
-- Run this script in your Supabase SQL Editor

-- Enable necessary extensions
create extension if not exists "uuid-ossp";

-- =============================================
-- USER PROFILES
-- =============================================

-- Create profiles table for extended user information
create table if not exists profiles (
  id uuid references auth.users on delete cascade primary key,
  business_name text,
  contact_info jsonb,
  subscription_tier text default 'free',
  currency text default 'USD',
  avatar_url text,
  first_name text,
  last_name text,
  display_name text,
  job_title text,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create function to handle profile updates
create or replace function handle_updated_at()
returns trigger as $$
begin
  new.updated_at = timezone('utc'::text, now());
  return new;
end;
$$ language plpgsql;

-- Create trigger for profile updates
create trigger profiles_updated_at before update on profiles
  for each row execute procedure handle_updated_at();

-- Function to automatically create profile on user signup
create or replace function handle_new_user()
returns trigger as $$
begin
  insert into public.profiles (id, business_name, currency, avatar_url, first_name, last_name, display_name, job_title)
  values (
    new.id, 
    new.raw_user_meta_data->>'business_name',
    coalesce(new.raw_user_meta_data->>'currency', 'USD'),
    null,
    new.raw_user_meta_data->>'first_name',
    new.raw_user_meta_data->>'last_name',
    new.raw_user_meta_data->>'display_name',
    new.raw_user_meta_data->>'job_title'
  );
  return new;
end;
$$ language plpgsql security definer;

-- Trigger for automatic profile creation
create trigger on_auth_user_created
  after insert on auth.users
  for each row execute procedure public.handle_new_user();

-- =============================================
-- PRODUCT CATEGORIES
-- =============================================

create table if not exists categories (
  id uuid default gen_random_uuid() primary key,
  user_id uuid references auth.users(id) on delete cascade not null,
  name text not null,
  description text,
  color text default '#3B82F6', -- Default blue color
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null,
  
  -- Ensure unique category names per user
  unique(user_id, name)
);

-- Create trigger for category updates
create trigger categories_updated_at before update on categories
  for each row execute procedure handle_updated_at();

-- =============================================
-- PRODUCTS
-- =============================================

-- Master Products Table (supports both simple products and product families)
create table if not exists products (
  id uuid default gen_random_uuid() primary key,
  user_id uuid references auth.users(id) on delete cascade not null,
  category_id uuid references categories(id) on delete set null,
  
  -- Master Product Information
  name text not null,                     -- "Cotton T-Shirt" or "Office Chair"
  description text,                       -- Shared description
  brand text,                            -- Product brand
  supplier text,                         -- Default supplier
  base_sku text,                         -- Base SKU pattern: "TSH-COTTON" or full SKU for simple products
  
  -- Product Type and Settings
  has_variants boolean default false,     -- Simple product (false) vs Product family (true)
  track_inventory boolean default true,
  is_active boolean default true,
  
  -- Shared Attributes (for simple products or variant defaults)
  base_cost decimal(10,2),               -- Base manufacturing cost
  packaging_cost decimal(10,2) default 0, -- Packaging cost per unit
  
  -- Simple Product Fields (only used when has_variants = false)
  price decimal(10,2),                   -- Selling price for simple products
  size text,                             -- Size for simple products
  color text,                            -- Color for simple products
  stock_quantity integer default 0,      -- Stock for simple products
  low_stock_threshold integer default 10, -- Alert threshold for simple products
  barcode text,                          -- Barcode for simple products
  image_url text,                        -- Primary image
  
  -- Discount/Sale Fields (for simple products)
  sale_price decimal(10,2),              -- Optional sale price
  sale_start_date date,                  -- Sale period start
  sale_end_date date,                    -- Sale period end
  
  -- Additional Fields
  batch_reference text,                  -- Optional batch tracking
  purchase_date date,                    -- Purchase date
  notes text,                           -- Additional notes
  
  -- Computed columns for simple products (removed CURRENT_DATE for immutability)
  total_cost decimal(10,2) generated always as (
    coalesce(base_cost, 0) + coalesce(packaging_cost, 0)
  ) stored,
  
  effective_price decimal(10,2) generated always as (
    case 
      when sale_price is not null 
      then sale_price
      else coalesce(price, 0)
    end
  ) stored,
  
  is_on_sale boolean generated always as (
    sale_price is not null 
  ) stored,
  
  profit_amount decimal(10,2) generated always as (
    case 
      when sale_price is not null 
      then sale_price - (coalesce(base_cost, 0) + coalesce(packaging_cost, 0))
      else coalesce(price, 0) - (coalesce(base_cost, 0) + coalesce(packaging_cost, 0))
    end
  ) stored,
  
  profit_margin decimal(5,2) generated always as (
    case 
      when (coalesce(base_cost, 0) + coalesce(packaging_cost, 0)) > 0 
      then case 
             when sale_price is not null 
             then ((sale_price - (coalesce(base_cost, 0) + coalesce(packaging_cost, 0))) 
                   / (coalesce(base_cost, 0) + coalesce(packaging_cost, 0)) * 100)
             else ((coalesce(price, 0) - (coalesce(base_cost, 0) + coalesce(packaging_cost, 0))) 
                   / (coalesce(base_cost, 0) + coalesce(packaging_cost, 0)) * 100)
           end
      else 0 
    end
  ) stored,
  
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null,
  
  -- Ensure unique base_sku per user
  unique(user_id, base_sku)
);

-- Product Variants Table (for complex products with variations)
create table if not exists product_variants (
  id uuid default gen_random_uuid() primary key,
  product_id uuid references products(id) on delete cascade not null,
  user_id uuid references auth.users(id) on delete cascade not null,
  
  -- Variant Identification
  sku text not null,                     -- "TSH-COTTON-RED-S"
  variant_name text,                     -- "Red Small" or auto-generated
  
  -- Variant Attributes
  size text,                             -- "Small", "Medium", "10oz"
  color text,                            -- "Red", "Blue", "#FF0000"
  material text,                         -- "Cotton", "Polyester"
  style text,                            -- "Regular", "Slim Fit"
  weight decimal(8,2),                   -- Physical weight
  dimensions jsonb,                      -- {length: 10, width: 5, height: 2}
  
  -- Variant-Specific Pricing & Costs
  cost_adjustment decimal(10,2) default 0, -- Additional cost vs base_cost
  price decimal(10,2) not null,          -- Selling price for this variant
  
  -- Discount/Sale Fields (per variant)
  sale_price decimal(10,2),              -- Optional sale price
  sale_start_date date,                  -- Sale period start
  sale_end_date date,                    -- Sale period end
  
  -- Inventory Management
  stock_quantity integer default 0,
  low_stock_threshold integer default 10,
  reserved_quantity integer default 0,   -- For pending orders
  
  -- Variant Settings
  is_active boolean default true,
  barcode text,                          -- Individual barcode per variant
  image_urls text[],                     -- Variant-specific images
  
  -- Note: Removing computed columns that reference other tables via subqueries as PostgreSQL doesn't allow this
  -- These calculations will need to be handled in application code or as database views/functions
  
  effective_price decimal(10,2) generated always as (
    case 
      when sale_price is not null 
      then sale_price
      else price
    end
  ) stored,
  
  is_on_sale boolean generated always as (
    sale_price is not null 
  ) stored,
  
  available_quantity integer generated always as (
    stock_quantity - coalesce(reserved_quantity, 0)
  ) stored,
  
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null,
  
  unique(user_id, sku)
);

-- Create trigger for product updates
create trigger products_updated_at before update on products
  for each row execute procedure handle_updated_at();

-- Create trigger for product_variants updates  
create trigger product_variants_updated_at before update on product_variants
  for each row execute procedure handle_updated_at();

-- =============================================
-- STOCK MOVEMENTS TRACKING
-- =============================================

-- Stock movements table for inventory audit trails
create table if not exists stock_movements (
  id uuid default gen_random_uuid() primary key,
  user_id uuid references auth.users(id) on delete cascade not null,
  
  -- Product Reference (works for both simple products and variants)
  product_id uuid references products(id) on delete cascade,
  variant_id uuid references product_variants(id) on delete cascade,
  
  -- Movement Details
  movement_type text not null check (movement_type in (
    'initial_stock',    -- Initial inventory setup
    'purchase',         -- Stock received from supplier
    'sale',            -- Stock sold to customer  
    'adjustment',      -- Manual stock adjustment
    'transfer',        -- Transfer between locations
    'damage',          -- Damaged/lost inventory
    'return',          -- Customer return (increase stock)
    'reserve',         -- Reserve for pending order
    'unreserve'        -- Release reserved stock
  )),
  
  quantity_change integer not null,      -- Positive = increase, Negative = decrease
  previous_quantity integer not null,    -- Stock before this movement
  new_quantity integer not null,        -- Stock after this movement
  
  -- Additional Information
  reason text,                          -- Human-readable reason
  reference_id text,                    -- Link to sale, purchase order, etc.
  unit_cost decimal(10,2),             -- Cost per unit at time of movement
  notes text,                          -- Additional notes
  
  -- Metadata
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  
  -- Ensure exactly one of product_id or variant_id is set
  check (
    (product_id is not null and variant_id is null) or 
    (product_id is null and variant_id is not null)
  )
);

-- =============================================
-- STOCK HISTORY TABLE
-- =============================================

-- Create stock_history table for inventory audit trails
CREATE TABLE IF NOT EXISTS stock_history (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  
  -- Product/Variant Reference
  product_id uuid REFERENCES products(id) ON DELETE CASCADE,
  variant_id uuid REFERENCES product_variants(id) ON DELETE CASCADE,
  
  -- Stock Movement Information
  change_type text NOT NULL, -- 'adjustment', 'sale', 'return', 'purchase_order', etc.
  change_reason text NOT NULL, -- 'initial_stock_correction', 'shipment_received', 'damaged_goods', etc.
  change_notes text, -- Additional notes about the change
  
  -- Quantity Changes
  previous_quantity integer NOT NULL DEFAULT 0,
  new_quantity integer NOT NULL DEFAULT 0,
  quantity_change integer NOT NULL, -- Can be positive (added) or negative (removed)
  
  -- Source Information
  source_type text, -- 'manual', 'order', 'purchase_order', 'api', etc.
  source_id uuid, -- Reference to order_id, purchase_order_id, etc.
  source_reference text, -- Human-readable reference (e.g., order number)
  
  -- User Information
  created_by uuid REFERENCES auth.users(id), -- User who made the change
  created_by_name text, -- Name of the user who made the change (for display)
  
  -- Timestamps
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  
  -- Ensure either product_id or variant_id is set (but not both for the same record)
  CONSTRAINT check_product_or_variant CHECK (
    (product_id IS NOT NULL AND variant_id IS NULL) OR 
    (product_id IS NULL AND variant_id IS NOT NULL)
  )
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_stock_history_user_id ON stock_history(user_id);
CREATE INDEX IF NOT EXISTS idx_stock_history_product_id ON stock_history(product_id);
CREATE INDEX IF NOT EXISTS idx_stock_history_variant_id ON stock_history(variant_id);
CREATE INDEX IF NOT EXISTS idx_stock_history_created_at ON stock_history(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_stock_history_change_type ON stock_history(change_type);
CREATE INDEX IF NOT EXISTS idx_stock_history_source_type ON stock_history(source_type);

-- Create function to automatically log stock adjustments
-- This function can be called from your application when stock is adjusted
CREATE OR REPLACE FUNCTION log_stock_adjustment(
  p_user_id uuid,
  p_product_id uuid DEFAULT NULL,
  p_variant_id uuid DEFAULT NULL,
  p_previous_quantity integer,
  p_new_quantity integer,
  p_change_reason text,
  p_change_notes text DEFAULT NULL,
  p_source_type text DEFAULT 'manual',
  p_source_id uuid DEFAULT NULL,
  p_source_reference text DEFAULT NULL,
  p_created_by uuid DEFAULT NULL,
  p_created_by_name text DEFAULT NULL
)
RETURNS uuid AS $$
DECLARE
  v_history_id uuid;
BEGIN
  INSERT INTO stock_history (
    user_id,
    product_id,
    variant_id,
    change_type,
    change_reason,
    change_notes,
    previous_quantity,
    new_quantity,
    quantity_change,
    source_type,
    source_id,
    source_reference,
    created_by,
    created_by_name
  ) VALUES (
    p_user_id,
    p_product_id,
    p_variant_id,
    'adjustment',
    p_change_reason,
    p_change_notes,
    p_previous_quantity,
    p_new_quantity,
    p_new_quantity - p_previous_quantity,
    p_source_type,
    p_source_id,
    p_source_reference,
    p_created_by,
    p_created_by_name
  )
  RETURNING id INTO v_history_id;
  
  RETURN v_history_id;
END;
$$ LANGUAGE plpgsql;

-- Grant necessary permissions
GRANT ALL ON stock_history TO authenticated;

-- Stock history policies
alter table stock_history enable row level security;

create policy "Users can view own stock history" on stock_history
  for select using (auth.uid() = user_id);

create policy "Users can create own stock history" on stock_history
  for insert with check (auth.uid() = user_id);

create policy "Users can update own stock history" on stock_history
  for update using (auth.uid() = user_id);

create policy "Users can delete own stock history" on stock_history
  for delete using (auth.uid() = user_id);

-- =============================================
-- EXPENSES
-- =============================================

create table if not exists expenses (
  id uuid default gen_random_uuid() primary key,
  user_id uuid references auth.users(id) on delete cascade not null,
  expense_id text, -- Auto-generated format: EXP-001, EXP-002, etc.
  category text not null,
  amount decimal(10,2) not null check (amount > 0),
  description text,
  vendor text, -- Vendor or supplier information
  payment_method text, -- Cash, Credit Card, Debit Card, Bank Transfer, or custom
  receipt_url text,
  expense_date date not null,
  is_recurring boolean default false,
  recurring_frequency text, -- 'weekly', 'monthly', 'yearly'
  tags text[],
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create trigger for expense updates
create trigger expenses_updated_at before update on expenses
  for each row execute procedure handle_updated_at();

-- =============================================
-- CUSTOMERS (for future invoice functionality)
-- =============================================

create table if not exists customers (
  id uuid default gen_random_uuid() primary key,
  user_id uuid references auth.users(id) on delete cascade not null,
  name text not null,
  email text,
  phone text,
  address jsonb,
  company text,
  notes text,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create trigger for customer updates
create trigger customers_updated_at before update on customers
  for each row execute procedure handle_updated_at();

-- =============================================
-- SALES TRANSACTIONS (for future functionality)
-- =============================================

create table if not exists sales (
  id uuid default gen_random_uuid() primary key,
  user_id uuid references auth.users(id) on delete cascade not null,
  customer_id uuid references customers(id) on delete set null,
  total_amount decimal(10,2) not null check (total_amount >= 0),
  payment_method text,
  payment_status text default 'pending', -- 'pending', 'completed', 'failed'
  sale_date timestamp with time zone default timezone('utc'::text, now()) not null,
  notes text,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

create table if not exists sale_items (
  id uuid default gen_random_uuid() primary key,
  sale_id uuid references sales(id) on delete cascade not null,
  product_id uuid references products(id) on delete restrict not null,
  quantity integer not null check (quantity > 0),
  unit_price decimal(10,2) not null check (unit_price >= 0),
  total_price decimal(10,2) generated always as (quantity * unit_price) stored,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- =============================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =============================================

-- Profiles policies
alter table profiles enable row level security;

create policy "Users can view own profile" on profiles
  for select using (auth.uid() = id);

create policy "Users can update own profile" on profiles
  for update using (auth.uid() = id);

create policy "Users can insert own profile" on profiles
  for insert with check (auth.uid() = id);

-- Categories policies
alter table categories enable row level security;

create policy "Users can view own categories" on categories
  for select using (auth.uid() = user_id);

create policy "Users can create own categories" on categories
  for insert with check (auth.uid() = user_id);

create policy "Users can update own categories" on categories
  for update using (auth.uid() = user_id);

create policy "Users can delete own categories" on categories
  for delete using (auth.uid() = user_id);

-- Products policies
alter table products enable row level security;

create policy "Users can view own products" on products
  for select using (auth.uid() = user_id);

create policy "Users can create own products" on products
  for insert with check (auth.uid() = user_id);

create policy "Users can update own products" on products
  for update using (auth.uid() = user_id);

create policy "Users can delete own products" on products
  for delete using (auth.uid() = user_id);

-- Expenses policies
alter table expenses enable row level security;

create policy "Users can view own expenses" on expenses
  for select using (auth.uid() = user_id);

create policy "Users can create own expenses" on expenses
  for insert with check (auth.uid() = user_id);

create policy "Users can update own expenses" on expenses
  for update using (auth.uid() = user_id);

create policy "Users can delete own expenses" on expenses
  for delete using (auth.uid() = user_id);

-- Customers policies
alter table customers enable row level security;

create policy "Users can view own customers" on customers
  for select using (auth.uid() = user_id);

create policy "Users can create own customers" on customers
  for insert with check (auth.uid() = user_id);

create policy "Users can update own customers" on customers
  for update using (auth.uid() = user_id);

create policy "Users can delete own customers" on customers
  for delete using (auth.uid() = user_id);

-- Sales policies
alter table sales enable row level security;

create policy "Users can view own sales" on sales
  for select using (auth.uid() = user_id);

create policy "Users can create own sales" on sales
  for insert with check (auth.uid() = user_id);

create policy "Users can update own sales" on sales
  for update using (auth.uid() = user_id);

create policy "Users can delete own sales" on sales
  for delete using (auth.uid() = user_id);

-- Sale items policies
alter table sale_items enable row level security;

create policy "Users can view own sale items" on sale_items
  for select using (
    auth.uid() = (select user_id from sales where id = sale_id)
  );

create policy "Users can create own sale items" on sale_items
  for insert with check (
    auth.uid() = (select user_id from sales where id = sale_id)
  );

create policy "Users can update own sale items" on sale_items
  for update using (
    auth.uid() = (select user_id from sales where id = sale_id)
  );

create policy "Users can delete own sale items" on sale_items
  for delete using (
    auth.uid() = (select user_id from sales where id = sale_id)
  );

-- Stock movements policies
alter table stock_movements enable row level security;

create policy "Users can view own stock movements" on stock_movements
  for select using (auth.uid() = user_id);

create policy "Users can create own stock movements" on stock_movements
  for insert with check (auth.uid() = user_id);

create policy "Users can update own stock movements" on stock_movements
  for update using (auth.uid() = user_id);

create policy "Users can delete own stock movements" on stock_movements
  for delete using (auth.uid() = user_id);

-- Product variants policies
alter table product_variants enable row level security;

create policy "Users can view own product variants" on product_variants
  for select using (auth.uid() = user_id);

create policy "Users can create own product variants" on product_variants
  for insert with check (auth.uid() = user_id);

create policy "Users can update own product variants" on product_variants
  for update using (auth.uid() = user_id);

create policy "Users can delete own product variants" on product_variants
  for delete using (auth.uid() = user_id);

-- =============================================
-- USEFUL VIEWS FOR ANALYTICS
-- =============================================

-- View for dashboard metrics
create or replace view user_dashboard_metrics as
select 
  p.user_id,
  count(distinct pr.id) as total_products,
  count(distinct c.id) as total_categories,
  coalesce(sum(pr.stock_quantity * pr.price), 0) as inventory_value,
  count(case when pr.stock_quantity <= pr.low_stock_threshold then 1 end) as low_stock_items,
  coalesce(sum(case when e.expense_date >= date_trunc('month', current_date) then e.amount else 0 end), 0) as monthly_expenses
from profiles p
left join products pr on p.id = pr.user_id and pr.is_active = true
left join categories c on p.id = c.user_id
left join expenses e on p.id = e.user_id
group by p.user_id;

-- Grant necessary permissions
grant all on products to authenticated;
grant all on product_variants to authenticated;

-- Grant access to the view
grant select on user_dashboard_metrics to authenticated;

-- RLS for the view
create policy "Users can view own metrics" on user_dashboard_metrics
  for select using (auth.uid() = user_id);

-- =============================================
-- FUNCTIONS FOR BUSINESS LOGIC
-- =============================================

-- Function to generate SKU
create or replace function generate_sku(product_name text)
returns text as $$
declare
  prefix text;
  timestamp_suffix text;
begin
  -- Get first 3 characters of each word, uppercase
  prefix := upper(substring(regexp_replace(product_name, '[^a-zA-Z\s]', '', 'g') from 1 for 3));
  
  -- Get last 6 digits of current timestamp
  timestamp_suffix := right(extract(epoch from now())::text, 6);
  
  return prefix || '-' || timestamp_suffix;
end;
$$ language plpgsql;

-- Function to check and alert for low stock
create or replace function check_low_stock()
returns table(product_id uuid, product_name text, current_stock integer, threshold integer) as $$
begin
  return query
  select p.id, p.name, p.stock_quantity, p.low_stock_threshold
  from products p
  where p.stock_quantity <= p.low_stock_threshold
    and p.is_active = true
    and p.user_id = auth.uid();
end;
$$ language plpgsql security definer;

-- Function to generate expense ID in EXP-001 format
create or replace function generate_expense_id(user_uuid uuid)
returns text as $$
declare
  next_number integer;
  expense_id_text text;
begin
  -- Get the next expense number for this user
  select coalesce(max(cast(substring(expense_id from 5) as integer)), 0) + 1
  into next_number
  from expenses
  where user_id = user_uuid
    and expense_id is not null
    and expense_id ~ '^EXP-[0-9]+$';
  
  -- Format as EXP-001, EXP-002, etc.
  expense_id_text := 'EXP-' || lpad(next_number::text, 3, '0');
  
  return expense_id_text;
end;
$$ language plpgsql security definer;

-- Trigger function to auto-generate expense_id before insert
create or replace function set_expense_id()
returns trigger as $$
begin
  if new.expense_id is null then
    new.expense_id := generate_expense_id(new.user_id);
  end if;
  return new;
end;
$$ language plpgsql;

-- Create trigger for auto-generating expense IDs
create trigger expenses_set_expense_id
  before insert on expenses
  for each row
  execute function set_expense_id();

-- =============================================
-- INDEXES for Performance
-- =============================================

-- Products indexes
create index if not exists idx_products_user_id on products(user_id);
create index if not exists idx_products_category_id on products(category_id);
create index if not exists idx_products_base_sku on products(base_sku);
create index if not exists idx_products_active on products(is_active);

-- Product variants indexes
create index if not exists idx_product_variants_product_id on product_variants(product_id);
create index if not exists idx_product_variants_user_id on product_variants(user_id);
create index if not exists idx_product_variants_sku on product_variants(sku);

-- Expenses indexes
create index if not exists idx_expenses_user_id on expenses(user_id);
create index if not exists idx_expenses_date on expenses(expense_date);
create index if not exists idx_expenses_category on expenses(category);
create index if not exists idx_expenses_expense_id on expenses(expense_id);

-- Categories indexes
create index if not exists idx_categories_user_id on categories(user_id);

-- Sales indexes
create index if not exists idx_sales_user_id on sales(user_id);
create index if not exists idx_sales_date on sales(sale_date);
create index if not exists idx_sale_items_sale_id on sale_items(sale_id);

-- =============================================
-- USER PREFERENCES TABLE
-- =============================================

-- Create user_preferences table for storing user notification and UI preferences
create table if not exists user_preferences (
  user_id uuid references auth.users on delete cascade primary key,
  email_notifications boolean default true,
  push_notifications boolean default true,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create trigger for user_preferences updates
create trigger user_preferences_updated_at before update on user_preferences
  for each row execute procedure handle_updated_at();

-- User preferences policies
alter table user_preferences enable row level security;

create policy "Users can view own preferences" on user_preferences
  for select using (auth.uid() = user_id);

create policy "Users can create own preferences" on user_preferences
  for insert with check (auth.uid() = user_id);

create policy "Users can update own preferences" on user_preferences
  for update using (auth.uid() = user_id);

create policy "Users can delete own preferences" on user_preferences
  for delete using (auth.uid() = user_id);

-- Indexes for user_preferences
create index if not exists idx_user_preferences_user_id on user_preferences(user_id);

-- =============================================
-- PRODUCT ATTRIBUTES TABLE
-- =============================================

-- Create product_attributes table for storing user-defined product attributes
create table if not exists product_attributes (
  id uuid default gen_random_uuid() primary key,
  user_id uuid references auth.users(id) on delete cascade not null,
  name text not null,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null,
  
  -- Ensure unique attribute names per user
  unique(user_id, name)
);

-- Create trigger for product_attributes updates
create trigger product_attributes_updated_at before update on product_attributes
  for each row execute procedure handle_updated_at();

-- Product attributes policies
alter table product_attributes enable row level security;

create policy "Users can view own product attributes" on product_attributes
  for select using (auth.uid() = user_id);

create policy "Users can create own product attributes" on product_attributes
  for insert with check (auth.uid() = user_id);

create policy "Users can update own product attributes" on product_attributes
  for update using (auth.uid() = user_id);

create policy "Users can delete own product attributes" on product_attributes
  for delete using (auth.uid() = user_id);

-- Indexes for product_attributes
create index if not exists idx_product_attributes_user_id on product_attributes(user_id);
create index if not exists idx_product_attributes_name on product_attributes(name);

-- =============================================
-- PRODUCT ATTRIBUTE VALUES TABLE
-- =============================================

-- Create product_attribute_values table for storing values associated with product attributes
create table if not exists product_attribute_values (
  id uuid default gen_random_uuid() primary key,
  user_id uuid references auth.users(id) on delete cascade not null,
  attribute_id uuid references product_attributes(id) on delete cascade not null,
  value text not null,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null,
  
  -- Ensure unique values per attribute per user
  unique(user_id, attribute_id, value)
);

-- Create trigger for product_attribute_values updates
create trigger product_attribute_values_updated_at before update on product_attribute_values
  for each row execute procedure handle_updated_at();

-- Product attribute values policies
alter table product_attribute_values enable row level security;

create policy "Users can view own product attribute values" on product_attribute_values
  for select using (auth.uid() = user_id);

create policy "Users can create own product attribute values" on product_attribute_values
  for insert with check (auth.uid() = user_id);

create policy "Users can update own product attribute values" on product_attribute_values
  for update using (auth.uid() = user_id);

create policy "Users can delete own product attribute values" on product_attribute_values
  for delete using (auth.uid() = user_id);

-- Indexes for product_attribute_values
create index if not exists idx_product_attribute_values_user_id on product_attribute_values(user_id);
create index if not exists idx_product_attribute_values_attribute_id on product_attribute_values(attribute_id);
create index if not exists idx_product_attribute_values_value on product_attribute_values(value);

-- =============================================
-- SAMPLE DATA (Optional - for testing)
-- =============================================

-- Uncomment the following to insert sample categories
-- Note: Replace the UUID with an actual user ID from your auth.users table

/*
-- Sample categories
insert into categories (user_id, name, description, color) values
  ('your-user-id-here', 'Electronics', 'Electronic devices and accessories', '#3B82F6'),
  ('your-user-id-here', 'Clothing', 'Apparel and fashion items', '#EF4444'),
  ('your-user-id-here', 'Books', 'Books and educational materials', '#10B981');

-- Sample products
insert into products (user_id, category_id, name, description, price, cost, stock_quantity) values
  ('your-user-id-here', 
   (select id from categories where name = 'Electronics' and user_id = 'your-user-id-here'), 
   'Wireless Headphones', 'High-quality wireless headphones with noise cancellation', 99.99, 45.00, 25),
  ('your-user-id-here', 
   (select id from categories where name = 'Clothing' and user_id = 'your-user-id-here'), 
   'T-Shirt', 'Cotton t-shirt available in multiple colors', 19.99, 8.50, 50);
*/

-- =============================================
-- COMPLETION MESSAGE
-- =============================================

-- Create a completion log
do $$
begin
  raise notice 'ONKO database setup completed successfully!';
  raise notice 'Tables created: profiles, categories, products, expenses, customers, sales, sale_items';
  raise notice 'RLS policies applied for all tables';
  raise notice 'Triggers and functions created';
  raise notice 'Ready for application use!';
end $$;