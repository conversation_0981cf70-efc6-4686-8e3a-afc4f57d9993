'use client'

import { useState, useEffect } from 'react'
import { CustomersTab } from '@/components/sales/customers/CustomersTab'
import { NewCustomerModal } from '@/components/sales/customers/NewCustomerModal'
import { useToast } from '@/components/ui/use-toast'
import { Button } from '@/components/ui/button'
import { FileSpreadsheet } from 'lucide-react'
import { PageHeader } from '@/components/ui/page-header'

export default function CustomersPage() {
  const [isMobile, setIsMobile] = useState(false)
  const [isAddCustomerModalOpen, setIsAddCustomerModalOpen] = useState(false)
  const [isExporting, setIsExporting] = useState(false)
  const [filteredCustomers, setFilteredCustomers] = useState<any[]>([])
  const { toast } = useToast()

  // Mobile detection
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Export to CSV function (placeholder)
  const exportToCSV = () => {
    setIsExporting(true)
    
    try {
      // TODO: Implement actual CSV export logic when customers data is available
      toast({
        title: "Export Not Available",
        description: "CSV export will be available when customers are implemented.",
      })
    } catch (error) {
      console.error('CSV export error:', error)
      toast({
        title: "Export Failed",
        description: "Failed to export customers. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsExporting(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Page Header with Title and Actions */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <PageHeader 
          title="Customers"
          description="Manage your customer profiles"
        />
        
        <div className="flex flex-col sm:flex-row sm:items-center gap-2">
          {/* Export Button */}
          <Button 
            variant="outline" 
            size="sm" 
            className="flex items-center gap-1.5 h-8 px-3"
            onClick={exportToCSV}
            disabled={isExporting || filteredCustomers.length === 0}
          >
            <FileSpreadsheet className="h-4 w-4" />
            <span>Export CSV</span>
          </Button>
          
          {/* Add Customer Button */}
          <Button
            className="flex items-center gap-1.5 h-8 px-3 bg-blue-600 hover:bg-blue-700"
            onClick={() => setIsAddCustomerModalOpen(true)}
          >
            <span>+ New Customer</span>
          </Button>
        </div>
      </div>
      
      {/* Main Content - Customers Tab */}
      <CustomersTab 
        isMobile={isMobile} 
      />
      
      {/* New Customer Modal */}
      <NewCustomerModal
        isOpen={isAddCustomerModalOpen}
        onClose={() => setIsAddCustomerModalOpen(false)}
      />
    </div>
  )
}