import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { getPendingInvitations, resendInvitation, cancelInvitation } from '@/lib/team-management'
import { PendingInvitation } from '@/lib/team-management'

// Fetch pending invitations
export const usePendingInvitations = () => {
  return useQuery<PendingInvitation[], Error>({
    queryKey: ['pendingInvitations'],
    queryFn: getPendingInvitations,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

// Resend invitation
export const useResendInvitation = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (invitationId: string) => resendInvitation(invitationId),
    onSuccess: () => {
      // Invalidate and refetch pending invitations
      queryClient.invalidateQueries({ queryKey: ['pendingInvitations'] })
    },
  })
}

// Cancel invitation
export const useCancelInvitation = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (invitationId: string) => cancelInvitation(invitationId),
    onSuccess: () => {
      // Invalidate and refetch pending invitations
      queryClient.invalidateQueries({ queryKey: ['pendingInvitations'] })
    },
  })
}