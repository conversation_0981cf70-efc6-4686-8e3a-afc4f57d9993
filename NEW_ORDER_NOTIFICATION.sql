-- =============================================
-- NEW ORDER NOTIFICATION IMPLEMENTATION (Organization-Based)
-- =============================================

-- Function to create notification when new order is created
CREATE OR REPLACE FUNCTION notify_new_order()
RETURNS TRIGGER AS $$
DECLARE
  v_customer_name TEXT;
  v_order_number TEXT;
BEGIN
  -- Get customer name if customer exists
  IF NEW.customer_id IS NOT NULL THEN
    SELECT COALESCE(full_name, 'Unknown Customer')
    INTO v_customer_name
    FROM customers
    WHERE id = NEW.customer_id;
  ELSE
    v_customer_name := 'Unknown Customer';
  END IF;
  
  -- Generate order number from sale ID (matching frontend pattern)
  v_order_number := 'ORD-' || LEFT(NEW.id::TEXT, 8);
  
  -- Create notification
  INSERT INTO notifications (
    organization_id,
    type,
    title,
    message,
    related_entity_type,
    related_entity_id
  ) VALUES (
    NEW.organization_id,
    'new_order',
    'New Order Created',
    'You have a new order (' || v_order_number || ') from ' || v_customer_name || ' for ' || NEW.total_amount::TEXT || '.',
    'order',
    NEW.id
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to call notification function after new order is inserted
CREATE TRIGGER new_order_notification_trigger
  AFTER INSERT ON sales
  FOR EACH ROW
  EXECUTE FUNCTION notify_new_order();

-- =============================================
-- OVERDUE INVOICE CHECK FUNCTION (Organization-Based)
-- =============================================

-- Function to check for overdue invoices and create notifications
CREATE OR REPLACE FUNCTION check_overdue_invoices()
RETURNS void AS $$
DECLARE
  v_invoice record;
  v_customer_name text;
BEGIN
  -- Loop through overdue invoices that haven't been notified about yet
  FOR v_invoice IN
    SELECT i.id, i.organization_id, i.invoice_number, i.total, i.due_date, i.customer_id
    FROM invoices i
    WHERE i.due_date < CURRENT_DATE
      AND i.status NOT IN ('Paid', 'Cancelled')
      AND NOT EXISTS (
        SELECT 1 FROM notifications n 
        WHERE n.organization_id = i.organization_id 
          AND n.related_entity_type = 'invoice'
          AND n.related_entity_id = i.id
          AND n.type = 'overdue_invoice'
      )
  LOOP
    -- Get customer name if customer exists
    IF v_invoice.customer_id IS NOT NULL THEN
      SELECT COALESCE(full_name, 'Unknown Customer')
      INTO v_customer_name
      FROM customers
      WHERE id = v_invoice.customer_id;
    ELSE
      v_customer_name := 'Unknown Customer';
    END IF;
    
    -- Create notification for overdue invoice
    INSERT INTO notifications (
      organization_id,
      type,
      title,
      message,
      related_entity_type,
      related_entity_id
    ) VALUES (
      v_invoice.organization_id,
      'overdue_invoice',
      'Invoice Overdue',
      'Invoice ' || v_invoice.invoice_number || ' for ' || v_customer_name || ' is now overdue. Amount: ' || v_invoice.total::TEXT || '.',
      'invoice',
      v_invoice.id
    );
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION check_overdue_invoices() TO authenticated;