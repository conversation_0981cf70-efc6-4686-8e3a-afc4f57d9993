-- =============================================
-- TEAM MANAGEMENT TABLES
-- =============================================

-- Create organizations table
create table if not exists organizations (
  id uuid default gen_random_uuid() primary key,
  name text not null,
  owner_id uuid references auth.users(id) on delete cascade not null,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create organization_members table
create table if not exists organization_members (
  id uuid default gen_random_uuid() primary key,
  organization_id uuid references organizations(id) on delete cascade not null,
  user_id uuid references auth.users(id) on delete cascade not null,
  role text not null check (role in ('admin', 'staff')),
  invited_by uuid references auth.users(id),
  invited_at timestamp with time zone default timezone('utc'::text, now()) not null,
  accepted_at timestamp with time zone,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null,
  
  -- Ensure a user can only be a member of an organization once
  unique(organization_id, user_id)
);

-- Create invitations table
create table if not exists invitations (
  id uuid default gen_random_uuid() primary key,
  email text not null,
  organization_id uuid references organizations(id) on delete cascade not null,
  role text not null check (role in ('admin', 'staff')),
  invited_by uuid references auth.users(id) not null,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  expires_at timestamp with time zone not null,
  accepted_at timestamp with time zone,
  status text not null check (status in ('pending', 'accepted', 'expired', 'revoked')) default 'pending',
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null,
  
  -- Ensure an email can only be invited to an organization once
  unique(organization_id, email)
);

-- Create indexes for better query performance
create index if not exists idx_organization_members_org_id on organization_members(organization_id);
create index if not exists idx_organization_members_user_id on organization_members(user_id);
create index if not exists idx_invitations_email on invitations(email);
create index if not exists idx_invitations_org_id on invitations(organization_id);
create index if not exists idx_invitations_status on invitations(status);

-- Create function and trigger to automatically update updated_at column
create or replace function update_updated_at_column()
returns trigger as $$
begin
   new.updated_at = timezone('utc'::text, now());
   return new;
end;
$$ language plpgsql;

create trigger update_invitations_updated_at 
before update on invitations 
for each row 
execute procedure update_updated_at_column();

-- Enable RLS on all tables
alter table organizations enable row level security;
alter table organization_members enable row level security;
alter table invitations enable row level security;

-- Organization policies
create policy "Users can view their organizations" on organizations
  for select using (
    id in (
      select organization_id 
      from organization_members 
      where user_id = auth.uid()
    )
  );

create policy "Organization owners can create organizations" on organizations
  for insert with check (owner_id = auth.uid());

create policy "Organization owners can update their organizations" on organizations
  for update using (owner_id = auth.uid());

create policy "Organization owners can delete their organizations" on organizations
  for delete using (owner_id = auth.uid());

-- Organization members policies
create policy "Users can view organization members" on organization_members
  for select using (
    organization_id in (
      select organization_id 
      from organization_members 
      where user_id = auth.uid()
    )
  );

create policy "Organization admins can invite members" on organization_members
  for insert with check (
    organization_id in (
      select organization_id 
      from organization_members 
      where user_id = auth.uid() and role = 'admin'
    )
  );

create policy "Organization admins can update member roles" on organization_members
  for update using (
    organization_id in (
      select organization_id 
      from organization_members 
      where user_id = auth.uid() and role = 'admin'
    )
  );

create policy "Organization admins can remove members" on organization_members
  for delete using (
    organization_id in (
      select organization_id 
      from organization_members 
      where user_id = auth.uid() and role = 'admin'
    )
  );

-- Invitations policies
create policy "Users can view invitations for their organizations" on invitations
  for select using (
    organization_id in (
      select organization_id 
      from organization_members 
      where user_id = auth.uid()
    )
  );

create policy "Organization admins can create invitations" on invitations
  for insert with check (
    organization_id in (
      select organization_id 
      from organization_members 
      where user_id = auth.uid() and role = 'admin'
    )
  );

create policy "Organization admins can update invitations" on invitations
  for update using (
    organization_id in (
      select organization_id 
      from organization_members 
      where user_id = auth.uid() and role = 'admin'
    )
  );

create policy "Organization admins can delete invitations" on invitations
  for delete using (
    organization_id in (
      select organization_id 
      from organization_members 
      where user_id = auth.uid() and role = 'admin'
    )
  );

-- Grant necessary permissions
grant all on organizations to authenticated;
grant all on organization_members to authenticated;
grant all on invitations to authenticated;

-- Create function to handle organization creation when a user signs up
create or replace function handle_new_organization()
returns trigger as $$
begin
  -- Create a default organization for the user
  insert into organizations (name, owner_id)
  values (coalesce(new.raw_user_meta_data->>'business_name', 'My Organization'), new.id);
  
  -- Add the user as an admin member of their organization
  insert into organization_members (organization_id, user_id, role, invited_by)
  values (
    (select id from organizations where owner_id = new.id order by created_at limit 1),
    new.id,
    'admin',
    new.id
  );
  
  return new;
end;
$$ language plpgsql security definer;

-- Create trigger for automatic organization creation
create trigger on_auth_user_created_for_org
  after insert on auth.users
  for each row execute procedure public.handle_new_organization();

-- Update the handle_new_user function to work with organizations
create or replace function handle_new_user()
returns trigger as $$
begin
  insert into public.profiles (id, business_name, currency, avatar_url, first_name, last_name, display_name, job_title)
  values (
    new.id, 
    new.raw_user_meta_data->>'business_name',
    coalesce(new.raw_user_meta_data->>'currency', 'USD'),
    null,
    new.raw_user_meta_data->>'first_name',
    new.raw_user_meta_data->>'last_name',
    new.raw_user_meta_data->>'display_name',
    new.raw_user_meta_data->>'job_title'
  );
  return new;
end;
$$ language plpgsql security definer;