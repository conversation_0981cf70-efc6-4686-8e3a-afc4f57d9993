'use client'

import React, { createContext, useContext, useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase'
import { useAuth } from '@/contexts/auth-context'
import { SUPPORTED_CURRENCIES, CurrencyCode } from '@/lib/currency'

interface CurrencyContextType {
  userCurrency: CurrencyCode
  isLoading: boolean
  formatCurrency: (amount: number, currency?: CurrencyCode) => string
  getCurrencyInfo: (currency?: CurrencyCode) => { symbol: string; name: string; locale: string }
  getCurrencySymbol: (currency?: CurrencyCode) => string
  updateUserCurrency: (currency: CurrencyCode) => Promise<boolean>
  supportedCurrencies: typeof SUPPORTED_CURRENCIES
}

const CurrencyContext = createContext<CurrencyContextType | undefined>(undefined)

export function CurrencyProvider({ children }: { children: React.ReactNode }) {
  const { user } = useAuth()
  const supabase = createSupabaseClient()
  const [userCurrency, setUserCurrency] = useState<CurrencyCode>('USD')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (user) {
      loadUserCurrency()
    } else {
      // Reset to default when user logs out
      setUserCurrency('USD')
      setIsLoading(false)
    }
  }, [user])

  const loadUserCurrency = async () => {
    // Skip if no user
    if (!user?.id) {
      setIsLoading(false)
      return
    }

    try {
      setIsLoading(true)
      const { data, error } = await supabase
        .from('profiles')
        .select('currency')
        .eq('id', user.id)
        .single()

      if (error) {
        console.error('Error loading user currency:', error)
        // Keep default currency but stop loading
        setIsLoading(false)
        return
      }

      if (data?.currency && SUPPORTED_CURRENCIES[data.currency as CurrencyCode]) {
        // Only log if currency actually changes
        if (userCurrency !== data.currency) {
          console.log(`[Currency] Setting user currency to: ${data.currency}`)
        }
        setUserCurrency(data.currency as CurrencyCode)
      } else {
        // Only log if we're not already using USD
        if (userCurrency !== 'USD') {
          console.log('[Currency] No valid currency found, using USD as default')
        }
        setUserCurrency('USD')
      }
    } catch (error) {
      console.error('Error loading user currency:', error)
      // Keep default currency but stop loading
    } finally {
      setIsLoading(false)
    }
  }

  const updateUserCurrency = async (currency: CurrencyCode) => {
    if (!user) return false

    try {
      const { error } = await supabase
        .from('profiles')
        .update({ currency })
        .eq('id', user.id)

      if (error) {
        console.error('Error updating user currency:', error)
        return false
      }

      console.log(`[Currency] Updated user currency to: ${currency}`)
      setUserCurrency(currency)
      return true
    } catch (error) {
      console.error('Error updating user currency:', error)
      return false
    }
  }

  const formatCurrency = (amount: number, currency?: CurrencyCode) => {
    const currencyToUse = currency || userCurrency
    const currencyInfo = SUPPORTED_CURRENCIES[currencyToUse]
    
    // Debug log for currency formatting
    if (currencyToUse !== userCurrency) {
      console.log(`[Currency] Formatting with override currency: ${currencyToUse} (user: ${userCurrency})`)
    }
    
    const formatted = new Intl.NumberFormat(currencyInfo.locale, {
      style: 'currency',
      currency: currencyToUse,
    }).format(amount)
    
    return formatted
  }

  const getCurrencyInfo = (currency?: CurrencyCode) => {
    const currencyToUse = currency || userCurrency
    return SUPPORTED_CURRENCIES[currencyToUse]
  }

  const getCurrencySymbol = (currency?: CurrencyCode) => {
    return getCurrencyInfo(currency).symbol
  }

  const value = {
    userCurrency,
    isLoading,
    formatCurrency,
    getCurrencyInfo,
    getCurrencySymbol,
    updateUserCurrency,
    supportedCurrencies: SUPPORTED_CURRENCIES,
  }

  return (
    <CurrencyContext.Provider value={value}>
      {children}
    </CurrencyContext.Provider>
  )
}

export function useCurrency() {
  const context = useContext(CurrencyContext)
  if (context === undefined) {
    throw new Error('useCurrency must be used within a CurrencyProvider')
  }
  return context
}