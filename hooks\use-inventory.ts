import { useQuery, useQueryClient } from '@tanstack/react-query'
import { getProductsForOrganization } from '@/lib/supabase'
import type { ProductRow, CategoryRow } from '@/lib/supabase'
import type { InventoryItem } from '@/components/products/inventory/types'

// Extended ProductRow type with category_name property (added during data transformation)
type ExtendedProductRow = ProductRow & {
  category_name?: string | null
  variants?: any[]
}

// Debounce function to prevent rapid requests
function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  return function (...args: Parameters<T>) {
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(() => func(...args), wait)
  }
}

/**
 * Custom hook to fetch inventory data with React Query
 * @param organizationId - The organization ID to fetch inventory for
 * @returns React Query result object with inventory data
 */
export function useInventory(organizationId: string | undefined) {
  return useQuery<InventoryItem[], Error>({
    queryKey: ['inventory', organizationId],
    queryFn: async () => {
      if (!organizationId) {
        throw new Error('Organization ID is required')
      }
      
      try {
        const data = await getProductsForOrganization(organizationId)
        
        // Transform data to include category name and process variants
        const transformedProducts = (data || []).map(product => {
          // Handle categories which might be an array or object
          let categoryName = null
          if (product.categories) {
            if (Array.isArray(product.categories) && product.categories.length > 0) {
              categoryName = (product.categories[0] as CategoryRow)?.name || null
            } else if (!Array.isArray(product.categories) && (product.categories as CategoryRow)?.name) {
              categoryName = (product.categories as CategoryRow).name
            }
          }
          
          return {
            ...product,
            category_name: categoryName,
            variants: product.product_variants || []
          }
        })
        
        // Transform products and variants into flat inventory items
        const inventoryItems: InventoryItem[] = []
        
        // Process all products (both simple and variable)
        transformedProducts.forEach(product => {
          // Process simple products (without variants)
          if (!product.has_variants) {
            inventoryItems.push({
              id: product.id,
              productId: product.id,
              variantId: null,
              name: product.name,
              sku: product.base_sku || '',
              stockOnHand: product.stock_quantity || 0,
              committed: 0, // This would come from order data
              available: product.stock_quantity || 0,
              incoming: 0, // This would come from purchase order data
              status: product.stock_quantity === 0 
                ? 'out_of_stock' 
                : (product.stock_quantity || 0) <= (product.low_stock_threshold || 10)
                  ? 'low_stock'
                  : 'in_stock',
              type: 'product',
              lowStockThreshold: product.low_stock_threshold || 10
            })
          }
          // Process variable products and their variants
          else if (product.has_variants && product.variants && product.variants.length > 0) {
            // Calculate parent product status based on variants
            let productStatus: 'in_stock' | 'low_stock' | 'out_of_stock' = 'in_stock';
            let totalStock = 0;
            let totalCommitted = 0;
            let minThreshold = Infinity;
            
            // Check if all variants are out of stock
            const allOutOfStock = product.variants.every(v => (v.stock_quantity || 0) === 0);
            
            // Check if any variant is out of stock
            const hasOutOfStock = product.variants.some(v => (v.stock_quantity || 0) === 0);
            
            // Check if any variant is low stock
            const hasLowStock = product.variants.some(v => {
              const stockQty = v.stock_quantity || 0;
              const threshold = v.low_stock_threshold || 10;
              return stockQty > 0 && stockQty <= threshold;
            });
            
            // Determine parent product status based on variants
            if (allOutOfStock) {
              productStatus = 'out_of_stock';
            } else if (hasOutOfStock || hasLowStock) {
              productStatus = 'low_stock';
            }
            
            // Calculate totals for parent product
            product.variants.forEach(variant => {
              totalStock += variant.stock_quantity || 0;
              totalCommitted += variant.reserved_quantity || 0;
              minThreshold = Math.min(minThreshold, variant.low_stock_threshold || 10);
            });
            
            // If minThreshold wasn't updated, set to default
            if (minThreshold === Infinity) {
              minThreshold = 10;
            }
            
            inventoryItems.push({
              id: product.id,
              productId: product.id,
              variantId: null,
              name: product.name,
              sku: product.base_sku || '',
              stockOnHand: totalStock,
              committed: totalCommitted,
              available: totalStock - totalCommitted,
              incoming: 0, // This would come from purchase order data
              status: productStatus,
              type: 'product',
              lowStockThreshold: minThreshold
            })
            
            // Process variants
            product.variants.forEach(variant => {
              inventoryItems.push({
                id: variant.id,
                productId: variant.product_id,
                variantId: variant.id,
                name: `${product.name} - ${variant.variant_name || ''}`,
                sku: variant.sku,
                stockOnHand: variant.stock_quantity || 0,
                committed: variant.reserved_quantity || 0,
                available: (variant.stock_quantity || 0) - (variant.reserved_quantity || 0),
                incoming: 0, // This would come from purchase order data
                status: variant.stock_quantity === 0 
                  ? 'out_of_stock' 
                  : (variant.stock_quantity || 0) <= (variant.low_stock_threshold || 10)
                    ? 'low_stock'
                    : 'in_stock',
                type: 'variant',
                lowStockThreshold: variant.low_stock_threshold || 10
              })
            })
          }
          // Handle variable products with no variants (edge case)
          else if (product.has_variants) {
            // Treat as out of stock if it's a variable product but has no variants
            inventoryItems.push({
              id: product.id,
              productId: product.id,
              variantId: null,
              name: product.name,
              sku: product.base_sku || '',
              stockOnHand: 0,
              committed: 0,
              available: 0,
              incoming: 0,
              status: 'out_of_stock',
              type: 'product',
              lowStockThreshold: product.low_stock_threshold || 10
            })
          }
        })
        
        return inventoryItems
      } catch (error) {
        console.error('Error in useInventory query function:', error)
        // Re-throw the error so React Query can handle it properly
        throw error
      }
    },
    enabled: !!organizationId,
    staleTime: 30 * 1000, // Reduced to 30 seconds for more responsive updates
    gcTime: 5 * 60 * 1000, // 5 minutes garbage collection time
    refetchOnMount: true,
    refetchOnWindowFocus: true, // Important for tab switching
    refetchOnReconnect: true,
    refetchInterval: false,
    refetchIntervalInBackground: false,
    retry: 3, // Increased retry count
    retryDelay: (attemptIndex) => {
      return Math.min(500 * Math.pow(2, attemptIndex), 5000)
    },
    networkMode: 'online', // Handle offline scenarios
    // Add a timeout to prevent indefinite loading
    meta: {
      errorPolicy: 'all'
    }
  })
}

/**
 * Custom hook to invalidate inventory queries and trigger refetching with debouncing
 * @returns Object with functions to invalidate queries
 */
export function useInvalidateInventory() {
  const queryClient = useQueryClient()
  
  // Debounced invalidate function
  const debouncedInvalidateInventory = debounce(() => {
    queryClient.invalidateQueries({ queryKey: ['inventory'] })
  }, 1000) // 1 second debounce
  
  return {
    invalidateInventory: debouncedInvalidateInventory
  }
}