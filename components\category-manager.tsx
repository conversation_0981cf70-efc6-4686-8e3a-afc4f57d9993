'use client'

import { useState, useEffect, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  FolderOpen,
  Plus,
  Edit3,
  Trash2,
  Save,
  X,
  BarChart3,
  TrendingUp,
  AlertTriangle,
  Check,
  Merge,
  Download,
  FileSpreadsheet,
  FileText,
  ChevronDown
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useToast } from '@/components/ui/use-toast'
import { type ExpenseRow } from '@/lib/supabase'
import { useCurrency } from '@/lib/currency'
import { cn } from '@/lib/utils'

// Category statistics interface
interface CategoryStats {
  category: string
  count: number
  totalAmount: number
  averageAmount: number
  percentage: number
  lastUsed: Date | null
  isDefault: boolean
  canDelete: boolean
}

// Default categories that cannot be deleted
const DEFAULT_CATEGORIES = [
  'Office', 'Travel', 'Meals', 'Software', 'Marketing', 
  'Utilities', 'Equipment', 'Professional', 'Insurance', 'General'
]

interface CategoryManagerProps {
  expenses: ExpenseRow[]
  onCategoryUpdate?: (oldCategory: string, newCategory: string) => void
  onCategoryDelete?: (category: string) => void
  isCollapsible?: boolean
}

export function CategoryManager({
  expenses,
  onCategoryUpdate,
  onCategoryDelete,
  isCollapsible = true
}: CategoryManagerProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [editingCategory, setEditingCategory] = useState<string | null>(null)
  const [newCategoryName, setNewCategoryName] = useState('')
  const [isAddingNew, setIsAddingNew] = useState(false)
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [mergeTarget, setMergeTarget] = useState('')
  
  const { toast } = useToast()
  const { formatCurrency } = useCurrency()

  // Calculate category statistics
  const categoryStats = useMemo((): CategoryStats[] => {
    const stats = new Map<string, {
      count: number
      totalAmount: number
      lastUsed: Date | null
    }>()

    // Initialize all categories (including unused ones)
    const allCategories = new Set<string>()
    expenses.forEach(expense => {
      if (expense.category) allCategories.add(expense.category)
    })
    DEFAULT_CATEGORIES.forEach(cat => allCategories.add(cat))

    // Calculate stats for each category
    allCategories.forEach(category => {
      stats.set(category, { count: 0, totalAmount: 0, lastUsed: null })
    })

    // Process expenses
    expenses.forEach(expense => {
      if (expense.category) {
        const current = stats.get(expense.category)!
        const expenseDate = new Date(expense.expense_date || expense.created_at)
        
        current.count++
        current.totalAmount += expense.amount
        
        if (!current.lastUsed || expenseDate > current.lastUsed) {
          current.lastUsed = expenseDate
        }
      }
    })

    const totalAmount = expenses.reduce((sum, exp) => sum + exp.amount, 0)

    return Array.from(stats.entries()).map(([category, data]) => ({
      category,
      count: data.count,
      totalAmount: data.totalAmount,
      averageAmount: data.count > 0 ? data.totalAmount / data.count : 0,
      percentage: totalAmount > 0 ? (data.totalAmount / totalAmount) * 100 : 0,
      lastUsed: data.lastUsed,
      isDefault: DEFAULT_CATEGORIES.includes(category),
      canDelete: !DEFAULT_CATEGORIES.includes(category) && data.count === 0
    }))
    .sort((a, b) => {
      // Sort by usage (count), then by total amount
      if (b.count !== a.count) return b.count - a.count
      return b.totalAmount - a.totalAmount
    })
  }, [expenses])

  // Handle category rename
  const handleRenameCategory = async (oldName: string, newName: string) => {
    if (!newName.trim() || newName === oldName) {
      setEditingCategory(null)
      setNewCategoryName('')
      return
    }

    // Check if new name already exists
    if (categoryStats.some(cat => cat.category.toLowerCase() === newName.toLowerCase() && cat.category !== oldName)) {
      toast({
        title: "Category Already Exists",
        description: `A category named "${newName}" already exists.`,
        variant: "destructive",
      })
      return
    }

    try {
      // Here you would typically update the database
      // For now, we'll just notify the parent component
      onCategoryUpdate?.(oldName, newName)
      
      toast({
        title: "Category Renamed",
        description: `Category "${oldName}" has been renamed to "${newName}".`,
      })
      
      setEditingCategory(null)
      setNewCategoryName('')
    } catch (error) {
      toast({
        title: "Error Renaming Category",
        description: "Failed to rename category. Please try again.",
        variant: "destructive",
      })
    }
  }

  // Handle category deletion
  const handleDeleteCategory = async (category: string) => {
    const categoryData = categoryStats.find(cat => cat.category === category)
    
    if (!categoryData?.canDelete) {
      toast({
        title: "Cannot Delete Category",
        description: categoryData?.isDefault 
          ? "Default categories cannot be deleted."
          : "Cannot delete category with existing expenses.",
        variant: "destructive",
      })
      return
    }

    try {
      onCategoryDelete?.(category)
      
      toast({
        title: "Category Deleted",
        description: `Category "${category}" has been deleted.`,
      })
    } catch (error) {
      toast({
        title: "Error Deleting Category",
        description: "Failed to delete category. Please try again.",
        variant: "destructive",
      })
    }
  }

  // Toggle category selection for merging
  const toggleCategorySelection = (category: string) => {
    setSelectedCategories(prev => 
      prev.includes(category) 
        ? prev.filter(c => c !== category) 
        : [...prev, category]
    )
  }

  // Handle merge categories
  const handleMergeCategories = () => {
    if (selectedCategories.length < 2) {
      toast({
        title: "Insufficient Selection",
        description: "Please select at least 2 categories to merge.",
        variant: "destructive",
      })
      return
    }

    if (!mergeTarget) {
      toast({
        title: "No Target Category",
        description: "Please select a target category for merging.",
        variant: "destructive",
      })
      return
    }

    toast({
      title: "Categories Merged",
      description: `Categories ${selectedCategories.join(', ')} have been merged into "${mergeTarget}".`,
    })

    setSelectedCategories([])
    setMergeTarget('')
  }

  // Export categories
  const handleExportCategories = (format: 'csv' | 'json' | 'txt') => {
    toast({
      title: "Export Started",
      description: `Exporting categories as ${format.toUpperCase()}...`,
    })
  }

  if (categoryStats.length === 0) {
    return (
      <div className="text-center py-8">
        <FolderOpen className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-base font-medium text-gray-900 mb-1">No Categories Found</h3>
        <p className="text-sm text-gray-500">Add expenses to create categories</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Add New Category */}
      {isAddingNew ? (
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center gap-2">
            <Input
              value={newCategoryName}
              onChange={(e) => setNewCategoryName(e.target.value)}
              placeholder="Enter new category name"
              className="flex-1 text-sm h-9"
              autoFocus
            />
            <Button 
              size="sm" 
              onClick={() => {
                if (newCategoryName.trim()) {
                  // In a real app, you would add to database
                  toast({
                    title: "Category Added",
                    description: `New category "${newCategoryName}" has been added.`,
                  })
                  setIsAddingNew(false)
                  setNewCategoryName('')
                }
              }}
              className="h-9 px-3 text-sm"
            >
              <Save className="h-4 w-4 mr-1" />
              Save
            </Button>
            <Button 
              size="sm" 
              variant="outline" 
              onClick={() => {
                setIsAddingNew(false)
                setNewCategoryName('')
              }}
              className="h-9 px-3 text-sm"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      ) : (
        <div className="flex justify-between items-center">
          <h4 className="text-sm font-medium text-gray-900">Categories</h4>
          <Button 
            size="sm" 
            onClick={() => setIsAddingNew(true)}
            className="h-8 px-2 text-xs"
          >
            <Plus className="h-3 w-3 mr-1" />
            Add Category
          </Button>
        </div>
      )}

      {/* Category List */}
      <div className="space-y-3">
        {categoryStats.map((stat) => (
          <div 
            key={stat.category} 
            className={cn(
              "flex items-center justify-between p-3 rounded-lg border",
              selectedCategories.includes(stat.category) ? "bg-blue-50 border-blue-200" : "bg-white border-gray-200"
            )}
          >
            <div className="flex items-center space-x-3">
              {selectedCategories.includes(stat.category) ? (
                <div className="w-5 h-5 rounded-full bg-primary flex items-center justify-center">
                  <Check className="h-3 w-3 text-white" />
                </div>
              ) : (
                <button 
                  onClick={() => toggleCategorySelection(stat.category)}
                  className="w-5 h-5 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
                />
              )}
              
              {editingCategory === stat.category ? (
                <div className="flex items-center gap-2">
                  <Input
                    value={newCategoryName}
                    onChange={(e) => setNewCategoryName(e.target.value)}
                    className="w-32 text-sm h-8"
                    autoFocus
                  />
                  <Button 
                    size="sm" 
                    onClick={() => handleRenameCategory(stat.category, newCategoryName)}
                    className="h-8 px-2 text-xs"
                  >
                    <Save className="h-3 w-3" />
                  </Button>
                  <Button 
                    size="sm" 
                    variant="outline" 
                    onClick={() => {
                      setEditingCategory(null)
                      setNewCategoryName('')
                    }}
                    className="h-8 px-2 text-xs"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              ) : (
                <div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-gray-900">{stat.category}</span>
                    {stat.isDefault && (
                      <span className="inline-flex items-center rounded-full bg-gray-100 px-2 py-0.5 text-xs font-medium text-gray-800">
                        Default
                      </span>
                    )}
                  </div>
                  <div className="flex items-center gap-3 mt-1">
                    <span className="text-xs text-gray-500">
                      {stat.count} {stat.count === 1 ? 'item' : 'items'}
                    </span>
                    {stat.totalAmount > 0 && (
                      <span className="text-xs text-gray-500">
                        {formatCurrency(stat.totalAmount)}
                      </span>
                    )}
                    {stat.lastUsed && (
                      <span className="text-xs text-gray-500">
                        Last used: {stat.lastUsed.toLocaleDateString()}
                      </span>
                    )}
                  </div>
                </div>
              )}
            </div>
            
            <div className="flex items-center space-x-1">
              <Button 
                size="sm" 
                variant="outline" 
                onClick={() => {
                  setEditingCategory(stat.category)
                  setNewCategoryName(stat.category)
                }}
                disabled={editingCategory !== null}
                className="h-6 w-6 p-0"
              >
                <Edit3 className="h-3 w-3" />
              </Button>
              <Button 
                size="sm" 
                variant="outline" 
                onClick={() => handleDeleteCategory(stat.category)}
                disabled={!stat.canDelete || editingCategory !== null}
                className="h-6 w-6 p-0"
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
          </div>
        ))}
      </div>

      {/* Bulk Actions */}
      {selectedCategories.length > 0 && (
        <div className="bg-gray-50 rounded-lg p-4 space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-900">
              {selectedCategories.length} selected
            </span>
            <Button 
              size="sm" 
              variant="ghost" 
              onClick={() => setSelectedCategories([])}
              className="h-8 px-2 text-xs"
            >
              Clear
            </Button>
          </div>
          
          <div className="flex items-center gap-2">
            <Label htmlFor="merge-target" className="text-sm text-gray-700">Merge into:</Label>
            <Select value={mergeTarget} onValueChange={setMergeTarget}>
              <SelectTrigger className="w-40 text-sm h-8">
                <SelectValue placeholder="Select target" />
              </SelectTrigger>
              <SelectContent>
                {categoryStats
                  .filter(stat => !selectedCategories.includes(stat.category))
                  .map(stat => (
                    <SelectItem key={stat.category} value={stat.category}>
                      {stat.category}
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
            <Button 
              size="sm" 
              onClick={handleMergeCategories}
              disabled={!mergeTarget}
              className="h-8 px-2 text-xs"
            >
              <Merge className="h-3 w-3 mr-1" />
              Merge
            </Button>
          </div>
        </div>
      )}

      {/* Export Options */}
      <div className="flex justify-end">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="h-8 px-2 text-xs">
              <Download className="h-3 w-3 mr-1" />
              Export
              <ChevronDown className="h-3 w-3 ml-1" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => handleExportCategories('csv')}>
              <FileSpreadsheet className="h-4 w-4 mr-2" />
              Export as CSV
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleExportCategories('json')}>
              <FileText className="h-4 w-4 mr-2" />
              Export as JSON
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleExportCategories('txt')}>
              <FileText className="h-4 w-4 mr-2" />
              Export as Text
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  )
}