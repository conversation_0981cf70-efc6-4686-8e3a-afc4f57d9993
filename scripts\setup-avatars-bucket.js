// <PERSON>ript to create and configure the avatars storage bucket in Supabase
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Please set NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY in .env.local');
  process.exit(1);
}

// Create Supabase client with service role key (has admin privileges)
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function setupAvatarsBucket() {
  console.log('🔧 Setting up avatars storage bucket...');
  
  try {
    // Step 1: Check if bucket already exists
    const { data: buckets, error: listError } = await supabase.storage.listBuckets();
    
    if (listError) {
      throw new Error(`Error listing buckets: ${listError.message}`);
    }
    
    const avatarsBucket = buckets.find(bucket => bucket.name === 'avatars');
    
    // Step 2: Create bucket if it doesn't exist
    if (!avatarsBucket) {
      console.log('Creating new avatars bucket...');
      
      const { data, error } = await supabase.storage.createBucket('avatars', {
        public: true, // Make the bucket publicly accessible
        fileSizeLimit: 5 * 1024 * 1024, // 5MB limit
        allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
      });
      
      if (error) {
        throw new Error(`Error creating bucket: ${error.message}`);
      }
      
      console.log('✅ Avatars bucket created successfully');
    } else {
      console.log('✅ Avatars bucket already exists');
      
      // Update bucket settings to ensure correct configuration
      const { error } = await supabase.storage.updateBucket('avatars', {
        public: true,
        fileSizeLimit: 5 * 1024 * 1024,
        allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
      });
      
      if (error) {
        throw new Error(`Error updating bucket: ${error.message}`);
      }
      
      console.log('✅ Avatars bucket settings updated');
    }
    
    // Step 3: Set up bucket policy to allow public access for avatars
    console.log('Configuring storage policies...');
    
    // Create a policy to allow authenticated users to upload files
    try {
      await supabase.rpc('create_storage_policy', {
        bucket_name: 'avatars',
        policy_name: 'authenticated can upload',
        definition: `(bucket_id = 'avatars'::text) AND (auth.role() = 'authenticated'::text)`,
        policy_for: 'INSERT'
      });
    } catch (error) {
      console.log('Note: Policy may already exist or require manual creation:', error.message);
    }
    
    // Create a policy to allow public access to read files
    try {
      await supabase.rpc('create_storage_policy', {
        bucket_name: 'avatars',
        policy_name: 'anyone can download',
        definition: `(bucket_id = 'avatars'::text)`,
        policy_for: 'SELECT'
      });
    } catch (error) {
      console.log('Note: Policy may already exist or require manual creation:', error.message);
    }
    
    // Create a policy to allow users to update their own avatars
    try {
      await supabase.rpc('create_storage_policy', {
        bucket_name: 'avatars',
        policy_name: 'users can update own avatars',
        definition: `(bucket_id = 'avatars'::text) AND (auth.uid()::text = (storage.foldername(name))[1])`,
        policy_for: 'UPDATE'
      });
    } catch (error) {
      console.log('Note: Policy may already exist or require manual creation:', error.message);
    }
    
    // Create a policy to allow users to delete their own avatars
    try {
      await supabase.rpc('create_storage_policy', {
        bucket_name: 'avatars',
        policy_name: 'users can delete own avatars',
        definition: `(bucket_id = 'avatars'::text) AND (auth.uid()::text = (storage.foldername(name))[1])`,
        policy_for: 'DELETE'
      });
    } catch (error) {
      console.log('Note: Policy may already exist or require manual creation:', error.message);
    }
    
    console.log('✅ Storage policies configured');
    console.log('\n🎉 Avatars storage bucket setup completed successfully!');
    console.log('\nYou can now upload profile pictures through the application.');
    
  } catch (error) {
    console.error('❌ Error setting up avatars bucket:', error.message);
    console.error('\n💡 Manual Setup Instructions:');
    console.error('1. Go to your Supabase dashboard: https://supabase.com/dashboard');
    console.error('2. Select your project');
    console.error('3. Go to Storage > Buckets');
    console.error('4. Create a new bucket named "avatars"');
    console.error('5. Set the bucket visibility to "Public"');
    console.error('6. Configure the following policies:');
    console.error('   - Allow authenticated users to upload');
    console.error('   - Allow public read access');
    console.error('   - Allow users to update/delete their own files');
    process.exit(1);
  }
}

setupAvatarsBucket();