import { ProductRow, ProductVariantRow } from '@/lib/supabase'

// Purchase Order Status Enum
export type PurchaseOrderStatus = 
  | 'draft'
  | 'open'
  | 'partially_received'
  | 'received'
  | 'cancelled'

// Purchase Order Interface
export interface PurchaseOrder {
  id: string
  user_id: string
  po_number: string
  supplier: string
  status: PurchaseOrderStatus
  issue_date: string // ISO date string
  expected_arrival_date: string | null // ISO date string
  received_date: string | null // ISO date string

  // Financial Information
  subtotal: number
  tax_rate: number
  tax_amount: number
  shipping_cost: number
  other_charges: number
  discount_amount: number
  total_value: number
  currency: string
  notes: string | null
  reference_number: string | null
  created_at: string // ISO date string
  updated_at: string // ISO date string
  
  // Additional properties
  item_count?: number
  
  // Computed properties for display
  formatted_issue_date?: string
  formatted_expected_arrival_date?: string
  formatted_received_date?: string
  status_display?: string
  status_color?: string
}

// Purchase Order Item Interface
export interface PurchaseOrderItem {
  id: string
  purchase_order_id: string
  user_id: string

  // Product/Variant Reference
  product_id: string | null
  variant_id: string | null

  // Item Information
  product_name: string
  variant_name?: string | null // Display name for variant
  sku: string | null
  description: string | null

  // Variant Attributes (new fields)
  variant_attributes?: {
    size?: string
    color?: string
    material?: string
    style?: string
  } | null

  // Quantity and Pricing
  quantity_ordered: number
  quantity_received: number
  unit_cost: number
  subtotal: number

  // Metadata
  created_at: string // ISO date string
  updated_at: string // ISO date string

  // Related data (optional, for display purposes)
  product?: ProductRow
  variant?: ProductVariantRow
}

// Extended Purchase Order with items
export interface ExtendedPurchaseOrder extends PurchaseOrder {
  items: PurchaseOrderItem[]
}

// Form data for creating/editing purchase orders
export interface PurchaseOrderFormData {
  supplier: string
  issue_date: Date
  expected_arrival_date: Date | undefined
  notes: string
  reference_number: string
  items: PurchaseOrderItemFormData[]
  
  // Financial fields
  subtotal: number
  tax_rate: number
  tax_amount: number
  shipping_cost: number
  other_charges: number
  discount_amount: number
  total_value: number
}

// Form data for purchase order items
export interface PurchaseOrderItemFormData {
  id?: string
  productId: string | null
  variantId: string | null
  productName: string
  sku: string
  quantity: number
  unitCost: number
  subtotal: number
  
  // Additional fields
  variantName?: string
  variantAttributes?: {
    size?: string
    color?: string
    material?: string
    style?: string
  }
}

// Filter options for purchase orders
export interface PurchaseOrderFilters {
  search: string
  status: PurchaseOrderStatus | 'all'
  supplier: string
  dateRange: {
    from: Date | undefined
    to: Date | undefined
  }
}

// Default filter values
export const INITIAL_PURCHASE_ORDER_FILTERS: PurchaseOrderFilters = {
  search: '',
  status: 'all',
  supplier: '',
  dateRange: {
    from: undefined,
    to: undefined
  }
}