'use client';

import { useState } from 'react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, XCircle, RefreshCw } from 'lucide-react';
import type { VaultIntegration } from '@/types/integrations';

interface VaultStatusCardProps {
  integration: VaultIntegration;
  onDisconnect: () => void;
}

export default function VaultStatusCard({ integration, onDisconnect }: VaultStatusCardProps) {
  const { toast } = useToast();
  const [isDisconnectModalOpen, setIsDisconnectModalOpen] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<{connected: boolean; message: string} | null>(null);
  const supabase = createClientComponentClient();

  const checkConnectionStatus = async () => {
    try {
      // In a real implementation, this would check connection to the Vault API
      // For now, we'll simulate this with a timeout
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setConnectionStatus({
        connected: true,
        message: 'Successfully connected to Vault'
      });
    } catch (error: any) {
      setConnectionStatus({
        connected: false,
        message: `Connection failed: ${error.message || 'Unknown error'}`
      });
    }
  };

  const handleSyncData = async () => {
    setIsSyncing(true);
    try {
      // In a real implementation, this would sync data from Vault
      // For now, we'll simulate this with a timeout
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      toast({
        title: 'Sync Initiated',
        description: 'Data sync has been initiated. This may take a few minutes.',
      });
    } catch (error: any) {
      toast({
        title: 'Sync Failed',
        description: error.message || 'Failed to initiate data sync',
        variant: 'destructive',
      });
    } finally {
      setIsSyncing(false);
    }
  };

  const handleConfirmDisconnect = async () => {
    try {
      // Delete the integration
      const { error } = await supabase
        .from('integrations')
        .delete()
        .eq('id', integration.id);

      if (error) throw error;

      toast({
        title: 'Disconnected',
        description: 'Vault has been successfully disconnected.',
      });

      setIsDisconnectModalOpen(false);
      onDisconnect();
    } catch (error: any) {
      toast({
        title: 'Disconnect Failed',
        description: error.message || 'Failed to disconnect Vault',
        variant: 'destructive',
      });
    }
  };

  // Format the last sync time
  const formatLastSync = (dateString?: string) => {
    if (!dateString) return 'Never';
    
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    return `${Math.floor(diffInSeconds / 86400)} days ago`;
  };

  // Check if credentials are properly set
  const hasValidCredentials = integration.vault_url && integration.api_key;

  return (
    <>
      <Card className="flex flex-col">
        <CardHeader>
          <div className="flex items-center space-x-3">
            <div className="bg-blue-100 p-2 rounded-md">
              <div className="bg-blue-500 text-white p-2 rounded">
                {/* Vault Icon Placeholder */}
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
                  <path d="M9.5 9h5L16 12h-5Z"/>
                  <path d="M12 9v5"/>
                </svg>
              </div>
            </div>
            <div>
              <CardTitle>Vault</CardTitle>
              <Badge variant={hasValidCredentials ? "success" : "destructive"} className="mt-1">
                <span className="flex items-center">
                  {hasValidCredentials ? (
                    <>
                      <span className="w-2 h-2 rounded-full bg-green-500 mr-1"></span>
                      Connected
                    </>
                  ) : (
                    <>
                      <span className="w-2 h-2 rounded-full bg-red-500 mr-1"></span>
                      Incomplete Setup
                    </>
                  )}
                </span>
              </Badge>
            </div>
          </div>
          <CardDescription>
            Securely store and manage your financial data with Vault
          </CardDescription>
        </CardHeader>
        <CardContent className="flex-grow space-y-4">
          <div>
            <h4 className="text-sm font-medium text-muted-foreground">Vault URL</h4>
            <p className="text-sm">{integration.vault_url || 'Not available'}</p>
          </div>
          
          <div>
            <h4 className="text-sm font-medium text-muted-foreground">Connection Status</h4>
            {hasValidCredentials ? (
              <div className="flex items-center text-sm text-green-600">
                <CheckCircle className="h-4 w-4 mr-1" />
                Credentials verified
              </div>
            ) : (
              <div className="flex items-center text-sm text-red-600">
                <XCircle className="h-4 w-4 mr-1" />
                Missing credentials
              </div>
            )}
          </div>
          
          <div>
            <div className="flex justify-between items-center">
              <h4 className="text-sm font-medium text-muted-foreground">Connection Test</h4>
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={checkConnectionStatus}
                className="h-6 px-2 text-xs"
              >
                <RefreshCw className="h-3 w-3 mr-1" />
                Check
              </Button>
            </div>
            {connectionStatus ? (
              <Alert variant={connectionStatus.connected ? "default" : "destructive"} className="mt-1 py-2">
                {connectionStatus.connected ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  <XCircle className="h-4 w-4" />
                )}
                <AlertDescription className="text-xs">
                  {connectionStatus.message}
                </AlertDescription>
              </Alert>
            ) : (
              <p className="text-sm text-muted-foreground">Click "Check" to verify connection</p>
            )}
          </div>
          
          <div>
            <h4 className="text-sm font-medium text-muted-foreground">Last Sync</h4>
            <p className="text-sm">{formatLastSync(integration.updated_at)}</p>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button 
            onClick={handleSyncData}
            disabled={!hasValidCredentials || isSyncing}
          >
            {isSyncing ? 'Syncing...' : 'Sync Data'}
          </Button>
          <Button 
            variant="destructive" 
            onClick={() => setIsDisconnectModalOpen(true)}
          >
            Disconnect
          </Button>
        </CardFooter>
      </Card>

      <Dialog open={isDisconnectModalOpen} onOpenChange={setIsDisconnectModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Disconnect Vault</DialogTitle>
            <DialogDescription>
              Are you sure you want to disconnect your Vault? This will:
              <div className="mt-2 space-y-1">
                <ul className="list-disc list-inside space-y-1">
                  <li>Stop automatic syncing of financial data</li>
                  <li>Remove all integration credentials</li>
                  <li>Preserve existing synced data in Onko</li>
                </ul>
              </div>
              <div className="mt-3 font-medium">This action cannot be undone.</div>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDisconnectModalOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleConfirmDisconnect}>
              Disconnect Vault
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}