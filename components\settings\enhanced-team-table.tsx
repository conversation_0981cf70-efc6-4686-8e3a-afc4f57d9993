'use client'

import { useState } from 'react'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger
} from '@/components/ui/dropdown-menu'
import { Badge } from '@/components/ui/badge'
import { MoreHorizontal, User } from 'lucide-react'
import { TeamMember } from '@/hooks/use-team-members'
import { useUpdateUserRole, useRemoveUserFromTeam } from '@/hooks/use-team-members'
import { useToast } from '@/components/ui/use-toast'

interface EnhancedTeamTableProps {
  members: TeamMember[]
  isLoading: boolean
}

export function EnhancedTeamTable({ members, isLoading }: EnhancedTeamTableProps) {
  const { toast } = useToast()
  const { mutate: updateUserRole } = useUpdateUserRole()
  const { mutate: removeUserFromTeam } = useRemoveUserFromTeam()
  const [updatingUserId, setUpdatingUserId] = useState<string | null>(null)
  const [removingUserId, setRemovingUserId] = useState<string | null>(null)

  const handleRoleChange = (userId: string, newRole: 'admin' | 'staff') => {
    setUpdatingUserId(userId)
    updateUserRole(
      { userId, newRole },
      {
        onSuccess: () => {
          toast({
            title: 'Role updated',
            description: 'User role has been successfully updated.',
          })
          setUpdatingUserId(null)
        },
        onError: (error) => {
          toast({
            title: 'Error updating role',
            description: error.message || 'Failed to update user role.',
            variant: 'destructive',
          })
          setUpdatingUserId(null)
        },
      }
    )
  }

  const handleRemoveUser = (userId: string) => {
    setRemovingUserId(userId)
    removeUserFromTeam(userId, {
      onSuccess: () => {
        toast({
          title: 'User removed',
          description: 'User has been successfully removed from the team.',
        })
        setRemovingUserId(null)
      },
      onError: (error) => {
        toast({
          title: 'Error removing user',
          description: error.message || 'Failed to remove user from the team.',
          variant: 'destructive',
        })
        setRemovingUserId(null)
      },
    })
  }

  // Function to get user's display name
  const getUserDisplayName = (member: TeamMember) => {
    if (member.display_name) return member.display_name
    if (member.first_name && member.last_name) return `${member.first_name} ${member.last_name}`
    if (member.first_name) return member.first_name
    return member.email?.split('@')[0] || 'Unknown User'
  }

  // Function to get user initials for avatar fallback
  const getUserInitials = (member: TeamMember) => {
    if (member.first_name && member.last_name) {
      return `${member.first_name.charAt(0)}${member.last_name.charAt(0)}`.toUpperCase()
    }
    if (member.first_name) {
      return member.first_name.charAt(0).toUpperCase()
    }
    if (member.display_name) {
      return member.display_name.charAt(0).toUpperCase()
    }
    return member.email?.charAt(0).toUpperCase() || 'U'
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Member</TableHead>
            <TableHead>Email</TableHead>
            <TableHead>Role</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {members && members.length > 0 ? (
            members.map((member) => (
              <TableRow key={member.id}>
                <TableCell className="font-medium">
                  <div className="flex items-center space-x-3">
                    <Avatar className="h-8 w-8">
                      {member.avatar_url ? (
                        <AvatarImage src={member.avatar_url} alt={getUserDisplayName(member)} />
                      ) : (
                        <AvatarFallback className="bg-gray-200">
                          <User className="h-4 w-4 text-gray-600" />
                        </AvatarFallback>
                      )}
                    </Avatar>
                    <span>{getUserDisplayName(member)}</span>
                  </div>
                </TableCell>
                <TableCell>{member.email}</TableCell>
                <TableCell>
                  <Badge variant={member.role === 'admin' ? 'default' : 'secondary'}>
                    {member.role.charAt(0).toUpperCase() + member.role.slice(1)}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuSub>
                        <DropdownMenuSubTrigger>Change Role</DropdownMenuSubTrigger>
                        <DropdownMenuSubContent>
                          <DropdownMenuItem 
                            onClick={() => handleRoleChange(member.id, 'admin')}
                            disabled={updatingUserId === member.id}
                          >
                            {updatingUserId === member.id ? 'Updating...' : 'Admin'}
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleRoleChange(member.id, 'staff')}
                            disabled={updatingUserId === member.id}
                          >
                            {updatingUserId === member.id ? 'Updating...' : 'Staff'}
                          </DropdownMenuItem>
                        </DropdownMenuSubContent>
                      </DropdownMenuSub>
                      <DropdownMenuItem 
                        className="text-red-600"
                        onClick={() => handleRemoveUser(member.id)}
                        disabled={removingUserId === member.id}
                      >
                        {removingUserId === member.id ? 'Removing...' : 'Remove from Team'}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={4} className="text-center py-8 text-gray-500">
                No team members found
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  )
}