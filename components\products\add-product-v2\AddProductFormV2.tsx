import React from 'react'
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>ontent, 
  <PERSON><PERSON><PERSON><PERSON>er, 
  DialogTitle,
  DialogDescription
} from '@/components/ui/dialog'
import { ProductFormProvider } from './context/ProductFormContext'
import { FormNavigation } from './components/FormNavigation'
import { FormContent } from './components/FormContent'

interface AddProductFormV2Props {
  open: boolean
  onOpenChange: (open: boolean) => void
  onProductAdded?: () => void
}

export function AddProductFormV2({ open, onOpenChange, onProductAdded }: AddProductFormV2Props) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl h-[85vh] p-0 gap-0" hideCloseButton>
        <DialogHeader className="hidden">
          <DialogTitle>Add New Product (V2)</DialogTitle>
          <DialogDescription>
            Create a new product using the step-by-step form
          </DialogDescription>
        </DialogHeader>
        <ProductFormProvider>
          <div className="flex h-full bg-white rounded-lg overflow-hidden relative">
            {/* Custom close button positioned to avoid interfering with content */}
            <button
              onClick={() => onOpenChange(false)}
              className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground z-10"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
              <span className="sr-only">Close</span>
            </button>
            
            {/* Left Column - Navigation */}
            <div className="w-64 border-r border-gray-200 bg-gray-50 flex flex-col">
              <FormNavigation />
            </div>

            {/* Right Column - Content */}
            <div className="flex-1 flex flex-col overflow-hidden">
              <FormContent />
            </div>
          </div>
        </ProductFormProvider>
      </DialogContent>
    </Dialog>
  )
}