'use client';

import { useMutation } from '@tanstack/react-query';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

interface ConnectWooCommerceParams {
  storeUrl: string;
  consumerKey: string;
  consumerSecret: string;
}

interface ConnectWooCommerceResponse {
  success: boolean;
  integration_id?: string;
  message?: string;
  error?: string;
  webhook_secret?: string;
  webhook_registration_success?: boolean;
  webhook_registration_message?: string;
}

export const useConnectWooCommerce = ({
  onSuccess,
  onError
}: {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
} = {}) => {
  const supabase = createClientComponentClient();

  return useMutation({
    mutationFn: async ({ storeUrl, consumerKey, consumerSecret }: ConnectWooCommerceParams) => {
      try {
        // Validate inputs
        if (!storeUrl || !consumerKey || !consumerSecret) {
          throw new Error('All fields are required');
        }

        // Validate URL format
        try {
          new URL(storeUrl);
        } catch {
          throw new Error('Invalid store URL format');
        }

        // Check if user is authenticated
        const { data: { session } } = await supabase.auth.getSession();
        if (!session) {
          throw new Error('User not authenticated');
        }

        // Check if user is member of an organization
        const { data: orgMembership, error: orgError } = await supabase
          .from('organization_members')
          .select('organization_id')
          .eq('user_id', session.user.id)
          .single();

        if (orgError && orgError.code !== 'PGRST116') {
          throw new Error('Failed to verify organization membership: ' + orgError.message);
        }

        if (!orgMembership) {
          throw new Error('User must be a member of an organization to connect integrations');
        }

        // Call the Supabase RPC function to create the integration
        const { data, error } = await supabase.rpc('connect_woocommerce', {
          store_url: storeUrl,
          consumer_key: consumerKey,
          consumer_secret: consumerSecret
        });

        if (error) {
          throw new Error(error.message);
        }

        const response = data as ConnectWooCommerceResponse;
        
        if (!response.success) {
          throw new Error(response.error || 'Failed to connect to WooCommerce');
        }

        // Get the access token for the edge function
        const { data: { session: currentSession } } = await supabase.auth.getSession();
        const token = currentSession?.access_token;
        
        if (!token) {
          throw new Error('No access token available');
        }

        // Call the edge function to register the webhook
        const webhookResponse = await fetch(
          `${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/v1/register-woocommerce-webhook`,
          {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              integration_id: response.integration_id
            })
          }
        );

        // Check if the response is ok, if not throw an error with details
        if (!webhookResponse.ok) {
          let errorMessage = `Webhook registration failed with status ${webhookResponse.status}`;
          
          try {
            const errorData = await webhookResponse.json();
            errorMessage = errorData.error || errorData.message || errorMessage;
          } catch (parseError) {
            // If we can't parse the error response, use the text
            try {
              const errorText = await webhookResponse.text();
              errorMessage = errorText || errorMessage;
            } catch (textError) {
              // If we can't get the text, keep the original message
            }
          }
          
          console.warn('Webhook registration failed:', errorMessage);
          // Don't throw an error here as the connection was successful
          // But let's return the webhook result for debugging
          return {
            ...response,
            webhook_registration_success: false,
            webhook_registration_message: errorMessage
          };
        }

        const webhookResult = await webhookResponse.json();
        
        return {
          ...response,
          webhook_registration_success: true,
          webhook_registration_message: webhookResult.message
        };
      } catch (error: any) {
        if (error instanceof Error) {
          throw error;
        }
        throw new Error('Failed to connect to WooCommerce: ' + (error.message || 'Unknown error'));
      }
    },
    onSuccess: () => {
      console.log('WooCommerce connection successful');
      onSuccess?.();
    },
    onError
  });
};