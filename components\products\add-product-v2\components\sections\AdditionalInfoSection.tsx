import React from 'react'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { useProductForm } from '../../context/ProductFormContext'
import { SectionHeader } from '../shared/SectionHeader'
import { FormField } from '../shared/FormField'

export function AdditionalInfoSection() {
  const { formData, updateFormData, errors } = useProductForm()

  return (
    <div className="max-w-4xl">
      <SectionHeader
        title="Additional Information"
        description="Notes and additional product details"
      />

      <div className="space-y-4">
        {/* Notes */}
        <FormField
          label="Notes"
          error={errors.notes}
        >
          <Textarea
            value={formData.notes}
            onChange={(e) => updateFormData('notes', e.target.value)}
            placeholder="Any additional notes about this product..."
            rows={4}
            className="resize-none text-xs placeholder:text-xs"
          />
        </FormField>

        {/* Sale Information */}
        <div className="space-y-4">
          <h3 className="text-sm font-medium text-gray-900 border-b pb-1">Sale Information (Optional)</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              label="Sale Start Date"
              error={errors.sale_start_date}
            >
              <Input
                type="date"
                value={formData.sale_start_date ? new Date(formData.sale_start_date).toISOString().split('T')[0] : ''}
                onChange={(e) => updateFormData('sale_start_date', e.target.value ? new Date(e.target.value) : null)}
                className="h-8 text-xs"
              />
            </FormField>

            <FormField
              label="Sale End Date"
              error={errors.sale_end_date}
            >
              <Input
                type="date"
                value={formData.sale_end_date ? new Date(formData.sale_end_date).toISOString().split('T')[0] : ''}
                onChange={(e) => updateFormData('sale_end_date', e.target.value ? new Date(e.target.value) : null)}
                className="h-8 text-xs"
              />
            </FormField>
          </div>
        </div>

        {/* Form Summary */}
        <div className="mt-8 p-4 bg-green-50 border border-green-200 rounded-md">
          <div className="flex items-start gap-2">
            <div className="text-green-500 mt-0.5">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div>
              <h4 className="font-medium text-green-900 text-xs mb-1">
                Ready to Create Product
              </h4>
              <p className="text-xs text-green-700 mb-2">
                You've completed all the sections! Review your information and click "Create Product" to save.
              </p>
              <div className="text-xs text-green-600 space-y-1">
                <div>✓ Product Type: {formData.has_variants ? 'Variable Product' : 'Simple Product'}</div>
                <div>✓ Product Name: {formData.name || 'Not set'}</div>
                <div>✓ SKU: {formData.base_sku || 'Not set'}</div>
                {!formData.has_variants && (
                  <>
                    <div>✓ Base Cost: {formData.base_cost ? `$${formData.base_cost}` : 'Not set'}</div>
                    <div>✓ Stock: {formData.stock_quantity || 0} units</div>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Tips */}
        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
          <div className="flex items-start gap-2">
            <div className="text-blue-500 mt-0.5">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div>
              <h4 className="font-medium text-blue-900 text-xs mb-1">
                What's Next?
              </h4>
              <ul className="text-xs text-blue-700 space-y-1">
                <li>• After creating, you can edit any details in the Products table</li>
                <li>• For variable products, you'll create variants in the edit form</li>
                <li>• Stock levels can be adjusted anytime in the Inventory section</li>
                <li>• Use the Products Analytics to track performance</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
