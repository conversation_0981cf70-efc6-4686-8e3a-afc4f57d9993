// @ts-ignore
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
// @ts-ignore
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3';

// Import types for better development experience
import type { SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3';
import type { Database } from '../_generated/types.ts';

interface WebhookPayload {
  integration_id: string;
  check_status?: boolean;
}

const registerWebhook = async (req: Request): Promise<Response> => {
  try {
    // Add CORS headers for all requests
    const corsHeaders = {
      'Access-Control-Allow-Origin': '*', // In production, restrict this to your domain
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Authorization, Content-Type',
      'Access-Control-Max-Age': '86400',
    };

    // Handle preflight OPTIONS request
    if (req.method === 'OPTIONS') {
      return new Response(null, {
        status: 204,
        headers: corsHeaders,
      });
    }

    // Get the authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(
        JSON.stringify({ error: 'Missing or invalid Authorization header' }),
        { 
          status: 401, 
          headers: { 
            'Content-Type': 'application/json',
            ...corsHeaders
          } 
        }
      );
    }

    // Extract the token
    const token = authHeader.substring(7);
    
    // Create a Supabase client with the token
    const supabase = createClient<Database>(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        global: {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      }
    );

    // Parse the request body
    const payload: WebhookPayload = await req.json();
    
    // Get the integration record
    const { data: integration, error: integrationError } = await supabase
      .from('integrations')
      .select('id, organization_id, store_url, consumer_key, consumer_secret, consumer_secret_ref')
      .eq('id', payload.integration_id)
      .eq('platform', 'woocommerce')
      .single();

    if (integrationError || !integration) {
      return new Response(
        JSON.stringify({ error: 'Integration not found' }),
        { status: 404, headers: { 'Content-Type': 'application/json', ...corsHeaders } }
      );
    }

    // Validate required fields
    if (!integration.store_url || !integration.consumer_key || !integration.consumer_secret) {
      return new Response(
        JSON.stringify({ error: 'Missing required integration credentials' }),
        { status: 400, headers: { 'Content-Type': 'application/json', ...corsHeaders } }
      );
    }

    // If check_status is true, just check the webhook status
    if (payload.check_status) {
      // Check if webhook exists by trying to fetch it from WooCommerce
      const credentials = btoa(`${integration.consumer_key}:${integration.consumer_secret}`);
      
      // If we have a consumer_secret_ref, it might be the webhook ID
      if (integration.consumer_secret_ref) {
        try {
          const response = await fetch(
            `${integration.store_url.replace(/\/$/, '')}/wp-json/wc/v3/webhooks/${integration.consumer_secret_ref}`,
            {
              headers: {
                'Authorization': `Basic ${credentials}`,
              },
            }
          );
          
          if (response.ok) {
            const webhookData = await response.json();
            return new Response(
              JSON.stringify({ 
                message: 'Webhook is registered',
                webhook_data: webhookData
              }),
              { status: 200, headers: { 'Content-Type': 'application/json', ...corsHeaders } }
            );
          }
        } catch (error) {
          console.error('Error checking webhook status:', error);
        }
      }
      
      return new Response(
        JSON.stringify({ 
          error: 'Webhook not found or not properly registered',
          message: 'Webhook not found or not properly registered'
        }),
        { status: 404, headers: { 'Content-Type': 'application/json', ...corsHeaders } }
      );
    }

    // Register the webhook with WooCommerce
    const credentials = btoa(`${integration.consumer_key}:${integration.consumer_secret}`);
    const webhookUrl = `${Deno.env.get('SUPABASE_URL')}/functions/v1/woocommerce-webhook-handler`;
    
    const response = await fetch(`${integration.store_url.replace(/\/$/, '')}/wp-json/wc/v3/webhooks`, {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${credentials}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: 'ONKO Order Created',
        status: 'active',
        topic: 'order.created',
        delivery_url: webhookUrl,
        secret: integration.consumer_secret_ref || integration.consumer_secret,
      }),
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('Webhook registration failed:', errorText);
      return new Response(
        JSON.stringify({ 
          error: 'Failed to register webhook with WooCommerce',
          details: errorText,
          status: response.status
        }),
        { status: response.status, headers: { 'Content-Type': 'application/json', ...corsHeaders } }
      );
    }
    
    const webhookData = await response.json();
    
    // Update the integration with the webhook ID
    const { error: updateError } = await supabase
      .from('integrations')
      .update({ 
        consumer_secret_ref: webhookData.id?.toString() || integration.consumer_secret_ref
      })
      .eq('id', integration.id);

    if (updateError) {
      console.error('Failed to update integration with webhook ID:', updateError);
    }

    return new Response(
      JSON.stringify({ 
        message: 'Webhook registered successfully',
        webhook_url: webhookUrl,
        webhook_id: webhookData.id
      }),
      { 
        status: 200, 
        headers: { 
          'Content-Type': 'application/json',
          ...corsHeaders
        } 
      }
    );
  } catch (error) {
    console.error('Error registering webhook:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error', details: error.message }),
      { 
        status: 500, 
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        } 
      }
    );
  }
};

// Serve the function
serve(registerWebhook);