import { type ProductRow, type ProductVariantRow } from '@/lib/supabase'

// Extended ProductRow type with category_name property (added during data transformation)
export type ExtendedProductRow = ProductRow & {
  category_name?: string | null
  variants?: ProductVariantRow[]  // Add variants property
  categories?: {
    name: string
  } | null
}

// Define the inventory item type that combines product and variant data
export type InventoryItem = {
  id: string
  productId: string
  variantId: string | null
  name: string
  sku: string
  stockOnHand: number
  committed: number
  available: number
  incoming: number
  status: 'in_stock' | 'low_stock' | 'out_of_stock'
  type: 'product' | 'variant'
  lowStockThreshold: number
}