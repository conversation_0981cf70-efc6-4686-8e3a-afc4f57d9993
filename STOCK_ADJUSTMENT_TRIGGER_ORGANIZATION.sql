-- =============================================
-- STOCK ADJUSTMENT TRIGGER FUNCTION (ORGANIZATION-BASED)
-- =============================================
-- This function automatically creates notifications when stock is adjusted

-- Function to create a notification for stock adjustments
CREATE OR REPLACE FUNCTION create_stock_adjustment_notification()
RETURNS TRIGGER AS $$
DECLARE
  product_name TEXT;
  product_sku TEXT;
  organization_id_var UUID;
  notification_title TEXT;
  notification_message TEXT;
BEGIN
  -- Only proceed if stock quantity actually changed
  IF OLD.stock_quantity IS DISTINCT FROM NEW.stock_quantity THEN
    -- Get product information and organization_id
    SELECT p.name, p.base_sku, p.organization_id INTO product_name, product_sku, organization_id_var
    FROM products p
    WHERE p.id = NEW.id;
    
    -- Create notification title and message
    notification_title := 'Stock Adjustment: ' || COALESCE(product_name, 'Unknown Product');
    notification_message := 'Stock for "' || COALESCE(product_name, 'Unknown Product') || 
                           '" (SKU: ' || COALESCE(product_sku, 'N/A') || 
                           ') was adjusted from ' || COALESCE(OLD.stock_quantity, 0) || 
                           ' to ' || COALESCE(NEW.stock_quantity, 0) || '.';
    
    -- Insert notification with organization_id
    INSERT INTO notifications (
      organization_id,
      type,
      title,
      message,
      related_entity_type,
      related_entity_id,
      created_at
    ) VALUES (
      organization_id_var,
      'stock_adjustment',
      notification_title,
      notification_message,
      'product',
      NEW.id,
      NOW()
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for products table
CREATE TRIGGER stock_adjustment_notification_trigger
  AFTER UPDATE OF stock_quantity ON products
  FOR EACH ROW
  EXECUTE FUNCTION create_stock_adjustment_notification();

-- Function to create a notification for variant stock adjustments
CREATE OR REPLACE FUNCTION create_variant_stock_adjustment_notification()
RETURNS TRIGGER AS $$
DECLARE
  product_name TEXT;
  variant_name TEXT;
  variant_sku TEXT;
  organization_id_var UUID;
  notification_title TEXT;
  notification_message TEXT;
BEGIN
  -- Only proceed if stock quantity actually changed
  IF OLD.stock_quantity IS DISTINCT FROM NEW.stock_quantity THEN
    -- Get product and variant information and organization_id
    SELECT p.name, pv.variant_name, pv.sku, p.organization_id INTO product_name, variant_name, variant_sku, organization_id_var
    FROM product_variants pv
    JOIN products p ON pv.product_id = p.id
    WHERE pv.id = NEW.id;
    
    -- Create notification title and message
    notification_title := 'Stock Adjustment: ' || COALESCE(product_name, 'Unknown Product') || 
                         ' - ' || COALESCE(variant_name, 'Unknown Variant');
    notification_message := 'Stock for "' || COALESCE(product_name, 'Unknown Product') || 
                           ' - ' || COALESCE(variant_name, 'Unknown Variant') || 
                           '" (SKU: ' || COALESCE(variant_sku, 'N/A') || 
                           ') was adjusted from ' || COALESCE(OLD.stock_quantity, 0) || 
                           ' to ' || COALESCE(NEW.stock_quantity, 0) || '.';
    
    -- Insert notification with organization_id
    INSERT INTO notifications (
      organization_id,
      type,
      title,
      message,
      related_entity_type,
      related_entity_id,
      created_at
    ) VALUES (
      organization_id_var,
      'stock_adjustment',
      notification_title,
      notification_message,
      'variant',
      NEW.id,
      NOW()
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for product_variants table
CREATE TRIGGER variant_stock_adjustment_notification_trigger
  AFTER UPDATE OF stock_quantity ON product_variants
  FOR EACH ROW
  EXECUTE FUNCTION create_variant_stock_adjustment_notification();