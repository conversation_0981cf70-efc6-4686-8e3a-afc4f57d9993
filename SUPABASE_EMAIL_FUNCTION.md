# Supabase Email Function Implementation

## Overview

This document describes how to implement a Supabase Edge Function for sending invitation emails using SendGrid.

## Prerequisites

1. Supabase project with Edge Functions enabled
2. SendGrid account with API key
3. Supabase CLI installed

## Implementation Steps

### 1. Create the Function Directory

We've already created the directory structure:
```
supabase/functions/send-invitation/
```

### 2. Update the Function Code

The [index.ts](file:///Qoder/onko/supabase/functions/send-invitation/index.ts) file has been created with basic functionality. Let's enhance it to use SendGrid:

```typescript
// supabase/functions/send-invitation/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from '@supabase/supabase-js';

console.log("Send invitation function started");

serve(async (req: Request) => {
  try {
    // Get the request body
    const { invitationId } = await req.json();
    
    if (!invitationId) {
      return new Response(JSON.stringify({ error: 'Missing invitationId' }), {
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
    }

    // Create a Supabase client with the service role key for full access
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Get invitation details
    const { data: invitation, error: invitationError } = await supabase
      .from('invitations')
      .select(`
        id,
        email,
        role,
        organization_id,
        created_at,
        organizations (name)
      `)
      .eq('id', invitationId)
      .single();

    if (invitationError) {
      console.error('Error fetching invitation:', invitationError);
      return new Response(JSON.stringify({ error: invitationError.message }), {
        status: 500,
        headers: { "Content-Type": "application/json" }
      });
    }

    if (!invitation) {
      return new Response(JSON.stringify({ error: 'Invitation not found' }), {
        status: 404,
        headers: { "Content-Type": "application/json" }
      });
    }

    // Get organization name
    const organizationName = invitation.organizations?.name || 'Your Organization';

    // Send email using SendGrid
    const sendgridApiKey = Deno.env.get('SENDGRID_API_KEY');
    if (!sendgridApiKey) {
      console.error('SENDGRID_API_KEY environment variable not set');
      return new Response(JSON.stringify({ error: 'Email service not configured' }), {
        status: 500,
        headers: { "Content-Type": "application/json" }
      });
    }

    const emailData = {
      personalizations: [{
        to: [{ email: invitation.email }],
        subject: `You've been invited to join ${organizationName}`
      }],
      from: { email: Deno.env.get('SENDGRID_FROM_EMAIL') || '<EMAIL>' },
      content: [{
        type: 'text/html',
        value: `
          <p>Hello,</p>
          
          <p>You've been invited to join <strong>${organizationName}</strong> as a <strong>${invitation.role}</strong>.</p>
          
          <p>To accept this invitation, please click on the following link:</p>
          <p><a href="${Deno.env.get('SITE_URL') || 'http://localhost:3000'}/accept-invitation?token=${invitation.id}">Accept Invitation</a></p>
          
          <p>This invitation will expire in 7 days.</p>
          
          <p>If you did not expect this invitation, you can safely ignore this email.</p>
          
          <p>Best regards,<br>
          The ${organizationName} Team</p>
        `
      }]
    };

    const response = await fetch('https://api.sendgrid.com/v3/mail/send', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${sendgridApiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(emailData)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('SendGrid API error:', errorText);
      return new Response(JSON.stringify({ error: 'Failed to send email' }), {
        status: 500,
        headers: { "Content-Type": "application/json" }
      });
    }

    console.log(`Invitation email sent successfully to ${invitation.email}`);

    return new Response(JSON.stringify({ 
      message: 'Invitation email sent successfully',
      invitationId: invitation.id
    }), {
      status: 200,
      headers: { "Content-Type": "application/json" }
    });
  } catch (error) {
    console.error('Error in send invitation function:', error);
    return new Response(JSON.stringify({ error: (error as Error).message }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
});
```

### 3. Set Environment Variables

Set the required environment variables in your Supabase project:

```bash
supabase secrets set SENDGRID_API_KEY=your-sendgrid-api-key
supabase secrets set SENDGRID_FROM_EMAIL=<EMAIL>
supabase secrets set SITE_URL=https://yourdomain.com
```

### 4. Deploy the Function

Deploy the function to Supabase:

```bash
supabase functions deploy send-invitation --project-ref your-project-ref
```

### 5. Update the Client-Side Code

Update the [sendInvitationEmail](file:///Qoder/onko/lib/team-management.ts#L368-L434) function in [lib/team-management.ts](file:///Qoder/onko/lib/team-management.ts) to call the Supabase function:

```typescript
// Send invitation email via Supabase function
export async function sendInvitationEmail(invitationId: string) {
  try {
    // Call the Supabase Edge Function
    const response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/v1/send-invitation`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ invitationId })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to send invitation email');
    }

    const result = await response.json();
    console.log('Email sent successfully:', result.message);
    
    return { success: true, message: 'Invitation email sent successfully' };
  } catch (error) {
    console.error('Error sending invitation email:', error);
    throw error;
  }
}
```

## Testing the Function

1. Deploy the function to your Supabase project
2. Set the required environment variables
3. Call the function from your client-side code
4. Check the Supabase function logs for any errors
5. Verify that emails are received at the test email address

## Troubleshooting

1. **Function not found**: Ensure the function is deployed and the name matches
2. **Authentication errors**: Verify that the service role key is correct
3. **Email not sent**: Check SendGrid API key and from email address
4. **Environment variables not set**: Use `supabase secrets set` to configure all required variables