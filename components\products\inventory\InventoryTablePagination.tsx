'use client'

import { But<PERSON> } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { ChevronLeft, ChevronRight } from 'lucide-react'

interface InventoryTablePaginationProps {
  currentPage: number
  setCurrentPage: (page: number) => void
  itemsPerPage: number
  setItemsPerPage: (count: number) => void
  filteredInventoryItemsCount: number
  totalPages: number
}

export function InventoryTablePagination({
  currentPage,
  setCurrentPage,
  itemsPerPage,
  setItemsPerPage,
  filteredInventoryItemsCount,
  totalPages
}: InventoryTablePaginationProps) {
  return (
    <div className="flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 p-4 border border-gray-200 border-t-0 rounded-b-lg bg-gray-50">
      {/* Left: Items per page selector */}
      <div className="flex items-center gap-2 text-xs text-gray-600">
        <span>Show</span>
        <select
          value={itemsPerPage}
          onChange={(e) => {
            setItemsPerPage(Number(e.target.value))
            setCurrentPage(1) // Reset to first page when changing page size
          }}
          className="rounded border border-gray-200 px-2 py-1 text-xs h-7"
        >
          <option value={5}>5</option>
          <option value={10}>10</option>
          <option value={20}>20</option>
          <option value={50}>50</option>
        </select>
        <span>per page</span>
      </div>
      
      {/* Right: Pagination Controls and Stats */}
      <div className="flex items-center w-full md:w-auto justify-end">
        {/* Item Range and Navigation */}
        <div className="flex items-center gap-2">
          <p className="text-xs text-gray-600">
            <span className="hidden sm:inline">Showing </span>
            <span className="font-medium">{Math.min((currentPage - 1) * itemsPerPage + 1, filteredInventoryItemsCount)}-{Math.min(currentPage * itemsPerPage, filteredInventoryItemsCount)}</span>
            <span className="hidden sm:inline"> of </span>
            <span className="sm:hidden">/</span>
            <span className="font-medium">{filteredInventoryItemsCount}</span>
          </p>
          
          <div className="flex items-center rounded-md border border-gray-200 overflow-hidden">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setCurrentPage(Math.max(currentPage - 1, 1))}
              disabled={currentPage <= 1}
              className="h-7 w-7 p-0 rounded-none border-r border-gray-200"
            >
              <ChevronLeft className="h-3.5 w-3.5 text-gray-600" />
            </Button>
            
            <div className="hidden sm:flex">
              {Array.from({ length: Math.min(3, totalPages) }, (_, i) => {
                const page = Math.max(1, currentPage - 1) + i
                if (page > totalPages) return null
                
                return (
                  <Button
                    key={page}
                    variant={page === currentPage ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setCurrentPage(page)}
                    className={cn(
                      "h-7 w-7 p-0 rounded-none border-r border-gray-200 text-xs",
                      page === currentPage ? "bg-blue-600 hover:bg-blue-700 text-white" : "text-gray-700"
                    )}
                  >
                    {page}
                  </Button>
                )
              })}
            </div>
            
            <div className="sm:hidden flex items-center justify-center h-7 w-7 text-xs font-medium border-r border-gray-200">
              {currentPage}
            </div>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setCurrentPage(Math.min(currentPage + 1, totalPages))}
              disabled={currentPage >= totalPages}
              className="h-7 w-7 p-0 rounded-none"
            >
              <ChevronRight className="h-3.5 w-3.5 text-gray-600" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}