# Currency Setting Issue Analysis and Solution

## Overview

This document analyzes the issue where currency settings suddenly reverted to USD default and the update currency button became disabled. Based on the error logs and code analysis, the root cause is a mismatch between the database schema and the application's expectations.

## Problem Analysis

### Error Details

From the console logs, the primary error is:
```
Error loading profile: {code: '42703', details: null, hint: null, message: 'column profiles.avatar_url does not exist'}
```

This error occurs in multiple places:
1. `header.tsx` - When loading user profile for the header display
2. `page.tsx` (dashboard) - When loading user profile for business name
3. `page.tsx` (settings) - When loading user profile for settings display

### Root Cause

The database schema defined in `supabase-setup.sql` includes an `avatar_url` column in the `profiles` table:

```sql
create table if not exists profiles (
  id uuid references auth.users on delete cascade primary key,
  business_name text,
  contact_info jsonb,
  subscription_tier text default 'free',
  currency text default 'USD',
  avatar_url text,
  first_name text,
  last_name text,
  display_name text,
  job_title text,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);
```

However, the actual database table is missing this column, causing all queries that select `avatar_url` to fail. This cascades into issues with loading the complete profile, which includes the currency setting.

## Impact Analysis

1. **Currency Settings**: The currency update button is disabled because the application cannot properly load the current user currency due to profile loading failures.

2. **User Experience**: Users cannot update their currency settings, and the UI may not reflect the correct currency.

3. **Profile Loading**: Multiple components fail to load user profile data, affecting the header display and settings page.

## Solution Approach

### 1. Database Schema Fix

Add the missing `avatar_url` column to the `profiles` table:

```sql
ALTER TABLE profiles ADD COLUMN avatar_url text;
```

### 2. Alternative Approach - Code Modification

If preferred, modify the code to handle missing columns gracefully by adjusting the SELECT queries to not include `avatar_url` if it doesn't exist.

## Implementation Plan

### Step 1: Database Schema Update

Execute the following SQL command in your Supabase SQL editor:

```sql
ALTER TABLE profiles ADD COLUMN avatar_url text;
```

### Step 2: Verification

After adding the column, verify that:
1. The profile loading errors are resolved
2. The currency settings can be loaded properly
3. The update currency button is enabled when there are actual changes

### Step 3: Testing

1. Test currency selection in the settings page
2. Verify that currency changes are properly saved to the database
3. Confirm that the updated currency is reflected in the UI

## Code Components Affected

### 1. Header Component (`components/header.tsx`)
- Line 49: Profile loading query includes `avatar_url`

### 2. Dashboard Page (`app/dashboard/page.tsx`)
- Line 45: Profile loading query (does not include `avatar_url`, so may not be directly affected)

### 3. Settings Page (`app/dashboard/settings/page.tsx`)
- Line 45: Profile loading query includes `avatar_url`

### 4. Currency Hook (`lib/currency.ts`)
- Line 51: Currency loading query (does not include `avatar_url`, so may not be directly affected)

## Risk Assessment

### Low Risk
- Adding a column to an existing table is a low-risk operation
- The new column is nullable, so existing data is not affected
- No application logic changes are required

### Mitigation
- Perform the change during low-usage periods
- Have a backup plan to rollback if issues occur
- Test thoroughly after implementation

## Validation Steps

1. Execute the ALTER TABLE command
2. Refresh the application
3. Verify that console errors related to `avatar_url` are resolved
4. Check that currency settings load correctly
5. Confirm that the update currency button is functional
6. Test saving a new currency selection

## Conclusion

The issue is caused by a schema mismatch where the application expects an `avatar_url` column in the `profiles` table that does not exist in the database. Adding this missing column will resolve the profile loading errors and restore the currency functionality.