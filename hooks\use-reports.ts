import { useQuery } from '@tanstack/react-query'
import { getSupabaseClient } from '@/lib/supabase'

export interface ExpenseByCategory {
  category: string
  amount: number
}

export interface ProfitAndLossData {
  total_revenue: number
  total_cogs: number
  total_expenses: number
  expenses_by_category: ExpenseByCategory[]
}

// Updated interface for detailed cash flow data
export interface InvoiceDetail {
  id: number
  invoice_number: string
  customer_name: string
  issue_date: string
  total: number
}

export interface ExpenseDetail {
  id: number
  description: string
  category: string
  expense_date: string
  amount: number
}

export interface CashFromOperating {
  total: number
  paid_invoices: InvoiceDetail[]
  paid_expenses: ExpenseDetail[]
}

export interface CashFlowData {
  cash_from_operating: CashFromOperating
  cash_from_investing: { total: number }
  cash_from_financing: { total: number }
}

export interface DateRange {
  from?: Date
  to?: Date
}

export const useProfitAndLoss = (dateRange: DateRange) => {
  const supabase = getSupabaseClient()

  return useQuery<ProfitAndLossData, Error>({
    queryKey: ['profitAndLoss', dateRange],
    queryFn: async () => {
      // Default to current year if no date range is provided
      const startDate = dateRange.from ? new Date(dateRange.from).toISOString() : new Date(new Date().getFullYear(), 0, 1).toISOString()
      const endDate = dateRange.to ? new Date(dateRange.to).toISOString() : new Date().toISOString()

      const { data, error } = await supabase.rpc('get_profit_and_loss', {
        start_date: startDate,
        end_date: endDate
      })

      if (error) {
        throw new Error(`Error fetching profit and loss data: ${error.message}`)
      }

      return data as ProfitAndLossData
    },
    enabled: !!dateRange.from && !!dateRange.to,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}

export const useCashFlow = (dateRange: DateRange) => {
  const supabase = getSupabaseClient()

  return useQuery<CashFlowData, Error>({
    queryKey: ['cashFlow', dateRange],
    queryFn: async () => {
      // Default to current year if no date range is provided
      const startDate = dateRange.from ? new Date(dateRange.from).toISOString().split('T')[0] : new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0]
      const endDate = dateRange.to ? new Date(dateRange.to).toISOString().split('T')[0] : new Date().toISOString().split('T')[0]

      const { data, error } = await supabase.rpc('get_cash_flow', {
        start_date: startDate,
        end_date: endDate
      })

      if (error) {
        throw new Error(`Error fetching cash flow data: ${error.message}`)
      }

      return data as CashFlowData
    },
    enabled: !!dateRange.from && !!dateRange.to,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}