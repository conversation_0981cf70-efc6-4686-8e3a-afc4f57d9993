'use client'

import { X } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { type ProductFilters } from '@/components/products/products-catalog/product-filters'

interface ActiveFiltersDisplayProps {
  searchQuery: string
  setSearchQuery: (query: string) => void
  filters: ProductFilters
  setFilters: (filters: ProductFilters) => void
}

export function ActiveFiltersDisplay({
  searchQuery,
  setSearchQuery,
  filters,
  setFilters
}: ActiveFiltersDisplayProps) {
  // Count active filters
  const activeFilterCount = (() => {
    let count = 0
    if (searchQuery.trim()) count++
    if (filters.categories.length > 0) count += filters.categories.length
    if (filters.suppliers.length > 0) count += filters.suppliers.length
    if (filters.stockStatus.length > 0) count += filters.stockStatus.length
    if (filters.quantityRange.min !== null || filters.quantityRange.max !== null) count++
    return count
  })()

  // Generate active filter badges
  const getActiveFilterBadges = () => {
    const badges = []
    
    if (searchQuery.trim()) {
      badges.push(
        <Badge key="search" className="bg-blue-50 text-blue-700 border-none text-xs py-1 px-2 flex items-center gap-1.5">
          Search: "{searchQuery}"
          <Button 
            variant="ghost" 
            size="sm" 
            className="ml-1 h-3.5 w-3.5 p-0 hover:bg-blue-100"
            onClick={() => setSearchQuery('')}
          >
            <X className="h-3 w-3 cursor-pointer" />
          </Button>
        </Badge>
      )
    }
    
    if (filters.categories.length > 0) {
      badges.push(
        <Badge key="categories" className="bg-blue-50 text-blue-700 border-none text-xs py-1 px-2 flex items-center gap-1.5">
          Categories: {filters.categories.join(', ')}
          <Button 
            variant="ghost" 
            size="sm" 
            className="ml-1 h-3.5 w-3.5 p-0 hover:bg-blue-100"
            onClick={() => setFilters({ ...filters, categories: [] })}
          >
            <X className="h-3 w-3 cursor-pointer" />
          </Button>
        </Badge>
      )
    }
    
    if (filters.suppliers.length > 0) {
      badges.push(
        <Badge key="suppliers" className="bg-blue-50 text-blue-700 border-none text-xs py-1 px-2 flex items-center gap-1.5">
          Suppliers: {filters.suppliers.join(', ')}
          <Button 
            variant="ghost" 
            size="sm" 
            className="ml-1 h-3.5 w-3.5 p-0 hover:bg-blue-100"
            onClick={() => setFilters({ ...filters, suppliers: [] })}
          >
            <X className="h-3 w-3 cursor-pointer" />
          </Button>
        </Badge>
      )
    }
    
    if (filters.stockStatus.length > 0) {
      badges.push(
        <Badge key="stock-status" className="bg-blue-50 text-blue-700 border-none text-xs py-1 px-2 flex items-center gap-1.5">
          Stock Status: {filters.stockStatus.join(', ')}
          <Button 
            variant="ghost" 
            size="sm" 
            className="ml-1 h-3.5 w-3.5 p-0 hover:bg-blue-100"
            onClick={() => setFilters({ ...filters, stockStatus: [] })}
          >
            <X className="h-3 w-3 cursor-pointer" />
          </Button>
        </Badge>
      )
    }
    
    // Quantity range filter badge
    if (filters.quantityRange.min !== null || filters.quantityRange.max !== null) {
      const min = filters.quantityRange.min !== null ? filters.quantityRange.min : '';
      const max = filters.quantityRange.max !== null ? filters.quantityRange.max : '';
      badges.push(
        <Badge key="quantity-range" className="bg-blue-50 text-blue-700 border-none text-xs py-1 px-2 flex items-center gap-1.5">
          Quantity: {min}{min !== '' && max !== '' ? ' - ' : ''}{max}
          <Button 
            variant="ghost" 
            size="sm" 
            className="ml-1 h-3.5 w-3.5 p-0 hover:bg-blue-100"
            onClick={() => setFilters({ 
              ...filters, 
              quantityRange: { min: null, max: null } 
            })}
          >
            <X className="h-3 w-3 cursor-pointer" />
          </Button>
        </Badge>
      )
    }
    
    return badges
  }

  if (activeFilterCount === 0) {
    return null
  }

  return (
    <div className="flex flex-wrap gap-1.5 mb-4">
      {getActiveFilterBadges()}
    </div>
  )
}