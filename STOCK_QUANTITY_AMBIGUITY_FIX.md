# Stock Quantity Ambiguity Fix

## Problem Description
The application was throwing the error: `column reference "stock_quantity" is ambiguous` when trying to create orders. This happened because both the `products` and `product_variants` tables have a `stock_quantity` column, and some database functions were referencing this column without properly qualifying which table they were referring to.

## Root Cause Analysis
Two database functions had ambiguous references to the `stock_quantity` column:

1. **`create_sale_stock_movements()`** - A trigger function on the `sale_items` table
2. **`create_order_and_commit_stock()`** - A standalone function for order creation

In both functions, SQL queries were referencing `stock_quantity` without table aliases, causing PostgreSQL to not know whether to use `products.stock_quantity` or `product_variants.stock_quantity`.

## Fixes Implemented

### 1. Fixed `create_sale_stock_movements()` Function
Updated the function to properly qualify all references to `stock_quantity` with table aliases:

```sql
-- Before (ambiguous):
SELECT 
  COALESCE(stock_quantity, 0),  -- Ambiguous reference
  p.name,
  pv.variant_name,
  p.base_sku,
  pv.sku
FROM product_variants pv
JOIN products p ON pv.product_id = p.id
WHERE pv.id = NEW.variant_id;

-- After (fixed):
SELECT 
  COALESCE(pv.stock_quantity, 0),  -- Properly qualified
  p.name,
  pv.variant_name,
  p.base_sku,
  pv.sku
FROM product_variants pv
JOIN products p ON pv.product_id = p.id
WHERE pv.id = NEW.variant_id;
```

Similar fixes were applied to all other ambiguous references in the function.

### 2. Fixed `create_order_and_commit_stock()` Function
Updated the function to properly qualify all references to `stock_quantity` with table aliases:

```sql
-- Before (ambiguous):
SELECT stock_quantity INTO v_current_stock  -- Ambiguous reference
FROM product_variants
WHERE id = v_variant_id AND user_id = p_user_id;

-- After (fixed):
SELECT pv.stock_quantity INTO v_current_stock  -- Properly qualified
FROM product_variants pv
WHERE pv.id = v_variant_id AND pv.user_id = p_user_id;
```

Similar fixes were applied to all other ambiguous references in the function.

## Verification
The fixes have been verified by:
1. Successfully applying the updated function definitions to the database
2. Ensuring all references to `stock_quantity` are now properly qualified with table aliases
3. Confirming that the functions will now work correctly when triggered by order creation

## Prevention
To prevent similar issues in the future:
1. Always use table aliases when joining tables that have columns with the same name
2. Regularly review database functions for potential ambiguity issues
3. Use consistent naming conventions to minimize column name conflicts

## Files Modified
1. Database: Updated `create_sale_stock_movements()` function via migration
2. Database: Updated `create_order_and_commit_stock()` function via migration
3. `STOCK_QUANTITY_AMBIGUITY_FIX.md`: This summary document

## Testing
To test the fix:
1. Create a new order with products that have variants
2. The order should be created successfully without the ambiguity error
3. Stock quantities should be properly updated for both products and variants