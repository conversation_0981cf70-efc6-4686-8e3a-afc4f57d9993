'use client'

import { useMemo } from 'react'
import { type ExtendedProductRow, type InventoryItem } from './types'
import { type ProductFilters } from '../products-catalog/product-filters'

// Overload for when we already have pre-processed inventoryItems
export function useInventoryData(inventoryItems: InventoryItem[], searchQuery: string, filters: ProductFilters): {
  filteredInventoryItems: InventoryItem[];
  availableOptions: {
    categories: { value: string; label: string }[];
    suppliers: { value: string; label: string }[];
    stockStatus: { value: string; label: string }[];
  };
}

// Original overload for when we need to process products
export function useInventoryData(products: ExtendedProductRow[], searchQuery: string, filters: ProductFilters): {
  inventoryItems: InventoryItem[];
  filteredInventoryItems: InventoryItem[];
  availableOptions: {
    categories: { value: string; label: string }[];
    suppliers: { value: string; label: string }[];
    stockStatus: { value: string; label: string }[];
  };
}

export function useInventoryData(input: ExtendedProductRow[] | InventoryItem[], searchQuery: string, filters: ProductFilters) {
  // Transform products and variants into flat inventory items (only if input is products)
  const inventoryItems = useMemo(() => {
    // Check if input is already inventory items
    if (input.length > 0 && (input as InventoryItem[])[0].type) {
      // Already processed inventory items
      return input as InventoryItem[];
    }
    
    // Process products into inventory items
    const products = input as ExtendedProductRow[];
    const items: InventoryItem[] = []
    
    // Process all products (both simple and variable)
    products.forEach(product => {
      // Process simple products (without variants)
      if (!product.has_variants) {
        items.push({
          id: product.id,
          productId: product.id,
          variantId: null,
          name: product.name,
          sku: product.base_sku || '',
          stockOnHand: product.stock_quantity || 0,
          committed: 0, // This would come from order data
          available: product.stock_quantity || 0,
          incoming: 0, // This would come from purchase order data
          status: product.stock_quantity === 0 
            ? 'out_of_stock' 
            : (product.stock_quantity || 0) <= (product.low_stock_threshold || 10)
              ? 'low_stock'
              : 'in_stock',
          type: 'product',
          lowStockThreshold: product.low_stock_threshold || 10
        })
      }
      // Process variable products and their variants
      else if (product.has_variants && product.variants && product.variants.length > 0) {
        // Calculate parent product status based on variants
        let productStatus: 'in_stock' | 'low_stock' | 'out_of_stock' = 'in_stock';
        let totalStock = 0;
        let totalCommitted = 0;
        let minThreshold = Infinity;
        
        // Check if all variants are out of stock
        const allOutOfStock = product.variants.every(v => (v.stock_quantity || 0) === 0);
        
        // Check if any variant is out of stock
        const hasOutOfStock = product.variants.some(v => (v.stock_quantity || 0) === 0);
        
        // Check if any variant is low stock
        const hasLowStock = product.variants.some(v => {
          const stockQty = v.stock_quantity || 0;
          const threshold = v.low_stock_threshold || 10;
          return stockQty > 0 && stockQty <= threshold;
        });
        
        // Determine parent product status based on variants
        if (allOutOfStock) {
          productStatus = 'out_of_stock';
        } else if (hasOutOfStock || hasLowStock) {
          productStatus = 'low_stock';
        }
        
        // Calculate totals for parent product
        product.variants.forEach(variant => {
          totalStock += variant.stock_quantity || 0;
          totalCommitted += variant.reserved_quantity || 0;
          minThreshold = Math.min(minThreshold, variant.low_stock_threshold || 10);
        });
        
        // If minThreshold wasn't updated, set to default
        if (minThreshold === Infinity) {
          minThreshold = 10;
        }
        
        items.push({
          id: product.id,
          productId: product.id,
          variantId: null,
          name: product.name,
          sku: product.base_sku || '',
          stockOnHand: totalStock,
          committed: totalCommitted,
          available: totalStock - totalCommitted,
          incoming: 0, // This would come from purchase order data
          status: productStatus,
          type: 'product',
          lowStockThreshold: minThreshold
        })
        
        // Process variants
        product.variants.forEach(variant => {
          items.push({
            id: variant.id,
            productId: variant.product_id,
            variantId: variant.id,
            name: `${product.name} - ${variant.variant_name || ''}`,
            sku: variant.sku,
            stockOnHand: variant.stock_quantity || 0,
            committed: variant.reserved_quantity || 0,
            available: (variant.stock_quantity || 0) - (variant.reserved_quantity || 0),
            incoming: 0, // This would come from purchase order data
            status: variant.stock_quantity === 0 
              ? 'out_of_stock' 
              : (variant.stock_quantity || 0) <= (variant.low_stock_threshold || 10)
                ? 'low_stock'
                : 'in_stock',
            type: 'variant',
            lowStockThreshold: variant.low_stock_threshold || 10
          })
        })
      }
      // Handle variable products with no variants (edge case)
      else if (product.has_variants) {
        // Treat as out of stock if it's a variable product but has no variants
        items.push({
          id: product.id,
          productId: product.id,
          variantId: null,
          name: product.name,
          sku: product.base_sku || '',
          stockOnHand: 0,
          committed: 0,
          available: 0,
          incoming: 0,
          status: 'out_of_stock',
          type: 'product',
          lowStockThreshold: product.low_stock_threshold || 10
        })
      }
    })
    
    return items
  }, [input])

  // Filter inventory items based on search and filters
  // Only include parent products, not individual variants
  const filteredInventoryItems = useMemo(() => {
    // Start with only product items (not variants) and sort by created_at DESC (newest first)
    let result = [...inventoryItems]
      .filter(item => item.type === 'product')
      .sort((a, b) => {
        // If we're working with pre-processed inventory items, we can't access product data
        // So we'll sort by name as a fallback
        return a.name.localeCompare(b.name);
      });
    
    // Text search
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      result = result.filter(item => 
        item.name.toLowerCase().includes(query) ||
        item.sku.toLowerCase().includes(query)
      )
    }
    
    // For the other filters, we'll need to simplify since we don't have access to the full product data
    // Category filter - not applicable with pre-processed items
    // Supplier filter - not applicable with pre-processed items
    
    // Status filter
    if (filters.stockStatus.length > 0) {
      result = result.filter(item => filters.stockStatus.includes(item.status));
    }
    
    // Quantity range filter - simplified version
    if (filters.quantityRange.min !== null || filters.quantityRange.max !== null) {
      result = result.filter(item => {
        const stockQty = item.stockOnHand;
        const min = filters.quantityRange.min !== null ? filters.quantityRange.min : -Infinity;
        const max = filters.quantityRange.max !== null ? filters.quantityRange.max : Infinity;
        return stockQty >= min && stockQty <= max;
      });
    }
    
    return result
  }, [inventoryItems, searchQuery, filters])

  // Extract unique options - simplified version for pre-processed items
  const availableOptions = useMemo(() => {
    // For pre-processed inventory items, we'll return static options
    // In a full implementation, this would be more dynamic
    return {
      categories: [
        { value: 'Electronics', label: 'Electronics' },
        { value: 'Clothing', label: 'Clothing' },
        { value: 'Home & Garden', label: 'Home & Garden' },
        { value: 'Sports', label: 'Sports' },
        { value: 'Books', label: 'Books' }
      ],
      suppliers: [
        { value: 'Amazon', label: 'Amazon' },
        { value: 'Walmart', label: 'Walmart' },
        { value: 'Target', label: 'Target' },
        { value: 'Best Buy', label: 'Best Buy' }
      ],
      stockStatus: [
        { value: 'in_stock', label: 'In Stock' },
        { value: 'low_stock', label: 'Low Stock' },
        { value: 'out_of_stock', label: 'Out of Stock' }
      ]
    }
  }, [])

  // Check if we're working with pre-processed inventory items
  if (input.length > 0 && (input as InventoryItem[])[0].type) {
    // Return only the filtered items and options for pre-processed items
    return {
      filteredInventoryItems,
      availableOptions
    }
  } else {
    // Return full object for processed products
    return {
      inventoryItems,
      filteredInventoryItems,
      availableOptions
    }
  }
}