import React from 'react'
import { Package, Layers } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import { useProductForm } from '../../context/ProductFormContext'
import { SectionHeader } from '../shared/SectionHeader'

interface ProductTypeCardProps {
  title: string
  description: string
  icon: React.ReactNode
  isSelected: boolean
  onClick: () => void
  features: string[]
}

function ProductTypeCard({ title, description, icon, isSelected, onClick, features }: ProductTypeCardProps) {
  return (
    <Card 
      className={cn(
        "cursor-pointer transition-all duration-200 hover:shadow-md",
        isSelected && "ring-2 ring-blue-500 bg-blue-50"
      )}
      onClick={onClick}
    >
      <CardContent className="p-6">
        <div className="flex items-start gap-4">
          <div className={cn(
            "p-3 rounded-lg",
            isSelected ? "bg-blue-500 text-white" : "bg-gray-100 text-gray-600"
          )}>
            {icon}
          </div>
          
          <div className="flex-1">
            <h3 className={cn(
              "font-semibold text-lg mb-2",
              isSelected ? "text-blue-900" : "text-gray-900"
            )}>
              {title}
            </h3>
            
            <p className={cn(
              "text-sm mb-4",
              isSelected ? "text-blue-700" : "text-gray-600"
            )}>
              {description}
            </p>
            
            <ul className="space-y-1">
              {features.map((feature, index) => (
                <li key={index} className={cn(
                  "text-xs flex items-center gap-2",
                  isSelected ? "text-blue-600" : "text-gray-500"
                )}>
                  <span className={cn(
                    "w-1.5 h-1.5 rounded-full",
                    isSelected ? "bg-blue-500" : "bg-gray-400"
                  )} />
                  {feature}
                </li>
              ))}
            </ul>
          </div>
          
          {isSelected && (
            <div className="text-blue-500">
              <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

export function ProductTypeSection() {
  const { formData, updateFormData } = useProductForm()
  
  const handleTypeChange = (hasVariants: boolean) => {
    updateFormData('has_variants', hasVariants)
  }
  
  return (
    <div className="max-w-4xl">
      <SectionHeader
        title="Choose Product Type"
        description="Select whether this is a simple product or a variable product with multiple variants"
      />
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <ProductTypeCard
          title="Simple Product"
          description="A single product without variations"
          icon={<Package className="h-6 w-6" />}
          isSelected={!formData.has_variants}
          onClick={() => handleTypeChange(false)}
          features={[
            "Single SKU and price",
            "Fixed attributes (size, color)",
            "Simple inventory tracking",
            "Quick to set up"
          ]}
        />
        
        <ProductTypeCard
          title="Variable Product"
          description="A product with multiple variants (size, color, etc.)"
          icon={<Layers className="h-6 w-6" />}
          isSelected={formData.has_variants}
          onClick={() => handleTypeChange(true)}
          features={[
            "Create variants with attributes (size, color, etc.)",
            "Individual SKU, pricing & inventory per variant",
            "Flexible attribute combinations",
            "Manage all variants from one product"
          ]}
        />
      </div>
      
      {/* Removed the informational section that explained what happens after selecting a product type */}
    </div>
  )
}