'use client'

import { useState, useEffect, useCallback, useMemo } from 'react'
import { useToast } from '@/components/ui/use-toast'
import { EditExpenseForm } from '@/components/expenses/edit-expense-form'
import { ExpenseTableHeader, type ExpenseFilters as ExpenseFiltersType, INITIAL_FILTERS } from '@/components/expenses/expense-table-header'
import { EnhancedExpenseTable } from '@/components/enhanced-expense-table'
import { type ExpenseRow, getSupabaseClient } from '@/lib/supabase'
import { useAuth } from '@/contexts/auth-context'
import { useCurrency } from '@/lib/currency'
import { useAllExpenses, useInvalidateExpenses } from '@/hooks/use-expenses'
import { Skeleton } from '@/components/ui/skeleton'
import { Button } from '@/components/ui/button'

interface ExpensesTabProps {
  isMobile?: boolean
  className?: string
  onFiltersChange?: (filters: ExpenseFiltersType) => void
  onFilteredExpensesChange?: (expenses: ExpenseRow[]) => void
}

export function ExpensesTab({ 
  isMobile = false, 
  className,
  onFiltersChange,
  onFilteredExpensesChange: externalOnFilteredExpensesChange
}: ExpensesTabProps) {
  // Expenses state
  const [editingExpense, setEditingExpense] = useState<ExpenseRow | null>(null)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  
  // Filter state
  const [filters, setFilters] = useState<ExpenseFiltersType>(INITIAL_FILTERS)
  const [filteredExpenses, setFilteredExpenses] = useState<ExpenseRow[]>([])
  
  // Combined filter change handler
  const handleFiltersChange = (newFilters: ExpenseFiltersType) => {
    setFilters(newFilters)
    onFiltersChange?.(newFilters)
  }

  const { toast } = useToast()
  const { user, organizationId } = useAuth()
  const { formatCurrency } = useCurrency()
  
  // Use React Query to fetch expenses
  const { data: expenses = [], isLoading, error, refetch } = useAllExpenses(organizationId || undefined)
  
  // Use invalidate function to trigger refetching
  const { invalidateAllExpenses } = useInvalidateExpenses()
  
  // Memoize the Supabase client instance to prevent re-creation on every render
  const supabase = useMemo(() => getSupabaseClient(), [])

  // Update filtered expenses when expenses data changes
  // We avoid useCallback here to prevent circular dependencies
  const handleFilteredExpensesChange = (expenses: ExpenseRow[]) => {
    setFilteredExpenses(expenses)
    // Only call external handler if it's provided and different from current value
    if (externalOnFilteredExpensesChange) {
      externalOnFilteredExpensesChange(expenses)
    }
  }

  // Update filtered expenses when expenses data changes
  useEffect(() => {
    // Only update if we have expenses data
    if (expenses && Array.isArray(expenses)) {
      // Avoid updating if the data is the same (shallow comparison)
      if (JSON.stringify(expenses) !== JSON.stringify(filteredExpenses)) {
        handleFilteredExpensesChange(expenses)
      }
    }
  }, [expenses, filteredExpenses])

  // Listen for expense added/updated events to refresh the expense list
  useEffect(() => {
    const handleExpenseAdded = () => {
      invalidateAllExpenses()
    }

    const handleExpenseUpdated = () => {
      invalidateAllExpenses()
    }

    window.addEventListener('expenseAdded', handleExpenseAdded)
    window.addEventListener('expenseUpdated', handleExpenseUpdated)

    return () => {
      window.removeEventListener('expenseAdded', handleExpenseAdded)
      window.removeEventListener('expenseUpdated', handleExpenseUpdated)
    }
  }, [invalidateAllExpenses]);

  const handleExpenseAdded = () => {
    invalidateAllExpenses()
    // Dispatch dashboard refresh event
    window.dispatchEvent(new CustomEvent('dashboardRefresh'))
  }

  const handleExpenseEdit = (expense: ExpenseRow) => {
    setEditingExpense(expense)
    setIsEditModalOpen(true)
  }

  const handleExpenseUpdated = () => {
    // Close the edit modal
    setIsEditModalOpen(false)
    setEditingExpense(null)
    
    // Refresh the expenses list
    invalidateAllExpenses()
    
    // Dispatch dashboard refresh event
    window.dispatchEvent(new CustomEvent('dashboardRefresh'))
  }

  const handleExpenseDelete = async (expenseIds: string[]) => {
    if (!organizationId) return
    
    try {
      const { error } = await supabase
        .from('expenses')
        .delete()
        .in('id', expenseIds)
        .eq('organization_id', organizationId)
      
      if (error) {
        toast({
          title: "Error Deleting Expenses",
          description: `Failed to delete expenses: ${error.message}`,
          variant: "destructive",
        })
      } else {
        // Refresh the expenses list
        invalidateAllExpenses()
        
        // Dispatch dashboard refresh event
        window.dispatchEvent(new CustomEvent('dashboardRefresh'))
        
        toast({
          title: "Expenses Deleted",
          description: `Successfully deleted ${expenseIds.length} expense(s).`,
        })
      }
    } catch (error) {
      console.error('Error deleting expenses:', error)
      toast({
        title: "Unexpected Error",
        description: "Failed to delete expenses. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleExpenseView = (expense: ExpenseRow) => {
    toast({
      title: `Expense Details - ${expense.expense_id}`,
      description: `${expense.description} | ${expense.category} | ${formatCurrency(expense.amount)}`,
    })
  }

  // Render skeleton loading state
  if (isLoading) {
    return (
      <div className={className}>
        <div className="mb-6">
          <Skeleton className="h-12 w-full mb-4" />
          <div className="flex gap-2">
            <Skeleton className="h-8 w-24" />
            <Skeleton className="h-8 w-24" />
            <Skeleton className="h-8 w-24" />
          </div>
        </div>
        
        <div className="border border-gray-200 rounded-lg overflow-hidden">
          <div className="bg-gray-50 p-4 border-b">
            <Skeleton className="h-6 w-32" />
          </div>
          <div className="divide-y">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="p-4 flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div>
                    <Skeleton className="h-4 w-32 mb-2" />
                    <Skeleton className="h-3 w-24" />
                  </div>
                </div>
                <Skeleton className="h-8 w-8 rounded" />
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  // Render error state with user-friendly messages
  if (error) {
    // Type guard to ensure error is properly typed
    const errorMessage = error instanceof Error ? error.message : String(error);
    
    let userFriendlyMessage = "Failed to load expenses. Please try again."
    
    // Provide more specific error messages based on the error type
    if (errorMessage.includes("NetworkError") || errorMessage.includes("Failed to fetch")) {
      userFriendlyMessage = "Network connection error. Please check your internet connection and try again."
    } else if (errorMessage.includes("ERR_INSUFFICIENT_RESOURCES")) {
      userFriendlyMessage = "System resource limit reached. Please refresh the page or try again in a few minutes."
    } else if (errorMessage.includes("timeout")) {
      userFriendlyMessage = "Request timed out. Please try again."
    } else if (errorMessage.includes("401") || errorMessage.includes("403")) {
      userFriendlyMessage = "Authentication error. Please sign in again."
    } else if (errorMessage.includes("500")) {
      userFriendlyMessage = "Server error. Please try again later."
    }
    
    return (
      <div className={className}>
        <div className="flex items-center justify-center py-8">
          <div className="text-center max-w-md">
            <div className="text-red-500 mb-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
              <h3 className="text-lg font-medium">Unable to Load Expenses</h3>
            </div>
            <p className="text-muted-foreground text-sm mb-4">{userFriendlyMessage}</p>
            <div className="flex flex-col sm:flex-row gap-2 justify-center">
              <Button onClick={() => refetch()} variant="default" size="sm">
                Retry
              </Button>
              <Button 
                onClick={() => {
                  // Try to refresh the page as a last resort
                  window.location.reload()
                }} 
                variant="outline" 
                size="sm"
              >
                Refresh Page
              </Button>
            </div>
            <p className="text-xs text-muted-foreground mt-4">
              Error: {errorMessage}
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={className}>
      {/* Edit Expense Form Modal */}
      {editingExpense && (
        <EditExpenseForm
          expense={editingExpense}
          open={isEditModalOpen}
          onOpenChange={setIsEditModalOpen}
          onExpenseUpdated={handleExpenseUpdated}
        />
      )}

      {/* Integrated Table Header with Filters */}
      <ExpenseTableHeader
        expenses={expenses}
        filters={filters}
        onFiltersChange={handleFiltersChange}
        onFilteredExpensesChange={handleFilteredExpensesChange}
        isMobile={isMobile}
        enableFilterUrlSync={true}
      />
      
      {/* Enhanced Expense Table */}
      <div className="mt-4">
        <EnhancedExpenseTable
          expenses={filteredExpenses}
          onExpenseEdit={handleExpenseEdit}
          onExpenseDelete={handleExpenseDelete}
          onExpenseView={handleExpenseView}
          showBulkActions={false}
          defaultPageSize={isMobile ? 5 : 10}
        />
      </div>
    </div>
  )
}
