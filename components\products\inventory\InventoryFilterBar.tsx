'use client'

import { useState } from 'react'
import { Search, Filter, RotateCcw, Download, SlidersHorizontal } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet'
import { Label } from '@/components/ui/label'
import { cn } from '@/lib/utils'
import { type ProductFilters } from '@/components/products/products-catalog/product-filters'

interface InventoryFilterBarProps {
  searchQuery: string
  setSearchQuery: (query: string) => void
  filters: ProductFilters
  setFilters: (filters: ProductFilters) => void
  isMobile?: boolean
  isFilterSheetOpen: boolean
  setIsFilterSheetOpen: (isOpen: boolean) => void
  isExpanded: boolean
  setIsExpanded: (isExpanded: boolean) => void
  activeFilterCount: number
  clearAllFilters: () => void
  availableOptions: {
    categories: { value: string; label: string }[]
    suppliers: { value: string; label: string }[]
    stockStatus: { value: string; label: string }[]
  }
  handleStockStatusToggle: (status: 'in_stock' | 'low_stock' | 'out_of_stock') => void
}

export function InventoryFilterBar({
  searchQuery,
  setSearchQuery,
  filters,
  setFilters,
  isMobile = false,
  isFilterSheetOpen,
  setIsFilterSheetOpen,
  isExpanded,
  setIsExpanded,
  activeFilterCount,
  clearAllFilters,
  availableOptions,
  handleStockStatusToggle
}: InventoryFilterBarProps) {
  return (
    <div className="flex flex-col sm:flex-row gap-2 mb-4">
      <div className="relative flex-1">
        <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-gray-500" />
        <Input
          placeholder="Search by name or SKU..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className={cn(
            "pl-8 h-8 text-xs",
            isMobile && "text-sm min-h-[40px] touch-manipulation"
          )}
          autoComplete="off"
          spellCheck="false"
          autoCapitalize="none"
          autoCorrect="off"
        />
      </div>
      <div className="flex items-center gap-2">
        {!isMobile && (
          <Button 
            variant="outline" 
            size="sm" 
            className="h-8 text-xs relative"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            <SlidersHorizontal className="h-3.5 w-3.5" />
            {activeFilterCount > 0 && (
              <span className="absolute -top-1 -right-1 flex items-center justify-center w-4 h-4 rounded-full bg-red-500 text-white text-[10px] font-medium">
                {activeFilterCount}
              </span>
            )}
          </Button>
        )}
        
        {isMobile ? (
          <Sheet open={isFilterSheetOpen} onOpenChange={setIsFilterSheetOpen}>
            <SheetTrigger asChild>
              <Button 
                variant="outline" 
                size="sm" 
                className="relative touch-manipulation h-8 px-3 text-xs border-gray-300"
              >
                <SlidersHorizontal className="h-3.5 w-3.5 mr-2" />
                <span className="font-medium">Filters</span>
                {activeFilterCount > 0 && (
                  <span className="ml-1.5 flex items-center justify-center w-4 h-4 rounded-full bg-red-500 text-white text-xs font-medium">
                    {activeFilterCount}
                  </span>
                )}
              </Button>
            </SheetTrigger>
            <SheetContent 
              side="bottom" 
              className="h-[85vh] overflow-y-auto bg-background/95 backdrop-blur-sm"
              onOpenAutoFocus={(e) => e.preventDefault()}
            >
              <SheetHeader className="text-left pb-3 border-b">
                <SheetTitle className="text-base font-semibold">Filter Inventory</SheetTitle>
              </SheetHeader>
              <div className="py-3 space-y-4">
                {/* Stock Status filter */}
                <div className="space-y-2">
                  <Label className="text-xs font-medium text-foreground">Stock Status</Label>
                  <div className="border border-border rounded-md bg-background/50 shadow-xs">
                    <div className="p-2 space-y-1">
                      {availableOptions.stockStatus.map(status => (
                        <label
                          key={status.value}
                          className={cn(
                            "flex items-center space-x-2 p-1.5 rounded transition-all cursor-pointer group",
                            "hover:bg-accent/50 hover:text-accent-foreground text-xs"
                          )}
                        >
                          <input
                            type="checkbox"
                            checked={filters.stockStatus.includes(status.value as any)}
                            onChange={() => handleStockStatusToggle(status.value as any)}
                            className="h-3 rounded border-border text-primary focus:ring-primary/20 focus:ring-1"
                          />
                          <span className="font-medium text-xs">{status.label}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                </div>
                
                {/* Categories filter */}
                <div className="space-y-2">
                  <Label className="text-xs font-medium text-foreground">Categories</Label>
                  <div className="border border-border rounded-md bg-background/50 shadow-xs">
                    <div className="max-h-32 overflow-y-auto p-2 space-y-1">
                      {availableOptions.categories.length > 0 ? (
                        availableOptions.categories.map(category => (
                          <label
                            key={category.value}
                            className={cn(
                              "flex items-center space-x-2 p-1.5 rounded transition-all cursor-pointer group",
                              "hover:bg-accent/50 hover:text-accent-foreground text-xs"
                            )}
                          >
                            <input
                              type="checkbox"
                              checked={filters.categories.includes(category.value)}
                              onChange={() => {
                                const newCategories = filters.categories.includes(category.value)
                                  ? filters.categories.filter(c => c !== category.value)
                                  : [...filters.categories, category.value]
                              
                                setFilters({
                                  ...filters,
                                  categories: newCategories
                                })
                              }}
                              className="h-3 rounded border-border text-primary focus:ring-primary/20 focus:ring-1"
                            />
                            <span className="font-medium text-xs">{category.label}</span>
                          </label>
                        ))
                      ) : (
                        <div className="text-center py-3 text-muted-foreground">
                          <p className="text-xs">No categories available</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                
                {/* Suppliers filter */}
                <div className="space-y-2">
                  <Label className="text-xs font-medium text-foreground">Suppliers</Label>
                  <div className="border border-border rounded-md bg-background/50 shadow-xs">
                    <div className="max-h-32 overflow-y-auto p-2 space-y-1">
                      {availableOptions.suppliers.length > 0 ? (
                        availableOptions.suppliers.map(supplier => (
                          <label
                            key={supplier.value}
                            className={cn(
                              "flex items-center space-x-2 p-1.5 rounded transition-all cursor-pointer group",
                              "hover:bg-accent/50 hover:text-accent-foreground text-xs"
                            )}
                          >
                            <input
                              type="checkbox"
                              checked={filters.suppliers.includes(supplier.value)}
                              onChange={() => {
                                const newSuppliers = filters.suppliers.includes(supplier.value)
                                  ? filters.suppliers.filter(s => s !== supplier.value)
                                  : [...filters.suppliers, supplier.value]
                              
                                setFilters({
                                  ...filters,
                                  suppliers: newSuppliers
                                })
                              }}
                              className="h-3 rounded border-border text-primary focus:ring-primary/20 focus:ring-1"
                            />
                            <span className="font-medium text-xs">{supplier.label}</span>
                          </label>
                        ))
                      ) : (
                        <div className="text-center py-3 text-muted-foreground">
                          <p className="text-xs">No suppliers available</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                
                {/* Quantity Range filter */}
                <div className="space-y-2">
                  <Label className="text-xs font-medium text-foreground">Quantity Range</Label>
                  <div className="border border-border rounded-md bg-background/50 shadow-xs p-3">
                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <Label className="text-xs mb-1 block">Min</Label>
                        <Input
                          type="number"
                          placeholder="Min"
                          value={filters.quantityRange.min ?? ''}
                          onChange={(e) => setFilters({
                            ...filters,
                            quantityRange: {
                              ...filters.quantityRange,
                              min: e.target.value === '' ? null : Number(e.target.value)
                            }
                          })}
                          className="h-8 text-xs placeholder:text-xs"
                        />
                      </div>
                      <div>
                        <Label className="text-xs mb-1 block">Max</Label>
                        <Input
                          type="number"
                          placeholder="Max"
                          value={filters.quantityRange.max ?? ''}
                          onChange={(e) => setFilters({
                            ...filters,
                            quantityRange: {
                              ...filters.quantityRange,
                              max: e.target.value === '' ? null : Number(e.target.value)
                            }
                          })}
                          className="h-8 text-xs placeholder:text-xs"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="sticky bottom-0 bg-background/95 backdrop-blur-sm pt-3 border-t">
                <div className="flex gap-2">
                  <Button 
                    onClick={clearAllFilters} 
                    variant="outline" 
                    size="sm"
                    className="flex-1 touch-manipulation h-9 px-3 text-xs border-gray-300"
                    disabled={activeFilterCount === 0}
                  >
                    <RotateCcw className="h-3.5 w-3.5 mr-1.5" />
                    Clear All {activeFilterCount > 0 && `(${activeFilterCount})`}
                  </Button>
                  <Button 
                    onClick={() => setIsFilterSheetOpen(false)}
                    size="sm"
                    className="flex-1 touch-manipulation h-9 font-medium text-xs"
                  >
                    Apply Filters
                  </Button>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        ) : null}
      </div>
    </div>
  )
}