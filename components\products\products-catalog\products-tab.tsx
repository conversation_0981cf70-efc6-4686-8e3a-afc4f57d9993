'use client'

import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { useToast } from '@/components/ui/use-toast'
import { EditProductForm } from '@/components/products/products-catalog/edit-product-form'
import { ProductDetailView } from '@/components/products/products-catalog/product-detail-view'
import { ProductTableHeader } from '@/components/products/products-catalog/product-table-header'
import { type ProductFilters, INITIAL_PRODUCT_FILTERS } from '@/components/products/products-catalog/product-filters'
import { EnhancedProductTable } from '@/components/products/products-catalog/enhanced-product-table'
import { useAllProducts, useInvalidateProducts, type ExtendedProductRow } from '@/hooks/use-products'
import { getSupabaseClient, type ProductVariantRow } from '@/lib/supabase'
import { useAuth } from '@/contexts/auth-context'
import { useCurrency } from '@/lib/currency'
import { FileSpreadsheet } from 'lucide-react'
import { ErrorBoundary } from '@/components/ui/error-boundary'

interface ProductsTabProps {
  isMobile?: boolean
  className?: string
  onFiltersChange?: (filters: ProductFilters) => void
  onFilteredProductsChange?: (products: ExtendedProductRow[]) => void
  onSelectedProductsChange?: (productIds: string[]) => void
}

export function ProductsTab({
  isMobile = false,
  className,
  onFiltersChange,
  onFilteredProductsChange: externalOnFilteredProductsChange,
  onSelectedProductsChange
}: ProductsTabProps) {
  // Products state
  const [isLoadingProducts, setIsLoadingProducts] = useState(false)
  const [loadError, setLoadError] = useState<string | null>(null)
  
  // Edit form state
  const [editingProduct, setEditingProduct] = useState<ExtendedProductRow | null>(null)
  const [isEditFormOpen, setIsEditFormOpen] = useState(false)
  
  // Detail view state
  const [viewingProduct, setViewingProduct] = useState<ExtendedProductRow | null>(null)
  const [isDetailViewOpen, setIsDetailViewOpen] = useState(false)
  
  // Filter state
  const [filters, setFilters] = useState<ProductFilters>(INITIAL_PRODUCT_FILTERS)
  const [filteredProducts, setFilteredProducts] = useState<ExtendedProductRow[]>([])
  const [selectedProducts, setSelectedProducts] = useState<string[]>([])
  
  // Combined filter change handler
  const handleFiltersChange = (newFilters: ProductFilters) => {
    setFilters(newFilters)
    onFiltersChange?.(newFilters)
  }

  // Combined filtered products change handler (memoized to prevent infinite loops)
  const handleFilteredProductsChange = useCallback((products: ExtendedProductRow[]) => {
    setFilteredProducts(products)
    externalOnFilteredProductsChange?.(products)
  }, [externalOnFilteredProductsChange])

  // Handle selected products change
  const handleSelectedProductsChange = useCallback((productIds: string[]) => {
    setSelectedProducts(productIds)
    onSelectedProductsChange?.(productIds)
  }, [onSelectedProductsChange])
  
  const { toast } = useToast()
  const { user, organizationId } = useAuth()
  const { userCurrency, formatCurrency } = useCurrency()
  const supabase = getSupabaseClient()
  
  // Use React Query to fetch products
  const { data: productsData, isLoading, error, refetch } = useAllProducts(organizationId || undefined)
  const products = (productsData || []) as ExtendedProductRow[]
  
  // Transform products to match what EnhancedProductTable expects
  const transformedProducts = useMemo(() => {
    return products.map(product => {
      // Explicitly cast to any to access the variants property
      const productWithVariants = product as any;
      return {
        ...product,
        category_name: productWithVariants.category_name || null,
        variants: productWithVariants.variants || []
      };
    });
  }, [products]);
  
  // Transform filtered products to match what EnhancedProductTable expects
  const transformedFilteredProducts = useMemo(() => {
    return filteredProducts.map(product => {
      // Explicitly cast to any to access the variants property
      const productWithVariants = product as any;
      return {
        ...product,
        category_name: productWithVariants.category_name || null,
        variants: productWithVariants.variants || []
      };
    });
  }, [filteredProducts])
  
  // Use invalidate function to trigger refetching
  const { invalidateAll } = useInvalidateProducts()

  // Extract unique categories from products
  const categories = useMemo(() => {
    const categorySet = new Set<{ value: string; label: string }>()
    
    products.forEach((product: ExtendedProductRow) => {
      if (product.category_name) {
        categorySet.add({ 
          value: product.category_name, 
          label: product.category_name 
        })
      }
    })
    
    return Array.from(categorySet)
  }, [products])

  // Refs to store the latest functions to avoid dependency issues
  const handleFilteredProductsChangeRef = useRef(handleFilteredProductsChange)
  const onSelectedProductsChangeRef = useRef(onSelectedProductsChange)
  
  // Update refs when functions change
  useEffect(() => {
    handleFilteredProductsChangeRef.current = handleFilteredProductsChange
    onSelectedProductsChangeRef.current = onSelectedProductsChange
  }, [handleFilteredProductsChange, onSelectedProductsChange])

  // Update filtered products when products data changes
  // Use useRef to track previous products to prevent infinite loop
  const prevProductsRef = useRef<ExtendedProductRow[]>([])
  useEffect(() => {
    // Only call the handler if products actually changed
    if (JSON.stringify(products) !== JSON.stringify(prevProductsRef.current)) {
      prevProductsRef.current = products
      handleFilteredProductsChangeRef.current(products)
    }
  }, [products])

  // Listen for product added/updated events to refresh the product list
  useEffect(() => {
    const handleProductAdded = () => {
      invalidateAll()
    }

    const handleProductUpdated = () => {
      invalidateAll()
    }

    // Listen for inventory updates (e.g., when an order is created)
    const handleInventoryUpdated = () => {
      invalidateAll()
    }

    window.addEventListener('productAdded', handleProductAdded)
    window.addEventListener('productUpdated', handleProductUpdated)
    window.addEventListener('inventoryUpdated', handleInventoryUpdated)

    return () => {
      window.removeEventListener('productAdded', handleProductAdded)
      window.removeEventListener('productUpdated', handleProductUpdated)
      window.removeEventListener('inventoryUpdated', handleInventoryUpdated)
    }
  }, [invalidateAll])

  const handleProductAdded = () => {
    invalidateAll()
  }

  const handleProductEdit = (product: ExtendedProductRow) => {
    setEditingProduct(product)
    setIsEditFormOpen(true)
  }

  const handleProductUpdated = () => {
    invalidateAll()
    setIsEditFormOpen(false)
    setEditingProduct(null)
  }

  const handleProductDelete = async (productIds: string[]) => {
    if (!organizationId) return
    
    try {
      const { error } = await supabase
        .from('products')
        .delete()
        .in('id', productIds)
        .eq('organization_id', organizationId)
      
      if (error) {
        toast({
          title: "Error Deleting Products",
          description: `Failed to delete products: ${error.message}`,
          variant: "destructive",
        })
      } else {
        // Refresh the products list
        invalidateAll()
        
        // Dispatch dashboard refresh event
        window.dispatchEvent(new CustomEvent('dashboardRefresh'))
        
        toast({
          title: "Products Deleted",
          description: `Successfully deleted ${productIds.length} product(s).`,
        })
      }
    } catch (error) {
      console.error('Error deleting products:', error)
      toast({
        title: "Unexpected Error",
        description: "Failed to delete products. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleProductView = (product: ExtendedProductRow) => {
    setViewingProduct(product)
    setIsDetailViewOpen(true)
  }

  // Render skeleton loading state with timeout
  const [showLoading, setShowLoading] = useState(true)
  
  useEffect(() => {
    if (isLoading) {
      setShowLoading(true)
      const timer = setTimeout(() => {
        setShowLoading(false)
      }, 5000) // Show loading for maximum 5 seconds
      
      return () => clearTimeout(timer)
    }
  }, [isLoading])

  if (isLoading && showLoading) {
    return (
      <div className={className}>
        <div className="mb-6">
          <Skeleton className="h-12 w-full mb-4" />
          <div className="flex gap-2">
            <Skeleton className="h-8 w-24" />
            <Skeleton className="h-8 w-24" />
            <Skeleton className="h-8 w-24" />
          </div>
        </div>
        
        <div className="border border-gray-200 rounded-lg overflow-hidden">
          <div className="bg-gray-50 p-4 border-b">
            <Skeleton className="h-6 w-32" />
          </div>
          <div className="divide-y">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="p-4 flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div>
                    <Skeleton className="h-4 w-32 mb-2" />
                    <Skeleton className="h-3 w-24" />
                  </div>
                </div>
                <Skeleton className="h-8 w-8 rounded" />
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  // If loading timed out but we still don't have data, show error state
  if (isLoading && !showLoading) {
    return (
      <div className={className}>
        <div className="flex items-center justify-center py-8">
          <div className="text-center max-w-md">
            <div className="text-red-500 mb-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
              <h3 className="text-lg font-medium">Loading Taking Too Long</h3>
            </div>
            <p className="text-muted-foreground text-sm mb-4">
              We're having trouble loading your products. Please check your internet connection.
            </p>
            <div className="flex flex-col sm:flex-row gap-2 justify-center">
              <Button onClick={() => refetch()} variant="default" size="sm">
                Retry
              </Button>
              <Button 
                onClick={() => {
                  // Try to refresh the page as a last resort
                  window.location.reload()
                }} 
                variant="outline" 
                size="sm"
              >
                Refresh Page
              </Button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Render error state with user-friendly messages
  if (error) {
    // Type guard to ensure error is properly typed
    const errorMessage = error instanceof Error ? error.message : String(error)
    
    let userFriendlyMessage = "Failed to load products. Please try again."
    
    // Provide more specific error messages based on the error type
    if (errorMessage.includes("NetworkError") || errorMessage.includes("Failed to fetch")) {
      userFriendlyMessage = "Network connection error. Please check your internet connection and try again."
    } else if (errorMessage.includes("ERR_INSUFFICIENT_RESOURCES")) {
      userFriendlyMessage = "System resource limit reached. Please refresh the page or try again in a few minutes."
    } else if (errorMessage.includes("timeout")) {
      userFriendlyMessage = "Request timed out. Please try again."
    } else if (errorMessage.includes("401") || errorMessage.includes("403")) {
      userFriendlyMessage = "Authentication error. Please sign in again."
    } else if (errorMessage.includes("500")) {
      userFriendlyMessage = "Server error. Please try again later."
    }
    
    return (
      <div className={className}>
        <div className="flex items-center justify-center py-8">
          <div className="text-center max-w-md">
            <div className="text-red-500 mb-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
              <h3 className="text-lg font-medium">Unable to Load Products</h3>
            </div>
            <p className="text-muted-foreground text-sm mb-4">{userFriendlyMessage}</p>
            <div className="flex flex-col sm:flex-row gap-2 justify-center">
              <Button onClick={() => refetch()} variant="default" size="sm">
                Retry
              </Button>
              <Button 
                onClick={() => {
                  // Try to refresh the page as a last resort
                  window.location.reload()
                }} 
                variant="outline" 
                size="sm"
              >
                Refresh Page
              </Button>
            </div>
            <p className="text-xs text-muted-foreground mt-4">
              Error: {errorMessage}
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <ErrorBoundary>
      <div className={className}>
        {/* Edit Product Form - Modal */}
        {editingProduct && (
          <EditProductForm
            product={editingProduct}
            open={isEditFormOpen}
            onOpenChange={setIsEditFormOpen}
            onProductUpdated={handleProductUpdated}
          />
        )}

        {/* Product Detail View - Modal */}
        {viewingProduct && (
          <ProductDetailView
            product={viewingProduct}
            open={isDetailViewOpen}
            onOpenChange={setIsDetailViewOpen}
            onProductUpdated={handleProductUpdated}
          />
        )}

        {/* Integrated Table Header with Filters */}
        <ProductTableHeader
          products={transformedProducts}
          filters={filters}
          onFiltersChange={handleFiltersChange}
          onFilteredProductsChange={(products) => {
            // Transform products back to ExtendedProductRow format
            const extendedProducts = products.map(product => ({
              ...product,
              category_name: (product as any).category_name || null,
              variants: (product as any).variants || []
            }))
            handleFilteredProductsChange(extendedProducts)
          }}
          isMobile={isMobile}
          enableFilterUrlSync={true}
          categories={categories}
        />
        
        {/* Enhanced Product Table */}
        <div className="mt-4">
          <EnhancedProductTable
            products={transformedProducts}
            onProductEdit={handleProductEdit}
            onProductDelete={handleProductDelete}
            onProductView={handleProductView}
            onSelectedProductsChange={handleSelectedProductsChange}
            showBulkActions={true}
            defaultPageSize={isMobile ? 5 : 10}
            isMobile={isMobile}
          />
        </div>
      </div>
    </ErrorBoundary>
  )
}