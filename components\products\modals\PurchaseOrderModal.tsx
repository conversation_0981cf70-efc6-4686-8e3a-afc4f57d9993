'use client'

import { useState, useEffect } from 'react'
import { 
  <PERSON><PERSON>, 
  <PERSON>alog<PERSON>ontent, 
  DialogHeader, 
  DialogTitle,
  DialogDescription
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { DatePicker } from '@/components/ui/date-picker'
import { getSupabaseClient } from '@/lib/supabase'
import { useToast } from '@/components/ui/use-toast'
import { type InventoryItem } from '../inventory/types'
import { createPurchaseOrder, createDraftProduct } from '@/components/products/purchase-orders/purchase-order-service'
import { useAuth } from '@/contexts/auth-context'
import { useCurrency } from '@/contexts/currency-context'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { PlusCircle, X } from 'lucide-react'
import { CustomDropdown } from '@/components/ui/custom-dropdown'
import { formatCurrencyStandalone } from '@/lib/currency'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { EnhancedProductSelector } from './EnhancedProductSelector'

interface VariantAttributes {
  size?: string
  color?: string
  material?: string
  style?: string
}

interface LineItem {
  id: string
  productId: string | null
  variantId: string | null
  productName: string
  variantName?: string // Display name for variant (e.g., "Size: 6, Color: Gold")
  sku: string
  quantity: number
  unitCost: number
  subtotal: number
  variantAttributes?: VariantAttributes // Structured variant attributes
  isNewProduct?: boolean // Flag to indicate if this is a new product being created
}

// New interface for product selection
interface SelectableProduct {
  id: string
  name: string
  base_sku: string
  supplier: string
  base_cost: number
  packaging_cost: number
  has_variants: boolean
  product_variants?: any[]
}

interface PurchaseOrderModalProps {
  isOpen: boolean
  onClose: () => void
  selectedItem: InventoryItem | null
  products: any[] // ExtendedProductRow[]
  mode?: 'inventory' | 'purchase-orders' // Default to 'inventory' for backward compatibility

}

export function PurchaseOrderModal({ isOpen, onClose, selectedItem, products, mode = 'inventory' }: PurchaseOrderModalProps) {
  const { user } = useAuth()
  const { formatCurrency } = useCurrency()
  const [supplier, setSupplier] = useState('')
  const [poNumber, setPoNumber] = useState('')
  const [issueDate, setIssueDate] = useState<Date>(new Date())
  const [expectedArrival, setExpectedArrival] = useState<Date | undefined>(undefined)
  const [lineItems, setLineItems] = useState<LineItem[]>([])
  const [errors, setErrors] = useState<{poNumber?: string}>({})
  const [availableSuppliers, setAvailableSuppliers] = useState<string[]>([])

  // Financial fields
  const [taxRate, setTaxRate] = useState<number>(0)
  const [shippingCost, setShippingCost] = useState<number>(0)
  const [otherCharges, setOtherCharges] = useState<number>(0)
  const [discountAmount, setDiscountAmount] = useState<number>(0)
  
  const { toast } = useToast()
  // Use getSupabaseClient instead of createSupabaseClient to ensure we get a fresh client
  const supabase = getSupabaseClient()

  // Get unique suppliers from products
  useEffect(() => {
    if (mode === 'purchase-orders' && products.length > 0) {
      const suppliers = Array.from(new Set(products.map(p => p.supplier).filter(Boolean))) as string[]
      setAvailableSuppliers(suppliers)
    }
  }, [products, mode])

  // Generate PO number when supplier changes in purchase-orders mode
  useEffect(() => {
    if (mode === 'purchase-orders' && supplier) {
      // Generate PO number (simplified version)
      const date = new Date()
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      setPoNumber(`PO-${supplier.substring(0, 3).toUpperCase()}-${year}${month}${day}`)
    }
  }, [supplier, mode])

  // Track if modal was previously open to prevent unnecessary resets
  const [wasOpen, setWasOpen] = useState(false)

  // Initialize form when selectedItem or mode changes
  useEffect(() => {
    if (!isOpen) {
      setWasOpen(false)
      return
    }

    // Only reset if modal is newly opened (not just re-rendering)
    if (wasOpen) return
    setWasOpen(true)

    // Reset form
    setErrors({})

    if (mode === 'inventory' && selectedItem) {
      // Inventory mode: Pre-fill with selected item data
      
      // Set supplier from product data
      const product = products.find(p => p.id === selectedItem.productId)
      setSupplier(product?.supplier || '')
      
      // Generate PO number (simplified version)
      const date = new Date()
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      setPoNumber(`PO-${year}-${month}-${day}-001`)
      
      // Set issue date to today
      setIssueDate(new Date())
      
      // Add selected item as first line item
      const unitCost = selectedItem.type === 'product'
        ? (product?.base_cost || 0)
        : (() => {
            const variant = product?.variants?.find((v: any) => v.id === selectedItem.variantId);
            // Use base_cost if available (new format), otherwise fall back to cost_adjustment (legacy)
            return (variant?.base_cost !== undefined && variant?.base_cost !== null && variant?.base_cost > 0)
              ? variant.base_cost
              : (variant?.cost_adjustment || 0);
          })()
      
      const newItem: LineItem = {
        id: `item-${Date.now()}`,
        productId: selectedItem.type === 'product' ? selectedItem.productId : null,
        variantId: selectedItem.type === 'variant' ? selectedItem.variantId : null,
        productName: selectedItem.name,
        sku: selectedItem.sku,
        quantity: 1,
        unitCost: unitCost,
        subtotal: unitCost
      }
      
      setLineItems([newItem])
    } else if (mode === 'purchase-orders') {
      // Purchase orders mode: Start with empty form
      setSupplier('')
      setPoNumber('')
      setIssueDate(new Date())
      setExpectedArrival(undefined)
      setLineItems([])
      // Reset financial fields
      setTaxRate(0)
      setShippingCost(0)
      setOtherCharges(0)
      setDiscountAmount(0)
    }
  }, [isOpen, selectedItem?.productId, selectedItem?.variantId, mode, wasOpen])

  // Update subtotal when quantity or unit cost changes
  useEffect(() => {
    setLineItems(prevItems => 
      prevItems.map(item => ({
        ...item,
        subtotal: item.quantity * item.unitCost
      }))
    )
  }, [])

  // Handle quantity change for a line item
  const handleQuantityChange = (id: string, quantity: number) => {
    setLineItems(prevItems => 
      prevItems.map(item => 
        item.id === id 
          ? { ...item, quantity: quantity, subtotal: quantity * item.unitCost } 
          : item
      )
    )
  }

  // Handle unit cost change for a line item
  const handleUnitCostChange = (id: string, unitCost: number) => {
    setLineItems(prevItems => 
      prevItems.map(item => 
        item.id === id 
          ? { ...item, unitCost: unitCost, subtotal: item.quantity * unitCost } 
          : item
      )
    )
  }

  // Add another product from the same supplier
  const handleAddProduct = () => {
    // For purchase-orders mode, we'll show the searchable input
    // For inventory mode, we'll add an empty row as before
    if (mode === 'purchase-orders') {
      const newItem: LineItem = {
        id: `search-${Date.now()}`,
        productId: null,
        variantId: null,
        productName: '',
        sku: '',
        quantity: 1,
        unitCost: 0,
        subtotal: 0,
        isNewProduct: true // Mark as new product creation
      }
      setLineItems(prev => [...prev, newItem])
    } else {
      // Inventory mode - keep existing behavior
      const newItem: LineItem = {
        id: `item-${Date.now()}`,
        productId: null,
        variantId: null,
        productName: '',
        sku: '',
        quantity: 1,
        unitCost: 0,
        subtotal: 0
      }
      setLineItems(prev => [...prev, newItem])
    }
  }

  // Calculate financial totals
  const calculateSubtotal = () => {
    return lineItems.reduce((total, item) => total + item.subtotal, 0)
  }

  const calculateTaxAmount = () => {
    return (calculateSubtotal() * taxRate) / 100
  }

  const calculateTotal = () => {
    const subtotal = calculateSubtotal()
    const tax = calculateTaxAmount()
    return subtotal + tax + shippingCost + otherCharges - discountAmount
  }

  // Handle form submission
  const handleSubmit = async (saveAsDraft: boolean) => {
    // Validate required fields
    const newErrors: {poNumber?: string} = {}
    
    // Check if supplier is selected (required in both modes now)
    if (!supplier.trim()) {
      toast({
        title: "Error",
        description: "Please select a supplier",
        variant: "destructive"
      })
      return
    }
    
    // Only require PO number in inventory mode, it's auto-generated in purchase-orders mode
    if (mode === 'inventory' && !poNumber.trim()) {
      newErrors.poNumber = 'PO Number is required'
    }
    
    // Validate line items
    if (lineItems.length === 0) {
      toast({
        title: "Error",
        description: "At least one line item is required",
        variant: "destructive"
      })
      return
    }
    
    for (const item of lineItems) {
      if (!item.productName.trim()) {
        toast({
          title: "Error",
          description: "Product name is required for all items",
          variant: "destructive"
        })
        return
      }
      
      if (item.quantity <= 0) {
        toast({
          title: "Error",
          description: "Quantity must be greater than zero",
          variant: "destructive"
        })
        return
      }
      
      if (item.unitCost < 0) {
        toast({
          title: "Error",
          description: "Unit cost cannot be negative",
          variant: "destructive"
        })
        return
      }

      // Additional validation for new products
      if (item.isNewProduct) {
        if (!item.sku.trim()) {
          toast({
            title: "Error",
            description: "SKU is required for new products",
            variant: "destructive"
          })
          return
        }

        if (item.unitCost <= 0) {
          toast({
            title: "Error",
            description: "Unit cost must be greater than zero for new products",
            variant: "destructive"
          })
          return
        }
      }
    }
    
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors)
      return
    }
    
    if (!user) {
      toast({
        title: "Error",
        description: "User not authenticated",
        variant: "destructive"
      })
      return
    }
    
    try {
      // Separate new products from existing products
      const newProducts = lineItems.filter(item => item.isNewProduct)
      const existingProducts = lineItems.filter(item => !item.isNewProduct)
      
      // Create new products with Draft status if any
      const createdProducts: Record<string, string> = {} // Map of product names to product IDs
      if (newProducts.length > 0) {
        for (const item of newProducts) {
          const newProduct = await createDraftProduct(user.id, {
            name: item.productName,
            sku: item.sku,
            supplier: supplier,
            base_cost: item.unitCost
          })
          createdProducts[item.productName] = newProduct.id
        }
      }
      
      // Prepare form data
      const formData = {
        supplier,
        issue_date: issueDate,
        expected_arrival_date: expectedArrival,
        notes: '',
        reference_number: poNumber, // Use the PO number here
        // Financial data
        subtotal: calculateSubtotal(),
        tax_rate: taxRate,
        tax_amount: calculateTaxAmount(),
        shipping_cost: shippingCost,
        other_charges: otherCharges,
        discount_amount: discountAmount,
        total_value: calculateTotal(),
        items: lineItems.map(item => ({
          productId: item.isNewProduct ? createdProducts[item.productName] : item.productId,
          variantId: item.variantId,
          productName: item.productName,
          variantName: item.variantName,
          sku: item.sku,
          quantity: item.quantity,
          unitCost: item.unitCost,
          subtotal: item.subtotal,
          variantAttributes: item.variantAttributes
        }))
      }
      
      // Create the purchase order
      await createPurchaseOrder(user.id, formData)
      
      toast({
        title: saveAsDraft ? "Purchase Order Saved" : "Purchase Order Created",
        description: saveAsDraft 
          ? "Purchase order has been saved as draft" 
          : "Purchase order has been created successfully"
      })
      
      // Close the modal
      onClose()
    } catch (error: any) {
      console.error('Error creating purchase order:', error)
      toast({
        title: "Error",
        description: error.message || "Failed to create purchase order. Please try again.",
        variant: "destructive"
      })
    }
  }

  // Early return if not open
  if (!isOpen) {
    return null
  }
  
  // Early return if in inventory mode but no selected item
  const shouldRender = !(mode === 'inventory' && !selectedItem);
  
  if (!shouldRender) {
    return null;
  }

  // Determine if line items should be disabled
  // Changed: Allow line items to be enabled even without supplier initially
  // We'll validate supplier selection during form submission instead
  const isLineItemsDisabled = mode === 'purchase-orders' && lineItems.length === 0 && !supplier

  return (
    <Dialog open={isOpen} onOpenChange={() => {}} modal={true}>
      <DialogContent className="w-[95vw] max-w-3xl max-h-[90vh] sm:max-h-[85vh] overflow-y-auto rounded-xl shadow-xl border-0 bg-white dark:bg-gray-900" hideCloseButton>
        <DialogHeader className="border-b border-gray-100 dark:border-gray-800 pb-4">
          <div className="flex items-start justify-between">
            <div className="space-y-1">
              <DialogTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                New Purchase Order
              </DialogTitle>
              <DialogDescription className="text-sm text-gray-600 dark:text-gray-400">
                Create a purchase order to reorder inventory
              </DialogDescription>
            </div>
            <button
              onClick={onClose}
              className="p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
            >
              <svg className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          {/* Supplier Information */}
          <div className="space-y-4">
            <h3 className="text-base font-semibold text-gray-900 dark:text-gray-100">
              Supplier Information
            </h3>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-xs font-medium text-gray-900 dark:text-gray-100">
                  Supplier
                </label>
                {mode === 'inventory' ? (
                  <Input
                    type="text"
                    value={supplier}
                    onChange={(e) => setSupplier(e.target.value)}
                    className="h-8 text-xs bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700"
                    readOnly
                  />
                ) : (
                  <CustomDropdown
                    options={availableSuppliers.map(sup => ({ value: sup, label: sup }))}
                    value={supplier}
                    onValueChange={setSupplier}
                    onAddCustomOption={(newOption) => {
                      const newSupplier = newOption.value;
                      setSupplier(newSupplier);
                      // Add to available suppliers if not already present
                      if (!availableSuppliers.includes(newSupplier)) {
                        setAvailableSuppliers(prev => [...prev, newSupplier]);
                      }
                    }}
                    placeholder="Select supplier"
                    allowCustom={true}
                    customPlaceholder="Add new supplier"
                    className="h-8 text-xs border-gray-200 dark:border-gray-700 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200"
                  />
                )}
              </div>
              <div className="space-y-2">
                <label className="text-xs font-medium text-gray-700 dark:text-gray-300">
                  PO Number
                </label>
                <Input
                  type="text"
                  value={poNumber}
                  onChange={(e) => {
                    setPoNumber(e.target.value)
                    if (errors.poNumber) {
                      setErrors(prev => ({ ...prev, poNumber: undefined }))
                    }
                  }}
                  className={`h-8 text-xs ${errors.poNumber ? 'border-red-500' : ''}`}
                  aria-invalid={!!errors.poNumber}
                  aria-describedby={errors.poNumber ? "po-number-error" : undefined}
                  readOnly={mode === 'purchase-orders'} // Make it read-only in purchase-orders mode
                />
                {errors.poNumber && (
                  <p id="po-number-error" className="text-xs text-red-500">
                    {errors.poNumber}
                  </p>
                )}
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-xs font-medium text-gray-700 dark:text-gray-300">
                  Issue Date
                </label>
                <DatePicker
                  date={issueDate}
                  onDateChange={(date) => date && setIssueDate(date)}
                  className="h-8 text-xs"
                />
              </div>
              <div className="space-y-2">
                <label className="text-xs font-medium text-gray-700 dark:text-gray-300">
                  Expected Arrival
                </label>
                <DatePicker
                  date={expectedArrival}
                  onDateChange={setExpectedArrival}
                  placeholder="Select date"
                  className="h-8 text-xs"
                />
              </div>
            </div>
          </div>
          
          {/* Line Items */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-base font-semibold text-gray-900 dark:text-gray-100">
                Line Items
              </h3>
              <Button
                variant="outline"
                size="sm"
                onClick={handleAddProduct}
                className="h-8 px-3 text-xs font-medium bg-black text-white border-gray-300 hover:bg-gray-900 hover:text-white hover:border-gray-400 transition-all duration-200 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={isLineItemsDisabled}
              >
                <svg className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Add Product
              </Button>
            </div>
            
            {isLineItemsDisabled ? (
              <div className="border border-gray-200 dark:border-gray-700 rounded-xl p-12 text-center bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700">
                <div className="mx-auto w-16 h-16 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center mb-4">
                  <svg className="h-8 w-8 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                  </svg>
                </div>
                {mode === 'purchase-orders' && !supplier ? (
                  <div className="space-y-2">
                    <p className="text-gray-600 dark:text-gray-400 font-medium">Select a supplier first</p>
                    <p className="text-sm text-gray-500 dark:text-gray-500">Choose a supplier to start adding products to your purchase order</p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <p className="text-gray-600 dark:text-gray-400 font-medium">No products added yet</p>
                    <p className="text-sm text-gray-500 dark:text-gray-500">Click "Add Product" to start building your purchase order</p>
                  </div>
                )}
              </div>
            ) : (
              <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden bg-white dark:bg-gray-800 shadow-sm">
                <div className="max-h-[300px] overflow-y-auto">
                  <Table>
                    <TableHeader className="sticky top-0 bg-white dark:bg-gray-800 z-10">
                      <TableRow className="bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
                        <TableHead className="text-xs font-semibold text-gray-900 dark:text-gray-100 h-8 px-3 w-2/5">Product</TableHead>
                        <TableHead className="text-xs font-semibold text-gray-900 dark:text-gray-100 text-left h-8 px-2 w-32">SKU</TableHead>
                        <TableHead className="text-xs font-semibold text-gray-900 dark:text-gray-100 text-left h-8 px-2 w-20">Qty</TableHead>
                        <TableHead className="text-xs font-semibold text-gray-900 dark:text-gray-100 text-left h-8 px-2 w-24">Unit Cost</TableHead>
                        <TableHead className="text-xs font-semibold text-gray-900 dark:text-gray-100 text-left h-8 px-3 w-24">Subtotal</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                    {lineItems.map((item, index) => (
                      <TableRow key={item.id} className="border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-all duration-200 group">
                        <TableCell className="p-2 align-middle w-2/5">
                          {mode === 'purchase-orders' ? (
                            <div className="space-y-2">
                              <div className="relative">
                                <EnhancedProductSelector
                                  products={products}
                                  supplier={supplier}
                                  value={{
                                    productId: item.productId,
                                    variantId: item.variantId,
                                    productName: item.productName,
                                    variantName: item.variantName,
                                    sku: item.sku,
                                    unitCost: item.unitCost,
                                    variantAttributes: item.variantAttributes
                                  }}
                                  onChange={(product) => {
                                    setLineItems(prev =>
                                      prev.map((i, idx) =>
                                        idx === index ? {
                                          ...i,
                                          productId: product?.productId || null,
                                          variantId: product?.variantId || null,
                                          productName: product?.productName || '',
                                          variantName: product?.variantName || undefined,
                                          sku: product?.sku || '',
                                          unitCost: product?.unitCost || 0,
                                          subtotal: (product?.unitCost || 0) * i.quantity,
                                          variantAttributes: product?.variantAttributes || undefined
                                        } : i
                                      )
                                    )
                                  }}
                                  onNewProduct={(productName, generatedSKU) => {
                                    setLineItems(prev =>
                                      prev.map((i, idx) =>
                                        idx === index ? {
                                          ...i,
                                          productName: productName,
                                          sku: generatedSKU,
                                          isNewProduct: true
                                        } : i
                                      )
                                    )
                                  }}
                                />
                                {item.isNewProduct && item.productName && (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      // Remove the newly created product
                                      setLineItems(prev => prev.filter(i => i.id !== item.id));
                                    }}
                                  >
                                    <X className="h-3 w-3" />
                                  </Button>
                                )}
                              </div>
                            </div>
                          ) : (
                            <div className="relative">
                              <Input
                                type="text"
                                value={item.productName}
                                onChange={(e) => setLineItems(prev =>
                                  prev.map((i, idx) =>
                                    idx === index ? { ...i, productName: e.target.value } : i
                                  )
                                )}
                                className="text-xs h-8 w-full pr-10"
                                placeholder="Product name"
                                readOnly={!!item.productId} // Read-only if product is selected
                              />
                              {item.productName && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    // Clear the product name
                                    setLineItems(prev =>
                                      prev.map((i, idx) =>
                                        idx === index ? { ...i, productName: '' } : i
                                      )
                                    );
                                  }}
                                >
                                  <X className="h-3 w-3" />
                                </Button>
                              )}
                            </div>
                          )}
                        </TableCell>
                        <TableCell className="p-2 align-top text-left w-32">
                          {item.isNewProduct ? (
                            <Input
                              type="text"
                              value={item.sku}
                              onChange={(e) => setLineItems(prev =>
                                prev.map((i, idx) =>
                                  idx === index ? { ...i, sku: e.target.value } : i
                                )
                              )}
                              placeholder="Enter SKU"
                              className="h-8 w-full text-xs border-gray-200 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500/20"
                            />
                          ) : (
                            <div className="text-xs text-gray-700 dark:text-gray-300 py-2">
                              {item.sku || '—'}
                            </div>
                          )}
                        </TableCell>
                        <TableCell className="p-2 align-top text-left w-20">
                          <Input
                            type="number"
                            min="1"
                            value={item.quantity}
                            onChange={(e) => handleQuantityChange(item.id, Number(e.target.value))}
                            className="h-8 w-12 text-xs text-center border-gray-200 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                          />
                        </TableCell>
                        <TableCell className="p-2 align-top text-left w-24">
                          <Input
                            type="number"
                            min="0"
                            step="0.01"
                            value={item.unitCost}
                            onChange={(e) => handleUnitCostChange(item.id, Number(e.target.value))}
                            className="h-8 w-16 text-xs text-center border-gray-200 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                          />
                        </TableCell>
                        <TableCell className="p-2 align-top text-left text-xs font-semibold w-24">
                          <div className="text-left text-gray-900 dark:text-gray-100">
                            {formatCurrency(item.subtotal)}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                    </TableBody>
                  </Table>
                </div>
              </div>
            )}
            
            {/* Financial Summary */}
            {!isLineItemsDisabled && (
              <div className="flex justify-end">
                <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-3 w-[280px]">
                  <div className="space-y-2">
                    {/* Subtotal */}
                    <div className="flex justify-between items-center py-1">
                      <span className="text-xs text-gray-900 dark:text-gray-100">Subtotal</span>
                      <span className="text-xs font-medium text-gray-900 dark:text-gray-100">
                        {formatCurrency(calculateSubtotal())}
                      </span>
                    </div>

                    {/* Tax */}
                    <div className="flex justify-between items-center py-1">
                      <span className="text-xs text-gray-900 dark:text-gray-100">Tax</span>
                      <input
                        type="number"
                        min="0"
                        max="100"
                        step="0.01"
                        value={taxRate || ''}
                        onChange={(e) => setTaxRate(Number(e.target.value) || 0)}
                        className="h-8 w-16 text-xs text-right px-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                        placeholder="0%"
                      />
                    </div>

                    {/* Shipping */}
                    <div className="flex justify-between items-center py-1">
                      <span className="text-xs text-gray-900 dark:text-gray-100">Shipping</span>
                      <input
                        type="number"
                        min="0"
                        step="0.01"
                        value={shippingCost || ''}
                        onChange={(e) => setShippingCost(Number(e.target.value) || 0)}
                        className="h-8 w-16 text-xs text-right px-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                        placeholder="0.00"
                      />
                    </div>

                    {/* Other */}
                    <div className="flex justify-between items-center py-1">
                      <span className="text-xs text-gray-900 dark:text-gray-100">Other</span>
                      <input
                        type="number"
                        min="0"
                        step="0.01"
                        value={otherCharges || ''}
                        onChange={(e) => setOtherCharges(Number(e.target.value) || 0)}
                        className="h-8 w-16 text-xs text-right px-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                        placeholder="0.00"
                      />
                    </div>

                    {/* Discount */}
                    <div className="flex justify-between items-center py-1">
                      <span className="text-xs text-gray-900 dark:text-gray-100">Discount</span>
                      <input
                        type="number"
                        min="0"
                        step="0.01"
                        value={discountAmount || ''}
                        onChange={(e) => setDiscountAmount(Number(e.target.value) || 0)}
                        className="h-8 w-16 text-xs text-right px-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                        placeholder="0.00"
                      />
                    </div>

                    {/* Total */}
                    <div className="border-t border-gray-200 dark:border-gray-600 pt-2 mt-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-semibold text-gray-900 dark:text-gray-100">Total</span>
                        <span className="text-sm font-bold text-gray-900 dark:text-gray-100">
                          {formatCurrency(calculateTotal())}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
        
        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-100 dark:border-gray-800">
          <Button
            variant="outline"
            onClick={() => handleSubmit(true)}
            className="h-9 px-4 text-sm font-medium border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200"
            disabled={isLineItemsDisabled || lineItems.length === 0}
          >
            Save as Draft
          </Button>
          <Button
            onClick={() => handleSubmit(false)}
            className="h-9 px-4 text-sm font-medium bg-blue-600 hover:bg-blue-700 transition-all duration-200"
            disabled={isLineItemsDisabled || lineItems.length === 0}
          >
            Create Purchase Order
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}