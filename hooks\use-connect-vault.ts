'use client';

import { useMutation } from '@tanstack/react-query';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

interface ConnectVaultParams {
  vaultUrl: string;
  apiKey: string;
  accountId?: string;
}

interface ConnectVaultResponse {
  success: boolean;
  integration_id?: string;
  message?: string;
  error?: string;
}

export const useConnectVault = ({
  onSuccess,
  onError
}: {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
} = {}) => {
  const supabase = createClientComponentClient();

  return useMutation({
    mutationFn: async ({ vaultUrl, apiKey, accountId }: ConnectVaultParams) => {
      try {
        // Validate inputs
        if (!vaultUrl || !apiKey) {
          throw new Error('Vault URL and API Key are required');
        }

        // Validate URL format
        try {
          new URL(vaultUrl);
        } catch {
          throw new Error('Invalid Vault URL format');
        }

        // Check if user is authenticated
        const { data: { session } } = await supabase.auth.getSession();
        if (!session) {
          throw new Error('User not authenticated');
        }

        // Check if user is member of an organization
        const { data: orgMembership, error: orgError } = await supabase
          .from('organization_members')
          .select('organization_id')
          .eq('user_id', session.user.id)
          .single();

        if (orgError && orgError.code !== 'PGRST116') {
          throw new Error('Failed to verify organization membership: ' + orgError.message);
        }

        if (!orgMembership) {
          throw new Error('User must be a member of an organization to connect integrations');
        }

        // Call the Supabase RPC function to create the integration
        const { data, error } = await supabase.rpc('connect_vault', {
          vault_url: vaultUrl,
          api_key: apiKey,
          account_id: accountId || null
        });

        if (error) {
          throw new Error(error.message);
        }

        const response = data as ConnectVaultResponse;
        
        if (!response.success) {
          throw new Error(response.error || 'Failed to connect to Vault');
        }

        return response;
      } catch (error: any) {
        if (error instanceof Error) {
          throw error;
        }
        throw new Error('Failed to connect to Vault: ' + (error.message || 'Unknown error'));
      }
    },
    onSuccess: () => {
      console.log('Vault connection successful');
      onSuccess?.();
    },
    onError
  });
};