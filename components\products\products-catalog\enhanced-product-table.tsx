import React, { useState, useEffect, useMemo } from 'react'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu'
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle,
  DialogFooter,
  DialogDescription
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useToast } from '@/components/ui/use-toast'
import { getSupabaseClient, type ProductVariantRow } from '@/lib/supabase'
import { useAuth } from '@/contexts/auth-context'
import { useCurrency } from '@/lib/currency'
import { cn } from '@/lib/utils'
import { 
  Package, 
  MoreHorizontal, 
  Eye, 
  Edit, 
  Trash2,
  ChevronLeft,
  ChevronRight,
  ChevronUp,
  ChevronDown
} from 'lucide-react'
import { type ExtendedProductRow } from '@/hooks/use-products'

// Sort configuration
type SortField = 'name' | 'price' | 'effective_price' | 'stock_quantity' | 'category_name' | 'supplier' | 'created_at' | 'profit_margin'
type SortDirection = 'asc' | 'desc'

interface SortConfig {
  field: SortField
  direction: SortDirection
}

// Pagination configuration
interface PaginationConfig {
  currentPage: number
  pageSize: number
  totalItems: number
  totalPages: number
}

interface AdjustedProduct extends Omit<ExtendedProductRow, 'variants'> {
  variants?: ProductVariantRow[];
  isVariant?: boolean;
  product_id?: string;
  parent_product_name?: string;
  variant_name?: string;
  sku?: string;
}

interface EnhancedProductTableProps {
  products: ExtendedProductRow[]
  onProductEdit?: (product: ExtendedProductRow) => void
  onProductDelete?: (productIds: string[]) => void
  onProductView?: (product: ExtendedProductRow) => void
  onSelectedProductsChange?: (productIds: string[]) => void
  showBulkActions?: boolean
  defaultPageSize?: number
  isMobile?: boolean
}

export function EnhancedProductTable({
  products,
  onProductEdit,
  onProductDelete,
  onProductView,
  onSelectedProductsChange,
  showBulkActions = true,
  defaultPageSize = 10,
  isMobile = false
}: EnhancedProductTableProps) {
  const [sortConfig, setSortConfig] = useState<SortConfig>({ 
    field: 'created_at', 
    direction: 'desc' 
  })
  const [pagination, setPagination] = useState({
    currentPage: 1,
    pageSize: defaultPageSize
  })
  const [selectedProducts, setSelectedProducts] = useState<string[]>([])
  const [expandedProducts, setExpandedProducts] = useState<Record<string, boolean>>({})
  const [isAdjustStockDialogOpen, setIsAdjustStockDialogOpen] = useState(false)
  const [productToAdjust, setProductToAdjust] = useState<AdjustedProduct | null>(null)
  const [newStockQuantity, setNewStockQuantity] = useState<number>(0)
  
  const { toast } = useToast()
  const { user, organizationId } = useAuth()
  const { userCurrency, formatCurrency } = useCurrency()
  // Use getSupabaseClient instead of createSupabaseClient to ensure we get a fresh client
  const supabase = getSupabaseClient()

  // Sort products based on the current sort configuration
  const sortedProducts = useMemo(() => {
    const sorted = [...products].sort((a, b) => {
      let aValue: any = a[sortConfig.field as keyof ExtendedProductRow]
      let bValue: any = b[sortConfig.field as keyof ExtendedProductRow]

      // Handle computed fields
      if (sortConfig.field === 'category_name') {
        aValue = (a as any).category_name || ''
        bValue = (b as any).category_name || ''
      }

      // Handle date fields
      if (sortConfig.field === 'created_at') {
        aValue = new Date(aValue).getTime()
        bValue = new Date(bValue).getTime()
      }

      // Handle string fields
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase()
        bValue = bValue?.toLowerCase() || ''
      }

      // Handle null/undefined values
      if (aValue === null || aValue === undefined) aValue = ''
      if (bValue === null || bValue === undefined) bValue = ''

      if (aValue < bValue) {
        return sortConfig.direction === 'asc' ? -1 : 1
      }
      if (aValue > bValue) {
        return sortConfig.direction === 'asc' ? 1 : -1
      }
      return 0
    })
    return sorted
  }, [products, sortConfig])

  // Calculate pagination configuration
  const paginationConfig: PaginationConfig = useMemo(() => {
    const totalItems = sortedProducts.length
    const totalPages = Math.ceil(totalItems / pagination.pageSize)
    
    return {
      currentPage: pagination.currentPage,
      pageSize: pagination.pageSize,
      totalItems,
      totalPages
    }
  }, [sortedProducts.length, pagination])

  // Get the products for the current page
  const paginatedProducts = useMemo(() => {
    const startIndex = (pagination.currentPage - 1) * pagination.pageSize
    const endIndex = startIndex + pagination.pageSize
    const paginated = sortedProducts.slice(startIndex, endIndex)
    return paginated
  }, [sortedProducts, pagination])

  // Handle sorting when a column header is clicked
  const handleSort = (field: SortField) => {
    setSortConfig(current => {
      // If clicking the same field that's already sorted
      if (current.field === field) {
        // If ascending, switch to descending
        if (current.direction === 'asc') {
          return { field, direction: 'desc' };
        } 
        // If descending, reset to default sorting
        else {
          return { field: 'created_at', direction: 'desc' };
        }
      }
      // If clicking a different field, sort ascending first
      else {
        return { field, direction: 'asc' };
      }
    });
  }

  // Handle pagination when a page is selected
  const handlePageChange = (page: number) => {
    setPagination(current => ({
      ...current,
      currentPage: Math.max(1, Math.min(page, paginationConfig.totalPages))
    }))
    setSelectedProducts([]) // Clear selection when changing pages
  }

  // Handle page size change
  const handlePageSizeChange = (pageSize: number) => {
    setPagination(current => ({
      currentPage: 1,
      pageSize
    }))
    setSelectedProducts([]) // Clear selection when changing page size
  }

  // Handle select all/none for bulk actions
  const handleSelectAll = (checked: boolean) => {
    const newSelection = checked ? paginatedProducts.map(product => product.id) : []
    setSelectedProducts(newSelection)
    onSelectedProductsChange?.(newSelection)
  }

  // Handle individual product selection
  const handleSelectProduct = (productId: string, checked: boolean) => {
    const newSelection = checked
      ? [...selectedProducts, productId]
      : selectedProducts.filter(id => id !== productId)
    
    setSelectedProducts(newSelection)
    onSelectedProductsChange?.(newSelection)
  }

  // Handle bulk delete action
  const handleBulkDelete = () => {
    if (selectedProducts.length === 0) return
    
    const selectedProductsData = products.filter(product => selectedProducts.includes(product.id))
    const totalValue = selectedProductsData.reduce((sum, product) => 
      sum + ((product.effective_price || product.price || 0) * (product.stock_quantity || 0)), 0)
    
    const confirmMessage = selectedProducts.length === 1
      ? `Are you sure you want to delete "${selectedProductsData[0]?.name}"?\n\nThis action cannot be undone.`
      : `Are you sure you want to delete ${selectedProducts.length} products?\n\nTotal inventory value: ${formatCurrency(totalValue)}\n\nThis action cannot be undone.`
    
    if (window.confirm(confirmMessage)) {
      try {
        onProductDelete?.(selectedProducts)
        setSelectedProducts([])
      } catch (error) {
        console.error('Error deleting products:', error)
        toast({
          title: "Error Deleting Products",
          description: "Failed to delete selected products. Please try again.",
          variant: "destructive",
        })
      }
    }
  }

  // Handle stock adjustment
  const handleAdjustStock = async () => {
    if (!productToAdjust || !organizationId) return
    
    try {
      // Get a fresh Supabase client to ensure we have the current session
      const supabase = getSupabaseClient();
      
      const previousQuantity = productToAdjust.stock_quantity || 0;
      
      // Check if this is a variant adjustment
      const isVariantAdjustment = (productToAdjust as any).isVariant;
      
      if (isVariantAdjustment) {
        // Update variant stock
        const variantId = productToAdjust.id;
        const { error: updateError } = await supabase
          .from('product_variants')
          .update({ 
            stock_quantity: newStockQuantity,
            updated_at: new Date().toISOString()
          })
          .eq('id', variantId)
          .eq('organization_id', organizationId)
        
        if (updateError) {
          throw new Error(`Failed to update stock: ${updateError.message}`)
        }
        
        // Create stock movement record
        const { error: movementError } = await supabase
          .from('stock_movements')
          .insert([{
            organization_id: organizationId,
            product_id: (productToAdjust as any).product_id,
            variant_id: variantId,
            movement_type: newStockQuantity > previousQuantity ? 'stock_in' : 'stock_out',
            quantity_change: Math.abs(newStockQuantity - previousQuantity),
            previous_quantity: previousQuantity,
            new_quantity: newStockQuantity,
            reason: 'Manual stock adjustment from product table',
            reference: 'Manual adjustment from product table kebab menu',
            created_at: new Date().toISOString()
          }])
        
        if (movementError) {
          console.error('Error creating stock movement:', movementError)
        }
      } else {
        // For variable products, we'll need to handle this differently
        if (productToAdjust.has_variants && productToAdjust.variants && productToAdjust.variants.length > 0) {
          // For variable products, we should show a message that stock adjustment should be done in the inventory section
          toast({
            title: "Variable Product Stock Adjustment",
            description: "For variable products, please adjust stock quantities in the Inventory section where you can manage each variant individually.",
            variant: "destructive",
          })
          return
        }
        
        // Update simple product stock
        const { error: updateError } = await supabase
          .from('products')
          .update({ 
            stock_quantity: newStockQuantity,
            updated_at: new Date().toISOString()
          })
          .eq('id', productToAdjust.id)
          .eq('organization_id', organizationId)
        
        if (updateError) {
          throw new Error(`Failed to update stock: ${updateError.message}`)
        }
        
        // Create stock movement record
        const { error: movementError } = await supabase
          .from('stock_movements')
          .insert([{
            organization_id: organizationId,
            product_id: productToAdjust.id,
            movement_type: newStockQuantity > previousQuantity ? 'stock_in' : 'stock_out',
            quantity_change: Math.abs(newStockQuantity - previousQuantity),
            previous_quantity: previousQuantity,
            new_quantity: newStockQuantity,
            reason: 'Manual stock adjustment from product table',
            reference: 'Manual adjustment from product table kebab menu',
            created_at: new Date().toISOString()
          }])
        
        if (movementError) {
          console.error('Error creating stock movement:', movementError)
        }
      }
      
      // Close dialog and refresh
      setIsAdjustStockDialogOpen(false)
      setProductToAdjust(null)
      setNewStockQuantity(0)
      
      // Dispatch a custom event to trigger table refresh
      window.dispatchEvent(new CustomEvent('productUpdated'))
      
      toast({
        title: "Stock Updated",
        description: `Product stock quantity updated to ${newStockQuantity} units.`,
      })
    } catch (error) {
      console.error('Error updating stock:', error)
      toast({
        title: "Error Updating Stock",
        description: error instanceof Error ? error.message : "Failed to update stock quantity",
        variant: "destructive",
      })
    }
  }

  // Open adjust stock dialog
  const openAdjustStockDialog = (product: AdjustedProduct) => {
    setProductToAdjust(product)
    setNewStockQuantity(product.stock_quantity || 0)
    setIsAdjustStockDialogOpen(true)
  }

  // Toggle product expansion to show variants
  const toggleProductExpansion = (productId: string) => {
    setExpandedProducts(prev => ({
      ...prev,
      [productId]: !prev[productId]
    }))
  }

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  // Get sort icon for column headers
  const getSortIcon = (field: SortField) => {
    if (sortConfig.field !== field) {
      return (
        <div className="flex flex-col items-center justify-center ml-1 opacity-50">
          <ChevronUp className="h-3 w-3" />
          <ChevronDown className="h-3 w-3 -mt-1" />
        </div>
      )
    }
    
    if (sortConfig.direction === 'asc') {
      return <ChevronUp className="h-4 w-4 text-blue-600 ml-1" />
    } else {
      return <ChevronDown className="h-4 w-4 text-blue-600 ml-1" />
    }
  }

  // Calculate selection states for the select all checkbox
  const allSelected = paginatedProducts.length > 0 && selectedProducts.length === paginatedProducts.length
  const someSelected = selectedProducts.length > 0 && !allSelected

  return (
    <>
      {/* Bulk Actions - Only show when items are selected */}
      {showBulkActions && selectedProducts.length > 0 && (
        <div className="flex items-center space-x-3 mb-2">
          <span className="text-sm text-muted-foreground">
            {selectedProducts.length} selected
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={handleBulkDelete}
            className="text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      )}
      
      {/* Adjust Stock Dialog */}
      <Dialog open={isAdjustStockDialogOpen} onOpenChange={setIsAdjustStockDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Adjust Stock Quantity</DialogTitle>
            <DialogDescription className="sr-only">
              Adjust the stock quantity for a product or variant
            </DialogDescription>
          </DialogHeader>
          {productToAdjust && (
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="text-sm font-medium">
                  {(productToAdjust as any).isVariant 
                    ? `${productToAdjust.name} - ${(productToAdjust as any).variant_name || 'Unnamed Variant'}` 
                    : productToAdjust.name}
                </div>
                <div className="text-xs text-muted-foreground">
                  Current stock: {productToAdjust.stock_quantity || 0} units
                </div>
                {(productToAdjust as any).isVariant && (productToAdjust as any).sku && (
                  <div className="text-xs text-muted-foreground">
                    SKU: {(productToAdjust as any).sku}
                  </div>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="new-stock-quantity" className="text-sm font-medium">
                  New Stock Quantity
                </Label>
                <Input
                  id="new-stock-quantity"
                  type="number"
                  min="0"
                  value={newStockQuantity}
                  onChange={(e) => setNewStockQuantity(Number(e.target.value))}
                  className="w-full"
                />
              </div>
              
              <DialogFooter>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsAdjustStockDialogOpen(false)}
                  className="h-8 text-xs"
                >
                  Cancel
                </Button>
                <Button
                  size="sm"
                  onClick={handleAdjustStock}
                  className="h-8 text-xs"
                >
                  Update Stock
                </Button>
              </DialogFooter>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {products.length === 0 ? (
        <div className="text-center py-12 border border-gray-200 rounded-lg">
          <Package className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
          <p className="text-muted-foreground text-lg">No products to display.</p>
          <p className="text-muted-foreground text-sm mt-2">Add your first product to get started.</p>
        </div>
      ) : (
        <>
          {/* Table Container */}
          <div className="border border-gray-200 rounded-t-lg overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 bg-white">
                <thead className="bg-gray-50">
                  <tr>
                    {showBulkActions && (
                      <th 
                        scope="col" 
                        className="text-left py-3 px-6 font-medium text-gray-900 text-xs"
                      >
                        <input
                          type="checkbox"
                          className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          checked={allSelected}
                          ref={(input) => {
                            if (input) {
                              input.indeterminate = someSelected;
                            }
                          }}
                          onChange={(e) => handleSelectAll(e.target.checked)}
                        />
                      </th>
                    )}
                    <th 
                      scope="col" 
                      className="text-left py-3 px-6 font-medium text-gray-900 text-xs"
                    >
                      <div className="flex items-center gap-1">
                        <span 
                          className="cursor-pointer hover:bg-gray-100 transition-colors flex items-center"
                          onClick={() => handleSort('name')}
                        >
                          Product
                          {getSortIcon('name')}
                        </span>
                      </div>
                    </th>
                    <th 
                      scope="col" 
                      className="text-left py-3 px-6 font-medium text-gray-900 text-xs"
                    >
                      <div className="flex items-center gap-1">
                        <span 
                          className="cursor-pointer hover:bg-gray-100 transition-colors flex items-center"
                          onClick={() => handleSort('category_name')}
                        >
                          Category
                          {getSortIcon('category_name')}
                        </span>
                      </div>
                    </th>
                    {!isMobile && (
                      <th 
                        scope="col" 
                        className="text-left py-3 px-6 font-medium text-gray-900 text-xs"
                      >
                        <div className="flex items-center gap-1">
                          <span 
                            className="cursor-pointer hover:bg-gray-100 transition-colors flex items-center"
                            onClick={() => handleSort('supplier')}
                          >
                            Supplier
                            {getSortIcon('supplier')}
                          </span>
                        </div>
                      </th>
                    )}
                    <th 
                      scope="col" 
                      className="text-left py-3 px-6 font-medium text-gray-900 text-xs"
                    >
                      <div className="flex items-center gap-1">
                        <span 
                          className="cursor-pointer hover:bg-gray-100 transition-colors flex items-center"
                          onClick={() => handleSort('stock_quantity')}
                        >
                          Stock
                          {getSortIcon('stock_quantity')}
                        </span>
                      </div>
                    </th>
                    <th 
                      scope="col" 
                      className="text-left py-3 px-6 font-medium text-gray-900 text-xs"
                    >
                      <div className="flex items-center gap-1">
                        <span 
                          className="cursor-pointer hover:bg-gray-100 transition-colors flex items-center"
                          onClick={() => handleSort('effective_price')}
                        >
                          Price
                          {getSortIcon('effective_price')}
                        </span>
                      </div>
                    </th>
                    {!isMobile && (
                      <th 
                        scope="col" 
                        className="text-left py-3 px-6 font-medium text-gray-900 text-xs"
                      >
                        <div className="flex items-center gap-1">
                          <span>Status</span>
                        </div>
                      </th>
                    )}
                    <th scope="col" className="text-center py-3 px-6 font-medium text-gray-900 w-32 text-xs">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {paginatedProducts.length === 0 ? (
                    <tr>
                      <td colSpan={showBulkActions ? 8 : 7} className="py-8 text-center text-muted-foreground">
                        No products match the current filters.
                      </td>
                    </tr>
                  ) : (
                    paginatedProducts.map((product) => (
                      <React.Fragment key={product.id}>
                        <tr 
                          className="hover:bg-gray-50"
                        >
                          {showBulkActions && (
                            <td className="py-4 px-6">
                              <input
                                type="checkbox"
                                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                checked={selectedProducts.includes(product.id)}
                                onChange={(e) => handleSelectProduct(product.id, e.target.checked)}
                              />
                            </td>
                          )}
                          <td className="py-4 px-6">
                            <div className="max-w-[220px]">
                              <div className="font-medium text-gray-900 truncate text-xs flex items-center gap-1" title={product.name}>
                                {product.has_variants && product.variants && product.variants.length > 0 && (
                                  <Button
                                    variant="ghost" 
                                    size="sm" 
                                    className="h-4 w-4 p-0 text-gray-500 hover:text-gray-700 hover:bg-gray-100"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      toggleProductExpansion(product.id);
                                    }}
                                  >
                                    {expandedProducts[product.id] ? (
                                      <ChevronDown className="h-3.5 w-3.5" />
                                    ) : (
                                      <ChevronRight className="h-3.5 w-3.5" />
                                    )}
                                    <span className="sr-only">
                                      {expandedProducts[product.id] ? 'Collapse variants' : 'Expand variants'}
                                    </span>
                                  </Button>
                                )}
                                {product.name}
                              </div>
                              <div className="text-2xs text-gray-500 space-y-1 mt-1">
                                {product.has_variants && product.variants && product.variants.length > 0 ? (
                                  <div className="text-2xs">
                                    {(() => {
                                      // Determine which attributes vary
                                      const hasSize = product.variants.some(v => v.size);
                                      const hasColor = product.variants.some(v => v.color);
                                      const hasMaterial = product.variants.some(v => (v as any).material);
                                      const hasStyle = product.variants.some(v => v.style);
                                      
                                      const attributes = [];
                                      if (hasSize) attributes.push('size');
                                      if (hasColor) attributes.push('color');
                                      if (hasMaterial) attributes.push('material');
                                      if (hasStyle) attributes.push('style');
                                      
                                      if (attributes.length === 0) return 'Varies';
                                      return `Varies by ${attributes.join(', ')}`;
                                    })()}
                                  </div>
                                ) : (
                                  <div className="text-2xs">
                                    {(() => {
                                      const attributes = [];
                                      if (product.size) attributes.push(product.size);
                                      if (product.color) attributes.push(product.color);
                                      if ((product as any).material) attributes.push((product as any).material);
                                      if ((product as any).style) attributes.push((product as any).style);
                                      
                                      if (attributes.length === 0) return product.base_sku || '';
                                      return attributes.join(' • ');
                                    })()}
                                  </div>
                                )}
                              </div>
                            </div>
                          </td>
                          <td className="py-4 px-6 text-xs text-gray-700">
                            {(product as any).category_name || 'Uncategorized'}
                          </td>
                          {!isMobile && (
                            <td className="py-4 px-6 text-xs text-gray-700">
                              {product.supplier || '-'}
                            </td>
                          )}
                          <td className="py-4 px-6 text-left">
                            <div className="flex flex-col items-start space-y-1">
                              {product.has_variants && product.variants && product.variants.length > 0 ? (
                                <React.Fragment key={`variant-info-${product.id}`}>
                                  <span className="text-xs font-medium text-gray-700">
                                    {product.variants.reduce((sum, variant) => sum + (variant.stock_quantity || 0), 0)}
                                  </span>
                                  <span className="text-2xs text-gray-500">
                                    {product.variants.length} variants
                                  </span>
                                </React.Fragment>
                              ) : (
                                <span className="text-xs text-gray-900" key={`stock-${product.id}`}>
                                  {product.stock_quantity}
                                </span>
                              )}
                            </div>
                          </td>
                          <td className="py-4 px-6 text-xs text-gray-700">
                            <div className="flex flex-col">
                              <div className="font-semibold text-gray-900">
                                {product.has_variants && product.variants && product.variants.length > 0 ? (
                                  (() => {
                                    // Get all prices from variants
                                    const variantPrices = product.variants.map(v => v.effective_price || v.price || 0);
                                    
                                    // Check if all prices are the same
                                    const allSamePrice = variantPrices.every(price => price === variantPrices[0]);
                                    
                                    if (allSamePrice) {
                                      // If all prices are the same, show that single price
                                      return formatCurrency(variantPrices[0], userCurrency);
                                    } else {
                                      // Otherwise, show minimum price with '+' symbol
                                      const minPrice = Math.min(...variantPrices);
                                      return `${formatCurrency(minPrice, userCurrency)}+`;
                                    }
                                  })()
                                ) : (
                                  formatCurrency(product.effective_price || product.price || 0, userCurrency)
                                )}
                              </div>
                              {product.is_on_sale && !product.has_variants && (
                                <div className="text-xs text-gray-500 line-through">
                                  {formatCurrency(product.price || 0, userCurrency)}
                                </div>
                              )}
                            </div>
                          </td>
                          {!isMobile && (
                            <td className="py-4 px-6 text-xs text-gray-700">
                              <div className="flex flex-col items-start space-y-1">
                                {(() => {
                                  // Check product status
                                  let status = 'in_stock';
                                  const stockQuantity = product.stock_quantity || 0;
                                  
                                  // Check variants status for products with variants
                                  if (product.has_variants && product.variants && product.variants.length > 0) {
                                    // Check if any variant is low or out of stock
                                    const hasLowStockVariant = product.variants.some(
                                      v => (v.stock_quantity || 0) > 0 && (v.stock_quantity || 0) <= (v.low_stock_threshold || 10)
                                    );
                                    const hasOutOfStockVariant = product.variants.some(
                                      v => (v.stock_quantity || 0) === 0
                                    );
                                    
                                    if (hasOutOfStockVariant) {
                                      status = 'out_of_stock';
                                    } else if (hasLowStockVariant) {
                                      status = 'low_stock';
                                    }
                                  } else {
                                    // Simple product status
                                    if (stockQuantity === 0) {
                                      status = 'out_of_stock';
                                    } else if (stockQuantity <= (product.low_stock_threshold || 10)) {
                                      status = 'low_stock';
                                    }
                                  }
                                  
                                  return (
                                    <span className={cn(
                                      "inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium",
                                      status === 'in_stock' && "bg-green-50 text-green-700 border border-green-100",
                                      status === 'low_stock' && "bg-yellow-50 text-yellow-700 border border-yellow-100",
                                      status === 'out_of_stock' && "bg-red-50 text-red-700 border border-red-100"
                                    )}>
                                      {status === 'in_stock' && 'In Stock'}
                                      {status === 'low_stock' && 'Low Stock'}
                                      {status === 'out_of_stock' && 'Out of Stock'}
                                    </span>
                                  );
                                })()}
                              </div>
                            </td>
                          )}
                          <td className="py-4 px-6 text-center">
                            <div className="flex items-center justify-center relative w-full">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" className="h-8 w-8 p-0">
                                    <span className="sr-only">Open menu</span>
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end" className="w-48">
                                  <DropdownMenuItem
                                    onClick={() => onProductView?.(product)}
                                    className="cursor-pointer text-xs py-2"
                                  >
                                    <span>View</span>
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={() => onProductEdit?.(product)}
                                    className="cursor-pointer text-xs py-2"
                                  >
                                    <span>Edit</span>
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={() => openAdjustStockDialog({
                                      ...product,
                                      variants: product.variants as ProductVariantRow[] | undefined
                                    })}
                                    className="cursor-pointer text-xs py-2"
                                  >
                                    <span>Adjust Stock</span>
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem
                                    onClick={() => {
                                      if (window.confirm(`Are you sure you want to delete "${product.name}"? This action cannot be undone.`)) {
                                        onProductDelete?.([product.id])
                                      }
                                    }}
                                    className="cursor-pointer text-red-600 focus:text-red-600 focus:bg-red-50 text-xs py-2"
                                  >
                                    <span>Delete</span>
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </td>
                        </tr>
                        
                        {/* Expanded rows for variants */}
                        {expandedProducts[product.id] && product.has_variants && product.variants && (
                          product.variants.map((variant) => (
                            <tr key={variant.id} className="border-t hover:bg-gray-50/50 transition-colors bg-gray-50">
                              {showBulkActions && (
                                <td className="py-3 px-6"></td>
                              )}
                              <td className="py-3 px-6 pl-14">
                                <div className="font-medium text-xs">
                                  {variant.variant_name || 'Unnamed Variant'}
                                </div>
                                <div className="text-2xs text-gray-500 mt-1">
                                  {variant.sku || 'No SKU'}
                                </div>
                              </td>
                              <td className="py-3 px-6 text-xs text-gray-700">
                                {(product as any).category_name || 'Uncategorized'}
                              </td>
                              {!isMobile && (
                                <td className="py-3 px-6 text-xs text-gray-700">
                                  {product.supplier || '-'}
                                </td>
                              )}
                              <td className="py-3 px-6 text-xs text-gray-700">
                                <div className="text-xs font-medium">
                                  {variant.stock_quantity || 0}
                                </div>
                              </td>
                              <td className="py-3 px-6 text-xs text-gray-700">
                                <div className="flex flex-col">
                                  <div className="font-semibold text-gray-900">
                                    {formatCurrency(variant.effective_price || variant.price || 0, userCurrency)}
                                  </div>
                                  {variant.is_on_sale && (
                                    <div className="text-xs text-gray-500 line-through">
                                      {formatCurrency(variant.price || 0, userCurrency)}
                                    </div>
                                  )}
                                </div>
                              </td>
                              {!isMobile && (
                                <td className="py-3 px-6 text-xs text-gray-700">
                                  <div className="flex flex-col items-start space-y-1">
                                    {(() => {
                                      // Determine variant status
                                      let status = 'in_stock';
                                      const stockQuantity = variant.stock_quantity || 0;
                                      
                                      if (stockQuantity === 0) {
                                        status = 'out_of_stock';
                                      } else if (stockQuantity <= (variant.low_stock_threshold || 10)) {
                                        status = 'low_stock';
                                      }
                                      
                                      return (
                                        <span className={cn(
                                          "inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium",
                                          status === 'in_stock' && "bg-green-50 text-green-700 border border-green-100",
                                          status === 'low_stock' && "bg-yellow-50 text-yellow-700 border border-yellow-100",
                                          status === 'out_of_stock' && "bg-red-50 text-red-700 border border-red-100"
                                        )}>
                                          {status === 'in_stock' && 'In Stock'}
                                          {status === 'low_stock' && 'Low Stock'}
                                          {status === 'out_of_stock' && 'Out of Stock'}
                                        </span>
                                      );
                                    })()}
                                  </div>
                                </td>
                              )}
                              <td className="py-3 px-6 text-center">
                                <div className="flex items-center justify-center relative w-full">
                                  <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                      <Button variant="ghost" className="h-7 w-7 p-0">
                                        <span className="sr-only">Open menu</span>
                                        <MoreHorizontal className="h-3.5 w-3.5" />
                                      </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end" className="w-48">
                                      <DropdownMenuItem
                                        onClick={() => openAdjustStockDialog({
                                          id: variant.id,
                                          organization_id: variant.organization_id,
                                          name: `${product.name} - ${variant.variant_name || 'Unnamed Variant'}`,
                                          stock_quantity: variant.stock_quantity,
                                          isVariant: true,
                                          product_id: product.id,
                                          parent_product_name: product.name,
                                          variant_name: variant.variant_name || undefined,
                                          sku: variant.sku
                                        } as unknown as AdjustedProduct)}
                                        className="cursor-pointer text-xs py-2"
                                      >
                                        <span>Adjust Stock</span>
                                      </DropdownMenuItem>
                                    </DropdownMenuContent>
                                  </DropdownMenu>
                                </div>
                              </td>
                            </tr>
                          ))
                        )}
                      </React.Fragment>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
          
          {/* Pagination */}
          <div className="flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 p-4 border border-gray-200 border-t-0 rounded-b-lg bg-gray-50">
            {/* Left: Items per page selector */}
            <div className="flex items-center gap-2 text-xs text-gray-600">
              <span>Show</span>
              <select
                value={pagination.pageSize}
                onChange={(e) => handlePageSizeChange(Number(e.target.value))}
                className="rounded border border-gray-200 px-2 py-1 text-xs h-7"
              >
                <option value={5}>5</option>
                <option value={10}>10</option>
                <option value={20}>20</option>
                <option value={50}>50</option>
              </select>
              <span>per page</span>
            </div>
            
            {/* Right: Pagination Controls and Stats */}
            <div className="flex items-center w-full md:w-auto justify-end">
              {/* Item Range and Navigation */}
              <div className="flex items-center gap-2">
                <p className="text-xs text-gray-600">
                  <span className="hidden sm:inline">Showing </span>
                  <span className="font-medium">{Math.min((pagination.currentPage - 1) * pagination.pageSize + 1, paginationConfig.totalItems)}-{Math.min(pagination.currentPage * pagination.pageSize, paginationConfig.totalItems)}</span>
                  <span className="hidden sm:inline"> of </span>
                  <span className="sm:hidden">/</span>
                  <span className="font-medium">{paginationConfig.totalItems}</span>
                </p>
                
                <div className="flex items-center rounded-md border border-gray-200 overflow-hidden">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(pagination.currentPage - 1)}
                    disabled={pagination.currentPage <= 1}
                    className="h-7 w-7 p-0 rounded-none border-r border-gray-200"
                  >
                    <ChevronLeft className="h-3.5 w-3.5 text-gray-600" />
                  </Button>
                  
                  <div className="hidden sm:flex">
                    {Array.from({ length: Math.min(3, paginationConfig.totalPages) }, (_, i) => {
                      const page = Math.max(1, pagination.currentPage - 1) + i
                      if (page > paginationConfig.totalPages) return null
                      
                      return (
                        <Button
                          key={page}
                          variant={page === pagination.currentPage ? "default" : "ghost"}
                          size="sm"
                          onClick={() => handlePageChange(page)}
                          className={`h-7 w-7 p-0 rounded-none border-r border-gray-200 text-xs ${
                            page === pagination.currentPage 
                              ? "bg-blue-600 hover:bg-blue-700 text-white" 
                              : "text-gray-700"
                          }`}
                        >
                          {page}
                        </Button>
                      )
                    })}
                  </div>
                  
                  <div className="sm:hidden flex items-center justify-center h-7 w-7 text-xs font-medium border-r border-gray-200">
                    {pagination.currentPage}
                  </div>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(pagination.currentPage + 1)}
                    disabled={pagination.currentPage >= paginationConfig.totalPages}
                    className="h-7 w-7 p-0 rounded-none"
                  >
                    <ChevronRight className="h-3.5 w-3.5 text-gray-600" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </>
  )
}