import { createSupabaseClient } from '@/lib/supabase';

export class NotificationService {
  private supabase = createSupabaseClient();

  // Create a new notification
  async createNotification(data: {
    organizationId: string;
    type: string;
    title: string;
    message: string;
    relatedEntityType?: string;
    relatedEntityId?: string;
  }) {
    try {
      const { error } = await this.supabase
        .from('notifications')
        .insert({
          organization_id: data.organizationId,
          type: data.type,
          title: data.title,
          message: data.message,
          related_entity_type: data.relatedEntityType,
          related_entity_id: data.relatedEntityId,
        });

      if (error) {
        console.error('Error creating notification:', error);
        return { error };
      }
      
      return { error: null };
    } catch (error) {
      console.error('Error creating notification:', error);
      return { error };
    }
  }

  // Check if organization has enabled notifications
  async getOrganizationNotificationPreferences(organizationId: string) {
    try {
      const { data, error } = await this.supabase
        .from('organization_preferences')
        .select('*')
        .eq('organization_id', organizationId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 means no rows returned
        console.error('Error fetching organization preferences:', error);
        // Return default preferences if error
        return {
          email_notifications: true,
          push_notifications: true
        };
      }

      // If no preferences found, return defaults
      if (!data) {
        return {
          email_notifications: true,
          push_notifications: true
        };
      }

      return {
        email_notifications: data.email_notifications ?? true,
        push_notifications: data.push_notifications ?? true
      };
    } catch (error) {
      console.error('Error fetching organization preferences:', error);
      // Return default preferences if error
      return {
        email_notifications: true,
        push_notifications: true
      };
    }
  }

  // Get unread notification count for an organization
  async getUnreadNotificationCount(organizationId: string): Promise<number> {
    try {
      const { count, error } = await this.supabase
        .from('notifications')
        .select('*', { count: 'exact', head: true })
        .eq('organization_id', organizationId)
        .eq('is_read', false);

      if (error) {
        console.error('Error fetching notification count:', error);
        return 0;
      }

      return count || 0;
    } catch (error) {
      console.error('Error fetching notification count:', error);
      return 0;
    }
  }

  // Mark notification as read
  async markAsRead(notificationId: string) {
    try {
      const { error } = await this.supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('id', notificationId);

      if (error) {
        console.error('Error marking notification as read:', error);
        return { error };
      }
      
      return { error: null };
    } catch (error) {
      console.error('Error marking notification as read:', error);
      return { error };
    }
  }

  // Mark notification as unread
  async markAsUnread(notificationId: string) {
    try {
      const { error } = await this.supabase
        .from('notifications')
        .update({ is_read: false })
        .eq('id', notificationId);

      if (error) {
        console.error('Error marking notification as unread:', error);
        return { error };
      }
      
      return { error: null };
    } catch (error) {
      console.error('Error marking notification as unread:', error);
      return { error };
    }
  }

  // Mark all notifications as read
  async markAllAsRead(organizationId: string) {
    try {
      const { error } = await this.supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('organization_id', organizationId)
        .eq('is_read', false);

      if (error) {
        console.error('Error marking all notifications as read:', error);
        return { error };
      }
      
      return { error: null };
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      return { error };
    }
  }

  // Get notifications for an organization with pagination
  async getNotifications(organizationId: string, limit: number = 10, offset: number = 0) {
    try {
      const { data, error } = await this.supabase
        .from('notifications')
        .select('*')
        .eq('organization_id', organizationId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        console.error('Error fetching notifications:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching notifications:', error);
      return [];
    }
  }

  // Get total count of notifications for an organization
  async getNotificationCount(organizationId: string) {
    try {
      const { count, error } = await this.supabase
        .from('notifications')
        .select('*', { count: 'exact', head: true })
        .eq('organization_id', organizationId);

      if (error) {
        console.error('Error fetching notification count:', error);
        return 0;
      }

      return count || 0;
    } catch (error) {
      console.error('Error fetching notification count:', error);
      return 0;
    }
  }

  // Archive a notification
  async archiveNotification(notificationId: string) {
    try {
      const { error } = await this.supabase
        .from('notifications')
        .update({ is_archived: true })
        .eq('id', notificationId);

      if (error) {
        console.error('Error archiving notification:', error);
        return { error };
      }
      
      return { error: null };
    } catch (error) {
      console.error('Error archiving notification:', error);
      return { error };
    }
  }

  // Unarchive a notification
  async unarchiveNotification(notificationId: string) {
    try {
      const { error } = await this.supabase
        .from('notifications')
        .update({ is_archived: false })
        .eq('id', notificationId);

      if (error) {
        console.error('Error unarchiving notification:', error);
        return { error };
      }
      
      return { error: null };
    } catch (error) {
      console.error('Error unarchiving notification:', error);
      return { error };
    }
  }

  // Delete a notification permanently
  async deleteNotification(notificationId: string) {
    try {
      const { error } = await this.supabase
        .from('notifications')
        .delete()
        .eq('id', notificationId);

      if (error) {
        console.error('Error deleting notification:', error);
        return { error };
      }
      
      return { error: null };
    } catch (error) {
      console.error('Error deleting notification:', error);
      return { error };
    }
  }
}