'use client'

import React, { useState, useEffect, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useToast } from '@/components/ui/use-toast'
import { createSupabaseClient } from '@/lib/supabase'
import { useAuth } from '@/contexts/auth-context'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Upload, X } from 'lucide-react'

interface OrganizationSettingsProps {
  initialCompanyName?: string
  initialCompanyAddress?: string
  initialTaxId?: string
  initialLogoUrl?: string | null
  onCompanyInfoUpdate?: (info: {
    companyName: string
    companyAddress: string
    taxId: string
  }) => void
  onLogoUpdate?: (logoUrl: string | null) => void
}

export function OrganizationSettings({
  initialCompanyName = '',
  initialCompanyAddress = '',
  initialTaxId = '',
  initialLogoUrl = null,
  onCompanyInfoUpdate,
  onLogoUpdate
}: OrganizationSettingsProps) {
  const { user } = useAuth()
  const { toast } = useToast()
  const supabase = createSupabaseClient()
  const fileInputRef = useRef<HTMLInputElement>(null)
  
  // Form states
  const [companyName, setCompanyName] = useState(initialCompanyName)
  const [companyAddress, setCompanyAddress] = useState(initialCompanyAddress)
  const [taxId, setTaxId] = useState(initialTaxId)
  const [logoUrl, setLogoUrl] = useState<string | null>(initialLogoUrl)
  const [isLoading, setIsLoading] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)
  
  // Track initial values for comparison
  const [initialValues] = useState({
    companyName: initialCompanyName,
    companyAddress: initialCompanyAddress,
    taxId: initialTaxId,
    logoUrl: initialLogoUrl
  })
  
  // Check for changes whenever form fields change
  useEffect(() => {
    const hasFormChanges = 
      companyName !== initialValues.companyName ||
      companyAddress !== initialValues.companyAddress ||
      taxId !== initialValues.taxId;
    
    setHasChanges(hasFormChanges);
  }, [companyName, companyAddress, taxId, initialValues]);
  
  // Load company information from the database
  useEffect(() => {
    const loadCompanyInfo = async () => {
      if (!user) return;
      
      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('business_name, contact_info')
          .eq('id', user.id)
          .single()
          
        if (error) {
          console.error('Error loading company info:', error)
          return
        }
        
        if (data) {
          setCompanyName(data.business_name || '')
          
          if (data.contact_info) {
            setCompanyAddress(data.contact_info.address || '')
            setTaxId(data.contact_info.tax_id || '')
            setLogoUrl(data.contact_info.logo_url || null)
          }
          
          // Update initial values
          initialValues.companyName = data.business_name || ''
          initialValues.companyAddress = data.contact_info?.address || ''
          initialValues.taxId = data.contact_info?.tax_id || ''
          initialValues.logoUrl = data.contact_info?.logo_url || null
        }
      } catch (error) {
        console.error('Error loading company info:', error)
      }
    }
    
    loadCompanyInfo()
  }, [user])
  
  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file || !user) return

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast({
        title: 'Invalid file type',
        description: 'Please select an image file only',
        variant: 'destructive'
      })
      return
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: 'File too large',
        description: 'Please select an image smaller than 5MB',
        variant: 'destructive'
      })
      return
    }

    setIsUploading(true)
    
    try {
      // Upload file to Supabase Storage
      const fileExt = file.name.split('.').pop() || 'jpg'
      const fileName = `${user.id}/company-logo.${fileExt}`
      
      const { error: uploadError } = await supabase.storage
        .from('company-logos')
        .upload(fileName, file, {
          upsert: true
        })

      if (uploadError) throw uploadError

      // Get public URL for the uploaded file
      const { data: { publicUrl } } = supabase.storage
        .from('company-logos')
        .getPublicUrl(fileName)

      // Update local state
      setLogoUrl(publicUrl)
      
      // Update parent component if callback provided
      if (onLogoUpdate) {
        onLogoUpdate(publicUrl)
      }
      
      toast({
        title: 'Logo uploaded',
        description: 'Your company logo has been successfully uploaded'
      })
    } catch (error) {
      console.error('Error uploading company logo:', error)
      toast({
        title: 'Upload failed',
        description: 'Failed to upload company logo. Please try again.',
        variant: 'destructive'
      })
    } finally {
      setIsUploading(false)
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const handleRemoveLogo = async () => {
    if (!user || !logoUrl) return;

    try {
      // Extract file name from URL
      const fileName = logoUrl.split('/').pop()
      
      if (fileName) {
        // Remove file from storage
        const { error: deleteError } = await supabase.storage
          .from('company-logos')
          .remove([`${user.id}/company-logo.${fileName.split('.').pop()}`])

        if (deleteError) {
          console.error('Error removing logo:', deleteError)
        }
      }
      
      // Update local state
      setLogoUrl(null)
      
      // Update parent component if callback provided
      if (onLogoUpdate) {
        onLogoUpdate(null)
      }
      
      toast({
        title: 'Logo removed',
        description: 'Your company logo has been successfully removed'
      })
    } catch (error) {
      console.error('Error removing company logo:', error)
      toast({
        title: 'Removal failed',
        description: 'Failed to remove company logo. Please try again.',
        variant: 'destructive'
      })
    }
  }

  const triggerFileSelect = () => {
    fileInputRef.current?.click()
  }

  const handleCompanyInfoUpdate = async () => {
    if (!user) return;
    
    setIsLoading(true)
    try {
      // Prepare contact info object
      const contactInfo = {
        address: companyAddress.trim(),
        tax_id: taxId.trim(),
        logo_url: logoUrl
      }
      
      // Update profile with business name and contact info
      const { error: profileError } = await supabase
        .from('profiles')
        .update({ 
          business_name: companyName.trim(),
          contact_info: contactInfo
        })
        .eq('id', user.id)

      if (profileError) {
        throw new Error(`Failed to update profile: ${profileError.message}`)
      }
      
      // Also update the organization name in the organizations table
      // First, get the user's organization ID
      const { data: orgMember, error: orgMemberError } = await supabase
        .from('organization_members')
        .select('organization_id')
        .eq('user_id', user.id)
        .single()
      
      if (orgMemberError) {
        console.warn('Could not find organization membership:', orgMemberError)
      } else if (orgMember) {
        // Update the organization name
        const { error: orgUpdateError } = await supabase
          .from('organizations')
          .update({ 
            name: companyName.trim(),
            updated_at: new Date().toISOString()
          })
          .eq('id', orgMember.organization_id)
        
        if (orgUpdateError) {
          console.warn('Could not update organization name:', orgUpdateError)
        }
      }
      
      // Update parent state if callback provided
      if (onCompanyInfoUpdate) {
        onCompanyInfoUpdate({
          companyName: companyName.trim(),
          companyAddress: companyAddress.trim(),
          taxId: taxId.trim()
        })
      }
      
      // Update initial values to reflect the new state
      initialValues.companyName = companyName.trim()
      initialValues.companyAddress = companyAddress.trim()
      initialValues.taxId = taxId.trim()
      
      // Reset changes flag
      setHasChanges(false)
      
      toast({
        title: "Company Information Updated",
        description: "Your company information has been updated successfully.",
      })
    } catch (error) {
      console.error('Error updating company information:', error)
      toast({
        title: "Update Failed",
        description: error instanceof Error ? error.message : "Failed to update company information. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Company Information</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Company Logo Section */}
          <div className="space-y-4">
            <div>
              <Label className="text-sm font-medium text-gray-700">Company Logo</Label>
              <p className="text-xs text-muted-foreground mt-1">
                Upload your company logo (JPG, PNG, up to 5MB)
              </p>
            </div>
            
            <div className="flex items-center gap-4">
              <Avatar className="w-16 h-16">
                {logoUrl ? (
                  <AvatarImage src={logoUrl} alt="Company logo" />
                ) : (
                  <AvatarFallback className="bg-gray-100">
                    <div className="bg-gray-200 border-2 border-dashed rounded-xl w-16 h-16" />
                  </AvatarFallback>
                )}
              </Avatar>
              
              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  onClick={triggerFileSelect}
                  disabled={isUploading}
                  className="h-8 px-2 text-xs"
                >
                  <Upload className="w-4 h-4 mr-1" />
                  {isUploading ? 'Uploading...' : 'Upload'}
                </Button>
                
                {logoUrl && (
                  <Button 
                    variant="outline" 
                    onClick={handleRemoveLogo}
                    disabled={isUploading}
                    className="h-8 px-2 text-xs"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                )}
              </div>
              
              <input
                type="file"
                ref={fileInputRef}
                className="hidden"
                accept="image/*"
                onChange={handleFileChange}
              />
            </div>
          </div>
          
          {/* Company Details Section */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="company-name" className="text-sm font-medium text-gray-700">Company Name</Label>
              <Input
                id="company-name"
                value={companyName}
                onChange={(e) => setCompanyName(e.target.value)}
                placeholder="Your company name"
                className="text-sm h-9"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="company-address" className="text-sm font-medium text-gray-700">Company Address</Label>
              <Textarea
                id="company-address"
                value={companyAddress}
                onChange={(e) => setCompanyAddress(e.target.value)}
                placeholder="Enter your company address"
                className="text-sm"
                rows={3}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="tax-id" className="text-sm font-medium text-gray-700">Tax ID</Label>
              <Input
                id="tax-id"
                value={taxId}
                onChange={(e) => setTaxId(e.target.value)}
                placeholder="Enter your tax identification number"
                className="text-sm h-9"
              />
            </div>
            
            <div className="pt-2">
              <Button 
                onClick={handleCompanyInfoUpdate}
                disabled={isLoading || !hasChanges}
                variant={hasChanges ? "default" : "outline"}
                className="h-9 px-3 text-sm"
              >
                {isLoading ? 'Updating...' : 'Update Company Information'}
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}