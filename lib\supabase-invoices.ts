import { getSupabaseClient } from '@/lib/supabase'

// Define the type for our invoice data
export interface InvoiceRow {
  id: string
  organization_id: string
  order_id: string | null
  customer_id: string
  invoice_number: string
  issue_date: string
  due_date: string | null
  status: 'Draft' | 'Sent' | 'Paid' | 'Overdue'
  total: number
  created_at: string
  updated_at: string
  customer_name?: string | null
}

/**
 * Function to get all invoices for an organization
 * @param organizationId - The organization ID to fetch invoices for
 * @returns Promise with array of invoices
 */
export async function getAllInvoicesForOrganization(organizationId: string) {
  const supabase = getSupabaseClient()
  
  try {
    const { data, error } = await supabase
      .from('invoices')
      .select(`
        id,
        organization_id,
        order_id,
        customer_id,
        invoice_number,
        issue_date,
        due_date,
        status,
        total,
        created_at,
        updated_at,
        customers(full_name)
      `)
      .eq('organization_id', organizationId)
      .order('created_at', { ascending: false })
    
    if (error) {
      throw new Error(error.message)
    }
    
    // Transform the data to match our interface
    return data.map(invoice => ({
      id: invoice.id,
      organization_id: invoice.organization_id,
      order_id: invoice.order_id,
      customer_id: invoice.customer_id,
      invoice_number: invoice.invoice_number,
      issue_date: invoice.issue_date,
      due_date: invoice.due_date,
      status: invoice.status as 'Draft' | 'Sent' | 'Paid' | 'Overdue',
      total: invoice.total,
      created_at: invoice.created_at,
      updated_at: invoice.updated_at,
      customer_name: (invoice as any).customers?.full_name || 'Unknown Customer'
    }))
  } catch (error) {
    console.error('Error fetching invoices for organization:', error)
    throw error
  }
}

/**
 * Function to get an invoice by ID
 * @param invoiceId - The ID of the invoice to fetch
 * @returns Promise with the invoice data
 */
export async function getInvoiceById(invoiceId: string) {
  const supabase = getSupabaseClient()
  
  try {
    const { data, error } = await supabase
      .from('invoices')
      .select(`
        id,
        organization_id,
        order_id,
        customer_id,
        invoice_number,
        issue_date,
        due_date,
        status,
        total,
        created_at,
        updated_at,
        customers(full_name)
      `)
      .eq('id', invoiceId)
      .single()
    
    if (error) {
      throw new Error(error.message)
    }
    
    // Transform the data to match our interface
    return {
      id: data.id,
      organization_id: data.organization_id,
      order_id: data.order_id,
      customer_id: data.customer_id,
      invoice_number: data.invoice_number,
      issue_date: data.issue_date,
      due_date: data.due_date,
      status: data.status as 'Draft' | 'Sent' | 'Paid' | 'Overdue',
      total: data.total,
      created_at: data.created_at,
      updated_at: data.updated_at,
      customer_name: (data as any).customers?.full_name || 'Unknown Customer'
    }
  } catch (error) {
    console.error('Error fetching invoice by ID:', error)
    throw error
  }
}

/**
 * Function to create a new invoice
 * @param organizationId - The organization ID creating the invoice
 * @param invoiceData - The invoice data to insert
 * @returns Promise with the created invoice
 */
export async function createInvoice(organizationId: string, invoiceData: Omit<InvoiceRow, 'id' | 'organization_id' | 'created_at' | 'updated_at'>) {
  const supabase = getSupabaseClient()
  
  try {
    const { data, error } = await supabase
      .from('invoices')
      .insert({
        organization_id: organizationId,
        order_id: invoiceData.order_id,
        customer_id: invoiceData.customer_id,
        invoice_number: invoiceData.invoice_number,
        issue_date: invoiceData.issue_date,
        due_date: invoiceData.due_date,
        status: invoiceData.status,
        total: invoiceData.total
      })
      .select()
      .single()
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data
  } catch (error) {
    console.error('Error creating invoice:', error)
    throw error
  }
}

/**
 * Function to generate an invoice from an order
 * @param organizationId - The organization ID creating the invoice
 * @param orderId - The ID of the order to generate an invoice from
 * @returns Promise with the created invoice
 */
export async function generateInvoiceFromOrder(organizationId: string, orderId: string) {
  const supabase = getSupabaseClient()
  
  try {
    // First, get the order details
    const { data: order, error: orderError } = await supabase
      .from('sales')
      .select(`
        id,
        organization_id,
        customer_id,
        order_number,
        order_date,
        status,
        subtotal,
        tax,
        shipping,
        discount,
        total
      `)
      .eq('id', orderId)
      .eq('organization_id', organizationId)
      .single()
    
    if (orderError) {
      throw new Error(`Error fetching order: ${orderError.message}`)
    }
    
    // Generate a unique invoice number
    const date = new Date()
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const random = Math.floor(1000 + Math.random() * 9000)
    const invoiceNumber = `INV-${year}${month}${day}-${random}`
    
    // Create the invoice
    const { data: invoice, error: invoiceError } = await supabase
      .from('invoices')
      .insert({
        organization_id: organizationId,
        order_id: orderId,
        customer_id: order.customer_id,
        invoice_number: invoiceNumber,
        issue_date: new Date().toISOString(),
        due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
        status: 'Draft',
        total: order.total
      })
      .select()
      .single()
    
    if (invoiceError) {
      throw new Error(`Error creating invoice: ${invoiceError.message}`)
    }
    
    return invoice
  } catch (error) {
    console.error('Error generating invoice from order:', error)
    throw error
  }
}

/**
 * Function to update an existing invoice
 * @param invoiceId - The ID of the invoice to update
 * @param invoiceData - The invoice data to update
 * @returns Promise with the updated invoice
 */
export async function updateInvoice(invoiceId: string, invoiceData: Partial<InvoiceRow>) {
  const supabase = getSupabaseClient()
  
  try {
    const { data, error } = await supabase
      .from('invoices')
      .update(invoiceData)
      .eq('id', invoiceId)
      .select()
      .single()
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data
  } catch (error) {
    console.error('Error updating invoice:', error)
    throw error
  }
}

/**
 * Function to delete an invoice
 * @param invoiceId - The ID of the invoice to delete
 * @returns Promise with the deletion result
 */
export async function deleteInvoice(invoiceId: string) {
  const supabase = getSupabaseClient()
  
  try {
    const { error } = await supabase
      .from('invoices')
      .delete()
      .eq('id', invoiceId)
    
    if (error) {
      throw new Error(error.message)
    }
    
    return { success: true }
  } catch (error) {
    console.error('Error deleting invoice:', error)
    throw error
  }
}

/**
 * Function to update the status of an invoice
 * @param invoiceId - The ID of the invoice to update
 * @param newStatus - The new status for the invoice
 * @param additionalData - Additional data to update with the status
 * @returns Promise with the updated invoice data
 */
export async function updateInvoiceStatus(
  invoiceId: string, 
  newStatus: 'Draft' | 'Sent' | 'Paid' | 'Overdue', 
  additionalData?: Partial<any>
) {
  const supabase = getSupabaseClient()
  
  try {
    const updateData: any = { 
      status: newStatus,
      updated_at: new Date().toISOString()
    }
    
    // Add any additional data to update
    if (additionalData) {
      Object.assign(updateData, additionalData)
    }
    
    const { data, error } = await supabase
      .from('invoices')
      .update(updateData)
      .eq('id', invoiceId)
      .select()
      .single()
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data
  } catch (error) {
    console.error('Error updating invoice status:', error)
    throw error
  }
}