import React from 'react'
import { CheckCircle, AlertCircle, ChevronRight, Check } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useProductForm } from '../context/ProductFormContext'
import { FORM_SECTIONS } from '../config/sections'

interface NavigationItemProps {
  section: typeof FORM_SECTIONS[0]
  isActive: boolean
  isCompleted: boolean
  hasErrors: boolean
  onClick: () => void
}

function NavigationItem({ section, isActive, isCompleted, hasErrors, onClick }: NavigationItemProps) {
  const Icon = section.icon
  
  return (
    <button
      onClick={onClick}
      className={cn(
        "w-full flex items-center gap-2 p-2 rounded-md text-left transition-all duration-200 group",
        isActive && "bg-blue-500 text-white shadow-md",
        isCompleted && !isActive && "bg-green-50 text-green-700 hover:bg-green-100",
        hasErrors && !isActive && "bg-red-50 text-red-700 border border-red-200 hover:bg-red-100",
        !isActive && !isCompleted && !hasErrors && "hover:bg-gray-100 text-gray-700"
      )}
    >
      <Icon className={cn(
        "h-4 w-4 flex-shrink-0",
        isActive && "text-white",
        isCompleted && !isActive && "text-green-600",
        hasErrors && !isActive && "text-red-600"
      )} />
      
      <div className="flex-1 min-w-0">
        <div className={cn(
          "font-medium text-xs truncate",
          isActive && "text-white"
        )}>
          {section.label}
        </div>

      </div>
      
      <div className="flex-shrink-0">
        {isCompleted && !hasErrors ? (
          <Check className={cn(
            "h-4 w-4",
            isActive ? "text-white" : "text-green-600"
          )} />
        ) : hasErrors ? (
          <AlertCircle className={cn(
            "h-4 w-4",
            isActive ? "text-white" : "text-red-600"
          )} />
        ) : (
          <ChevronRight className={cn(
            "h-3 w-3",
            isActive ? "text-white" : "text-gray-400"
          )} />
        )}
      </div>
    </button>
  )
}

export function FormNavigation() {
  const { currentSection, setCurrentSection, errors, isValid } = useProductForm()
  
  // Calculate progress based on completed sections
  const completedSections = FORM_SECTIONS.filter(section => isValid[section.id]).length
  const progress = (completedSections / FORM_SECTIONS.length) * 100
  
  return (
    <div className="flex flex-col h-full bg-gray-50">
      {/* Header */}
      <div className="px-6 py-3 border-b border-gray-200">
        <h1 className="text-base font-semibold text-gray-900">
          Add New Product
        </h1>
      </div>

      {/* Navigation sections */}
      <nav className="flex-1 px-6 py-4 space-y-1 overflow-y-auto">
        {FORM_SECTIONS.map((section) => (
          <NavigationItem
            key={section.id}
            section={section}
            isActive={currentSection === section.id}
            isCompleted={isValid[section.id] || false}
            hasErrors={!!errors[section.id]}
            onClick={() => setCurrentSection(section.id)}
          />
        ))}
      </nav>
      

    </div>
  )
}
