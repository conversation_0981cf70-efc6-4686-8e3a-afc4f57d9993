'use client'

import { useState, useMemo } from 'react'
import { type ProductFilters, INITIAL_PRODUCT_FILTERS } from '../products-catalog/product-filters'

export function useInventoryFilters() {
  const [searchQuery, setSearchQuery] = useState('')
  const [filters, setFilters] = useState(INITIAL_PRODUCT_FILTERS)

  // Count active filters
  const activeFilterCount = useMemo(() => {
    let count = 0
    if (searchQuery.trim()) count++
    if (filters.categories.length > 0) count += filters.categories.length
    if (filters.suppliers.length > 0) count += filters.suppliers.length
    if (filters.stockStatus.length > 0) count += filters.stockStatus.length
    if (filters.quantityRange.min !== null || filters.quantityRange.max !== null) count++
    return count
  }, [searchQuery, filters])

  // Clear all filters
  const clearAllFilters = () => {
    setSearchQuery('')
    setFilters({
      ...INITIAL_PRODUCT_FILTERS,
      quantityRange: { min: null, max: null }
    })
  }

  // Multi-select toggle handlers
  const handleStockStatusToggle = (status: 'in_stock' | 'low_stock' | 'out_of_stock') => {
    const newStatuses = filters.stockStatus.includes(status)
      ? filters.stockStatus.filter(s => s !== status)
      : [...filters.stockStatus, status]
    
    setFilters({
      ...filters,
      stockStatus: newStatuses
    })
  }

  return {
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    activeFilterCount,
    clearAllFilters,
    handleStockStatusToggle
  }
}