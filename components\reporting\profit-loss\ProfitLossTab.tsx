'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { DateRangeSelector } from '@/components/ui/date-range-selector'
import { DateRange } from 'react-day-picker'
import { useProfitAndLoss } from '@/hooks/use-reports'
import { PageHeader } from '@/components/ui/page-header'

interface ProfitLossTabProps {
  dateRange?: DateRange
}

export function ProfitLossTab({ dateRange }: ProfitLossTabProps) {
  const { data: profitAndLossData, isLoading, error } = useProfitAndLoss({
    from: dateRange?.from,
    to: dateRange?.to
  })

  const handleExport = (format: 'pdf' | 'csv') => {
    console.log(`Exporting as ${format}`)
    // Export functionality will be implemented later
  }

  // Calculate gross profit and net profit
  const grossProfit = profitAndLossData 
    ? profitAndLossData.total_revenue - profitAndLossData.total_cogs 
    : 0
  
  const netProfit = profitAndLossData 
    ? grossProfit - profitAndLossData.total_expenses 
    : 0

  return (
    <div className="space-y-6">
      {/* Header Controls */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        {/* Removed duplicate PageHeader component to prevent title duplication */}
        
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={() => handleExport('pdf')}
            disabled={isLoading}
          >
            Export PDF
          </Button>
          <Button 
            variant="outline" 
            onClick={() => handleExport('csv')}
            disabled={isLoading}
          >
            Export CSV
          </Button>
        </div>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="flex justify-center items-center h-64">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary mb-2"></div>
            <p>Generating Profit & Loss Report...</p>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4">
          <p className="text-destructive font-medium">Error loading report: {error.message}</p>
        </div>
      )}

      {/* Financial Statement Table */}
      {profitAndLossData && !isLoading && (
        <Card>
          <CardHeader>
            {/* Removed duplicate CardTitle component to prevent title duplication */}
          </CardHeader>
          <CardContent>
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2 font-medium">Account</th>
                  <th className="text-right py-2 font-medium">Amount</th>
                </tr>
              </thead>
              <tbody>
                {/* Revenue Section */}
                <tr>
                  <td className="py-2">Revenue</td>
                  <td className="text-right py-2">${profitAndLossData.total_revenue.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</td>
                </tr>
                
                {/* Cost of Goods Sold Section */}
                <tr className="border-b">
                  <td className="py-2">Cost of Goods Sold</td>
                  <td className="text-right py-2">(${profitAndLossData.total_cogs.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })})</td>
                </tr>
                
                {/* Gross Profit */}
                <tr className="border-b bg-muted">
                  <td className="py-2 font-medium">Gross Profit</td>
                  <td className="text-right py-2 font-medium">${grossProfit.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</td>
                </tr>
                
                {/* Operating Expenses */}
                <tr>
                  <td className="py-2 pt-4" colSpan={2}>Operating Expenses</td>
                </tr>
                
                {profitAndLossData.expenses_by_category.map((expense, index) => (
                  <tr key={index}>
                    <td className="py-2 pl-4">{expense.category}</td>
                    <td className="text-right py-2">(${expense.amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })})</td>
                  </tr>
                ))}
                
                <tr className="border-b">
                  <td className="py-2 pl-4 font-medium">Total Expenses</td>
                  <td className="text-right py-2 font-medium">(${profitAndLossData.total_expenses.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })})</td>
                </tr>
                
                {/* Net Profit */}
                <tr>
                  <td className="py-2 font-bold text-lg">Net Profit</td>
                  <td className={`text-right py-2 font-bold text-lg ${netProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    ${netProfit.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </td>
                </tr>
              </tbody>
            </table>
          </CardContent>
        </Card>
      )}
    </div>
  )
}