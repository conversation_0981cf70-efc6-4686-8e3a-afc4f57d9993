import React, { useState, useRef, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface TagInputProps {
  values: string[];
  onChange: (values: string[]) => void;
  placeholder?: string;
  className?: string;
}

export function TagInput({ values, onChange, placeholder, className }: TagInputProps) {
  const [inputValue, setInputValue] = useState('');
  const [isInputFocused, setIsInputFocused] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const addTags = (newValues: string[]) => {
    const uniqueNewValues = newValues
      .map(value => value.trim())
      .filter(value => value && !values.includes(value));
    
    if (uniqueNewValues.length > 0) {
      onChange([...values, ...uniqueNewValues]);
      setInputValue('');
    }
  };

  const removeTag = (valueToRemove: string) => {
    onChange(values.filter(value => value !== valueToRemove));
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault();
      if (inputValue.trim()) {
        // Split by comma and add all values
        const newValues = inputValue.split(',').map(v => v.trim()).filter(v => v);
        addTags(newValues);
      }
    } else if (e.key === 'Backspace' && inputValue === '' && values.length > 0) {
      // Remove the last tag when backspace is pressed on empty input
      removeTag(values[values.length - 1]);
    }
  };

  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    const paste = e.clipboardData.getData('text');
    if (paste.includes(',')) {
      e.preventDefault();
      const newValues = paste.split(',').map(v => v.trim()).filter(v => v);
      addTags(newValues);
    }
  };

  const handleBlur = () => {
    setIsInputFocused(false);
    // Add the current input as a tag when blurring, if it's not empty
    if (inputValue.trim()) {
      addTags([inputValue.trim()]);
    }
  };

  const handleFocus = () => {
    setIsInputFocused(true);
  };

  return (
    <div 
      className={cn(
        "flex flex-wrap items-center gap-2 h-8 px-3 py-1 border rounded-md bg-background text-xs border-input",
        isInputFocused ? "ring-2 ring-ring ring-offset-2" : "",
        className
      )}
      onClick={() => inputRef.current?.focus()}
    >
      {values.map((value, index) => (
        <Badge key={index} variant="secondary" className="flex items-center gap-1 pr-1 py-0.5">
          <span className="text-xs">{value}</span>
          <button
            type="button"
            className="rounded-full hover:bg-secondary-foreground/20 focus:outline-none"
            onClick={(e) => {
              e.stopPropagation();
              removeTag(value);
            }}
          >
            <X className="h-3 w-3" />
          </button>
        </Badge>
      ))}
      <Input
        ref={inputRef}
        type="text"
        value={inputValue}
        onChange={(e) => setInputValue(e.target.value)}
        onKeyDown={handleKeyDown}
        onPaste={handlePaste}
        onFocus={handleFocus}
        onBlur={handleBlur}
        placeholder={values.length === 0 ? placeholder : ''}
        className="flex-1 border-0 shadow-none p-0 h-5 focus-visible:ring-0 focus-visible:ring-offset-0 text-xs placeholder:text-xs"
      />
    </div>
  );
}