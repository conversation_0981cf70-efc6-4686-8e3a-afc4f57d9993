// Product filters interface
export interface ProductFilters {
  searchQuery: string
  categories: string[]
  stockStatus: ('in_stock' | 'low_stock' | 'out_of_stock')[]
  priceRange: {
    min: number | null
    max: number | null
  }
  productType: ('simple' | 'variable')[]
  suppliers: string[]
  saleStatus: ('on_sale' | 'regular')[]
  quantityRange: {
    min: number | null
    max: number | null
  }
}

// Initial product filters state
export const INITIAL_PRODUCT_FILTERS: ProductFilters = {
  searchQuery: '',
  categories: [],
  stockStatus: [],
  priceRange: {
    min: null,
    max: null
  },
  productType: [],
  suppliers: [],
  saleStatus: [],
  quantityRange: {
    min: null,
    max: null
  }
}