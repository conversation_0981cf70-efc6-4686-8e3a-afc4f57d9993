'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { useToast } from '@/components/ui/use-toast'
import { useAllExpenses } from '@/hooks/use-expenses'
import { useAuth } from '@/contexts/auth-context'
import { getSupabaseClient } from '@/lib/supabase'
import { Skeleton } from '@/components/ui/skeleton'
import { AlertCircle } from 'lucide-react'

interface SettingsTabProps {
  isMobile?: boolean
  contextualFilters?: any
  filteredExpenseIds?: string[]
}

export function SettingsTab({ 
  isMobile = false, 
  contextualFilters,
  filteredExpenseIds = []
}: SettingsTabProps) {
  const { user, organizationId } = useAuth()
  const { toast } = useToast()
  
  // Use React Query to fetch expenses
  const { data: expenses = [], isLoading, error, refetch } = useAllExpenses(organizationId || undefined)
  
  // Settings state
  const [autoGenerateIds, setAutoGenerateIds] = useState(true)
  const [defaultCategory, setDefaultCategory] = useState('')
  const [defaultPaymentMethod, setDefaultPaymentMethod] = useState('')
  const [isSaving, setIsSaving] = useState(false)

  // Handle saving settings
  const handleSaveSettings = async () => {
    setIsSaving(true)
    try {
      // In a real implementation, you would save these settings to the database
      // For now, we'll just show a toast
      toast({
        title: "Settings Saved",
        description: "Your expense settings have been saved successfully.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save settings. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  // Handle export all expenses
  const handleExportAll = async () => {
    try {
      // Create CSV header with metadata
      const metadataLines = [
        '# All Expense Report',
        `# Generated on: ${new Date().toISOString()}`,
        `# Total Expenses: ${expenses.length}`,
        `# Total Amount: ${expenses.reduce((sum, exp) => sum + exp.amount, 0).toFixed(2)}`,
        '#',
        '# Report Data:'
      ];
      
      // CSV header row
      const headers = ['Expense ID', 'Date', 'Description', 'Category', 'Vendor', 'Payment Method', 'Amount', 'Created At'];
      
      // CSV data rows with proper escaping
      const dataRows = expenses.map(expense => {
        const expenseDate = new Date(expense.expense_date || expense.created_at);
        const createdDate = new Date(expense.created_at);
        
        return [
          expense.expense_id || 'N/A',
          expenseDate.toLocaleDateString('en-US'),
          `"${(expense.description || '').replace(/"/g, '""')}"`, // Escape quotes
          expense.category || '',
          `"${(expense.vendor || '').replace(/"/g, '""')}"`, // Escape quotes
          expense.payment_method || '',
          expense.amount.toFixed(2),
          createdDate.toISOString()
        ].join(',');
      });
      
      // Combine all parts
      const csvContent = [
        ...metadataLines,
        headers.join(','),
        ...dataRows
      ].join('\n');

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `all-expenses-export-${new Date().toISOString().split('T')[0]}.csv`;
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast({
        title: "CSV Export Complete",
        description: `Successfully exported all ${expenses.length} expenses to CSV format.`,
      });
    } catch (error) {
      console.error('CSV export error:', error);
      toast({
        title: "CSV Export Failed",
        description: "Failed to export expenses. Please try again.",
        variant: "destructive",
      });
    }
  }

  // Handle delete all expenses
  const handleDeleteAll = async () => {
    if (!user?.id || !organizationId) return;
    
    // Confirm before deleting
    if (!window.confirm('Are you sure you want to delete ALL expenses? This action cannot be undone.')) {
      return;
    }
    
    try {
      const supabase = getSupabaseClient();
      const { error } = await supabase
        .from('expenses')
        .delete()
        .eq('organization_id', organizationId);
      
      if (error) {
        throw new Error(error.message);
      }
      
      // Refresh the expenses list
      refetch();
      
      toast({
        title: "All Expenses Deleted",
        description: "All expenses have been successfully deleted.",
      });
    } catch (error) {
      console.error('Error deleting all expenses:', error);
      toast({
        title: "Error",
        description: "Failed to delete all expenses. Please try again.",
        variant: "destructive",
      });
    }
  }

  // Render skeleton loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-10 w-full" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Render error state
  if (error) {
    // Type guard to ensure error is properly typed
    const errorMessage = error instanceof Error ? error.message : String(error);
    
    let userFriendlyMessage = "Failed to load expense data for settings. Please try again."
    
    // Provide more specific error messages based on the error type
    if (errorMessage.includes("NetworkError") || errorMessage.includes("Failed to fetch")) {
      userFriendlyMessage = "Network connection error. Please check your internet connection and try again."
    } else if (errorMessage.includes("ERR_INSUFFICIENT_RESOURCES")) {
      userFriendlyMessage = "System resource limit reached. Please refresh the page or try again in a few minutes."
    } else if (errorMessage.includes("timeout")) {
      userFriendlyMessage = "Request timed out. Please try again."
    } else if (errorMessage.includes("401") || errorMessage.includes("403")) {
      userFriendlyMessage = "Authentication error. Please sign in again."
    } else if (errorMessage.includes("500")) {
      userFriendlyMessage = "Server error. Please try again later."
    }
    
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center max-w-md">
          <div className="text-red-500 mb-2">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <h3 className="text-lg font-medium">Unable to Load Expense Settings</h3>
          </div>
          <p className="text-muted-foreground text-sm mb-4">{userFriendlyMessage}</p>
          <Button 
            onClick={() => window.location.reload()} 
            variant="default" 
            size="sm"
          >
            Refresh Page
          </Button>
          <p className="text-xs text-muted-foreground mt-4">
            Error: {errorMessage}
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* General Settings */}
      <Card>
        <CardHeader>
          <CardTitle>General Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="auto-generate-ids" className="text-base">
                Auto-generate Expense IDs
              </Label>
              <p className="text-sm text-muted-foreground">
                Automatically generate sequential expense IDs (EXP-001, EXP-002, etc.)
              </p>
            </div>
            <Switch
              id="auto-generate-ids"
              checked={autoGenerateIds}
              onCheckedChange={setAutoGenerateIds}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="default-category">Default Category</Label>
            <Input
              id="default-category"
              value={defaultCategory}
              onChange={(e) => setDefaultCategory(e.target.value)}
              placeholder="e.g., Office Supplies"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="default-payment-method">Default Payment Method</Label>
            <Input
              id="default-payment-method"
              value={defaultPaymentMethod}
              onChange={(e) => setDefaultPaymentMethod(e.target.value)}
              placeholder="e.g., Credit Card"
            />
          </div>
          
          <Button 
            onClick={handleSaveSettings} 
            disabled={isSaving}
            className="w-full md:w-auto"
          >
            {isSaving ? 'Saving...' : 'Save Settings'}
          </Button>
        </CardContent>
      </Card>

      {/* Data Management */}
      <Card>
        <CardHeader>
          <CardTitle>Data Management</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-3">
            <Button 
              onClick={handleExportAll} 
              variant="outline" 
              className="flex-1"
            >
              Export All Expenses (CSV)
            </Button>
            <Button 
              onClick={handleDeleteAll} 
              variant="destructive" 
              className="flex-1"
            >
              Delete All Expenses
            </Button>
          </div>
          
          <p className="text-sm text-muted-foreground">
            Note: Exporting will download all expenses in your account. Deleting all expenses is irreversible.
          </p>
        </CardContent>
      </Card>
    </div>
  )
}