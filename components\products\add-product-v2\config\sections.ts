import { 
  Package, 
  Info, 
  Tag, 
  DollarSign, 
  Archive, 
  Store, 
  FileText 
} from 'lucide-react'
import { FormSection } from '../types'

export const FORM_SECTIONS: FormSection[] = [
  {
    id: 'type',
    label: 'Product Type',
    icon: Package,
    required: true,
    description: 'Choose between simple or variable product'
  },
  {
    id: 'details',
    label: 'Product Details',
    icon: Info,
    required: true,
    description: 'Essential product information'
  },
  {
    id: 'pricing',
    label: 'Pricing & Variants',
    icon: DollarSign,
    required: true,
    description: 'Pricing and variant management'
  },
  {
    id: 'inventory',
    label: 'Inventory',
    icon: Archive,
    required: false,
    description: 'Stock levels and thresholds'
  },
  {
    id: 'supplier',
    label: 'Supplier',
    icon: Store,
    required: false,
    description: 'Supplier and sourcing information'
  },
  {
    id: 'additional',
    label: 'Additional Info',
    icon: FileText,
    required: false,
    description: 'Notes and additional details'
  }
]

export function getSectionById(id: string): FormSection | undefined {
  return FORM_SECTIONS.find(section => section.id === id)
}

export function getNextSection(currentId: string): string | null {
  const currentIndex = FORM_SECTIONS.findIndex(section => section.id === currentId)
  if (currentIndex === -1 || currentIndex === FORM_SECTIONS.length - 1) {
    return null
  }
  return FORM_SECTIONS[currentIndex + 1].id
}

export function getPreviousSection(currentId: string): string | null {
  const currentIndex = FORM_SECTIONS.findIndex(section => section.id === currentId)
  if (currentIndex <= 0) {
    return null
  }
  return FORM_SECTIONS[currentIndex - 1].id
}
