const supabaseConfig = require('../config/supabase');

class AuthController {
  // Real Supabase Auth implementation
  async signUp(email, password, metadata = {}) {
    try {
      console.log('Auth: Sign up requested for', email);
      
      // Use Supabase Auth API for real user registration
      const response = await fetch(`${supabaseConfig.SUPABASE_URL}/auth/v1/signup`, {
        method: 'POST',
        headers: {
          'apikey': supabaseConfig.SUPABASE_SERVICE_KEY,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: email,
          password: password,
          data: metadata // Additional user metadata like business name
        })
      });
      
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error_description || result.message || 'Sign up failed');
      }
      
      // Supabase sends confirmation email automatically
      return {
        success: true,
        message: 'Registration successful! Please check your email to confirm your account.',
        user: result.user,
        requiresEmailConfirmation: true,
        session: result.session
      };
    } catch (error) {
      console.error('Error in sign up:', error);
      throw error;
    }
  }

  async signIn(email, password) {
    try {
      console.log('Auth: Sign in requested for', email);
      
      // Use Supabase Auth API for real user authentication
      const response = await fetch(`${supabaseConfig.SUPABASE_URL}/auth/v1/token?grant_type=password`, {
        method: 'POST',
        headers: {
          'apikey': supabaseConfig.SUPABASE_SERVICE_KEY,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: email,
          password: password
        })
      });
      
      const result = await response.json();
      
      if (!response.ok) {
        if (result.error_description && result.error_description.includes('Email not confirmed')) {
          throw new Error('Please check your email and click the confirmation link before signing in.');
        }
        throw new Error(result.error_description || result.message || 'Sign in failed');
      }
      
      return {
        success: true,
        message: 'Sign in successful',
        user: result.user,
        accessToken: result.access_token,
        refreshToken: result.refresh_token,
        session: result
      };
    } catch (error) {
      console.error('Error in sign in:', error);
      throw error;
    }
  }

  async signOut(token) {
    try {
      // This will be implemented when we integrate real Supabase Auth
      console.log('Auth: Sign out requested');
      
      return {
        success: true,
        message: 'Sign out successful'
      };
    } catch (error) {
      console.error('Error in sign out:', error);
      throw error;
    }
  }

  async validateToken(token) {
    try {
      // This will be implemented when we integrate real Supabase Auth
      // For now, we'll assume any non-empty token is valid
      if (!token || token.length < 10) {
        return { valid: false, user: null };
      }
      
      return {
        valid: true,
        user: { id: 'demo-user', email: '<EMAIL>' }
      };
    } catch (error) {
      console.error('Error validating token:', error);
      return { valid: false, user: null };
    }
  }

  async refreshToken(refreshToken) {
    try {
      // This will be implemented when we integrate real Supabase Auth
      console.log('Auth: Token refresh requested');
      
      return {
        success: true,
        accessToken: null,
        refreshToken: null
      };
    } catch (error) {
      console.error('Error refreshing token:', error);
      throw error;
    }
  }
}

module.exports = new AuthController();