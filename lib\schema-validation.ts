/**
 * Schema validation utilities to ensure database schema matches application expectations
 */

import { getSupabaseClient } from '@/lib/supabase';

// Define the expected schema for the customers table
const EXPECTED_CUSTOMERS_SCHEMA = {
  table: 'customers',
  columns: [
    { name: 'id', type: 'uuid', nullable: false },
    { name: 'organization_id', type: 'uuid', nullable: false },
    { name: 'full_name', type: 'text', nullable: false },
    { name: 'email', type: 'text', nullable: true },
    { name: 'phone', type: 'text', nullable: true },
    { name: 'shipping_address', type: 'text', nullable: true },
    { name: 'billing_address', type: 'text', nullable: true },
    { name: 'notes', type: 'text', nullable: true },
    { name: 'created_at', type: 'timestamp with time zone', nullable: false },
    { name: 'updated_at', type: 'timestamp with time zone', nullable: false }
  ]
};

// Define expected RPC functions
const EXPECTED_RPC_FUNCTIONS = [
  {
    name: 'create_customer',
    parameters: [
      'p_organization_id',
      'p_full_name',
      'p_email',
      'p_phone',
      'p_shipping_address',
      'p_billing_address',
      'p_notes'
    ]
  },
  {
    name: 'get_all_customers_for_organization',
    parameters: ['p_organization_id']
  },
  {
    name: 'update_customer',
    parameters: [
      'p_customer_id',
      'p_full_name',
      'p_email',
      'p_phone',
      'p_shipping_address',
      'p_billing_address',
      'p_notes'
    ]
  },
  {
    name: 'delete_customer',
    parameters: ['p_customer_id']
  }
];

/**
 * Validate that the customers table schema matches expectations
 */
export async function validateCustomersTableSchema(): Promise<{ valid: boolean; errors: string[] }> {
  const supabase = getSupabaseClient();
  const errors: string[] = [];
  
  try {
    // Get actual table schema
    const { data: actualColumns, error } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type, is_nullable')
      .eq('table_name', EXPECTED_CUSTOMERS_SCHEMA.table)
      .order('ordinal_position');
      
    if (error) {
      return { valid: false, errors: [`Failed to fetch table schema: ${error.message}`] };
    }
    
    // Check if all expected columns exist
    for (const expectedColumn of EXPECTED_CUSTOMERS_SCHEMA.columns) {
      const actualColumn = actualColumns.find(col => col.column_name === expectedColumn.name);
      
      if (!actualColumn) {
        errors.push(`Missing expected column: ${expectedColumn.name}`);
        continue;
      }
      
      // Check data type
      if (actualColumn.data_type !== expectedColumn.type) {
        errors.push(`Column ${expectedColumn.name} has type ${actualColumn.data_type}, expected ${expectedColumn.type}`);
      }
      
      // Check nullability
      const isNullable = actualColumn.is_nullable === 'YES';
      if (isNullable && !expectedColumn.nullable) {
        errors.push(`Column ${expectedColumn.name} is nullable but expected to be NOT NULL`);
      } else if (!isNullable && expectedColumn.nullable) {
        errors.push(`Column ${expectedColumn.name} is NOT NULL but expected to be nullable`);
      }
    }
    
    // Check for unexpected columns
    const expectedColumnNames = EXPECTED_CUSTOMERS_SCHEMA.columns.map(col => col.name);
    for (const actualColumn of actualColumns) {
      if (!expectedColumnNames.includes(actualColumn.column_name)) {
        errors.push(`Unexpected column found: ${actualColumn.column_name}`);
      }
    }
    
    return { valid: errors.length === 0, errors };
  } catch (err) {
    return { valid: false, errors: [`Unexpected error during schema validation: ${err}`] };
  }
}

/**
 * Validate that all expected RPC functions exist with correct parameters
 */
export async function validateCustomerRpcFunctions(): Promise<{ valid: boolean; errors: string[] }> {
  const supabase = getSupabaseClient();
  const errors: string[] = [];
  
  try {
    // Get all function names
    const { data: functions, error } = await supabase.rpc('get_all_functions');
    
    if (error) {
      // If the helper function doesn't exist, query directly
      const { data: directFunctions, error: directError } = await supabase
        .from('pg_proc')
        .select('proname, proargnames')
        .ilike('proname', '%customer%');
        
      if (directError) {
        return { valid: false, errors: [`Failed to fetch RPC functions: ${directError.message}`] };
      }
      
      // Check each expected function
      for (const expectedFunction of EXPECTED_RPC_FUNCTIONS) {
        const actualFunction = directFunctions.find((fn: any) => fn.proname === expectedFunction.name);
        
        if (!actualFunction) {
          errors.push(`Missing expected RPC function: ${expectedFunction.name}`);
          continue;
        }
        
        // Check parameters if available
        if (actualFunction.proargnames && Array.isArray(actualFunction.proargnames)) {
          const actualParams = actualFunction.proargnames;
          const expectedParams = expectedFunction.parameters;
          
          // Check if all expected parameters exist
          for (const expectedParam of expectedParams) {
            if (!actualParams.includes(expectedParam)) {
              errors.push(`RPC function ${expectedFunction.name} missing expected parameter: ${expectedParam}`);
            }
          }
          
          // Check for unexpected parameters (only if we have expected params)
          if (expectedParams.length > 0) {
            for (const actualParam of actualParams) {
              if (!expectedParams.includes(actualParam)) {
                errors.push(`RPC function ${expectedFunction.name} has unexpected parameter: ${actualParam}`);
              }
            }
          }
        }
      }
    }
    
    return { valid: errors.length === 0, errors };
  } catch (err) {
    return { valid: false, errors: [`Unexpected error during RPC function validation: ${err}`] };
  }
}

/**
 * Run all schema validations
 */
export async function validateAllSchemas(): Promise<{ valid: boolean; errors: string[] }> {
  const errors: string[] = [];
  
  // Validate customers table
  const tableValidation = await validateCustomersTableSchema();
  if (!tableValidation.valid) {
    errors.push(...tableValidation.errors);
  }
  
  // Validate RPC functions
  const rpcValidation = await validateCustomerRpcFunctions();
  if (!rpcValidation.valid) {
    errors.push(...rpcValidation.errors);
  }
  
  return { valid: errors.length === 0, errors };
}