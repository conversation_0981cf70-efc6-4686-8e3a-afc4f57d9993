'use client'

import { InventoryTableRow } from './InventoryTableRow'
import { type ExtendedProductRow, type InventoryItem } from '@/components/products/inventory/types'

interface InventoryTableProps {
  paginatedItems: InventoryItem[]
  products: ExtendedProductRow[]
  expandedProducts: Record<string, boolean>
  setExpandedProducts: (expanded: Record<string, boolean>) => void
  openMenuId: string | null
  setOpenMenuId: (id: string | null) => void
  checkItemStockHistory: (item: InventoryItem) => Promise<boolean>
  itemsWithHistory: Record<string, boolean>
  setSelectedItem: (item: InventoryItem | null) => void
  setIsAdjustStockModalOpen: (isOpen: boolean) => void
  setIsStockHistoryModalOpen: (isOpen: boolean) => void
  setIsPurchaseOrderModalOpen: (isOpen: boolean) => void
}

export function InventoryTable({
  paginatedItems,
  products,
  expandedProducts,
  setExpandedProducts,
  openMenuId,
  setOpenMenuId,
  checkItemStockHistory,
  itemsWithHistory,
  setSelectedItem,
  setIsAdjustStockModalOpen,
  setIsStockHistoryModalOpen,
  setIsPurchaseOrderModalOpen
}: InventoryTableProps) {
  return (
    <div className="border border-gray-200 rounded-t-lg overflow-hidden">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200 bg-white">
          <thead className="bg-gray-50">
            <tr>
              <th 
                scope="col" 
                className="text-left py-3 px-6 font-medium text-gray-900 text-xs"
              >
                <div className="flex items-center gap-1">
                  <span>Product/Variant</span>
                </div>
              </th>
              <th 
                scope="col" 
                className="text-left py-3 px-6 font-medium text-gray-900 text-xs"
              >
                <div className="flex items-center gap-1">
                  <span>Stock on Hand</span>
                </div>
              </th>
              <th 
                scope="col" 
                className="text-left py-3 px-6 font-medium text-gray-900 text-xs"
              >
                <div className="flex items-center gap-1">
                  <span>Committed</span>
                </div>
              </th>
              <th 
                scope="col" 
                className="text-left py-3 px-6 font-medium text-gray-900 text-xs"
              >
                <div className="flex items-center gap-1">
                  <span>Available</span>
                </div>
              </th>
              <th 
                scope="col" 
                className="text-left py-3 px-6 font-medium text-gray-900 text-xs"
              >
                <div className="flex items-center gap-1">
                  <span>Incoming</span>
                </div>
              </th>
              <th 
                scope="col" 
                className="text-left py-3 px-6 font-medium text-gray-900 text-xs"
              >
                <div className="flex items-center gap-1">
                  <span>Status</span>
                </div>
              </th>
              <th scope="col" className="text-center py-3 px-6 font-medium text-gray-900 w-32 text-xs">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {paginatedItems.map((item) => {
              // Skip variant items in the main table - they're shown when expanded
              if (item.type === 'variant') return null
              
              // Check if this is a variable product with variants
              const product = products.find(p => p.id === item.productId)
              const isVariableProduct = item.type === 'product' && product?.has_variants || false
              
              // Get variants for this product if it's a variable product
              const productVariants = isVariableProduct 
                ? product?.variants || []
                : []
              
              // Check if this product is expanded
              const isExpanded = expandedProducts[item.productId] || false
              
              return (
                <InventoryTableRow
                  key={item.id}
                  item={item}
                  product={product}
                  isVariableProduct={isVariableProduct}
                  productVariants={productVariants}
                  isExpanded={isExpanded}
                  expandedProducts={expandedProducts}
                  setExpandedProducts={setExpandedProducts}
                  openMenuId={openMenuId}
                  setOpenMenuId={setOpenMenuId}
                  checkItemStockHistory={checkItemStockHistory}
                  itemsWithHistory={itemsWithHistory}
                  setSelectedItem={setSelectedItem}
                  setIsAdjustStockModalOpen={setIsAdjustStockModalOpen}
                  setIsStockHistoryModalOpen={setIsStockHistoryModalOpen}
                  setIsPurchaseOrderModalOpen={setIsPurchaseOrderModalOpen}
                />
              )
            })}
          </tbody>
        </table>
      </div>
    </div>
  )
}