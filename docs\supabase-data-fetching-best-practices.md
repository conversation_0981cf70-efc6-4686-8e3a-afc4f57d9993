# Supabase Data Fetching Best Practices

This document outlines the best practices for fetching data from Supabase to avoid schema cache issues and ensure reliable data retrieval.

## Problem Statement

We've encountered issues with Supabase relationship syntax causing schema cache problems, particularly with queries like:
```javascript
.select(`
  id,
  customers (full_name)
`)
```

These queries sometimes fail with errors like:
- "column customers_1.name does not exist"
- "Could not find a relationship between 'sale_items' and 'products' in the schema cache"

## Solution Approach

Instead of using relationship syntax in `.select()` calls, we fetch related data separately and then enrich the main records with the related information.

### 1. Use Separate Queries for Related Data

Instead of:
```javascript
// Problematic approach
const { data, error } = await supabase
  .from('sales')
  .select(`
    id,
    customer_id,
    customers (full_name)
  `)
```

Use:
```javascript
// Better approach
const { data: sales, error: salesError } = await supabase
  .from('sales')
  .select('id, customer_id')

// Then fetch customers separately
const customerIds = sales.map(sale => sale.customer_id)
const { data: customers, error: customersError } = await supabase
  .from('customers')
  .select('id, full_name')
  .in('id', customerIds)

// Then combine the data
const enrichedSales = sales.map(sale => {
  const customer = customers.find(c => c.id === sale.customer_id)
  return {
    ...sale,
    customer_name: customer ? customer.full_name : 'Unknown Customer'
  }
})
```

### 2. Use Utility Functions

We've created utility functions in [supabase-utils.ts](file:///X:/Qoder/onko/lib/supabase-utils.ts) and [supabase-enrichment.ts](file:///X:/Qoder/onko/lib/supabase-enrichment.ts) to handle common enrichment scenarios:

#### For Sales Data
```javascript
import { enrichSalesWithCustomerData } from '@/lib/supabase-utils'

const { data, error } = await supabase
  .from('sales')
  .select('id, customer_id, total_amount')

const enrichedSales = await enrichSalesWithCustomerData(data)
```

#### For Product Data
```javascript
import { enrichProductsWithRelatedData } from '@/lib/supabase-utils'

const { data, error } = await supabase
  .from('products')
  .select('id, category_id, name')

const enrichedProducts = await enrichProductsWithRelatedData(data)
```

#### Generic Enrichment
```javascript
import { enrichRecordsWithRelatedData } from '@/lib/supabase-utils'

const enrichedRecords = await enrichRecordsWithRelatedData(
  records,
  'related_table',
  'foreign_key',
  'related_key',
  'field1, field2',
  (relatedRecords) => {
    return relatedRecords.reduce((map, record) => {
      map[record.related_key] = { /* transformation */ }
      return map
    }, {})
  }
)
```

### 3. Benefits of This Approach

1. **Avoids Schema Cache Issues**: No dependency on relationship syntax that can cause cache problems
2. **More Reliable**: Separate queries are less likely to fail due to schema inconsistencies
3. **Better Performance**: Can parallelize related data fetching when needed
4. **Easier Debugging**: Issues with specific data fetching are isolated
5. **Reusable**: Utility functions can be used across the application

### 4. Implementation Examples

#### Before (Problematic)
```javascript
// In supabase.ts
export async function getAllProductsForOrganization(organizationId: string) {
  const { data, error } = await supabase
    .from('products')
    .select(`
      id,
      categories(
        name
      ),
      product_variants(
        id,
        variant_name
      )
    `)
    .eq('organization_id', organizationId)
  // ... rest of implementation
}
```

#### After (Fixed)
```javascript
// In supabase.ts
export async function getAllProductsForOrganization(organizationId: string) {
  const { data, error } = await supabase
    .from('products')
    .select('id, category_id, name')
    .eq('organization_id', organizationId)

  // Enrich with related data using utility function
  const enrichedProducts = await enrichProductsWithRelatedData(data)
  return enrichedProducts
}
```

### 5. Edge Functions

For Supabase Edge Functions, apply the same principle:

#### Before (Problematic)
```javascript
const { data: products, error } = await supabase
  .from('products')
  .select(`
    id,
    product_variants(
      id,
      stock_quantity
    )
  `)
```

#### After (Fixed)
```javascript
// Fetch products
const { data: products, error: productsError } = await supabase
  .from('products')
  .select('id, name')

// Fetch variants separately
const { data: variants, error: variantsError } = await supabase
  .from('product_variants')
  .select('id, product_id, stock_quantity')

// Combine data in application code
```

## Best Practices Summary

1. **Avoid relationship syntax** in `.select()` calls
2. **Use separate queries** for related data
3. **Leverage utility functions** for common enrichment patterns
4. **Handle errors gracefully** for each query
5. **Test thoroughly** after making changes
6. **Document patterns** for future reference

## When to Use This Approach

This approach should be used:
- When experiencing schema cache issues with relationship syntax
- When building new data fetching functions
- When refactoring existing problematic code
- In Supabase Edge Functions
- In any critical data fetching paths

## Migration Plan

1. Identify all current uses of relationship syntax
2. Replace with separate queries and enrichment
3. Use utility functions where applicable
4. Test thoroughly
5. Update documentation