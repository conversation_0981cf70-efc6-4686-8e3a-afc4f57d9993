{"name": "onko", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "db:recreate-products": "node scripts/complete-recreate-products.js", "db:verify-tables": "node scripts/verify-new-tables.js", "db:refresh-schema": "node scripts/force-refresh-schema.js", "db:verify-setup": "node scripts/verify-database-setup.js"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-tooltip": "^1.2.8", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/supabase-js": "^2.45.1", "@tanstack/react-query": "^5.51.23", "@tanstack/react-query-devtools": "^5.51.23", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "dotenv": "^17.2.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.427.0", "next": "14.2.5", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.52.2", "react-window": "^2.0.2", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8", "zustand": "^4.5.5"}, "devDependencies": {"@hookform/resolvers": "^3.9.0", "@testing-library/jest-dom": "^6.4.8", "@testing-library/react": "^16.0.0", "@types/jest": "^30.0.0", "@types/jspdf": "^1.3.3", "@types/lodash": "^4.17.20", "@types/node": "^20.15.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "autoprefixer": "^10.4.20", "eslint": "^8.57.0", "eslint-config-next": "14.2.5", "eslint-config-prettier": "^10.1.8", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.41", "prettier": "^3.6.2", "tailwindcss": "^3.4.9", "typescript": "^5.5.4"}}