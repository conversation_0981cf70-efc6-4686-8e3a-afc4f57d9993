class SignInTemplate {
  generate() {
    return (
'<!DOCTYPE html>' +
'<html lang="en">' +
'<head>' +
'    <meta charset="UTF-8">' +
'    <meta name="viewport" content="width=device-width, initial-scale=1.0">' +
'    <title>Sign In - ONKO</title>' +
'    <!-- TODO: Replace with PostCSS Tailwind in production - CDN is for development only -->' +
'    <script src="https://cdn.tailwindcss.com"></script>' +
'</head>' +
'<body class="bg-gray-50 min-h-screen flex items-center justify-center">' +
'    <div class="max-w-md w-full space-y-8 p-8">' +
'        <div class="text-center">' +
'            <h1 class="text-4xl font-bold text-gray-900 mb-2">ONKO</h1>' +
'            <p class="text-gray-600 mb-8">Business Management Platform</p>' +
'            <h2 class="text-2xl font-bold text-gray-900">Sign In</h2>' +
'            <p class="text-gray-600">Enter your credentials to access your account</p>' +
'        </div>' +
'        ' +
'        <form id="signinForm" class="space-y-6">' +
'            <div>' +
'                <label class="block text-sm font-medium text-gray-700">Email</label>' +
'                <input id="email" type="email" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="<EMAIL>" required>' +
'            </div>' +
'            <div>' +
'                <label class="block text-sm font-medium text-gray-700">Password</label>' +
'                <input id="password" type="password" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="Enter your password" required>' +
'            </div>' +
'            <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">Sign In</button>' +
'        </form>' +
'        ' +
'        <div class="text-center space-y-2">' +
'            <a href="/auth/signup" class="text-sm text-blue-600 hover:underline">Do not have an account? Sign up</a>' +
'        </div>' +
'        ' +
'        <div class="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">' +
'            <p class="text-sm text-green-800">' +
'                <strong>✅ Production Ready:</strong> Real Supabase authentication with JWT tokens and Row Level Security.' +
'            </p>' +
'        </div>' +
'' +
'        <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">' +
'            <p class="text-sm text-blue-800">' +
'                <strong>Demo:</strong> For testing, click "Sign In" to see the dashboard with your real data!' +
'            </p>' +
'        </div>' +
'    </div>' +
'    ' +
'    <script>' +
'        document.getElementById("signinForm").addEventListener("submit", async function(e) {' +
'            e.preventDefault();' +
'            ' +
'            const email = document.getElementById("email").value;' +
'            const password = document.getElementById("password").value;' +
'            const submitButton = e.target.querySelector("button[type=\"submit\"]");' +
'            ' +
'            if (!email || !password) {' +
'                alert("Please fill in all fields");' +
'                return;' +
'            }' +
'            ' +
'            // Disable button and show loading state' +
'            submitButton.disabled = true;' +
'            submitButton.textContent = "Signing In...";' +
'            ' +
'            try {' +
'                // Call our auth API endpoint' +
'                const response = await fetch("/api/auth/signin", {' +
'                    method: "POST",' +
'                    headers: {' +
'                        "Content-Type": "application/json"' +
'                    },' +
'                    body: JSON.stringify({' +
'                        email: email,' +
'                        password: password' +
'                    })' +
'                });' +
'                ' +
'                const result = await response.json();' +
'                ' +
'                if (result.success) {' +
'                    // Store auth token and redirect to dashboard' +
'                    if (result.data.accessToken) {' +
'                        localStorage.setItem("onko_auth_token", result.data.accessToken);' +
'                        localStorage.setItem("onko_user", JSON.stringify(result.data.user));' +
'                    }' +
'                    alert("Sign in successful! Redirecting to dashboard...");' +
'                    window.location.href = "/dashboard";' +
'                } else {' +
'                    // Handle specific error messages' +
'                    if (result.error.includes("Email not confirmed")) {' +
'                        showEmailConfirmationError(email);' +
'                    } else {' +
'                        alert("Sign in failed: " + result.error);' +
'                    }' +
'                }' +
'            } catch (error) {' +
'                console.error("Sign in error:", error);' +
'                alert("Network error occurred. Please try again.");' +
'            } finally {' +
'                // Re-enable button' +
'                submitButton.disabled = false;' +
'                submitButton.textContent = "Sign In";' +
'            }' +
'        });' +
'        ' +
'        function showEmailConfirmationError(email) {' +
'            // Show email confirmation reminder' +
'            const errorDiv = document.createElement("div");' +
'            errorDiv.className = "mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg";' +
'            errorDiv.innerHTML = ' +
'                "<div class=\"flex\">" +' +
'                "<svg class=\"h-5 w-5 text-yellow-400 mt-0.5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">" +' +
'                "<path fill-rule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clip-rule=\"evenodd\"></path>" +' +
'                "</svg>" +' +
'                "<div class=\"ml-3\">" +' +
'                "<h3 class=\"text-sm font-medium text-yellow-800\">Email Confirmation Required</h3>" +' +
'                "<p class=\"text-sm text-yellow-700 mt-1\">" +' +
'                "Please check your email <strong>" + email + "</strong> and click the confirmation link before signing in." +' +
'                "</p>" +' +
'                "<div class=\"mt-2\">" +' +
'                "<span class=\"text-sm text-yellow-600\">Check your email inbox and spam folder</span>" +
' +
'                "</button>" +' +
'                "</div>" +' +
'                "</div>" +' +
'                "</div>";' +
'            ' +
'            // Remove any existing error messages' +
'            const existingError = document.querySelector(".email-confirmation-error");' +
'            if (existingError) {' +
'                existingError.remove();' +
'            }' +
'            ' +
'            errorDiv.classList.add("email-confirmation-error");' +
'            document.querySelector("form").appendChild(errorDiv);' +
'        }' +
'        ' +
'        function resendConfirmation(email) {' +
'            // Future implementation: call resend confirmation API' +
'            alert("Confirmation email resent to " + email + "! Please check your inbox.");' +
'        }' +
'    </script>' +
'</body>' +
'</html>'
    );
  }
}

module.exports = new SignInTemplate();