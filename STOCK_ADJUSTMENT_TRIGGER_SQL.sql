-- =============================================
-- STOCK ADJUSTMENT TRIGGER FUNCTION
-- =============================================
-- This function automatically creates notifications when stock is adjusted

-- Function to create a notification for stock adjustments
CREATE OR REPLACE FUNCTION create_stock_adjustment_notification()
RETURNS TRIGGER AS $$
DECLARE
  product_name TEXT;
  product_sku TEXT;
  user_email TEXT;
  notification_title TEXT;
  notification_message TEXT;
BEGIN
  -- Only proceed if stock quantity actually changed
  IF OLD.stock_quantity IS DISTINCT FROM NEW.stock_quantity THEN
    -- Get product information
    SELECT p.name, p.base_sku INTO product_name, product_sku
    FROM products p
    WHERE p.id = NEW.id;
    
    -- Get user email for display name
    SELECT email INTO user_email
    FROM auth.users
    WHERE id = NEW.user_id;
    
    -- Create notification title and message
    notification_title := 'Stock Adjustment: ' || COALESCE(product_name, 'Unknown Product');
    notification_message := 'Stock for "' || COALESCE(product_name, 'Unknown Product') || 
                           '" (SKU: ' || COALESCE(product_sku, 'N/A') || 
                           ') was adjusted from ' || COALESCE(OLD.stock_quantity, 0) || 
                           ' to ' || COALESCE(NEW.stock_quantity, 0) || '.';
    
    -- Insert notification
    INSERT INTO notifications (
      user_id,
      type,
      title,
      message,
      related_entity_type,
      related_entity_id,
      created_at
    ) VALUES (
      NEW.user_id,
      'stock_adjustment',
      notification_title,
      notification_message,
      'product',
      NEW.id,
      NOW()
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for products table
CREATE TRIGGER stock_adjustment_notification_trigger
  AFTER UPDATE OF stock_quantity ON products
  FOR EACH ROW
  EXECUTE FUNCTION create_stock_adjustment_notification();

-- Function to create a notification for variant stock adjustments
CREATE OR REPLACE FUNCTION create_variant_stock_adjustment_notification()
RETURNS TRIGGER AS $$
DECLARE
  product_name TEXT;
  variant_name TEXT;
  variant_sku TEXT;
  user_email TEXT;
  notification_title TEXT;
  notification_message TEXT;
BEGIN
  -- Only proceed if stock quantity actually changed
  IF OLD.stock_quantity IS DISTINCT FROM NEW.stock_quantity THEN
    -- Get product and variant information
    SELECT p.name, pv.variant_name, pv.sku INTO product_name, variant_name, variant_sku
    FROM product_variants pv
    JOIN products p ON pv.product_id = p.id
    WHERE pv.id = NEW.id;
    
    -- Get user email for display name
    SELECT email INTO user_email
    FROM auth.users
    WHERE id = NEW.user_id;
    
    -- Create notification title and message
    notification_title := 'Stock Adjustment: ' || COALESCE(product_name, 'Unknown Product') || 
                         ' - ' || COALESCE(variant_name, 'Unknown Variant');
    notification_message := 'Stock for "' || COALESCE(product_name, 'Unknown Product') || 
                           ' - ' || COALESCE(variant_name, 'Unknown Variant') || 
                           '" (SKU: ' || COALESCE(variant_sku, 'N/A') || 
                           ') was adjusted from ' || COALESCE(OLD.stock_quantity, 0) || 
                           ' to ' || COALESCE(NEW.stock_quantity, 0) || '.';
    
    -- Insert notification
    INSERT INTO notifications (
      user_id,
      type,
      title,
      message,
      related_entity_type,
      related_entity_id,
      created_at
    ) VALUES (
      NEW.user_id,
      'stock_adjustment',
      notification_title,
      notification_message,
      'variant',
      NEW.id,
      NOW()
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for product_variants table
CREATE TRIGGER variant_stock_adjustment_notification_trigger
  AFTER UPDATE OF stock_quantity ON product_variants
  FOR EACH ROW
  EXECUTE FUNCTION create_variant_stock_adjustment_notification();