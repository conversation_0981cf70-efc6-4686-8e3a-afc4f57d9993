-- =============================================
-- MIGRATE PRODUCT_ATTRIBUTE_VALUES TABLE TO ORGANIZATION MODEL
-- =============================================

-- Add organization_id column to product_attribute_values table
ALTER TABLE product_attribute_values 
ADD COLUMN IF NOT EXISTS organization_id uuid REFERENCES organizations(id) ON DELETE CASCADE;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_product_attribute_values_organization_id ON product_attribute_values(organization_id);

-- Backfill organization_id from user_id
UPDATE product_attribute_values 
SET organization_id = (
  SELECT organization_id 
  FROM organization_members 
  WHERE user_id = product_attribute_values.user_id 
  LIMIT 1
)
WHERE organization_id IS NULL;

-- Make organization_id NOT NULL
ALTER TABLE product_attribute_values 
ALTER COLUMN organization_id SET NOT NULL;

-- Remove user_id column
ALTER TABLE product_attribute_values 
DROP COLUMN IF EXISTS user_id;

-- Update RLS policies for organization-based access
DROP POLICY IF EXISTS "Users can view their product attribute values" ON product_attribute_values;
DROP POLICY IF EXISTS "Users can insert product attribute values" ON product_attribute_values;
DROP POLICY IF EXISTS "Users can update their product attribute values" ON product_attribute_values;
DROP POLICY IF EXISTS "Users can delete their product attribute values" ON product_attribute_values;

-- Create new organization-based policies
CREATE POLICY "Organization members can view product attribute values" ON product_attribute_values
  FOR SELECT USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can insert product attribute values" ON product_attribute_values
  FOR INSERT WITH CHECK (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can update product attribute values" ON product_attribute_values
  FOR UPDATE USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can delete product attribute values" ON product_attribute_values
  FOR DELETE USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

-- Grant necessary permissions
GRANT ALL ON product_attribute_values TO authenticated;