'use client'

import { useState, useEffect, useMemo, useCallback } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Search, 
  Filter, 
  X, 
  SlidersHorizontal,
  RotateCcw
} from 'lucide-react'
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet'
import { Label } from '@/components/ui/label'
import { DatePicker } from '@/components/ui/date-picker'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { cn } from '@/lib/utils'

// Define the invoice filters type
export interface InvoiceFilters {
  searchQuery: string
  status: string[]
  dateRange: {
    from: Date | undefined
    to: Date | undefined
  }
}

export const INITIAL_INVOICE_FILTERS: InvoiceFilters = {
  searchQuery: '',
  status: [],
  dateRange: {
    from: undefined,
    to: undefined
  }
}

interface InvoiceTableHeaderProps {
  filters: InvoiceFilters
  onFiltersChange: (filters: InvoiceFilters) => void
  isMobile?: boolean
  className?: string
}

export function InvoiceTableHeader({
  filters,
  onFiltersChange,
  isMobile = false,
  className
}: InvoiceTableHeaderProps) {
  const [searchDebounce, setSearchDebounce] = useState(filters.searchQuery)
  const [isFilterSheetOpen, setIsFilterSheetOpen] = useState(false)

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      onFiltersChange({
        ...filters,
        searchQuery: searchDebounce
      })
    }, 300)
    
    return () => clearTimeout(timer)
  }, [searchDebounce, filters, onFiltersChange])

  // Available filter options
  const availableOptions = useMemo(() => {
    return {
      status: [
        { value: 'Draft', label: 'Draft' },
        { value: 'Sent', label: 'Sent' },
        { value: 'Paid', label: 'Paid' },
        { value: 'Overdue', label: 'Overdue' },
      ]
    }
  }, [])

  // Count active filters
  const activeFilterCount = useMemo(() => {
    let count = 0
    if (filters.searchQuery.trim()) count++
    if (filters.status.length > 0) count += filters.status.length
    if (filters.dateRange.from || filters.dateRange.to) count++
    return count
  }, [filters])

  // Clear all filters
  const clearAllFilters = useCallback(() => {
    setSearchDebounce('')
    onFiltersChange(INITIAL_INVOICE_FILTERS)
  }, [onFiltersChange])

  // Status toggle handler
  const handleStatusToggle = useCallback((status: string) => {
    const newStatuses = filters.status.includes(status)
      ? filters.status.filter(s => s !== status)
      : [...filters.status, status]
    
    onFiltersChange({
      ...filters,
      status: newStatuses
    })
  }, [filters, onFiltersChange])

  // Generate active filter badges
  const getActiveFilterBadges = useCallback(() => {
    const badges = []
    
    if (filters.searchQuery.trim()) {
      badges.push(
        <Badge key="search" className="bg-blue-50 text-blue-700 border-none text-xs py-1 px-2 flex items-center gap-1.5">
          Search: "{filters.searchQuery}"
          <Button 
            variant="ghost" 
            size="sm" 
            className="ml-1 h-3.5 w-3.5 p-0 hover:bg-blue-100"
            onClick={() => {
              setSearchDebounce('')
              onFiltersChange({ ...filters, searchQuery: '' })
            }}
          >
            <X className="h-3 w-3 cursor-pointer" />
          </Button>
        </Badge>
      )
    }
    
    if (filters.status.length > 0) {
      badges.push(
        <Badge key="status" className="bg-blue-50 text-blue-700 border-none text-xs py-1 px-2 flex items-center gap-1.5">
          Status: {filters.status.join(', ')}
          <Button 
            variant="ghost" 
            size="sm" 
            className="ml-1 h-3.5 w-3.5 p-0 hover:bg-blue-100"
            onClick={() => onFiltersChange({ ...filters, status: [] })}
          >
            <X className="h-3 w-3 cursor-pointer" />
          </Button>
        </Badge>
      )
    }
    
    if (filters.dateRange.from || filters.dateRange.to) {
      const dateText = `${filters.dateRange.from ? new Date(filters.dateRange.from).toLocaleDateString() : 'Any'} - ${filters.dateRange.to ? new Date(filters.dateRange.to).toLocaleDateString() : 'Any'}`
      badges.push(
        <Badge key="date-range" className="bg-blue-50 text-blue-700 border-none text-xs py-1 px-2 flex items-center gap-1.5">
          Date Range: {dateText}
          <Button 
            variant="ghost" 
            size="sm" 
            className="ml-1 h-3.5 w-3.5 p-0 hover:bg-blue-100"
            onClick={() => onFiltersChange({ ...filters, dateRange: { from: undefined, to: undefined } })}
          >
            <X className="h-3 w-3 cursor-pointer" />
          </Button>
        </Badge>
      )
    }
    
    return badges
  }, [filters, onFiltersChange])

  return (
    <div className={cn("space-y-4", className)}>
      {/* Top tier: Always visible filters */}
      <div className="flex flex-col sm:flex-row gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-gray-500" />
          <Input
            placeholder="Search invoices..."
            value={searchDebounce}
            onChange={(e) => setSearchDebounce(e.target.value)}
            className={cn(
              "pl-8 h-8 text-xs",
              isMobile && "text-sm min-h-[40px] touch-manipulation"
            )}
            autoComplete="off"
            spellCheck="false"
            autoCapitalize="none"
            autoCorrect="off"
          />
        </div>
        <div className="flex items-center gap-2">
          {isMobile ? (
            <Sheet open={isFilterSheetOpen} onOpenChange={setIsFilterSheetOpen}>
              <SheetTrigger asChild>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="relative touch-manipulation h-8 px-3 text-xs border-gray-300"
                >
                  <SlidersHorizontal className="h-3.5 w-3.5 mr-2" />
                  <span className="font-medium">Filters</span>
                  {activeFilterCount > 0 && (
                    <span className="ml-1.5 flex items-center justify-center w-4 h-4 rounded-full bg-red-500 text-white text-xs font-medium">
                      {activeFilterCount}
                    </span>
                  )}
                </Button>
              </SheetTrigger>
              <SheetContent 
                side="bottom" 
                className="h-[85vh] overflow-y-auto bg-background/95 backdrop-blur-sm"
                onOpenAutoFocus={(e) => e.preventDefault()}
              >
                <SheetHeader className="text-left pb-3 border-b">
                  <SheetTitle className="text-base font-semibold">Filter Invoices</SheetTitle>
                </SheetHeader>
                <div className="py-3 space-y-4">
                  {/* Status filter */}
                  <div className="space-y-2">
                    <Label className="text-xs font-medium text-foreground">Status</Label>
                    <div className="border border-border rounded-md bg-background/50 shadow-xs">
                      <div className="max-h-32 overflow-y-auto p-2 space-y-1">
                        {availableOptions.status.map(status => (
                          <label
                            key={status.value}
                            className={cn(
                              "flex items-center space-x-2 p-1.5 rounded transition-all cursor-pointer group",
                              "hover:bg-accent/50 hover:text-accent-foreground text-xs"
                            )}
                          >
                            <input
                              type="checkbox"
                              checked={filters.status.includes(status.value)}
                              onChange={() => handleStatusToggle(status.value)}
                              className="h-3 rounded border-border text-primary focus:ring-primary/20 focus:ring-1"
                            />
                            <span className="font-medium">{status.label}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>
                  
                  {/* Date Range filter */}
                  <div className="space-y-2">
                    <Label className="text-xs font-medium text-foreground">Date Range</Label>
                    <div className="space-y-2 p-2 border border-border rounded-md bg-background/50 shadow-xs">
                      <div className="flex gap-2">
                        <div className="flex-1">
                          <Label className="text-xs font-medium text-muted-foreground uppercase tracking-wide sr-only">From</Label>
                          <DatePicker
                            date={filters.dateRange.from}
                            onDateChange={(date) => onFiltersChange({
                              ...filters,
                              dateRange: { 
                                ...filters.dateRange, 
                                from: date || undefined
                              }
                            })}
                            placeholder="From"
                            className="w-full h-8 text-xs px-2 py-1"
                          />
                        </div>
                        <div className="flex-1">
                          <Label className="text-xs font-medium text-muted-foreground uppercase tracking-wide sr-only">To</Label>
                          <DatePicker
                            date={filters.dateRange.to}
                            onDateChange={(date) => onFiltersChange({
                              ...filters,
                              dateRange: { 
                                ...filters.dateRange, 
                                to: date || undefined
                              }
                            })}
                            placeholder="To"
                            className="w-full h-8 text-xs px-2 py-1"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="sticky bottom-0 bg-background/95 backdrop-blur-sm pt-3 border-t">
                  <div className="flex gap-2">
                    <Button 
                      onClick={clearAllFilters} 
                      variant="outline" 
                      size="sm"
                      className="flex-1 touch-manipulation h-9 px-3 text-xs border-gray-300"
                      disabled={activeFilterCount === 0}
                    >
                      <RotateCcw className="h-3.5 w-3.5 mr-1.5" />
                      Clear All {activeFilterCount > 0 && `(${activeFilterCount})`}
                    </Button>
                    <Button 
                      onClick={() => setIsFilterSheetOpen(false)}
                      size="sm"
                      className="flex-1 touch-manipulation h-9 font-medium text-xs"
                    >
                      Apply Filters
                    </Button>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          ) : (
            <Button 
              variant="outline" 
              size="sm" 
              className="h-8 text-xs relative"
              onClick={() => setIsFilterSheetOpen(true)}
            >
              <SlidersHorizontal className="h-3.5 w-3.5 mr-1.5" />
              <span className="font-medium">Filters</span>
              {activeFilterCount > 0 && (
                <span className="ml-1.5 flex items-center justify-center w-4 h-4 rounded-full bg-red-500 text-white text-xs font-medium">
                  {activeFilterCount}
                </span>
              )}
            </Button>
          )}
        </div>
      </div>
      
      {/* Active filters indicator */}
      {activeFilterCount > 0 && (
        <div className="flex flex-wrap gap-1.5">
          {getActiveFilterBadges()}
        </div>
      )}
    </div>
  )
}