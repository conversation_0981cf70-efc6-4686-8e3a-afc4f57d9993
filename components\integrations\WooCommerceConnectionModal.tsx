'use client';

import { useState } from 'react';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogFooter 
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { useConnectWooCommerce } from '@/hooks/use-connect-woocommerce';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, CheckCircle, Info } from 'lucide-react';

interface WooCommerceConnectionModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
  isConnected: boolean;
}

export default function WooCommerceConnectionModal({
  open,
  onOpenChange,
  onSuccess,
  isConnected
}: WooCommerceConnectionModalProps) {
  const { toast } = useToast();
  const [storeUrl, setStoreUrl] = useState('');
  const [consumerKey, setConsumerKey] = useState('');
  const [consumerSecret, setConsumerSecret] = useState('');
  const [connectionTestResult, setConnectionTestResult] = useState<{success: boolean; message: string} | null>(null);
  
  const { mutate: connectWooCommerce, isPending } = useConnectWooCommerce({
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'WooCommerce store connected successfully. Please register the webhook to enable automatic order syncing.',
      });
      onSuccess();
      // Reset form
      setStoreUrl('');
      setConsumerKey('');
      setConsumerSecret('');
      setConnectionTestResult(null);
    },
    onError: (error: Error) => {
      toast({
        title: 'Connection Failed',
        description: error.message || 'Failed to connect to WooCommerce store',
        variant: 'destructive',
      });
    }
  });

  const handleTestConnection = async () => {
    setConnectionTestResult(null);
    
    if (!storeUrl || !consumerKey || !consumerSecret) {
      toast({
        title: 'Validation Error',
        description: 'Please fill in all fields',
        variant: 'destructive',
      });
      return;
    }
    
    // Validate URL format
    try {
      new URL(storeUrl);
    } catch (e) {
      toast({
        title: 'Validation Error',
        description: 'Please enter a valid store URL (e.g., https://yourstore.com)',
        variant: 'destructive',
      });
      return;
    }
    
    // Validate credentials format
    if (!consumerKey.startsWith('ck_') || !consumerSecret.startsWith('cs_')) {
      toast({
        title: 'Validation Error',
        description: 'Please enter valid WooCommerce API credentials (Consumer Key should start with "ck_" and Consumer Secret should start with "cs_")',
        variant: 'destructive',
      });
      return;
    }

    try {
      // Test connection to WooCommerce API
      const credentials = btoa(`${consumerKey}:${consumerSecret}`);
      const response = await fetch(`${storeUrl.replace(/\/$/, '')}/wp-json/wc/v3/system_status`, {
        headers: {
          'Authorization': `Basic ${credentials}`,
        },
      });
      
      if (response.ok) {
        setConnectionTestResult({
          success: true,
          message: 'Successfully connected to WooCommerce store'
        });
      } else {
        const errorText = await response.text();
        setConnectionTestResult({
          success: false,
          message: `Connection failed: ${response.status} ${errorText}`
        });
      }
    } catch (error: any) {
      setConnectionTestResult({
        success: false,
        message: `Connection failed: ${error.message || 'Unknown error'}. Please check your store URL and credentials.`
      });
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!storeUrl || !consumerKey || !consumerSecret) {
      toast({
        title: 'Validation Error',
        description: 'Please fill in all fields',
        variant: 'destructive',
      });
      return;
    }
    
    // Validate URL format
    try {
      new URL(storeUrl);
    } catch (e) {
      toast({
        title: 'Validation Error',
        description: 'Please enter a valid store URL (e.g., https://yourstore.com)',
        variant: 'destructive',
      });
      return;
    }
    
    // Validate credentials format
    if (!consumerKey.startsWith('ck_') || !consumerSecret.startsWith('cs_')) {
      toast({
        title: 'Validation Error',
        description: 'Please enter valid WooCommerce API credentials (Consumer Key should start with "ck_" and Consumer Secret should start with "cs_")',
        variant: 'destructive',
      });
      return;
    }

    connectWooCommerce({
      storeUrl,
      consumerKey,
      consumerSecret
    });
  };

  return (
    <Dialog open={open} onOpenChange={(isOpen) => {
      onOpenChange(isOpen);
      // Reset form when dialog is closed
      if (!isOpen) {
        setStoreUrl('');
        setConsumerKey('');
        setConsumerSecret('');
        setConnectionTestResult(null);
      }
    }}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Connect to WooCommerce</DialogTitle>
          <DialogDescription>
            Enter your WooCommerce store credentials to connect your store with Onko.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="store-url">Store URL</Label>
              <Input
                id="store-url"
                placeholder="https://yourstore.com"
                value={storeUrl}
                onChange={(e) => setStoreUrl(e.target.value)}
                disabled={isPending}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="consumer-key">Consumer Key</Label>
              <Input
                id="consumer-key"
                placeholder="ck_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                value={consumerKey}
                onChange={(e) => setConsumerKey(e.target.value)}
                disabled={isPending}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="consumer-secret">Consumer Secret</Label>
              <Input
                id="consumer-secret"
                type="password"
                placeholder="cs_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                value={consumerSecret}
                onChange={(e) => setConsumerSecret(e.target.value)}
                disabled={isPending}
              />
            </div>
            
            <Alert variant="default">
              <Info className="h-4 w-4" />
              <AlertDescription>
                <div>
                  Make sure your WooCommerce REST API is enabled and the credentials have read/write permissions.
                </div>
                <div className="mt-2">
                  <a 
                    href="/WOO_COMMERCE_INTEGRATION_GUIDE.md" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:underline"
                  >
                    View detailed setup guide
                  </a>
                </div>
              </AlertDescription>
            </Alert>
            
            {connectionTestResult && (
              <Alert variant={connectionTestResult.success ? "default" : "destructive"}>
                {connectionTestResult.success ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  <AlertCircle className="h-4 w-4" />
                )}
                <AlertDescription>
                  {connectionTestResult.message}
                </AlertDescription>
              </Alert>
            )}
          </div>
          <DialogFooter className="gap-2">
            <Button 
              type="button" 
              variant="outline" 
              onClick={handleTestConnection}
              disabled={isPending}
            >
              Test Connection
            </Button>
            <Button type="submit" disabled={isPending}>
              {isPending ? 'Connecting...' : 'Connect Store'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}