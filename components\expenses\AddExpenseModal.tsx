import React from 'react'
import { 
  <PERSON><PERSON>, 
  <PERSON>alog<PERSON>ontent, 
  <PERSON><PERSON><PERSON>eader, 
  DialogTitle,
  DialogDescription
} from '@/components/ui/dialog'
import { AddExpenseForm } from './add-expense-form'

interface AddExpenseModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onExpenseAdded?: () => void
}

export function AddExpenseModal({ open, onOpenChange, onExpenseAdded }: AddExpenseModalProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl p-0 gap-0" hideCloseButton>
        <DialogHeader className="hidden">
          <DialogTitle>Add New Expense</DialogTitle>
          <DialogDescription>
            Record a new expense
          </DialogDescription>
        </DialogHeader>
        <div className="flex h-full bg-white rounded-lg overflow-hidden relative">
          {/* Custom close button positioned to avoid interfering with content */}
          <button
            onClick={() => onOpenChange(false)}
            className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground z-10"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
            <span className="sr-only">Close</span>
          </button>
          
          <div className="flex-1 flex flex-col overflow-hidden p-6">
            <AddExpenseForm 
              onExpenseAdded={() => {
                onExpenseAdded?.()
                onOpenChange(false)
              }}
              isCollapsible={false}
              initiallyExpanded={true}
              className="border-0 shadow-none p-0"
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}