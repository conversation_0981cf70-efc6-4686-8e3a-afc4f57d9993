-- =============================================
-- PURCHASE ORDERS TABLES
-- =============================================

-- Create purchase_orders table
create table if not exists purchase_orders (
  id uuid default gen_random_uuid() primary key,
  user_id uuid references auth.users(id) on delete cascade not null,
  
  -- Purchase Order Information
  po_number text not null, -- Format: PO-2025-08-31-001
  supplier text not null, -- Supplier name
  status text not null check (status in (
    'draft',           -- Draft order
    'open',            -- Sent to supplier
    'partially_received', -- Some items received
    'received',        -- All items received
    'cancelled'        -- Cancelled order
  )) default 'draft',
  
  -- Dates
  issue_date date not null default current_date, -- Date PO was created
  expected_arrival_date date, -- Expected delivery date
  received_date date, -- Date when fully received
  
  -- Financial Information
  total_value decimal(10,2) default 0, -- Total order value
  currency text default 'USD', -- Currency code
  
  -- Additional Information
  notes text, -- Additional notes
  reference_number text, -- Supplier reference number
  
  -- Metadata
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null,
  
  -- Ensure unique PO number per user
  unique(user_id, po_number)
);

-- Create purchase_order_items table for line items
create table if not exists purchase_order_items (
  id uuid default gen_random_uuid() primary key,
  purchase_order_id uuid references purchase_orders(id) on delete cascade not null,
  user_id uuid references auth.users(id) on delete cascade not null,
  
  -- Product/Variant Reference
  product_id uuid references products(id) on delete set null, -- For simple products
  variant_id uuid references product_variants(id) on delete set null, -- For variants
  
  -- Item Information
  product_name text not null, -- Product name at time of order
  sku text, -- SKU at time of order
  description text, -- Description at time of order
  
  -- Quantity and Pricing
  quantity_ordered integer not null check (quantity_ordered > 0),
  quantity_received integer default 0 check (quantity_received >= 0),
  unit_cost decimal(10,2) not null, -- Cost per unit
  subtotal decimal(10,2) generated always as (quantity_ordered * unit_cost) stored,
  
  -- Metadata
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null,
  
  -- Ensure exactly one of product_id or variant_id is set
  check (
    (product_id is not null and variant_id is null) or 
    (product_id is null and variant_id is not null)
  )
);

-- Create function to handle updated_at for purchase_orders
create or replace function handle_purchase_orders_updated_at()
returns trigger as $$
begin
  new.updated_at = timezone('utc'::text, now());
  return new;
end;
$$ language plpgsql;

-- Create function to handle updated_at for purchase_order_items
create or replace function handle_purchase_order_items_updated_at()
returns trigger as $$
begin
  new.updated_at = timezone('utc'::text, now());
  return new;
end;
$$ language plpgsql;

-- Create triggers for updated_at
create trigger purchase_orders_updated_at before update on purchase_orders
  for each row execute procedure handle_purchase_orders_updated_at();

create trigger purchase_order_items_updated_at before update on purchase_order_items
  for each row execute procedure handle_purchase_order_items_updated_at();

-- Create function to automatically update purchase order total when items change
create or replace function update_purchase_order_total()
returns trigger as $$
begin
  -- Update the total value of the purchase order
  update purchase_orders
  set total_value = (
    select coalesce(sum(subtotal), 0)
    from purchase_order_items
    where purchase_order_id = coalesce(new.purchase_order_id, old.purchase_order_id)
  )
  where id = coalesce(new.purchase_order_id, old.purchase_order_id);
  
  return coalesce(new, old);
end;
$$ language plpgsql;

-- Create triggers to automatically update purchase order total
create trigger update_po_total_insert after insert on purchase_order_items
  for each row execute procedure update_purchase_order_total();

create trigger update_po_total_update after update on purchase_order_items
  for each row execute procedure update_purchase_order_total();

create trigger update_po_total_delete after delete on purchase_order_items
  for each row execute procedure update_purchase_order_total();

-- Create function to automatically update purchase order status based on received quantities
create or replace function update_purchase_order_status()
returns trigger as $$
declare
  total_items integer;
  received_items integer;
  new_status text;
begin
  -- Count total items and received items for this purchase order
  select 
    count(*),
    count(*) filter (where quantity_ordered <= quantity_received)
  into total_items, received_items
  from purchase_order_items
  where purchase_order_id = coalesce(new.purchase_order_id, old.purchase_order_id);
  
  -- Determine new status based on received quantities
  if total_items = 0 then
    new_status := 'draft';
  elsif received_items = 0 then
    new_status := 'open';
  elsif received_items = total_items then
    new_status := 'received';
    -- Update received_date if not already set
    update purchase_orders
    set received_date = current_date
    where id = coalesce(new.purchase_order_id, old.purchase_order_id)
    and received_date is null;
  else
    new_status := 'partially_received';
  end if;
  
  -- Update the status of the purchase order
  update purchase_orders
  set status = new_status
  where id = coalesce(new.purchase_order_id, old.purchase_order_id);
  
  return coalesce(new, old);
end;
$$ language plpgsql;

-- Create triggers to automatically update purchase order status
create trigger update_po_status_insert after insert on purchase_order_items
  for each row execute procedure update_purchase_order_status();

create trigger update_po_status_update after update on purchase_order_items
  for each row execute procedure update_purchase_order_status();

create trigger update_po_status_delete after delete on purchase_order_items
  for each row execute procedure update_purchase_order_status();

-- Create function to generate PO number in PO-YYYY-MM-DD-XXX format
create or replace function generate_po_number(user_uuid uuid)
returns text as $$
declare
  next_number integer;
  po_number_text text;
begin
  -- Get the next PO number for this user today
  select coalesce(max(cast(substring(po_number from 15) as integer)), 0) + 1
  into next_number
  from purchase_orders
  where user_id = user_uuid
    and po_number is not null
    and po_number ~ ('^PO-' || to_char(current_date, 'YYYY-MM-DD') || '-[0-9]+$');
  
  -- Format as PO-YYYY-MM-DD-001, PO-YYYY-MM-DD-002, etc.
  po_number_text := 'PO-' || to_char(current_date, 'YYYY-MM-DD') || '-' || lpad(next_number::text, 3, '0');
  
  return po_number_text;
end;
$$ language plpgsql security definer;

-- Trigger function to auto-generate PO number before insert
create or replace function set_po_number()
returns trigger as $$
begin
  if new.po_number is null then
    new.po_number := generate_po_number(new.user_id);
  end if;
  return new;
end;
$$ language plpgsql;

-- Create trigger for auto-generating PO numbers
create trigger purchase_orders_set_po_number
  before insert on purchase_orders
  for each row
  execute function set_po_number();

-- =============================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =============================================

-- Purchase orders policies
alter table purchase_orders enable row level security;

create policy "Users can view own purchase orders" on purchase_orders
  for select using (auth.uid() = user_id);

create policy "Users can create own purchase orders" on purchase_orders
  for insert with check (auth.uid() = user_id);

create policy "Users can update own purchase orders" on purchase_orders
  for update using (auth.uid() = user_id);

create policy "Users can delete own purchase orders" on purchase_orders
  for delete using (auth.uid() = user_id);

-- Purchase order items policies
alter table purchase_order_items enable row level security;

create policy "Users can view own purchase order items" on purchase_order_items
  for select using (auth.uid() = user_id);

create policy "Users can create own purchase order items" on purchase_order_items
  for insert with check (auth.uid() = user_id);

create policy "Users can update own purchase order items" on purchase_order_items
  for update using (auth.uid() = user_id);

create policy "Users can delete own purchase order items" on purchase_order_items
  for delete using (auth.uid() = user_id);

-- =============================================
-- INDEXES for Performance
-- =============================================

-- Purchase orders indexes
create index if not exists idx_purchase_orders_user_id on purchase_orders(user_id);
create index if not exists idx_purchase_orders_po_number on purchase_orders(po_number);
create index if not exists idx_purchase_orders_status on purchase_orders(status);
create index if not exists idx_purchase_orders_issue_date on purchase_orders(issue_date);
create index if not exists idx_purchase_orders_supplier on purchase_orders(supplier);

-- Purchase order items indexes
create index if not exists idx_purchase_order_items_purchase_order_id on purchase_order_items(purchase_order_id);
create index if not exists idx_purchase_order_items_user_id on purchase_order_items(user_id);
create index if not exists idx_purchase_order_items_product_id on purchase_order_items(product_id);
create index if not exists idx_purchase_order_items_variant_id on purchase_order_items(variant_id);