-- =============================================
-- MIGRATE CATEGORIES TABLE TO ORGANIZATION MODEL
-- =============================================

-- Add organization_id column to categories table
ALTER TABLE categories 
ADD COLUMN IF NOT EXISTS organization_id uuid REFERENCES organizations(id) ON DELETE CASCADE;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_categories_organization_id ON categories(organization_id);

-- Backfill organization_id from user_id
UPDATE categories 
SET organization_id = (
  SELECT organization_id 
  FROM organization_members 
  WHERE user_id = categories.user_id 
  LIMIT 1
)
WHERE organization_id IS NULL;

-- Make organization_id NOT NULL
ALTER TABLE categories 
ALTER COLUMN organization_id SET NOT NULL;

-- Remove user_id column
ALTER TABLE categories 
DROP COLUMN IF EXISTS user_id;

-- Update RLS policies for organization-based access
DROP POLICY IF EXISTS "Users can view their categories" ON categories;
DROP POLICY IF EXISTS "Users can insert categories" ON categories;
DROP POLICY IF EXISTS "Users can update their categories" ON categories;
DROP POLICY IF EXISTS "Users can delete their categories" ON categories;

-- Create new organization-based policies
CREATE POLICY "Organization members can view categories" ON categories
  FOR SELECT USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can insert categories" ON categories
  FOR INSERT WITH CHECK (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can update categories" ON categories
  FOR UPDATE USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can delete categories" ON categories
  FOR DELETE USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

-- Grant necessary permissions
GRANT ALL ON categories TO authenticated;