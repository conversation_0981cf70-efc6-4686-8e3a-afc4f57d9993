'use client'

import { useState } from 'react'
import { type InventoryItem } from './types'

export function useModalManagement() {
  const [isAdjustStockModalOpen, setIsAdjustStockModalOpen] = useState(false)
  const [isStockHistoryModalOpen, setIsStockHistoryModalOpen] = useState(false)
  const [isPurchaseOrderModalOpen, setIsPurchaseOrderModalOpen] = useState(false)
  const [selectedItem, setSelectedItem] = useState<InventoryItem | null>(null)

  return {
    isAdjustStockModalOpen,
    setIsAdjustStockModalOpen,
    isStockHistoryModalOpen,
    setIsStockHistoryModalOpen,
    isPurchaseOrderModalOpen,
    setIsPurchaseOrderModalOpen,
    selectedItem,
    setSelectedItem
  }
}