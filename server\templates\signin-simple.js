class SignInTemplate {
  generate() {
    return [
      '<!DOCTYPE html>',
      '<html lang="en">',
      '<head>',
      '    <meta charset="UTF-8">',
      '    <meta name="viewport" content="width=device-width, initial-scale=1.0">',
      '    <title>Sign In - ONKO</title>',
      '    <!-- TODO: Replace with PostCSS Tailwind in production - CDN is for development only -->',
      '    <script src="https://cdn.tailwindcss.com"></script>',
      '</head>',
      '<body class="bg-gray-50 min-h-screen flex items-center justify-center">',
      '    <div class="max-w-md w-full space-y-8 p-8">',
      '        <div class="text-center">',
      '            <h1 class="text-4xl font-bold text-gray-900 mb-2">ONKO</h1>',
      '            <p class="text-gray-600 mb-8">Business Management Platform</p>',
      '            <h2 class="text-2xl font-bold text-gray-900">Sign In</h2>',
      '            <p class="text-gray-600">Enter your credentials to access your account</p>',
      '        </div>',
      '        ',
      '        <form id="signinForm" class="space-y-6">',
      '            <div>',
      '                <label class="block text-sm font-medium text-gray-700">Email</label>',
      '                <input id="email" type="email" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="<EMAIL>" required>',
      '            </div>',
      '            <div>',
      '                <label class="block text-sm font-medium text-gray-700">Password</label>',
      '                <input id="password" type="password" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="Enter your password" required>',
      '            </div>',
      '            <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">Sign In</button>',
      '        </form>',
      '        ',
      '        <div class="text-center space-y-2">',
      '            <a href="/auth/signup" class="text-sm text-blue-600 hover:underline">Do not have an account? Sign up</a>',
      '        </div>',
      '        ',
      '        <div class="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">',
      '            <p class="text-sm text-green-800">',
      '                <strong>✅ Production Ready:</strong> Real Supabase authentication with JWT tokens and Row Level Security.',
      '            </p>',
      '        </div>',
      '',
      '        <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">',
      '            <p class="text-sm text-blue-800">',
      '                <strong>Demo:</strong> For testing, click "Sign In" to see the dashboard with your real data!',
      '            </p>',
      '        </div>',
      '    </div>',
      '    ',
      '    <script>',
      '        document.getElementById("signinForm").addEventListener("submit", async function(e) {',
      '            e.preventDefault();',
      '            console.log("Form submitted");',
      '            ',
      '            const email = document.getElementById("email").value;',
      '            const password = document.getElementById("password").value;',
      '            const submitButton = e.target.querySelector("button[type=\\"submit\\"]");',
      '            ',
      '            if (!email || !password) {',
      '                alert("Please fill in all fields");',
      '                return;',
      '            }',
      '            ',
      '            submitButton.disabled = true;',
      '            submitButton.textContent = "Signing In...";',
      '            ',
      '            try {',
      '                const response = await fetch("/api/auth/signin", {',
      '                    method: "POST",',
      '                    headers: {',
      '                        "Content-Type": "application/json"',
      '                    },',
      '                    body: JSON.stringify({',
      '                        email: email,',
      '                        password: password',
      '                    })',
      '                });',
      '                ',
      '                const result = await response.json();',
      '                console.log("API result:", result);',
      '                ',
      '                if (result.success) {',
      '                    if (result.data.accessToken) {',
      '                        localStorage.setItem("onko_auth_token", result.data.accessToken);',
      '                        localStorage.setItem("onko_user", JSON.stringify(result.data.user));',
      '                    }',
      '                    alert("Sign in successful! Redirecting to dashboard...");',
      '                    window.location.href = "/dashboard";',
      '                } else {',
      '                    if (result.error.includes("Email not confirmed")) {',
      '                        alert("Please check your email " + email + " and click the confirmation link before signing in. Check your spam folder too.");',
      '                    } else {',
      '                        alert("Sign in failed: " + result.error);',
      '                    }',
      '                }',
      '            } catch (error) {',
      '                console.error("Sign in error:", error);',
      '                alert("Network error occurred. Please try again.");',
      '            } finally {',
      '                submitButton.disabled = false;',
      '                submitButton.textContent = "Sign In";',
      '            }',
      '        });',
      '    </script>',
      '</body>',
      '</html>'
    ].join('\n');
  }
}

module.exports = new SignInTemplate();