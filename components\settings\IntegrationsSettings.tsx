'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { WooCommerceConnectionModal, WooCommerceStatusCard, VaultConnectionModal, VaultStatusCard } from '@/components/integrations';
import type { Integration, VaultIntegration } from '@/types/integrations';

export default function IntegrationsSettings() {
  const { toast } = useToast();
  const [woocommerceIntegration, setWooCommerceIntegration] = useState<Integration | null>(null);
  const [vaultIntegration, setVaultIntegration] = useState<VaultIntegration | null>(null);
  const [isWooCommerceModalOpen, setIsWooCommerceModalOpen] = useState(false);
  const [isVaultModalOpen, setIsVaultModalOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState<any>(null);
  const [isOrgMember, setIsOrgMember] = useState(false);
  const supabase = createClientComponentClient();

  // Check connection status
  useEffect(() => {
    const fetchConnectionStatus = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        if (!session) {
          setLoading(false);
          return;
        }

        setUser(session.user);

        // Check if user is member of an organization
        const { data: orgMembership, error: orgError } = await supabase
          .from('organization_members')
          .select('organization_id')
          .eq('user_id', session.user.id)
          .single();

        if (orgError && orgError.code !== 'PGRST116') {
          throw orgError;
        }

        if (!orgMembership) {
          // User is not a member of any organization
          setIsOrgMember(false);
          setLoading(false);
          return;
        }

        setIsOrgMember(true);

        // Fetch WooCommerce integration
        const { data: wooData, error: wooError } = await supabase
          .from('integrations')
          .select('*')
          .eq('platform', 'woocommerce')
          .eq('organization_id', orgMembership.organization_id)
          .single();

        if (wooError && wooError.code !== 'PGRST116') {
          throw wooError;
        }

        setWooCommerceIntegration(wooData || null);

        // Fetch Vault integration
        const { data: vaultData, error: vaultError } = await supabase
          .from('integrations')
          .select('*')
          .eq('platform', 'vault')
          .eq('organization_id', orgMembership.organization_id)
          .single();

        if (vaultError && vaultError.code !== 'PGRST116') {
          throw vaultError;
        }

        setVaultIntegration(vaultData || null);
      } catch (error: any) {
        console.error('Error fetching connection status:', error);
        toast({
          title: 'Error',
          description: error.message || 'Failed to fetch connection status',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchConnectionStatus();
  }, [supabase, toast]);

  const handleConnectWooCommerce = () => {
    if (!user) {
      toast({
        title: 'Authentication Required',
        description: 'Please sign in to connect your WooCommerce store.',
        variant: 'destructive',
      });
      return;
    }
    
    if (!isOrgMember) {
      toast({
        title: 'Organization Required',
        description: 'You must be a member of an organization to connect integrations. Please contact your organization owner or create an organization first.',
        variant: 'destructive',
      });
      return;
    }
    
    setIsWooCommerceModalOpen(true);
  };

  const handleConnectVault = () => {
    if (!user) {
      toast({
        title: 'Authentication Required',
        description: 'Please sign in to connect your Vault.',
        variant: 'destructive',
      });
      return;
    }
    
    if (!isOrgMember) {
      toast({
        title: 'Organization Required',
        description: 'You must be a member of an organization to connect integrations. Please contact your organization owner or create an organization first.',
        variant: 'destructive',
      });
      return;
    }
    
    setIsVaultModalOpen(true);
  };

  const handleWooCommerceConnectionSuccess = () => {
    // Refetch the integration data
    const fetchIntegration = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        if (!session) return;

        // Check if user is member of an organization
        const { data: orgMembership } = await supabase
          .from('organization_members')
          .select('organization_id')
          .eq('user_id', session.user.id)
          .single();

        if (!orgMembership) return;

        const { data } = await supabase
          .from('integrations')
          .select('*')
          .eq('platform', 'woocommerce')
          .eq('organization_id', orgMembership.organization_id)
          .single();

        setWooCommerceIntegration(data || null);
      } catch (error: any) {
        console.error('Error fetching integration:', error);
        toast({
          title: 'Error',
          description: error.message || 'Failed to fetch integration data',
          variant: 'destructive',
        });
      }
    };

    fetchIntegration();
    setIsWooCommerceModalOpen(false);
    toast({
      title: 'Success',
      description: 'WooCommerce store connected successfully',
    });
  };

  const handleVaultConnectionSuccess = () => {
    // Refetch the integration data
    const fetchIntegration = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        if (!session) return;

        // Check if user is member of an organization
        const { data: orgMembership } = await supabase
          .from('organization_members')
          .select('organization_id')
          .eq('user_id', session.user.id)
          .single();

        if (!orgMembership) return;

        const { data } = await supabase
          .from('integrations')
          .select('*')
          .eq('platform', 'vault')
          .eq('organization_id', orgMembership.organization_id)
          .single();

        setVaultIntegration(data || null);
      } catch (error: any) {
        console.error('Error fetching integration:', error);
        toast({
          title: 'Error',
          description: error.message || 'Failed to fetch integration data',
          variant: 'destructive',
        });
      }
    };

    fetchIntegration();
    setIsVaultModalOpen(false);
    toast({
      title: 'Success',
      description: 'Vault connected successfully',
    });
  };

  const handleWooCommerceDisconnect = () => {
    setWooCommerceIntegration(null);
  };

  const handleVaultDisconnect = () => {
    setVaultIntegration(null);
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  // If user is not authenticated
  if (!user) {
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium">Integrations</h3>
          <p className="text-sm text-muted-foreground">
            Connect your business tools to automate workflows and sync data
          </p>
        </div>
        <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
          <h4 className="font-medium text-yellow-800">Authentication Required</h4>
          <p className="text-sm text-yellow-700">
            Please sign in to connect your integrations.
          </p>
        </div>
      </div>
    );
  }

  // If user is not a member of any organization
  if (!isOrgMember) {
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium">Integrations</h3>
          <p className="text-sm text-muted-foreground">
            Connect your business tools to automate workflows and sync data
          </p>
        </div>
        <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
          <h4 className="font-medium text-yellow-800">Organization Required</h4>
          <p className="text-sm text-yellow-700">
            You must be a member of an organization to connect integrations. Please contact your organization owner or create an organization first.
          </p>
        </div>
        
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <Card className="flex flex-col opacity-50">
            <CardHeader>
              <div className="flex items-center space-x-3">
                <div className="bg-purple-100 p-2 rounded-md">
                  <div className="bg-purple-500 text-white p-2 rounded">
                    {/* WooCommerce Icon Placeholder */}
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M21 12.5c0 1.4-1.1 2.5-2.5 2.5H10V9h8.5c1.4 0 2.5 1.1 2.5 2.5z"/>
                      <path d="M16 9h2c1.7 0 3 1.3 3 3v3c0 1.7-1.3 3-3 3h-2"/>
                      <path d="M3 3v18h18"/>
                      <path d="M3 9h4c1.7 0 3 1.3 3 3v3c0 1.7-1.3 3-3 3H3"/>
                    </svg>
                  </div>
                </div>
                <CardTitle>WooCommerce</CardTitle>
              </div>
              <CardDescription>
                Sync your orders and inventory automatically from your WooCommerce store
              </CardDescription>
            </CardHeader>
            <CardContent className="flex-grow">
              <p className="text-sm text-muted-foreground">
                Connect your WooCommerce store to automatically sync products, orders, and customer data.
              </p>
            </CardContent>
            <CardFooter>
              <Button 
                onClick={handleConnectWooCommerce}
                className="w-full"
                disabled
              >
                Connect
              </Button>
            </CardFooter>
          </Card>
          
          <Card className="flex flex-col opacity-50">
            <CardHeader>
              <div className="flex items-center space-x-3">
                <div className="bg-blue-100 p-2 rounded-md">
                  <div className="bg-blue-500 text-white p-2 rounded">
                    {/* Vault Icon Placeholder */}
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
                      <path d="M9.5 9h5L16 12h-5Z"/>
                      <path d="M12 9v5"/>
                    </svg>
                  </div>
                </div>
                <CardTitle>Vault</CardTitle>
              </div>
              <CardDescription>
                Securely store and manage your financial data with Vault
              </CardDescription>
            </CardHeader>
            <CardContent className="flex-grow">
              <p className="text-sm text-muted-foreground">
                Connect your Vault to securely store and manage financial data.
              </p>
            </CardContent>
            <CardFooter>
              <Button 
                onClick={handleConnectVault}
                className="w-full"
                disabled
              >
                Connect
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Integrations</h3>
        <p className="text-sm text-muted-foreground">
          Connect your business tools to automate workflows and sync data
        </p>
        <div className="mt-2">
          <a 
            href="/WOO_COMMERCE_INTEGRATION_GUIDE.md" 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-blue-600 hover:underline text-sm"
          >
            View WooCommerce Integration Guide
          </a>
        </div>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {woocommerceIntegration && woocommerceIntegration.status === 'connected' ? (
          <WooCommerceStatusCard 
            integration={woocommerceIntegration} 
            onDisconnect={handleWooCommerceDisconnect} 
          />
        ) : (
          <Card className="flex flex-col">
            <CardHeader>
              <div className="flex items-center space-x-3">
                <div className="bg-purple-100 p-2 rounded-md">
                  <div className="bg-purple-500 text-white p-2 rounded">
                    {/* WooCommerce Icon Placeholder */}
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M21 12.5c0 1.4-1.1 2.5-2.5 2.5H10V9h8.5c1.4 0 2.5 1.1 2.5 2.5z"/>
                      <path d="M16 9h2c1.7 0 3 1.3 3 3v3c0 1.7-1.3 3-3 3h-2"/>
                      <path d="M3 3v18h18"/>
                      <path d="M3 9h4c1.7 0 3 1.3 3 3v3c0 1.7-1.3 3-3 3H3"/>
                    </svg>
                  </div>
                </div>
                <CardTitle>WooCommerce</CardTitle>
              </div>
              <CardDescription>
                Sync your orders and inventory automatically from your WooCommerce store
              </CardDescription>
            </CardHeader>
            <CardContent className="flex-grow">
              <p className="text-sm text-muted-foreground">
                Connect your WooCommerce store to automatically sync products, orders, and customer data.
              </p>
            </CardContent>
            <CardFooter>
              <Button 
                onClick={handleConnectWooCommerce}
                className="w-full"
              >
                Connect
              </Button>
            </CardFooter>
          </Card>
        )}
        
        {vaultIntegration && vaultIntegration.status === 'connected' ? (
          <VaultStatusCard 
            integration={vaultIntegration} 
            onDisconnect={handleVaultDisconnect} 
          />
        ) : (
          <Card className="flex flex-col">
            <CardHeader>
              <div className="flex items-center space-x-3">
                <div className="bg-blue-100 p-2 rounded-md">
                  <div className="bg-blue-500 text-white p-2 rounded">
                    {/* Vault Icon Placeholder */}
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
                      <path d="M9.5 9h5L16 12h-5Z"/>
                      <path d="M12 9v5"/>
                    </svg>
                  </div>
                </div>
                <CardTitle>Vault</CardTitle>
              </div>
              <CardDescription>
                Securely store and manage your financial data with Vault
              </CardDescription>
            </CardHeader>
            <CardContent className="flex-grow">
              <p className="text-sm text-muted-foreground">
                Connect your Vault to securely store and manage financial data.
              </p>
            </CardContent>
            <CardFooter>
              <Button 
                onClick={handleConnectVault}
                className="w-full"
              >
                Connect
              </Button>
            </CardFooter>
          </Card>
        )}
      </div>

      {isWooCommerceModalOpen && (
        <WooCommerceConnectionModal 
          open={isWooCommerceModalOpen}
          onOpenChange={setIsWooCommerceModalOpen}
          onSuccess={handleWooCommerceConnectionSuccess}
          isConnected={!!woocommerceIntegration}
        />
      )}
      
      {isVaultModalOpen && (
        <VaultConnectionModal 
          open={isVaultModalOpen}
          onOpenChange={setIsVaultModalOpen}
          onSuccess={handleVaultConnectionSuccess}
          isConnected={!!vaultIntegration}
        />
      )}
    </div>
  );
}