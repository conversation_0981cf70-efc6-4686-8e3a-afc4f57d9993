// @ts-ignore
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
// @ts-ignore
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3';

interface ConnectVaultPayload {
  vault_url: string;
  api_key: string;
  account_id?: string;
}

interface WebhookResponse {
  success: boolean;
  integration_id?: string;
  message?: string;
  error?: string;
}

const connectVault = async (req: Request): Promise<Response> => {
  try {
    // Add CORS headers for all requests
    const corsHeaders = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Authorization, Content-Type',
      'Access-Control-Max-Age': '86400'
    };
    
    // Handle preflight OPTIONS request
    if (req.method === 'OPTIONS') {
      return new Response(null, {
        status: 204,
        headers: corsHeaders
      });
    }
    
    // Get the authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(
        JSON.stringify({ error: 'Missing or invalid Authorization header' }),
        { 
          status: 401, 
          headers: { 
            'Content-Type': 'application/json',
            ...corsHeaders
          } 
        }
      );
    }

    // Extract the token
    const token = authHeader.substring(7);
    
    // Create a Supabase client with the token
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        global: {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      }
    );

    // Parse the request body
    const payload: ConnectVaultPayload = await req.json();
    
    // Validate inputs
    if (!payload.vault_url || !payload.api_key) {
      return new Response(
        JSON.stringify({ error: 'Vault URL and API Key are required' }),
        { 
          status: 400, 
          headers: { 
            'Content-Type': 'application/json',
            ...corsHeaders
          } 
        }
      );
    }
    
    // Validate URL format
    try {
      new URL(payload.vault_url);
    } catch {
      return new Response(
        JSON.stringify({ error: 'Invalid Vault URL format' }),
        { 
          status: 400, 
          headers: { 
            'Content-Type': 'application/json',
            ...corsHeaders
          } 
        }
      );
    }

    // Get the current user's organization
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return new Response(
        JSON.stringify({ error: 'User not authenticated' }),
        { 
          status: 401, 
          headers: { 
            'Content-Type': 'application/json',
            ...corsHeaders
          } 
        }
      );
    }

    // Check if user is member of an organization
    const { data: orgMembership, error: orgError } = await supabase
      .from('organization_members')
      .select('organization_id')
      .eq('user_id', user.id)
      .single();

    if (orgError || !orgMembership) {
      return new Response(
        JSON.stringify({ error: 'User is not a member of any organization' }),
        { 
          status: 400, 
          headers: { 
            'Content-Type': 'application/json',
            ...corsHeaders
          } 
        }
      );
    }

    // Generate a secret reference
    const secretName = `vault_secret_${crypto.randomUUID()}`;
    
    // Check if integration already exists for this organization and platform
    const { data: existingIntegration, error: existingError } = await supabase
      .from('integrations')
      .select('id')
      .eq('organization_id', orgMembership.organization_id)
      .eq('platform', 'vault')
      .single();

    let integrationId: string;
    
    // If integration exists, update it
    if (existingIntegration) {
      const { error: updateError } = await supabase
        .from('integrations')
        .update({
          vault_url: payload.vault_url,
          consumer_key: payload.api_key,
          consumer_secret: payload.account_id || null,
          status: 'connected',
          secret_name: secretName,
          consumer_secret_ref: crypto.randomUUID(),
          updated_at: new Date()
        })
        .eq('id', existingIntegration.id);

      if (updateError) {
        throw updateError;
      }
      
      integrationId = existingIntegration.id;
    } else {
      // Insert new integration
      const { data: newIntegration, error: insertError } = await supabase
        .from('integrations')
        .insert({
          organization_id: orgMembership.organization_id,
          platform: 'vault',
          vault_url: payload.vault_url,
          consumer_key: payload.api_key,
          consumer_secret: payload.account_id || null,
          status: 'connected',
          secret_name: secretName,
          consumer_secret_ref: crypto.randomUUID()
        })
        .select('id')
        .single();

      if (insertError) {
        throw insertError;
      }
      
      integrationId = newIntegration.id;
    }

    // Test the connection to Vault API
    try {
      const testResponse = await fetch(`${payload.vault_url.replace(/\/$/, '')}/api/v1/accounts`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${payload.api_key}`,
          'Content-Type': 'application/json',
        },
      });

      if (!testResponse.ok) {
        console.warn('Vault connection test failed:', await testResponse.text());
      }
    } catch (testError) {
      console.warn('Vault connection test error:', testError);
    }

    // Return success result with integration ID
    const response: WebhookResponse = {
      success: true,
      integration_id: integrationId,
      message: 'Vault connected successfully'
    };

    return new Response(
      JSON.stringify(response),
      { 
        status: 200, 
        headers: { 
          'Content-Type': 'application/json',
          ...corsHeaders
        } 
      }
    );

  } catch (error: any) {
    console.error('Error connecting Vault:', error);
    
    // Return error result
    const response: WebhookResponse = {
      success: false,
      error: error.message || 'Failed to connect Vault'
    };
    
    return new Response(
      JSON.stringify(response),
      { 
        status: 500, 
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        } 
      }
    );
  }
};

// Serve the function
serve(connectVault);