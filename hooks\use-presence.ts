import { useState, useEffect, useCallback } from 'react'
import { getSupabaseClient } from '@/lib/supabase'
import type { RealtimePresence } from '@supabase/supabase-js'

// Define the type for online user presence
export interface PresenceUser {
  id: string
  email: string
  display_name?: string | null
  first_name?: string | null
  last_name?: string | null
  avatar_url?: string | null
  job_title?: string | null
}

export const usePresence = () => {
  const [onlineUsers, setOnlineUsers] = useState<PresenceUser[]>([])
  const [channel, setChannel] = useState<any>(null)
  const [currentUserId, setCurrentUserId] = useState<string | null>(null)

  // Get user's own profile info
  const getUserProfile = useCallback(async () => {
    const supabase = getSupabaseClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) return null
    
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('display_name, first_name, last_name, avatar_url, job_title')
      .eq('id', user.id)
      .single()
    
    if (error) {
      console.error('Error fetching user profile:', error)
      return null
    }
    
    // Set current user ID
    setCurrentUserId(user.id)
    
    return {
      id: user.id,
      email: user.email,
      ...profile
    }
  }, [])

  useEffect(() => {
    let isMounted = true
    const supabase = getSupabaseClient()
    
    const initializePresence = async () => {
      const userProfile = await getUserProfile()
      if (!userProfile || !isMounted) return
      
      // Create a channel for presence tracking
      const presenceChannel = supabase.channel('team_presence')
      
      // Track user's presence when they join
      presenceChannel
        .on('presence', { event: 'sync' }, () => {
          const presenceState = presenceChannel.presenceState()
          const users: PresenceUser[] = []
          
          // Process all presence states
          Object.keys(presenceState).forEach((key) => {
            const presences = presenceState[key] as any[]
            presences.forEach((presence) => {
              if (presence.user && presence.user.id !== userProfile.id) {
                users.push(presence.user)
              }
            })
          })
          
          if (isMounted) {
            setOnlineUsers(users)
          }
        })
        .on('presence', { event: 'join' }, ({ newPresences }) => {
          // Add new users to the online users list (excluding current user)
          const newUsers = newPresences
            .map((presence: any) => presence.user)
            .filter((user: PresenceUser) => user && user.id !== userProfile.id)
          
          if (newUsers.length > 0 && isMounted) {
            setOnlineUsers(prev => {
              const updated = [...prev]
              newUsers.forEach((newUser: PresenceUser) => {
                if (newUser && !updated.some(u => u.id === newUser.id)) {
                  updated.push(newUser)
                }
              })
              return updated
            })
          }
        })
        .on('presence', { event: 'leave' }, ({ leftPresences }) => {
          // Remove users from the online users list
          if (isMounted) {
            setOnlineUsers(prev => {
              const userIdsToRemove = leftPresences.map((presence: any) => presence.user?.id).filter(Boolean)
              return prev.filter(user => !userIdsToRemove.includes(user.id))
            })
          }
        })
        .subscribe(async (status) => {
          if (status === 'SUBSCRIBED') {
            // Track user presence when subscribed
            await presenceChannel.track({
              user: userProfile,
              online_at: new Date().toISOString(),
            })
          }
        })
      
      if (isMounted) {
        setChannel(presenceChannel)
      }
    }
    
    initializePresence()
    
    return () => {
      isMounted = false
      if (channel) {
        channel.unsubscribe()
      }
    }
  }, [getUserProfile])
  
  return { onlineUsers }
}