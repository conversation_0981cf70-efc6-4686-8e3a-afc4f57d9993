'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { 
  Popover,
  PopoverContent,
  PopoverTrigger 
} from '@/components/ui/popover'
import { Badge } from '@/components/ui/badge'
import { useCurrency } from '@/contexts/currency-context'
import { ChevronDown, X } from 'lucide-react'

interface ProductVariant {
  id: string
  sku: string
  variant_name: string
  size?: string
  color?: string
  material?: string
  style?: string
  cost_adjustment: number
  price: number
  stock_quantity: number
  is_active: boolean
}

interface SelectedVariant {
  variantId: string
  variantName: string
  sku: string
  unitCost: number
  variantAttributes: {
    size?: string
    color?: string
    material?: string
    style?: string
  }
}

interface VariantSelectorProps {
  variants: ProductVariant[]
  baseProductCost: number
  basePackagingCost: number
  value: SelectedVariant | null
  onChange: (variant: SelectedVariant | null) => void
  placeholder?: string
}

export function VariantSelector({ 
  variants, 
  baseProductCost, 
  basePackagingCost, 
  value, 
  onChange,
  placeholder = "Select variant..."
}: VariantSelectorProps) {
  const [open, setOpen] = useState(false)
  const { formatCurrency } = useCurrency()

  // Filter active variants
  const activeVariants = variants.filter(variant => variant.is_active)

  const handleSelect = (variant: ProductVariant) => {
    // As per user request, only show the base product cost, not including packaging or adjustments
    const totalCost = baseProductCost || 0
    
    const selectedVariant: SelectedVariant = {
      variantId: variant.id,
      variantName: variant.variant_name,
      sku: variant.sku,
      unitCost: totalCost,
      variantAttributes: {
        size: variant.size,
        color: variant.color,
        material: variant.material,
        style: variant.style
      }
    }
    
    onChange(selectedVariant)
    setOpen(false)
  }

  const handleClear = () => {
    onChange(null)
  }

  const renderVariantAttributes = (variant: ProductVariant) => {
    const attributes = []
    if (variant.size) attributes.push(`Size: ${variant.size}`)
    if (variant.color) attributes.push(`Color: ${variant.color}`)
    if (variant.material) attributes.push(`Material: ${variant.material}`)
    if (variant.style) attributes.push(`Style: ${variant.style}`)
    return attributes.join(', ')
  }



  if (activeVariants.length === 0) {
    return (
      <div className="text-xs text-muted-foreground p-2 border rounded-md bg-muted/50">
        No active variants available
      </div>
    )
  }

  return (
    <div>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <div className="relative">
            <Button
              variant="outline"
              className="w-full justify-between text-xs h-8 px-3 border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200"
              onClick={() => setOpen(true)}
            >
              <div className="flex-1 text-left">
                {value ? (
                  <div className="text-xs text-gray-700 dark:text-gray-300">
                    {value.variantName}
                  </div>
                ) : (
                  <span className="text-muted-foreground text-xs">{placeholder}</span>
                )}
              </div>
              <div className="flex items-center gap-1">
                {value ? (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-3 w-3 p-0"
                    onClick={(e) => {
                      e.stopPropagation()
                      handleClear()
                    }}
                  >
                    <X className="h-2 w-2" />
                  </Button>
                ) : (
                  <ChevronDown className="h-2 w-2" />
                )}
              </div>
            </Button>
          </div>
        </PopoverTrigger>
        <PopoverContent className="w-[400px] p-0" align="start">
          <div className="border rounded-md bg-background">
            <div className="px-2 py-1 text-xs font-medium text-muted-foreground border-b">
              Available Variants ({activeVariants.length})
            </div>
            <div className="max-h-[200px] overflow-y-auto">
              {activeVariants.map((variant) => {
                // As per user request, only show the base product cost, not including packaging or adjustments
                const totalCost = baseProductCost || 0
                
                return (
                  <div
                    key={variant.id}
                    onClick={() => handleSelect(variant)}
                    className="flex items-center justify-between cursor-pointer text-xs py-4 px-4 hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:border-blue-200 dark:hover:border-blue-700 border-b border-gray-100 dark:border-gray-700 last:border-b-0 transition-all duration-200 group"
                  >
                    <div className="flex-1">
                      <div className="font-medium text-gray-900 dark:text-gray-100">
                        {variant.variant_name}
                      </div>
                      <div className="text-muted-foreground text-xs mt-1">
                        {renderVariantAttributes(variant)}
                      </div>
                    </div>
                    <div className="text-right ml-2">
                      <div className="text-xs font-medium text-gray-900 dark:text-gray-100">
                        {formatCurrency(totalCost)}
                      </div>
                      <div className="text-muted-foreground text-xs">
                        SKU: {variant.sku}
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}