'use client'

import { But<PERSON> } from '@/components/ui/button'

export function SecuritySettings() {
  return (
    <div className="bg-white rounded-lg border border-gray-200">
      <div className="px-4 py-3 border-b border-gray-200">
        <h3 className="text-base font-medium text-gray-900">Security Settings</h3>
      </div>
      <div className="p-4">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">Two-Factor Authentication</h3>
              <p className="text-xs text-gray-500">Add an extra layer of security to your account</p>
            </div>
            <Button variant="outline" className="h-8 px-2 text-xs">
              Coming Soon
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}