'use client'

import React, { useState, useEffect, useMemo, useRef } from 'react'
import { 
  Package, 
  TrendingUp, 
  AlertTriangle, 
  XCircle,
  RefreshCw
} from 'lucide-react'
import { useAuth } from '@/contexts/auth-context'
import { useCurrency } from '@/lib/currency'
import { useToast } from '@/components/ui/use-toast'
import { EnhancedInventoryTable } from '../inventory/enhanced-inventory-table'
import { useInventory } from '@/hooks/use-inventory'
import { MetricCard } from '@/components/ui/metric-card'

export function InventoryTab() {
  const [debugInfo, setDebugInfo] = useState<any>(null)
  const { user, organizationId } = useAuth()
  const { formatCurrency } = useCurrency()
  const { toast } = useToast()
  const hasAttemptedRefetch = useRef(false)
  const refreshTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  
  // Use React Query hook for fetching inventory data
  const { 
    data: inventoryItems = [], 
    isLoading, 
    error, 
    refetch,
    isRefetching
  } = useInventory(organizationId || undefined)

  // Listen for refresh events from the adjust stock modal
  useEffect(() => {
    const handleRefresh = () => {
      // Clear any existing timeout to debounce rapid refresh calls
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current)
      }
      
      // Debounce the refresh by 300ms
      refreshTimeoutRef.current = setTimeout(() => {
        refetch()
      }, 300)
    }
    
    // Listen for inventory updates (e.g., when an order is created)
    const handleInventoryUpdated = () => {
      handleRefresh()
    }
    
    window.addEventListener('refreshInventoryData', handleRefresh)
    window.addEventListener('inventoryUpdated', handleInventoryUpdated)
    return () => {
      window.removeEventListener('refreshInventoryData', handleRefresh)
      window.removeEventListener('inventoryUpdated', handleInventoryUpdated)
      // Clean up timeout on unmount
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current)
      }
    }
  }, [refetch]) // Add refetch to dependency array

  // Handle tab visibility change to trigger refetch when tab becomes visible
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && !hasAttemptedRefetch.current) {
        hasAttemptedRefetch.current = true
        // When tab becomes visible, trigger a refetch if we're not already loading
        if (!isLoading) {
          refetch()
        }
        // Reset the flag after a delay to allow future refetches
        setTimeout(() => {
          hasAttemptedRefetch.current = false
        }, 5000)
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [isLoading, refetch])

  // Calculate inventory summary statistics
  const inventoryStats = React.useMemo(() => {
    // Initialize for detailed tracking of each product's contribution
    let totalValue = 0
    let totalProducts = 0
    let inStockItems = 0
    let lowStockItems = 0
    let outOfStockItems = 0
    const productValues: { name: string; value: number; runningTotal: number }[] = []
    
    // Count unique products (not variants)
    const productIds = new Set<string>()
    
    // Process each inventory item
    inventoryItems.forEach(item => {
      // Track unique products
      if (!productIds.has(item.productId)) {
        productIds.add(item.productId)
        totalProducts++
      }
      
      // For product items (not variants), calculate value
      if (item.type === 'product') {
        // TODO: Calculate product value based on price and stock
        // This would require fetching product details with prices
        const productValue = 0 // Placeholder
        totalValue += productValue
        productValues.push({
          name: item.name,
          value: productValue,
          runningTotal: totalValue
        })
      }
      
      // Count stock status
      switch (item.status) {
        case 'in_stock':
          inStockItems++
          break
        case 'low_stock':
          lowStockItems++
          break
        case 'out_of_stock':
          outOfStockItems++
          break
      }
    })
    
    return {
      totalValue,
      totalProducts,
      inStockItems,
      lowStockItems,
      outOfStockItems,
      productValues
    }
  }, [inventoryItems])

  // Handle manual refresh
  const handleManualRefresh = async () => {
    try {
      await refetch()
      toast({
        title: "Refreshed",
        description: "Inventory data has been refreshed successfully.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to refresh inventory data. Please try again.",
        variant: "destructive",
      })
    }
  }

  // Reset refetch flag when data is successfully loaded
  useEffect(() => {
    if (!isLoading && !error) {
      hasAttemptedRefetch.current = false
    }
  }, [isLoading, error])

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-64 space-y-4">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p className="text-gray-500">Loading inventory data...</p>
        {/* Add a manual refresh button in case auto-refresh fails */}
        <button 
          onClick={handleManualRefresh}
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 flex items-center gap-2"
          disabled={isRefetching}
        >
          <RefreshCw className={`h-4 w-4 ${isRefetching ? 'animate-spin' : ''}`} />
          {isRefetching ? 'Refreshing...' : 'Force Refresh'}
        </button>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-64 space-y-4">
        <div className="text-red-500 font-medium">Error loading inventory data</div>
        <div className="text-sm text-gray-500 text-center max-w-md">
          {error.message || "An unexpected error occurred while loading inventory data."}
        </div>
        <button 
          onClick={handleManualRefresh}
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 flex items-center gap-2"
          disabled={isRefetching}
        >
          <RefreshCw className={`h-4 w-4 ${isRefetching ? 'animate-spin' : ''}`} />
          {isRefetching ? 'Refreshing...' : 'Retry'}
        </button>
      </div>
    )
  }

  return (
    <div className="flex flex-col gap-4">
      {/* Inventory Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title="Total Products"
          value={inventoryStats.totalProducts}
          subtitle="Active products in inventory"
          icon={<Package className="h-3 w-3 text-blue-600" />}
        />
        
        <MetricCard
          title="In Stock"
          value={inventoryStats.inStockItems}
          subtitle="Items with sufficient stock"
          icon={<TrendingUp className="h-3 w-3 text-green-600" />}
        />
        
        <MetricCard
          title="Low Stock"
          value={inventoryStats.lowStockItems}
          subtitle="Items below threshold"
          icon={<AlertTriangle className="h-3 w-3 text-yellow-600" />}
        />
        
        <MetricCard
          title="Out of Stock"
          value={inventoryStats.outOfStockItems}
          subtitle="Items with zero stock"
          icon={<XCircle className="h-3 w-3 text-red-600" />}
        />
      </div>
      
      {/* Enhanced Inventory Table */}
      <EnhancedInventoryTable 
        inventoryItems={inventoryItems} 
        onInventoryItemsChange={(items) => {
          // Optional: Handle inventory items change if needed
        }}
      />
    </div>
  )
}