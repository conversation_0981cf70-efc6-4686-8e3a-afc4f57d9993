const fs = require('fs');
const path = require('path');
const signinTemplate = require('../templates/signin-simple');
const signupTemplate = require('../templates/signup');
const dashboardTemplate = require('../templates/dashboard');

class PageRoutes {
  handleRequest(req, res, pathname) {
    if (pathname === '/' || pathname === '/auth/signin') {
      this.serveSignInPage(res);
    } else if (pathname === '/auth/signup') {
      this.serveSignUpPage(res);
    } else if (pathname.startsWith('/dashboard')) {
      this.serveDashboardPage(res, pathname);
    } else {
      this.serveStaticFile(res, pathname);
    }
  }

  serveSignInPage(res) {
    const content = signinTemplate.generate();
    this.sendHtml(res, content);
  }

  serveSignUpPage(res) {
    const content = signupTemplate.generate();
    this.sendHtml(res, content);
  }

  serveDashboardPage(res, route) {
    const content = dashboardTemplate.generate(route);
    this.sendHtml(res, content);
  }

  serveStaticFile(res, pathname) {
    // Handle static files from public directory
    if (pathname === '/') {
      pathname = '/index.html';
    }

    const filePath = path.join(__dirname, '..', '..', 'public', pathname);
    
    fs.readFile(filePath, (err, data) => {
      if (err) {
        this.serve404(res);
        return;
      }

      // Determine content type
      const ext = path.extname(filePath).toLowerCase();
      const contentType = this.getContentType(ext);
      
      res.writeHead(200, { 'Content-Type': contentType });
      res.end(data);
    });
  }

  getContentType(ext) {
    const types = {
      '.html': 'text/html',
      '.css': 'text/css',
      '.js': 'application/javascript',
      '.json': 'application/json',
      '.png': 'image/png',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.gif': 'image/gif',
      '.svg': 'image/svg+xml',
      '.ico': 'image/x-icon'
    };
    return types[ext] || 'text/plain';
  }

  sendHtml(res, content) {
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(content);
  }

  serve404(res) {
    const content = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404 - Page Not Found - ONKO</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen flex items-center justify-center">
    <div class="text-center">
        <h1 class="text-6xl font-bold text-gray-900 mb-4">404</h1>
        <p class="text-xl text-gray-600 mb-8">Page not found</p>
        <div class="space-x-4">
            <a href="/dashboard" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg text-lg">
                Go to Dashboard
            </a>
            <a href="/auth/signin" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg text-lg">
                Sign In
            </a>
        </div>
    </div>
</body>
</html>
    `;
    res.writeHead(404, { 'Content-Type': 'text/html' });
    res.end(content);
  }
}

module.exports = new PageRoutes();