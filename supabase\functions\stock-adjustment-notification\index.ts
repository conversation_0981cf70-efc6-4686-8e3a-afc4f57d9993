// Supabase Edge Function to create notifications for stock adjustments
// WARNING: This function is deprecated as notifications are now created via database triggers
// This file is kept for reference but should not be used
// See COMPLETE_NOTIFICATION_TRIGGERS_SQL.sql for the database trigger implementation

console.log("Stock adjustment notification function loaded (DEPRECATED)");

Deno.serve(async (req: Request) => {
  // Add CORS headers
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Max-Age': '86400',
      },
    });
  }

  return new Response(JSON.stringify({ 
    status: "deprecated", 
    message: "This function is deprecated. Notifications are now created via database triggers."
  }), {
    status: 200,
    headers: { 
      "Content-Type": "application/json",
      'Access-Control-Allow-Origin': '*',
    }
  });
});