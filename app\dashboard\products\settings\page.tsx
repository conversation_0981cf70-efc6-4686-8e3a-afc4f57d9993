'use client'

import { useState, useEffect } from 'react'
import { SettingsTab } from '@/components/products/settings-tab'
import { usePersistedState } from '@/lib/use-persisted-state'
import { type ProductFilters, INITIAL_PRODUCT_FILTERS } from '@/components/products/products-catalog/product-filters'

export default function SettingsPage() {
  const [isMobile, setIsMobile] = useState(false)
  
  // Shared state for filters (for contextual settings) - with persistence
  const [globalFilters, setGlobalFilters] = usePersistedState<ProductFilters>(
    'products-filters', 
    INITIAL_PRODUCT_FILTERS
  )
  const [filteredProductIds, setFilteredProductIds] = useState<string[]>([])

  // Mobile detection
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  return (
    <div className="flex flex-col gap-4">
      <SettingsTab 
        isMobile={isMobile} 
      />
    </div>
  )
}