"use client"

import * as React from "react"
import { Check, ChevronDown, Plus, X } from "lucide-react"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

export interface SimpleDropdownOption {
  value: string
  label: string
  isCustom?: boolean
}

interface SimpleDropdownProps {
  options: SimpleDropdownOption[]
  value?: string
  onValueChange: (value: string) => void
  onAddCustomOption?: (option: SimpleDropdownOption) => void
  placeholder?: string
  allowCustom?: boolean
  customPlaceholder?: string
  className?: string
  disabled?: boolean
}

export function SimpleDropdown({
  options,
  value,
  onValueChange,
  onAddCustomOption,
  placeholder = "Select option...",
  allowCustom = true,
  customPlaceholder = "Add custom option...",
  className,
  disabled = false,
}: SimpleDropdownProps) {
  const [open, setOpen] = React.useState(false)
  const [showCustomInput, setShowCustomInput] = React.useState(false)
  const [customValue, setCustomValue] = React.useState("")
  const [search, setSearch] = React.useState("")

  const selectedOption = options.find((option) => option.value === value)

  const filteredOptions = options.filter(option =>
    option.label.toLowerCase().includes(search.toLowerCase())
  )

  const handleOptionSelect = (optionValue: string) => {
    // Only log in development mode to reduce console noise
    if (process.env.NODE_ENV === 'development') {
      console.log('SimpleDropdown: Option selected:', optionValue)
    }
    onValueChange(optionValue)
    setOpen(false)
    setSearch("")
  }

  const handleAddCustom = () => {
    if (customValue.trim() && onAddCustomOption) {
      const newOption: SimpleDropdownOption = {
        value: customValue.trim(),
        label: customValue.trim(),
        isCustom: true
      }
      onAddCustomOption(newOption)
      onValueChange(customValue.trim())
      setCustomValue("")
      setShowCustomInput(false)
      setOpen(false)
    }
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn("w-full justify-between", className)}
          disabled={disabled}
        >
          {selectedOption ? selectedOption.label : placeholder}
          <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0" align="start">
        <div className="flex flex-col">
          {/* Search Input */}
          <div className="flex items-center border-b px-3">
            <Input
              placeholder="Search options..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
            />
          </div>
          
          {/* Options List */}
          <div className="max-h-[300px] overflow-y-auto">
            {filteredOptions.length === 0 && !allowCustom ? (
              <div className="py-6 text-center text-sm">No options found.</div>
            ) : (
              <>
                {filteredOptions.map((option) => (
                  <div
                    key={option.value}
                    onClick={() => handleOptionSelect(option.value)}
                    className="flex items-center justify-between cursor-pointer px-2 py-1.5 text-sm hover:bg-accent hover:text-accent-foreground"
                  >
                    <div className="flex items-center">
                      <Check
                        className={cn(
                          "mr-2 h-4 w-4",
                          value === option.value ? "opacity-100" : "opacity-0"
                        )}
                      />
                      {option.label}
                    </div>
                    {option.isCustom && (
                      <span className="text-xs text-muted-foreground">Custom</span>
                    )}
                  </div>
                ))}
                
                {allowCustom && (
                  <>
                    {!showCustomInput ? (
                      <div
                        onClick={() => setShowCustomInput(true)}
                        className="flex items-center cursor-pointer px-2 py-1.5 text-sm text-primary hover:text-blue-700 hover:bg-accent"
                      >
                        <Plus className="mr-2 h-4 w-4" />
                        {customPlaceholder}
                      </div>
                    ) : (
                      <div className="p-2 border-t">
                        <div className="flex items-center space-x-2">
                          <Input
                            value={customValue}
                            onChange={(e) => setCustomValue(e.target.value)}
                            placeholder="Enter custom option"
                            className="flex-1"
                            autoFocus
                            onKeyDown={(e) => {
                              if (e.key === "Enter") {
                                e.preventDefault()
                                handleAddCustom()
                              } else if (e.key === "Escape") {
                                setCustomValue("")
                                setShowCustomInput(false)
                              }
                            }}
                          />
                          <Button
                            size="sm"
                            onClick={handleAddCustom}
                            disabled={!customValue.trim()}
                          >
                            <Check className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              setCustomValue("")
                              setShowCustomInput(false)
                            }}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    )}
                  </>
                )}
              </>
            )}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}