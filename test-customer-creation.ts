import { getSupabaseClient } from '@/lib/supabase';

async function testCustomerCreation() {
  const supabase = getSupabaseClient();
  
  // Test organization ID (you'll need to replace this with a valid organization ID from your database)
  const testOrganizationId = '4debdfd9-bde6-4653-a77c-181bc7ee7321'; // Example from our earlier query
  
  try {
    // Test creating a customer using the new function
    const { data, error } = await supabase.rpc('create_customer', {
      p_organization_id: testOrganizationId,
      p_full_name: 'Test Customer',
      p_email: '<EMAIL>',
      p_phone: '************',
      p_shipping_address: '123 Test Street, Test City, TS 12345',
      p_billing_address: '123 Test Street, Test City, TS 12345',
      p_notes: 'Test customer for validation'
    });
    
    if (error) {
      console.error('Error creating customer:', error);
      return;
    }
    
    console.log('Customer created successfully:', data);
    
    // Test fetching customers for the organization
    const { data: customers, error: fetchError } = await supabase.rpc('get_all_customers_for_organization', {
      p_organization_id: testOrganizationId
    });
    
    if (fetchError) {
      console.error('Error fetching customers:', fetchError);
      return;
    }
    
    console.log('Customers fetched successfully:', customers);
    
    // Clean up - delete the test customer
    if (data && data.id) {
      const { error: deleteError } = await supabase.rpc('delete_customer', {
        p_customer_id: data.id
      });
      
      if (deleteError) {
        console.error('Error deleting test customer:', deleteError);
      } else {
        console.log('Test customer deleted successfully');
      }
    }
  } catch (err) {
    console.error('Unexpected error:', err);
  }
}

// Run the test
testCustomerCreation();