'use client'

import React, { useState, useEffect } from 'react'
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  DialogHeader, 
  DialogTitle,
  DialogDescription
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select'
import { getSupabaseClient, type ProductVariantRow } from '@/lib/supabase'
import { useAuth } from '@/contexts/auth-context'
import { useCurrency } from '@/lib/currency'
import { useToast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import { Badge } from '@/components/ui/badge'
import { X, Edit3 } from 'lucide-react'
import { type ExtendedProductRow } from '@/hooks/use-products'

interface ProductDetailViewProps {
  product: ExtendedProductRow | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onProductUpdated?: () => void
}

export function ProductDetailView({ 
  product, 
  open, 
  onOpenChange,
  onProductUpdated 
}: ProductDetailViewProps) {
  const [variants, setVariants] = useState<ProductVariantRow[]>([])
  const [loading, setLoading] = useState(false)
  
  const { toast } = useToast()
  const { user, organizationId } = useAuth()
  const { userCurrency, formatCurrency } = useCurrency()
  const supabase = getSupabaseClient()

  useEffect(() => {
    if (product && open && product.has_variants) {
      loadVariants()
    }
  }, [product, open])

  const loadVariants = async () => {
    if (!product?.id || !organizationId) return
    
    setLoading(true)
    try {
      const { data, error } = await supabase
        .from('product_variants')
        .select('*')
        .eq('product_id', product.id)
        .eq('organization_id', organizationId)
        .order('created_at')

      if (error) {
        console.error('Error loading variants:', error)
        toast({
          title: "Error Loading Variants",
          description: `Failed to load product variants: ${error.message}`,
          variant: "destructive",
        })
      } else {
        setVariants(data || [])
      }
    } catch (error) {
      console.error('Error loading variants:', error)
      toast({
        title: "Unexpected Error",
        description: "Failed to load product variants. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  if (!product) return null

  const getStockStatus = (stock: number, threshold: number) => {
    if (stock === 0) return { status: 'Out of Stock', color: 'text-red-600', dotColor: 'bg-red-500' }
    if (stock <= threshold) return { status: 'Low Stock', color: 'text-yellow-600', dotColor: 'bg-yellow-500' }
    return { status: 'In Stock', color: 'text-green-600', dotColor: 'bg-green-500' }
  }

  const formatAttributes = (variant: ProductVariantRow) => {
    const attributes = []
    if (variant.size) attributes.push(`Size: ${variant.size}`)
    if (variant.color) attributes.push(`Color: ${variant.color}`)
    if (variant.material) attributes.push(`Material: ${variant.material}`)
    if (variant.style) attributes.push(`Style: ${variant.style}`)
    return attributes.join(', ') || 'No attributes'
  }

  const stockStatus = getStockStatus(product.stock_quantity || 0, product.low_stock_threshold || 10)

  // For variable products, calculate price range and average profit margin
  const getPriceRange = () => {
    if (!product.has_variants || variants.length === 0) return null
    
    const prices = variants.map(v => v.effective_price || v.price || 0)
    const minPrice = Math.min(...prices)
    const maxPrice = Math.max(...prices)
    
    if (minPrice === maxPrice) {
      return formatCurrency(minPrice, userCurrency)
    }
    
    return `${formatCurrency(minPrice, userCurrency)} - ${formatCurrency(maxPrice, userCurrency)}`
  }

  const getAverageProfitMargin = () => {
    if (!product.has_variants || variants.length === 0) return null
    
    // Calculate average profit margin from variants
    // Since product_variants table doesn't have profit_margin column,
    // we need to calculate it manually
    const totalMargin = variants.reduce((sum, variant) => {
      // Calculate profit margin manually for each variant
      // Using type assertion since the properties exist in the database but not in the TypeScript definition
      const baseCost = (variant as any).base_cost || 0;
      const costAdjustment = (variant as any).cost_adjustment || 0;
      const totalCost = baseCost + costAdjustment;
      const sellingPrice = variant.effective_price || variant.price || 0;
      const profitAmount = sellingPrice - totalCost;
      const profitMargin = totalCost > 0 ? (profitAmount / totalCost) * 100 : 0;
      
      return sum + profitMargin;
    }, 0)
    
    return (totalMargin / variants.length).toFixed(2)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-3xl max-h-[85vh] overflow-y-auto p-0 text-xs">
        <DialogHeader className="sr-only">
          <DialogTitle>Product Details</DialogTitle>
          <DialogDescription>
            Detailed information for {product.name}
          </DialogDescription>
        </DialogHeader>
        <div className="flex flex-col md:flex-row">
          {/* LEFT COLUMN: Financial Dashboard */}
          <div className="w-full md:w-1/2 p-8">
            {/* Product Header */}
            <header className="product-header">
              <DialogTitle className="text-xl font-bold mb-1">
                {product.name}
              </DialogTitle>
              <div className="text-sm text-gray-500">
                {product.base_sku || 'No SKU'}
              </div>
            </header>

            {/* Financial Information */}
            <section className="section mt-8">
              {product.has_variants ? (
                // For variable products, show price range and average profit margin
                <>
                  <div className="financial-card border border-gray-200 rounded-lg p-5 mb-6 bg-white shadow-sm">
                    <div className="label text-sm font-medium text-gray-500 mb-1">Selling Price Range</div>
                    <div className="value text-3xl font-bold text-gray-900 mb-4">
                      {getPriceRange() || 'N/A'}
                    </div>
                    <div className="text-sm text-gray-600">
                      Prices vary by variant
                    </div>
                  </div>
                  <div className="profit-card border border-gray-200 rounded-lg p-5 bg-white shadow-sm">
                    <div className="profit-header flex justify-between items-baseline mb-3">
                      <div className="label text-sm font-medium text-gray-500">Avg. Profit Margin</div>
                      <div className="value text-2xl font-bold text-green-500">
                        {getAverageProfitMargin() ? `${getAverageProfitMargin()}%` : 'N/A'}
                      </div>
                    </div>
                    <div className="profit-meter h-2 bg-gray-200 rounded-full overflow-hidden">
                      <div 
                        className="bar h-full bg-green-500 rounded-full" 
                        style={{ 
                          width: getAverageProfitMargin() ? `${Math.min(parseFloat(getAverageProfitMargin() || '0'), 100)}%` : '0%' 
                        }}
                      ></div>
                    </div>
                  </div>
                </>
              ) : (
                // For simple products, show the pricing information
                <div className="financial-card border border-gray-200 rounded-lg p-5 mb-6 bg-white shadow-sm">
                  <div className="label text-sm font-medium text-gray-500 mb-1">Selling Price</div>
                  <div className="value text-3xl font-bold text-gray-900 mb-4">
                    {formatCurrency(product.effective_price || product.price || 0, userCurrency)}
                  </div>
                  <div className="cost-breakdown flex flex-wrap items-center gap-3 text-sm text-gray-600 bg-gray-50 rounded-lg p-3">
                    <span>{formatCurrency(product.base_cost || 0, userCurrency)} <span className="operator text-gray-400">(Base)</span></span>
                    <span className="operator text-gray-400">+</span>
                    <span>{formatCurrency(product.packaging_cost || 0, userCurrency)} <span className="operator text-gray-400">(Pkg)</span></span>
                    <span className="operator text-gray-400">=</span>
                    <span className="total-cost font-semibold">
                      {formatCurrency((product.base_cost || 0) + (product.packaging_cost || 0), userCurrency)} Total Cost
                    </span>
                  </div>
                </div>
              )}

              {!product.has_variants && (
                <div className="profit-card border border-gray-200 rounded-lg p-5 bg-white shadow-sm">
                  <div className="profit-header flex justify-between items-baseline mb-3">
                    <div className="label text-sm font-medium text-gray-500">Profit Margin</div>
                    <div className="value text-2xl font-bold text-green-500">
                      {product.profit_margin !== null ? `${product.profit_margin.toFixed(2)}%` : 'N/A'}
                    </div>
                  </div>
                  <div className="profit-meter h-2 bg-gray-200 rounded-full overflow-hidden">
                    <div 
                      className="bar h-full bg-green-500 rounded-full" 
                      style={{ 
                        width: product.profit_margin !== null ? `${Math.min(product.profit_margin, 100)}%` : '0%' 
                      }}
                    ></div>
                  </div>
                </div>
              )}
            </section>
            
            {/* Product Description */}
            {product.description && (
              <div className="description-text mt-8 text-sm text-gray-600 leading-relaxed">
                {product.description}
              </div>
            )}
          </div>

          {/* RIGHT COLUMN: Details Panel */}
          <div className="w-full md:w-1/2 bg-gray-50 p-8 border-l border-gray-200">
            
            {/* Inventory Section */}
            <h3 className="detail-group-title text-xs font-semibold uppercase text-gray-500 tracking-wider mb-4">
              Inventory
            </h3>
            <div className="status-display flex items-center gap-2 mb-4">
              <span className={`dot w-2.5 h-2.5 rounded-full ${stockStatus.dotColor}`}></span>
              <span className={`font-semibold ${stockStatus.color}`}>{stockStatus.status}</span>
            </div>
            <dl className="details-list grid grid-cols-2 gap-4 text-sm">
              <dt className="text-gray-500">Current Stock</dt>
              <dd className="font-medium text-right">{product.stock_quantity || 0} units</dd>
              
              <dt className="text-gray-500">Low Stock Threshold</dt>
              <dd className="font-medium text-right">{product.low_stock_threshold || 10} units</dd>
            </dl>

            {/* Attributes Section */}
            <h3 className="detail-group-title text-xs font-semibold uppercase text-gray-500 tracking-wider mb-4 mt-8">
              Attributes
            </h3>
            <dl className="details-list grid grid-cols-2 gap-4 text-sm">
              <dt className="text-gray-500">Category</dt>
              <dd className="font-medium text-right">{(product as any).category_name || 'Uncategorized'}</dd>
              
              <dt className="text-gray-500">Brand</dt>
              <dd className="font-medium text-right">{product.brand || 'Not set'}</dd>
              
              <dt className="text-gray-500">Color</dt>
              <dd className="font-medium text-right">{product.color || 'Not specified'}</dd>
              
              <dt className="text-gray-500">Size</dt>
              <dd className="font-medium text-right">{product.size || 'Not specified'}</dd>
              
              <dt className="text-gray-500">Barcode</dt>
              <dd className="font-medium text-right font-mono">{product.barcode || 'Not set'}</dd>
            </dl>

            {/* Sourcing Section */}
            <h3 className="detail-group-title text-xs font-semibold uppercase text-gray-500 tracking-wider mb-4 mt-8">
              Sourcing
            </h3>
            <dl className="details-list grid grid-cols-2 gap-4 text-sm">
              <dt className="text-gray-500">Supplier</dt>
              <dd className="font-medium text-right">{product.supplier || 'Not set'}</dd>
              
              {product.purchase_date && (
                <>
                  <dt className="text-gray-500">Purchase Date</dt>
                  <dd className="font-medium text-right">
                    {new Date(product.purchase_date).toLocaleDateString()}
                  </dd>
                </>
              )}
              
              {product.batch_reference && (
                <>
                  <dt className="text-gray-500">Batch Reference</dt>
                  <dd className="font-medium text-right">{product.batch_reference}</dd>
                </>
              )}
            </dl>

            {/* Variants Section (if applicable) */}
            {product.has_variants && (
              <div className="mt-8">
                <h3 className="detail-group-title text-xs font-semibold uppercase text-gray-500 tracking-wider mb-4">
                  Variants
                </h3>
                <div className="space-y-3">
                  {variants.map((variant) => {
                    const variantStockStatus = getStockStatus(variant.stock_quantity, variant.low_stock_threshold || 10)
                    return (
                      <div key={variant.id} className="border border-gray-200 rounded-lg p-4 bg-white">
                        <div className="flex justify-between items-start">
                          <div>
                            <h4 className="font-medium text-sm">
                              {variant.variant_name || formatAttributes(variant)}
                            </h4>
                            {variant.barcode && (
                              <div className="text-xs text-gray-500 font-mono mt-1">
                                {variant.barcode}
                              </div>
                            )}
                          </div>
                          <Badge 
                            className={cn(
                              "text-xs",
                              variantStockStatus.status === 'In Stock' && "bg-green-100 text-green-800",
                              variantStockStatus.status === 'Low Stock' && "bg-yellow-100 text-yellow-800",
                              variantStockStatus.status === 'Out of Stock' && "bg-red-100 text-red-800"
                            )}
                          >
                            {variantStockStatus.status}
                          </Badge>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-2 mt-3 text-xs">
                          <div className="text-gray-500">SKU</div>
                          <div className="font-medium text-right">{variant.sku || 'N/A'}</div>
                          
                          <div className="text-gray-500">Stock</div>
                          <div className="font-medium text-right">{variant.stock_quantity || 0} units</div>
                          
                          <div className="text-gray-500">Price</div>
                          <div className="font-medium text-right">
                            {formatCurrency(variant.effective_price || variant.price || 0, userCurrency)}
                          </div>
                        </div>
                      </div>
                    )
                  })}
                  
                  {variants.length === 0 && !loading && (
                    <div className="text-center py-4 text-xs text-gray-500">
                      No variants found for this product
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}