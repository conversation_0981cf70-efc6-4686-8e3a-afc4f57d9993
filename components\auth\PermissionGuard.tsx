'use client'

import { useAuth } from '@/contexts/auth-context'
import { useEffect, useState } from 'react'

interface PermissionGuardProps {
  allowedRoles: string[]
  children: React.ReactNode
}

export function PermissionGuard({ allowedRoles, children }: PermissionGuardProps) {
  const { user, userRole } = useAuth()
  const [hasPermission, setHasPermission] = useState(false)

  useEffect(() => {
    if (!user) {
      setHasPermission(false)
      return
    }

    // Check if user's role is in allowed roles
    const isAllowed = allowedRoles.includes(userRole || '')
    
    // Also check if user is owner (owner has all permissions)
    const isOwner = userRole === 'owner'
    
    setHasPermission(isAllowed || isOwner)
  }, [user, userRole, allowedRoles])

  if (!hasPermission) {
    return null
  }

  return <>{children}</>
}
