// Types for the new modular add product form

export interface ProductFormData {
  // Product Type
  has_variants: boolean
  
  // Basic Information
  name: string
  description: string
  brand: string
  supplier: string
  base_sku: string
  category_id: string
  
  // Simple Product Fields (when has_variants = false)
  size: string
  color: string
  base_cost: number
  packaging_cost: number
  price: number | null
  stock_quantity: number
  low_stock_threshold: number
  barcode: string
  
  // Variable Product Attributes (when has_variants = true)
  variant_attributes: {
    name: string;
    values: string[];
  }[]
  
  // Sale/Discount Fields
  sale_price: number | null
  sale_start_date: Date | null
  sale_end_date: Date | null
  
  // Additional Fields
  batch_reference: string
  purchase_date: Date | null
  notes: string
}

export interface VariantAttribute {
  name: string
  value: string
}

export interface ProductVariant {
  id: string
  attributes: VariantAttribute[];
  price: number | null;
  base_cost: number | null;
  packaging_cost: number | null;
  quantity: number;
  sku: string;
  low_stock_threshold: number;
}

export interface CurrentVariant {
  attributes: VariantAttribute[];
  price: number | null;
  base_cost: number | null;
  packaging_cost: number | null;
  quantity: number;
  sku: string;
  low_stock_threshold: number;
}

export interface FormSection {
  id: string
  label: string
  icon: any
  required: boolean
  description?: string
}

export interface FormErrors {
  [key: string]: string | undefined
}

export interface FormValidation {
  [sectionId: string]: boolean
}

export const INITIAL_FORM_DATA: ProductFormData = {
  has_variants: false,
  name: '',
  description: '',
  brand: '',
  supplier: '',
  base_sku: '',
  category_id: '',
  size: '',
  color: '',
  base_cost: 0,
  packaging_cost: 0,
  price: null,
  stock_quantity: 0,
  low_stock_threshold: 10,
  barcode: '',
  variant_attributes: [],
  sale_price: null,
  sale_start_date: null,
  sale_end_date: null,
  batch_reference: '',
  purchase_date: null,
  notes: ''
}