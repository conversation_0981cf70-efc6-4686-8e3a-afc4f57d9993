import React from 'react'
import { Label } from '@/components/ui/label'
import { cn } from '@/lib/utils'

interface FormFieldProps {
  label: string | React.ReactNode
  required?: boolean
  error?: string
  action?: React.ReactNode
  children: React.ReactNode
  className?: string
}

export function FormField({
  label,
  required = false,
  error,
  action,
  children,
  className
}: FormFieldProps) {
  // Check if label is a string or a React element
  const isStringLabel = typeof label === 'string';
  
  return (
    <div className={cn('space-y-1', className)}>
      <div className="flex items-center justify-between">
        <Label className="text-xs font-medium text-gray-900">
          {isStringLabel ? (
            <>
              {label}
              {required && <span className="text-red-500 ml-1">*</span>}
            </>
          ) : (
            // For complex labels, we expect the required mark to be handled within the label itself
            label
          )}
        </Label>
        {action && (
          <div className="text-xs">
            {action}
          </div>
        )}
      </div>

      <div className="relative">
        {children}
      </div>

      {error && (
        <p className="text-xs text-red-600 flex items-center gap-1">
          <span className="inline-block w-1 h-1 bg-red-600 rounded-full"></span>
          {error}
        </p>
      )}
    </div>
  )
}