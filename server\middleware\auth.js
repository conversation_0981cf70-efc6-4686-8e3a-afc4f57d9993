const supabaseConfig = require('../config/supabase');

class AuthMiddleware {
  // Extract user token from Authorization header
  extractToken(req) {
    const authHeader = req.headers.authorization;
    return authHeader && authHeader.startsWith('Bearer ') ? authHeader.substring(7) : null;
  }

  // Validate if user token exists (basic check)
  requireAuth(req, res, next) {
    const token = this.extractToken(req);
    
    if (!token) {
      res.writeHead(401, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({ 
        success: false, 
        error: 'Authentication required. Please provide a valid token.' 
      }));
      return false;
    }
    
    req.userToken = token;
    return true;
  }

  // Set CORS headers for API requests
  setCorsHeaders(res) {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  }

  // Handle preflight OPTIONS requests
  handlePreflight(req, res) {
    if (req.method === 'OPTIONS') {
      this.setCorsHeaders(res);
      res.writeHead(200);
      res.end();
      return true;
    }
    return false;
  }

  // Complete middleware setup for API routes
  setupApiMiddleware(req, res) {
    // Set CORS headers
    this.setCorsHeaders(res);
    
    // Handle preflight requests
    if (this.handlePreflight(req, res)) {
      return false; // Request handled, don't continue
    }
    
    return true; // Continue to route handler
  }
}

module.exports = new AuthMiddleware();