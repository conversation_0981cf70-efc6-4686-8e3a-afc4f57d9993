import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { DatePicker } from '@/components/ui/date-picker';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';

interface GenerateInvoiceModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  orderNumber: string;
  onConfirm: (dueDate: Date | undefined, markAsSent: boolean) => void;
  isGenerating?: boolean;
}

export function GenerateInvoiceModal({
  open,
  onOpenChange,
  orderNumber,
  onConfirm,
  isGenerating = false,
}: GenerateInvoiceModalProps) {
  const [dueDate, setDueDate] = useState<Date | undefined>(
    new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
  );
  const [markAsSent, setMarkAsSent] = useState(false);

  const handleConfirm = () => {
    onConfirm(dueDate, markAsSent);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Generate Invoice for Order #{orderNumber}</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="dueDate" className="text-right text-xs">
              Due Date
            </Label>
            <div className="col-span-3">
              <DatePicker
                date={dueDate}
                onDateChange={setDueDate}
                placeholder="Select due date"
              />
            </div>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <div className="col-span-3 col-start-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="markAsSent"
                  checked={markAsSent}
                  onCheckedChange={(checked) => setMarkAsSent(checked as boolean)}
                />
                <Label htmlFor="markAsSent" className="text-xs font-normal leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                  Mark as Sent immediately
                </Label>
              </div>
            </div>
          </div>
        </div>
        <div className="flex justify-end space-x-2">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isGenerating}
          >
            Cancel
          </Button>
          <Button onClick={handleConfirm} disabled={isGenerating}>
            {isGenerating ? 'Generating...' : 'Confirm & Generate'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}