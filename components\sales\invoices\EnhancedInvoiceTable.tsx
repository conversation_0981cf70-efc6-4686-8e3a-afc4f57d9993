'use client'

import React, { useState, useMemo } from 'react'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu'
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle
} from '@/components/ui/dialog'
import { useCurrency } from '@/contexts/currency-context'
import { cn } from '@/lib/utils'
import { 
  MoreHorizontal, 
  Eye, 
  Edit, 
  Trash2,
  ChevronLeft,
  ChevronRight,
  ChevronUp,
  ChevronDown,
  Download,
  Send,
  CheckCircle,
  AlertCircle,
  Undo,
  Bell
} from 'lucide-react'
import { format } from 'date-fns'
import { Invoice } from '@/components/sales/invoices/types'
import { useUpdateInvoiceStatus } from '@/hooks/use-invoices'
import { useToast } from '@/components/ui/use-toast'

// Sort configuration
type SortField = keyof Invoice
type SortDirection = 'asc' | 'desc'

interface SortConfig {
  field: SortField
  direction: SortDirection
}

// Pagination configuration
interface PaginationConfig {
  currentPage: number
  pageSize: number
  totalItems: number
  totalPages: number
}

interface EnhancedInvoiceTableProps {
  invoices: Invoice[]
  onInvoiceView?: (invoice: Invoice) => void
  onInvoiceEdit?: (invoice: Invoice) => void
  onInvoiceDelete?: (invoiceIds: string[]) => void
  onSelectedInvoicesChange?: (invoiceIds: string[]) => void
  showBulkActions?: boolean
  defaultPageSize?: number
  isMobile?: boolean
}

export function EnhancedInvoiceTable({
  invoices,
  onInvoiceView,
  onInvoiceEdit,
  onInvoiceDelete,
  onSelectedInvoicesChange,
  showBulkActions = true,
  defaultPageSize = 10,
  isMobile = false
}: EnhancedInvoiceTableProps) {
  const [sortConfig, setSortConfig] = useState<SortConfig>({ 
    field: 'issue_date', 
    direction: 'desc' 
  })
  const [pagination, setPagination] = useState({
    currentPage: 1,
    pageSize: defaultPageSize
  })
  const [selectedInvoices, setSelectedInvoices] = useState<string[]>([])
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [invoiceToDelete, setInvoiceToDelete] = useState<Invoice | null>(null)
  
  const { formatCurrency } = useCurrency()
  const { toast } = useToast()
  const { mutate: updateInvoiceStatus } = useUpdateInvoiceStatus()

  // Sort invoices based on the current sort configuration
  const sortedInvoices = useMemo(() => {
    const sorted = [...invoices].sort((a, b) => {
      let aValue: any = a[sortConfig.field]
      let bValue: any = b[sortConfig.field]

      // Handle date fields
      if (sortConfig.field === 'issue_date' || sortConfig.field === 'due_date') {
        // Handle null values for due_date
        if (sortConfig.field === 'due_date') {
          if (aValue === null) aValue = ''
          if (bValue === null) bValue = ''
        } else {
          aValue = new Date(aValue).getTime()
          bValue = new Date(bValue).getTime()
        }
      }

      // Handle string fields
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase()
        bValue = bValue?.toLowerCase() || ''
      }

      // Handle null/undefined values
      if (aValue === null || aValue === undefined) aValue = ''
      if (bValue === null || bValue === undefined) bValue = ''

      if (aValue < bValue) {
        return sortConfig.direction === 'asc' ? -1 : 1
      }
      if (aValue > bValue) {
        return sortConfig.direction === 'asc' ? 1 : -1
      }
      return 0
    })
    return sorted
  }, [invoices, sortConfig])

  // Calculate pagination configuration
  const paginationConfig: PaginationConfig = useMemo(() => {
    const totalItems = sortedInvoices.length
    const totalPages = Math.ceil(totalItems / pagination.pageSize)
    
    return {
      currentPage: pagination.currentPage,
      pageSize: pagination.pageSize,
      totalItems,
      totalPages
    }
  }, [sortedInvoices.length, pagination])

  // Get the invoices for the current page
  const paginatedInvoices = useMemo(() => {
    const startIndex = (pagination.currentPage - 1) * pagination.pageSize
    const endIndex = startIndex + pagination.pageSize
    const paginated = sortedInvoices.slice(startIndex, endIndex)
    return paginated
  }, [sortedInvoices, pagination])

  // Handle sorting when a column header is clicked
  const handleSort = (field: SortField) => {
    setSortConfig(current => {
      // If clicking the same field that's already sorted
      if (current.field === field) {
        // If ascending, switch to descending
        if (current.direction === 'asc') {
          return { field, direction: 'desc' };
        } 
        // If descending, reset to default sorting
        else {
          return { field: 'issue_date', direction: 'desc' };
        }
      }
      // If clicking a different field, sort ascending first
      else {
        return { field, direction: 'asc' };
      }
    });
  }

  // Handle pagination when a page is selected
  const handlePageChange = (page: number) => {
    setPagination(current => ({
      ...current,
      currentPage: Math.max(1, Math.min(page, paginationConfig.totalPages))
    }))
    setSelectedInvoices([]) // Clear selection when changing pages
  }

  // Handle page size change
  const handlePageSizeChange = (pageSize: number) => {
    setPagination(current => ({
      currentPage: 1,
      pageSize
    }))
    setSelectedInvoices([]) // Clear selection when changing page size
  }

  // Handle select all/none for bulk actions
  const handleSelectAll = (checked: boolean) => {
    const newSelection = checked ? paginatedInvoices.map(invoice => invoice.id) : []
    setSelectedInvoices(newSelection)
    onSelectedInvoicesChange?.(newSelection)
  }

  // Handle individual invoice selection
  const handleSelectInvoice = (invoiceId: string, checked: boolean) => {
    const newSelection = checked
      ? [...selectedInvoices, invoiceId]
      : selectedInvoices.filter(id => id !== invoiceId)
    
    setSelectedInvoices(newSelection)
    onSelectedInvoicesChange?.(newSelection)
  }

  // Handle bulk delete action
  const handleBulkDelete = () => {
    if (selectedInvoices.length === 0) return
    
    const selectedInvoicesData = invoices.filter(invoice => selectedInvoices.includes(invoice.id))
    
    const confirmMessage = selectedInvoices.length === 1
      ? `Are you sure you want to delete invoice "${selectedInvoicesData[0]?.invoice_number}"?\n\nThis action cannot be undone.`
      : `Are you sure you want to delete ${selectedInvoices.length} invoices?\n\nThis action cannot be undone.`
    
    if (window.confirm(confirmMessage)) {
      try {
        onInvoiceDelete?.(selectedInvoices)
        setSelectedInvoices([])
      } catch (error) {
        console.error('Error deleting invoices:', error)
        // In a real implementation, we would use toast here
      }
    }
  }

  // Handle single invoice delete
  const handleDeleteInvoice = (invoice: Invoice) => {
    const confirmMessage = `Are you sure you want to delete invoice "${invoice.invoice_number}"?\n\nThis action cannot be undone.`
    
    if (window.confirm(confirmMessage)) {
      try {
        onInvoiceDelete?.([invoice.id])
      } catch (error) {
        console.error('Error deleting invoice:', error)
        // In a real implementation, we would use toast here
      }
    }
  }

  // Handle Mark as Sent action
  const handleMarkAsSent = (invoiceId: string) => {
    updateInvoiceStatus(
      { 
        invoiceId, 
        newStatus: 'Sent',
        additionalData: { issue_date: new Date().toISOString() }
      },
      {
        onSuccess: () => {
          toast({
            title: "Invoice Updated",
            description: "Invoice has been marked as sent.",
          })
        }
      }
    )
  }

  // Handle Mark as Paid action
  const handleMarkAsPaid = (invoiceId: string) => {
    updateInvoiceStatus(
      { 
        invoiceId, 
        newStatus: 'Paid'
      },
      {
        onSuccess: () => {
          toast({
            title: "Invoice Updated",
            description: "Invoice has been marked as paid.",
          })
        }
      }
    )
  }

  // Handle Mark as Unpaid action
  const handleMarkAsUnpaid = (invoiceId: string) => {
    updateInvoiceStatus(
      { 
        invoiceId, 
        newStatus: 'Sent'
      },
      {
        onSuccess: () => {
          toast({
            title: "Invoice Updated",
            description: "Invoice has been marked as unpaid.",
          })
        }
      }
    )
  }

  // Handle Send Reminder action (placeholder)
  const handleSendReminder = (invoiceId: string) => {
    toast({
      title: "Feature Coming Soon",
      description: "Reminder functionality will be implemented in a future update.",
    })
  }

  // Handle Create Refund action (placeholder)
  const handleCreateRefund = (invoiceId: string) => {
    toast({
      title: "Feature Coming Soon",
      description: "Refund functionality will be implemented in a future update.",
    })
  }

  // Handle Download PDF action (placeholder)
  const handleDownloadPDF = (invoiceId: string) => {
    toast({
      title: "Feature Coming Soon",
      description: "PDF download functionality will be implemented in a future update.",
    })
  }

  // Get context-aware actions based on invoice status
  const getContextMenuItems = (invoice: Invoice) => {
    const items = []
    
    // View Details action (available for all statuses)
    items.push(
      <DropdownMenuItem
        key="view"
        onClick={() => onInvoiceView?.(invoice)}
        className="cursor-pointer text-xs py-2"
      >
        <Eye className="h-4 w-4 mr-2" />
        <span>View Details</span>
      </DropdownMenuItem>
    )
    
    // Status-specific actions
    switch (invoice.status) {
      case 'Draft':
        items.push(
          <DropdownMenuItem
            key="mark-sent"
            onClick={() => handleMarkAsSent(invoice.id)}
            className="cursor-pointer text-xs py-2"
          >
            <Send className="h-4 w-4 mr-2" />
            <span>Mark as Sent</span>
          </DropdownMenuItem>
        )
        items.push(
          <DropdownMenuItem
            key="edit"
            onClick={() => onInvoiceEdit?.(invoice)}
            className="cursor-pointer text-xs py-2"
          >
            <Edit className="h-4 w-4 mr-2" />
            <span>Edit</span>
          </DropdownMenuItem>
        )
        break
        
      case 'Sent':
      case 'Overdue':
        items.push(
          <DropdownMenuItem
            key="mark-paid"
            onClick={() => handleMarkAsPaid(invoice.id)}
            className="cursor-pointer text-xs py-2"
          >
            <CheckCircle className="h-4 w-4 mr-2" />
            <span>Mark as Paid</span>
          </DropdownMenuItem>
        )
        items.push(
          <DropdownMenuItem
            key="send-reminder"
            onClick={() => handleSendReminder(invoice.id)}
            className="cursor-pointer text-xs py-2"
          >
            <Bell className="h-4 w-4 mr-2" />
            <span>Send Reminder</span>
          </DropdownMenuItem>
        )
        break
        
      case 'Paid':
        items.push(
          <DropdownMenuItem
            key="mark-unpaid"
            onClick={() => handleMarkAsUnpaid(invoice.id)}
            className="cursor-pointer text-xs py-2"
          >
            <Undo className="h-4 w-4 mr-2" />
            <span>Mark as Unpaid</span>
          </DropdownMenuItem>
        )
        items.push(
          <DropdownMenuItem
            key="create-refund"
            onClick={() => handleCreateRefund(invoice.id)}
            className="cursor-pointer text-xs py-2"
          >
            <Undo className="h-4 w-4 mr-2" />
            <span>Create Refund</span>
          </DropdownMenuItem>
        )
        break
    }
    
    // Download PDF action (available for all statuses)
    items.push(
      <DropdownMenuItem
        key="download"
        onClick={() => handleDownloadPDF(invoice.id)}
        className="cursor-pointer text-xs py-2"
      >
        <Download className="h-4 w-4 mr-2" />
        <span>Download PDF</span>
      </DropdownMenuItem>
    )
    
    // Delete action (available for all statuses)
    items.push(
      <DropdownMenuSeparator key="separator" />
    )
    items.push(
      <DropdownMenuItem
        key="delete"
        onClick={() => handleDeleteInvoice(invoice)}
        className="cursor-pointer text-red-600 focus:text-red-600 focus:bg-red-50 text-xs py-2"
      >
        <Trash2 className="h-4 w-4 mr-2" />
        <span>Delete</span>
      </DropdownMenuItem>
    )
    
    return items
  }

  // Get sort icon for column headers
  const getSortIcon = (field: SortField) => {
    if (sortConfig.field !== field) {
      return (
        <div className="flex flex-col items-center justify-center ml-1 opacity-50">
          <ChevronUp className="h-3 w-3" />
          <ChevronDown className="h-3 w-3 -mt-1" />
        </div>
      )
    }
    
    if (sortConfig.direction === 'asc') {
      return <ChevronUp className="h-4 w-4 text-blue-600 ml-1" />
    } else {
      return <ChevronDown className="h-4 w-4 text-blue-600 ml-1" />
    }
  }

  // Calculate selection states for the select all checkbox
  const allSelected = paginatedInvoices.length > 0 && selectedInvoices.length === paginatedInvoices.length
  const someSelected = selectedInvoices.length > 0 && !allSelected

  // Get status badge class
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'Paid':
        return 'bg-green-50 text-green-700 border border-green-100'
      case 'Sent':
        return 'bg-blue-50 text-blue-700 border border-blue-100'
      case 'Overdue':
        return 'bg-red-50 text-red-700 border border-red-100'
      case 'Draft':
        return 'bg-yellow-50 text-yellow-700 border border-yellow-100'
      default:
        return 'bg-gray-50 text-gray-700 border border-gray-100'
    }
  }

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  return (
    <>
      {/* Bulk Actions - Only show when items are selected */}
      {showBulkActions && selectedInvoices.length > 0 && (
        <div className="flex items-center space-x-3 mb-4">
          <span className="text-sm text-muted-foreground">
            {selectedInvoices.length} selected
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={handleBulkDelete}
            className="text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      )}
      
      {invoices.length === 0 ? (
        <div className="text-center py-12 border border-gray-200 rounded-lg">
          <div className="h-16 w-16 mx-auto text-muted-foreground mb-4 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="h-16 w-16">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002-2h2a2 2 0 002 2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
          </div>
          <p className="text-muted-foreground text-lg">No invoices to display.</p>
          <p className="text-muted-foreground text-sm mt-2">Create your first invoice to get started.</p>
        </div>
      ) : (
        <>
          {/* Table Container */}
          <div className="border border-gray-200 rounded-t-lg overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 bg-white">
                <thead className="bg-gray-50">
                  <tr>
                    {showBulkActions && (
                      <th 
                        scope="col" 
                        className="text-left py-3 px-6 font-medium text-gray-900 text-xs"
                      >
                        <input
                          type="checkbox"
                          className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          checked={allSelected}
                          ref={(input) => {
                            if (input) {
                              input.indeterminate = someSelected;
                            }
                          }}
                          onChange={(e) => handleSelectAll(e.target.checked)}
                        />
                      </th>
                    )}
                    <th 
                      scope="col" 
                      className="text-left py-3 px-6 font-medium text-gray-900 text-xs"
                    >
                      <div className="flex items-center gap-1">
                        <span 
                          className="cursor-pointer hover:bg-gray-100 transition-colors flex items-center"
                          onClick={() => handleSort('invoice_number')}
                        >
                          Invoice #
                          {getSortIcon('invoice_number')}
                        </span>
                      </div>
                    </th>
                    <th 
                      scope="col" 
                      className="text-left py-3 px-6 font-medium text-gray-900 text-xs"
                    >
                      <div className="flex items-center gap-1">
                        <span 
                          className="cursor-pointer hover:bg-gray-100 transition-colors flex items-center"
                          onClick={() => handleSort('customer_name')}
                        >
                          Customer
                          {getSortIcon('customer_name')}
                        </span>
                      </div>
                    </th>
                    <th 
                      scope="col" 
                      className="text-left py-3 px-6 font-medium text-gray-900 text-xs"
                    >
                      <div className="flex items-center gap-1">
                        <span 
                          className="cursor-pointer hover:bg-gray-100 transition-colors flex items-center"
                          onClick={() => handleSort('issue_date')}
                        >
                          Issue Date
                          {getSortIcon('issue_date')}
                        </span>
                      </div>
                    </th>
                    <th 
                      scope="col" 
                      className="text-left py-3 px-6 font-medium text-gray-900 text-xs"
                    >
                      <div className="flex items-center gap-1">
                        <span 
                          className="cursor-pointer hover:bg-gray-100 transition-colors flex items-center"
                          onClick={() => handleSort('due_date')}
                        >
                          Due Date
                          {getSortIcon('due_date')}
                        </span>
                      </div>
                    </th>
                    <th 
                      scope="col" 
                      className="text-left py-3 px-6 font-medium text-gray-900 text-xs"
                    >
                      <div className="flex items-center gap-1">
                        <span 
                          className="cursor-pointer hover:bg-gray-100 transition-colors flex items-center"
                          onClick={() => handleSort('status')}
                        >
                          Status
                          {getSortIcon('status')}
                        </span>
                      </div>
                    </th>
                    <th 
                      scope="col" 
                      className="text-left py-3 px-6 font-medium text-gray-900 text-xs"
                    >
                      <div className="flex items-center gap-1">
                        <span 
                          className="cursor-pointer hover:bg-gray-100 transition-colors flex items-center"
                          onClick={() => handleSort('total')}
                        >
                          Total
                          {getSortIcon('total')}
                        </span>
                      </div>
                    </th>
                    <th scope="col" className="text-center py-3 px-6 font-medium text-gray-900 w-32 text-xs">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {paginatedInvoices.length === 0 ? (
                    <tr>
                      <td colSpan={showBulkActions ? 8 : 7} className="py-8 text-center text-muted-foreground">
                        No invoices match the current filters.
                      </td>
                    </tr>
                  ) : (
                    paginatedInvoices.map((invoice) => (
                      <tr 
                        key={invoice.id}
                        className="hover:bg-gray-50"
                      >
                        {showBulkActions && (
                          <td className="py-4 px-6">
                            <input
                              type="checkbox"
                              className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                              checked={selectedInvoices.includes(invoice.id)}
                              onChange={(e) => handleSelectInvoice(invoice.id, e.target.checked)}
                            />
                          </td>
                        )}
                        <td className="py-4 px-6 text-xs font-medium text-gray-900">
                          {invoice.invoice_number}
                        </td>
                        <td className="py-4 px-6 text-xs text-gray-700">
                          {invoice.customer_name || 'N/A'}
                        </td>
                        <td className="py-4 px-6 text-xs text-gray-700">
                          {formatDate(invoice.issue_date)}
                        </td>
                        <td className="py-4 px-6 text-xs text-gray-700">
                          {invoice.due_date ? formatDate(invoice.due_date) : 'N/A'}
                        </td>
                        <td className="py-4 px-6">
                          <span className={cn(
                            "inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium",
                            getStatusBadgeClass(invoice.status)
                          )}>
                            {invoice.status}
                          </span>
                        </td>
                        <td className="py-4 px-6 text-xs font-medium text-gray-900">
                          {formatCurrency(invoice.total)}
                        </td>
                        <td className="py-4 px-6 text-center">
                          <div className="flex items-center justify-center relative w-full">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <span className="sr-only">Open menu</span>
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end" className="w-48">
                                {getContextMenuItems(invoice)}
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
          
          {/* Pagination */}
          <div className="flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 p-4 border border-gray-200 border-t-0 rounded-b-lg bg-gray-50">
            {/* Left: Items per page selector */}
            <div className="flex items-center gap-2 text-xs text-gray-600">
              <span>Show</span>
              <select
                value={pagination.pageSize}
                onChange={(e) => handlePageSizeChange(Number(e.target.value))}
                className="rounded border border-gray-200 px-2 py-1 text-xs h-7"
              >
                <option value={5}>5</option>
                <option value={10}>10</option>
                <option value={20}>20</option>
                <option value={50}>50</option>
              </select>
              <span>per page</span>
            </div>
            
            {/* Right: Pagination Controls and Stats */}
            <div className="flex items-center w-full md:w-auto justify-end">
              {/* Item Range and Navigation */}
              <div className="flex items-center gap-2">
                <p className="text-xs text-gray-600">
                  <span className="hidden sm:inline">Showing </span>
                  <span className="font-medium">{Math.min((pagination.currentPage - 1) * pagination.pageSize + 1, paginationConfig.totalItems)}-{Math.min(pagination.currentPage * pagination.pageSize, paginationConfig.totalItems)}</span>
                  <span className="hidden sm:inline"> of </span>
                  <span className="sm:hidden">/</span>
                  <span className="font-medium">{paginationConfig.totalItems}</span>
                </p>
                
                <div className="flex items-center rounded-md border border-gray-200 overflow-hidden">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(pagination.currentPage - 1)}
                    disabled={pagination.currentPage <= 1}
                    className="h-7 w-7 p-0 rounded-none border-r border-gray-200"
                  >
                    <ChevronLeft className="h-3.5 w-3.5 text-gray-600" />
                  </Button>
                  
                  <div className="hidden sm:flex">
                    {Array.from({ length: Math.min(3, paginationConfig.totalPages) }, (_, i) => {
                      const page = Math.max(1, pagination.currentPage - 1) + i
                      if (page > paginationConfig.totalPages) return null
                      
                      return (
                        <Button
                          key={page}
                          variant={page === pagination.currentPage ? "default" : "ghost"}
                          size="sm"
                          onClick={() => handlePageChange(page)}
                          className={`h-7 w-7 p-0 rounded-none border-r border-gray-200 text-xs ${
                            page === pagination.currentPage 
                              ? "bg-blue-600 hover:bg-blue-700 text-white" 
                              : "text-gray-700"
                          }`}
                        >
                          {page}
                        </Button>
                      )
                    })}
                  </div>
                  
                  <div className="sm:hidden flex items-center justify-center h-7 w-7 text-xs font-medium border-r border-gray-200">
                    {pagination.currentPage}
                  </div>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(pagination.currentPage + 1)}
                    disabled={pagination.currentPage >= paginationConfig.totalPages}
                    className="h-7 w-7 p-0 rounded-none"
                  >
                    <ChevronRight className="h-3.5 w-3.5 text-gray-600" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </>
  )
}