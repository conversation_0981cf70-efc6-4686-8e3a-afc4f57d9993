'use client'

import { use<PERSON>emo } from 'react'
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { MetricCard } from '@/components/ui/metric-card'
import { PageHeader } from '@/components/ui/page-header'
import { 
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js'
import { Bar, Pie } from 'react-chartjs-2'
import { useCurrency } from '@/lib/currency'
import { useAllExpenses } from '@/hooks/use-expenses'
import { useAuth } from '@/contexts/auth-context'
import { Skeleton } from '@/components/ui/skeleton'
import { AlertCircle, Wallet, TrendingUp, Pie<PERSON>hart as PieChartIcon } from 'lucide-react'

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
)

interface AnalyticsTabProps {
  isMobile?: boolean
  contextualFilters?: any
  filteredExpenseIds?: string[]
}

const COLORS = [
  'rgba(54, 162, 235, 0.8)',
  'rgba(75, 192, 192, 0.8)',
  'rgba(255, 206, 86, 0.8)',
  'rgba(255, 159, 64, 0.8)',
  'rgba(153, 102, 255, 0.8)',
  'rgba(255, 99, 132, 0.8)'
]

export function AnalyticsTab({ 
  isMobile = false, 
  contextualFilters,
  filteredExpenseIds = []
}: AnalyticsTabProps) {
  const { user, organizationId } = useAuth()
  const { userCurrency, formatCurrency } = useCurrency()
  
  // Use React Query to fetch expenses
  const { data: expenses = [], isLoading, error } = useAllExpenses(organizationId || undefined)

  // Filter expenses based on contextual filters and filteredExpenseIds
  const filteredExpenses = useMemo(() => {
    let result = expenses
    
    // Apply filteredExpenseIds filter if provided
    if (filteredExpenseIds.length > 0) {
      result = result.filter(expense => filteredExpenseIds.includes(expense.id))
    }
    
    // Apply contextual filters if provided
    if (contextualFilters) {
      // Text search
      if (contextualFilters.searchQuery?.trim()) {
        const query = contextualFilters.searchQuery.toLowerCase()
        result = result.filter(expense => 
          expense.description?.toLowerCase().includes(query) ||
          expense.vendor?.toLowerCase().includes(query) ||
          expense.category?.toLowerCase().includes(query) ||
          expense.expense_id?.toLowerCase().includes(query)
        )
      }

      // Category filter
      if (contextualFilters.categories?.length > 0) {
        result = result.filter(expense => 
          expense.category && contextualFilters.categories.includes(expense.category)
        )
      }

      // Payment method filter
      if (contextualFilters.paymentMethods?.length > 0) {
        result = result.filter(expense => 
          expense.payment_method && contextualFilters.paymentMethods.includes(expense.payment_method)
        )
      }

      // Date range filter
      if (contextualFilters.dateRange?.start || contextualFilters.dateRange?.end) {
        result = result.filter(expense => {
          const expenseDate = new Date(expense.expense_date || expense.created_at)
          
          if (contextualFilters.dateRange.start && expenseDate < contextualFilters.dateRange.start) {
            return false
          }
          
          if (contextualFilters.dateRange.end && expenseDate > contextualFilters.dateRange.end) {
            return false
          }
          
          return true
        })
      }

      // Amount range filter
      if (contextualFilters.amountRange?.min !== null || contextualFilters.amountRange?.max !== null) {
        result = result.filter(expense => {
          if (contextualFilters.amountRange.min !== null && expense.amount < contextualFilters.amountRange.min) {
            return false
          }
          
          if (contextualFilters.amountRange.max !== null && expense.amount > contextualFilters.amountRange.max) {
            return false
          }
          
          return true
        })
      }
    }
    
    return result
  }, [expenses, filteredExpenseIds, contextualFilters])

  // Calculate analytics data
  const analyticsData = useMemo(() => {
    if (filteredExpenses.length === 0) return null

    // Group expenses by category
    const categoryMap = new Map<string, { total: number, count: number }>()
    let totalAmount = 0
    
    filteredExpenses.forEach(expense => {
      totalAmount += expense.amount
      const category = expense.category || 'Uncategorized'
      const current = categoryMap.get(category) || { total: 0, count: 0 }
      categoryMap.set(category, {
        total: current.total + expense.amount,
        count: current.count + 1
      })
    })

    // Convert to array and sort by total amount
    const categoryData = Array.from(categoryMap.entries())
      .map(([name, data], index) => ({
        name,
        total: data.total,
        count: data.count,
        percentage: totalAmount > 0 ? (data.total / totalAmount) * 100 : 0,
        color: COLORS[index % COLORS.length]
      }))
      .sort((a, b) => b.total - a.total)

    // Get the top spending category
    const topCategory = categoryData.length > 0 ? categoryData[0] : null

    return {
      totalExpenses: filteredExpenses.length,
      totalAmount,
      categoryData,
      topCategory
    }
  }, [filteredExpenses])

  // Generate chart data for bar chart
  const barChartData = useMemo(() => {
    if (!analyticsData) {
      return {
        labels: [],
        datasets: []
      }
    }

    return {
      labels: analyticsData.categoryData.map(category => category.name),
      datasets: [
        {
          label: 'Total Amount',
          data: analyticsData.categoryData.map(category => category.total),
          backgroundColor: 'rgba(136, 132, 216, 0.8)',
          borderColor: 'rgba(136, 132, 216, 1)',
          borderWidth: 1
        }
      ]
    }
  }, [analyticsData])

  // Generate chart data for pie chart
  const pieChartData = useMemo(() => {
    if (!analyticsData) {
      return {
        labels: [],
        datasets: []
      }
    }

    return {
      labels: analyticsData.categoryData.map(category => category.name),
      datasets: [
        {
          data: analyticsData.categoryData.map(category => category.total),
          backgroundColor: analyticsData.categoryData.map(category => category.color),
          borderWidth: 1
        }
      ]
    }
  }, [analyticsData])

  // Chart options for bar chart
  const barChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            return `${context.dataset.label}: ${formatCurrency(context.parsed.y)}`
          }
        }
      }
    },
    scales: {
      x: {
        grid: {
          display: false
        },
        ticks: {
          autoSkip: false,
          maxRotation: isMobile ? 45 : 0,
          minRotation: isMobile ? 45 : 0
        }
      },
      y: {
        ticks: {
          callback: function(value: any) {
            return formatCurrency(value)
          }
        }
      }
    }
  }

  // Chart options for pie chart
  const pieChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right' as const,
        labels: {
          usePointStyle: true,
          padding: 20
        }
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            const label = context.label || ''
            const value = context.raw || 0
            const percentage = analyticsData?.categoryData.find(cat => cat.name === label)?.percentage || 0
            return `${label}: ${formatCurrency(value)} (${percentage.toFixed(1)}%)`
          }
        }
      }
    }
  }

  // Render skeleton loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <Skeleton className="h-4 w-24" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-32" />
              </CardContent>
            </Card>
          ))}
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
            </CardHeader>
            <CardContent className="h-80">
              <Skeleton className="h-full w-full" />
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
            </CardHeader>
            <CardContent className="h-80">
              <Skeleton className="h-full w-full" />
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  // Render error state
  if (error) {
    // Type guard to ensure error is properly typed
    const errorMessage = error instanceof Error ? error.message : String(error);
    
    let userFriendlyMessage = "Failed to load expense data for analytics. Please try again."
    
    // Provide more specific error messages based on the error type
    if (errorMessage.includes("NetworkError") || errorMessage.includes("Failed to fetch")) {
      userFriendlyMessage = "Network connection error. Please check your internet connection and try again."
    } else if (errorMessage.includes("ERR_INSUFFICIENT_RESOURCES")) {
      userFriendlyMessage = "System resource limit reached. Please refresh the page or try again in a few minutes."
    } else if (errorMessage.includes("timeout")) {
      userFriendlyMessage = "Request timed out. Please try again."
    } else if (errorMessage.includes("401") || errorMessage.includes("403")) {
      userFriendlyMessage = "Authentication error. Please sign in again."
    } else if (errorMessage.includes("500")) {
      userFriendlyMessage = "Server error. Please try again later."
    }
    
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center max-w-md">
          <div className="text-red-500 mb-2">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <h3 className="text-lg font-medium">Unable to Load Expense Analytics</h3>
          </div>
          <p className="text-muted-foreground text-sm mb-4">{userFriendlyMessage}</p>
          <Button 
            onClick={() => window.location.reload()} 
            variant="default" 
            size="sm"
          >
            Refresh Page
          </Button>
          <p className="text-xs text-muted-foreground mt-4">
            Error: {errorMessage}
          </p>
        </div>
      </div>
    )
  }

  // Render empty state
  if (!analyticsData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h3 className="text-lg font-medium mb-2">No Expense Data</h3>
          <p className="text-muted-foreground">
            Add some expenses to see analytics data.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <PageHeader 
        title="Expense Analytics" 
        description="Track and analyze your business expenses"
      />
      
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <MetricCard
          title="Total Expenses"
          value={analyticsData.totalExpenses}
          subtitle="Recorded expenses"
          icon={<Wallet className="h-4 w-4 text-primary" />}
        />
        <MetricCard
          title="Total Amount"
          value={formatCurrency(analyticsData.totalAmount)}
          subtitle="Total expenditure"
          icon={<TrendingUp className="h-4 w-4 text-primary" />}
        />
        <MetricCard
          title="Avg. per Expense"
          value={formatCurrency(analyticsData.totalExpenses > 0 ? analyticsData.totalAmount / analyticsData.totalExpenses : 0)}
          subtitle="Average spending"
          icon={<PieChartIcon className="h-4 w-4 text-primary" />}
        />
        <MetricCard
          title="Top Category"
          value={analyticsData.topCategory ? analyticsData.topCategory.name : "N/A"}
          subtitle={analyticsData.topCategory ? `${formatCurrency(analyticsData.topCategory.total)} (${analyticsData.topCategory.percentage.toFixed(1)}%)` : "No data"}
          icon={<PieChartIcon className="h-4 w-4 text-primary" />}
        />
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Bar Chart - Expenses by Category */}
        <Card>
          <CardHeader>
            <CardTitle>Expenses by Category</CardTitle>
          </CardHeader>
          <CardContent className="h-80">
            <Bar data={barChartData} options={barChartOptions} />
          </CardContent>
        </Card>

        {/* Pie Chart - Expense Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Expense Distribution</CardTitle>
          </CardHeader>
          <CardContent className="h-80">
            <Pie data={pieChartData} options={pieChartOptions} />
          </CardContent>
        </Card>
      </div>

      {/* Category Breakdown Table */}
      <Card>
        <CardHeader>
          <CardTitle>Category Breakdown</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2 px-4">Category</th>
                  <th className="text-right py-2 px-4">Total Amount</th>
                  <th className="text-right py-2 px-4">Number of Expenses</th>
                  <th className="text-right py-2 px-4">Percentage</th>
                </tr>
              </thead>
              <tbody>
                {analyticsData.categoryData.map((category) => (
                  <tr key={category.name} className="border-b">
                    <td className="py-2 px-4">{category.name}</td>
                    <td className="text-right py-2 px-4">{formatCurrency(category.total)}</td>
                    <td className="text-right py-2 px-4">{category.count}</td>
                    <td className="text-right py-2 px-4">{category.percentage.toFixed(1)}%</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}