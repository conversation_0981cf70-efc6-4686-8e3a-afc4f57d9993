-- SQL script to update the integrations table to properly support Vault integration
-- This script ensures the table structure can accommodate Vault-specific fields

-- Add comments to clarify the purpose of each column
COMMENT ON COLUMN public.integrations.platform IS 'The integration platform (e.g., woocommerce, vault)';
COMMENT ON COLUMN public.integrations.store_url IS 'The URL of the external service (e.g., WooCommerce store URL, Vault API URL)';
COMMENT ON COLUMN public.integrations.consumer_key IS 'API key or client ID for authentication';
COMMENT ON COLUMN public.integrations.consumer_secret IS 'API secret or client secret for authentication';
COMMENT ON COLUMN public.integrations.consumer_secret_ref IS 'Reference to the secret in secure storage (e.g., webhook ID, secret name)';

-- Add a check constraint to ensure valid platform values
ALTER TABLE public.integrations 
ADD CONSTRAINT valid_platform CHECK (platform IN ('woocommerce', 'vault'));

-- Update the existing integration to use the correct column names for Vault
-- For Vault integrations, we'll use:
-- store_url: Vault API URL
-- consumer_key: API Key
-- consumer_secret: Account ID (optional)
-- consumer_secret_ref: Secret reference for secure storage

-- Example of how to insert a Vault integration:
/*
INSERT INTO public.integrations (
  organization_id,
  platform,
  store_url,
  consumer_key,
  consumer_secret,
  status,
  secret_name,
  consumer_secret_ref
) VALUES (
  'YOUR_ORGANIZATION_ID',
  'vault',
  'https://api.vault.com',
  'YOUR_API_KEY',
  'YOUR_ACCOUNT_ID',
  'connected',
  'vault_secret_' || gen_random_uuid(),
  gen_random_uuid()::TEXT
);
*/

-- Create an index for faster platform lookups
CREATE INDEX IF NOT EXISTS idx_integrations_platform ON public.integrations (platform);

-- Create an index for faster organization lookups
CREATE INDEX IF NOT EXISTS idx_integrations_organization ON public.integrations (organization_id);