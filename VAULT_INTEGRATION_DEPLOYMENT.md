# Vault Integration Deployment Guide

This guide explains how to properly deploy and configure the Vault integration for Onko.

## Prerequisites

1. Supabase project with the Onko database schema
2. Supabase CLI installed
3. Deno runtime for edge functions
4. Vault account with API access

## Deployment Steps

### 1. Deploy the Database Function

First, deploy the `connect_vault` database function:

```sql
-- Run this in your Supabase SQL Editor or via Supabase CLI
-- Make sure to replace the file path with the correct location
\i CONNECT_VAULT_FUNCTION.sql
```

### 2. Deploy the Edge Function

Deploy the Vault connection edge function:

```bash
# Navigate to your project directory
cd supabase/functions/connect-vault

# Deploy the function
supabase functions deploy connect-vault
```

### 3. Update RLS Policies (if needed)

Ensure that the integrations table has proper Row Level Security policies:

```sql
-- Make sure users can only access their organization's integrations
ALTER TABLE integrations ENABLE ROW LEVEL SECURITY;

-- Policy for selecting integrations
CREATE POLICY "Users can view integrations for their organization" 
ON integrations FOR SELECT 
USING (
  organization_id IN (
    SELECT organization_id 
    FROM organization_members 
    WHERE user_id = auth.uid()
  )
);

-- Policy for inserting integrations
CREATE POLICY "Users can insert integrations for their organization" 
ON integrations FOR INSERT 
WITH CHECK (
  organization_id IN (
    SELECT organization_id 
    FROM organization_members 
    WHERE user_id = auth.uid()
  )
);

-- Policy for updating integrations
CREATE POLICY "Users can update integrations for their organization" 
ON integrations FOR UPDATE 
USING (
  organization_id IN (
    SELECT organization_id 
    FROM organization_members 
    WHERE user_id = auth.uid()
  )
);

-- Policy for deleting integrations
CREATE POLICY "Users can delete integrations for their organization" 
ON integrations FOR DELETE 
USING (
  organization_id IN (
    SELECT organization_id 
    FROM organization_members 
    WHERE user_id = auth.uid()
  )
);
```

### 4. Test the Integration

1. In the Onko application, navigate to Settings > Integrations
2. Click "Connect" on the Vault integration card
3. Enter your Vault credentials:
   - Vault URL: Your Vault API endpoint
   - API Key: Your Vault API key
   - Account ID: (Optional) Your Vault account identifier
4. Click "Test Connection" to verify your credentials
5. If successful, click "Connect Vault"

## Troubleshooting

### Common Issues

#### 1. "User not authenticated" Error
- Ensure you're logged into the Onko application
- Check that your session is still valid

#### 2. "User is not a member of any organization" Error
- Verify you're a member of an organization
- Contact your organization owner if you need access

#### 3. "Vault URL and API Key are required" Error
- Make sure both fields are filled in
- Check that the URL is properly formatted

#### 4. "Invalid Vault URL format" Error
- Ensure the URL starts with http:// or https://
- Verify the URL is accessible

### Checking Function Logs

To debug issues with the edge function:

```bash
# View logs for the connect-vault function
supabase functions logs connect-vault
```

## Security Considerations

### Credential Storage

Vault credentials are stored securely:
- Actual secrets are stored in the database with restricted access
- Database only contains references to the secrets
- Secrets are never exposed in API responses
- RLS policies ensure users can only access their organization's data

### Data Encryption

All data transmitted between Onko and Vault is encrypted:
- TLS for data in transit
- Database encryption for data at rest
- Regular security audits and compliance checks

## API Endpoints

The integration uses the following Vault API endpoints:
- `GET /api/v1/accounts` - For account verification
- `GET /api/v1/transactions` - For transaction data sync
- `POST /api/v1/transactions` - For creating new transactions
- `PUT /api/v1/transactions/{id}` - For updating existing transactions

## Maintenance

### Regular Tasks

1. **Monitor Function Logs**: Check for errors or unusual activity
2. **Review Connected Accounts**: Ensure only authorized integrations exist
3. **Rotate API Keys**: Regularly update Vault API keys for security
4. **Update Documentation**: Keep this guide updated with any changes

### Updating the Integration

When making changes to the integration:

1. Update the edge function code
2. Redeploy the function:
   ```bash
   supabase functions deploy connect-vault
   ```
3. Update the database function if needed:
   ```sql
   -- Run the updated CONNECT_VAULT_FUNCTION.sql file
   ```
4. Test the integration thoroughly

## Support

If you continue to experience issues, please contact support with:
- Error messages you're seeing
- Steps you've already taken to troubleshoot
- Screenshots of connection settings if relevant