# Storage Setup Instructions

## Overview
This document provides step-by-step instructions to properly configure Supabase Storage for the ONKO application, specifically for profile picture and company logo uploads.

## Prerequisites
Before setting up storage policies, ensure you have:
1. A Supabase project
2. The avatars bucket created (run `node scripts/setup-avatars-bucket.js` or create manually)
3. The company-logos bucket created (run `node scripts/setup-company-logos-bucket.js` or create manually)

## Step 1: Run the Setup Scripts
First, run the scripts to ensure both buckets exist with correct settings:

```bash
node scripts/setup-avatars-bucket.js
node scripts/setup-company-logos-bucket.js
```

If these fail or you prefer manual setup, follow the manual instructions below.

## Step 2: Set Up Storage Policies Manually (Required)

### Using Supabase Dashboard UI (Recommended)
Storage policies in Supabase are best managed through the dashboard UI:

1. Go to your Supabase dashboard
2. Select your project
3. Navigate to Storage > Buckets
4. Find the "avatars" and "company-logos" buckets (create them if they don't exist with settings below)
5. Click on the "Policies" tab for each bucket
6. Create the following policies for each bucket:

#### Insert (Upload) Policy
- **Name**: Authenticated users can upload files
- **Operation**: INSERT
- **Roles**: authenticated
- **Policy Definition**: `bucket_id = 'BUCKET_NAME' AND auth.role() = 'authenticated'`
(Replace BUCKET_NAME with 'avatars' or 'company-logos')

#### Select (Download) Policy
- **Name**: Anyone can view files
- **Operation**: SELECT
- **Roles**: public
- **Policy Definition**: `bucket_id = 'BUCKET_NAME'`
(Replace BUCKET_NAME with 'avatars' or 'company-logos')

#### Update Policy
- **Name**: Users can update their own files
- **Operation**: UPDATE
- **Roles**: authenticated
- **Policy Definition**: `bucket_id = 'BUCKET_NAME' AND auth.uid()::text = (storage.foldername(name))[1]`
(Replace BUCKET_NAME with 'avatars' or 'company-logos')

#### Delete Policy
- **Name**: Users can delete their own files
- **Operation**: DELETE
- **Roles**: authenticated
- **Policy Definition**: `bucket_id = 'BUCKET_NAME' AND auth.uid()::text = (storage.foldername(name))[1]`
(Replace BUCKET_NAME with 'avatars' or 'company-logos')

### Manual Bucket Creation (if needed)
If the buckets don't exist, create them manually:
1. Go to Storage > Buckets
2. Click "New Bucket"
3. For avatars bucket:
   - **Name**: avatars
   - **Public**: Yes (checked)
   - **File size limit**: 5MB
   - **Allowed MIME types**: 
     - image/jpeg
     - image/png
     - image/gif
     - image/webp
4. For company-logos bucket:
   - **Name**: company-logos
   - **Public**: Yes (checked)
   - **File size limit**: 5MB
   - **Allowed MIME types**: 
     - image/jpeg
     - image/png
     - image/gif
     - image/webp
     - image/svg+xml

## Verification
After setting up the policies:

1. Try uploading a profile picture in the application
2. Try uploading a company logo in the application
3. Check the browser console for any errors
4. Verify the files appear in the respective buckets in your Supabase dashboard

## Troubleshooting
If you continue to experience issues:

1. Double-check that all four policies have been created correctly for both buckets
2. Ensure both buckets exist and have the correct settings:
   - Public: Yes
   - File size limit: 5MB
   - Allowed MIME types: image/jpeg, image/png, image/gif, image/webp, image/svg+xml (for company-logos)
3. Check that your Supabase environment variables are correctly set in `.env.local`
4. Verify that users are properly authenticated when uploading files

## Additional Notes
- The file structure in the avatars bucket should be `{user_id}/avatar.{extension}`
- The file structure in the company-logos bucket should be `{user_id}/company-logo.{extension}`
- Users can only access their own files due to the RLS policies
- Public read access allows anyone to view images (needed for displaying profile pictures and company logos)
- If you encounter permission errors when running SQL scripts, it's because storage policies are typically managed through the dashboard UI rather than SQL commands