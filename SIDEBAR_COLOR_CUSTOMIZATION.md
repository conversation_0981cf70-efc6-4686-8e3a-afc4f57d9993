# Sidebar Color Customization Guide

This guide explains how to easily customize the sidebar colors in the ONKO application.

## Overview

All sidebar colors are now centralized in the `app/globals.css` file using CSS variables. This makes it easy to change colors in one place and have them applied throughout the application.

## Location of Color Definitions

The sidebar color variables are defined in `app/globals.css` in the "SIDEBAR COLOR SYSTEM" section.

## Available CSS Variables

| Variable | Description | Default Light Mode | Default Dark Mode |
|----------|-------------|--------------------|-------------------|
| `--sidebar-bg` | Background color of the entire sidebar | `0 0% 100%` (white) | `0 0% 3.9%` (dark gray) |
| `--sidebar-text` | Default text color for sidebar items | `0 0% 30%` (dark gray) | `0 0% 70%` (light gray) |
| `--sidebar-text-hover` | Text color when hovering over sidebar items | `0 0% 15%` (darker gray) | `0 0% 95%` (near white) |
| `--sidebar-hover-bg` | Background color when hovering over sidebar items | `0 0% 95%` (light gray) | `0 0% 15%` (medium gray) |
| `--sidebar-active-bg` | Background color for active parent menu items | `216 83% 43%` (blue) | `216 83% 43%` (blue) |
| `--sidebar-active-text` | Text color for active parent menu items | `0 0% 100%` (white) | `0 0% 100%` (white) |
| `--sidebar-submenu-active-bg` | Background color for active submenu items | `216 83% 43%` (blue) | `216 83% 43%` (blue) |
| `--sidebar-submenu-active-text` | Text color for active submenu items | `0 0% 100%` (white) | `0 0% 100%` (white) |
| `--sidebar-border` | Border color for sidebar elements | `0 0% 85%` (light gray) | `0 0% 15%` (dark gray) |
| `--sidebar-icon-active` | Color for icons in active menu items | `216 83% 43%` (blue) | `216 83% 43%` (blue) |

## How to Customize Colors

1. Open `app/globals.css`
2. Find the "SIDEBAR COLOR SYSTEM" section
3. Modify the CSS variables under `:root` for light mode or `.dark` for dark mode
4. Save the file
5. The changes will be applied automatically throughout the application

## Color Format

Colors are defined using the HSL format: `H S L`
- H: Hue (0-360)
- S: Saturation (0-100%)
- L: Lightness (0-100%)

Example: `216 83% 43%` represents a blue color.

## Example Customization

To change the sidebar background to a light blue in light mode:

```css
:root {
  /* Change this line */
  --sidebar-bg: 216 50% 95%; /* Light blue */
  /* ... other variables */
}
```

To change the active menu background to green:

```css
:root {
  /* Change this line */
  --sidebar-active-bg: 120 70% 40%; /* Green */
  --sidebar-submenu-active-bg: 120 70% 40%; /* Green */
  --sidebar-icon-active: 120 70% 40%; /* Green */
  /* ... other variables */
}
```

## Custom CSS Classes

Two custom CSS classes are also available for more specific styling:
- `.bg-sidebar-submenu-active`: Background for active submenu items
- `.text-sidebar-submenu-text`: Text color for active submenu items

These classes automatically use the corresponding CSS variables.