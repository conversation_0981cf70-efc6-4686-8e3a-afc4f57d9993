import React from 'react'
import { cn } from '@/lib/utils'

interface ProgressIndicatorProps {
  progress: number // 0-100
  className?: string
}

export function ProgressIndicator({ progress, className }: ProgressIndicatorProps) {
  return (
    <div className={cn('space-y-2', className)}>
      <div className="flex items-center justify-between text-xs">
        <span className="text-gray-600">Form Progress</span>
        <span className="font-medium text-gray-900">{Math.round(progress)}%</span>
      </div>
      
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div 
          className="bg-blue-500 h-2 rounded-full transition-all duration-300 ease-out"
          style={{ width: `${progress}%` }}
        />
      </div>
      
      <div className="text-xs text-gray-500">
        {progress < 30 && "Just getting started..."}
        {progress >= 30 && progress < 60 && "Making good progress!"}
        {progress >= 60 && progress < 90 && "Almost there!"}
        {progress >= 90 && progress < 100 && "Final touches..."}
        {progress === 100 && "Ready to save!"}
      </div>
    </div>
  )
}
