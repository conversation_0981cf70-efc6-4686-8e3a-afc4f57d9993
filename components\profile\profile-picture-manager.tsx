'use client'

import { useState, useRef } from 'react'
import { useAuth } from '@/contexts/auth-context'
import { createSupabaseClient } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import { useToast } from '@/components/ui/use-toast'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { User } from 'lucide-react'

interface ProfilePictureManagerProps {
  currentAvatarUrl: string | null
  onAvatarUpdate: (newAvatarUrl: string | null) => void
}

export function ProfilePictureManager({ 
  currentAvatarUrl, 
  onAvatarUpdate 
}: ProfilePictureManagerProps) {
  const { user } = useAuth()
  const { toast } = useToast()
  const supabase = createSupabaseClient()
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [isUploading, setIsUploading] = useState(false)
  // Use local state for avatar rendering to prevent flickering from parent re-renders
  const [localAvatarUrl, setLocalAvatarUrl] = useState<string | null>(currentAvatarUrl)
  const [avatarTimestamp, setAvatarTimestamp] = useState<number>(Date.now())

  // Update local state when prop changes significantly
  if (currentAvatarUrl !== localAvatarUrl) {
    setLocalAvatarUrl(currentAvatarUrl)
    // Only update timestamp when avatar actually changes
    if ((currentAvatarUrl && !localAvatarUrl) || (!currentAvatarUrl && localAvatarUrl)) {
      setAvatarTimestamp(Date.now())
    }
  }

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file || !user) return

    // Validate file type
    if (!file.type.startsWith('image/jpeg')) {
      toast({
        title: 'Invalid file type',
        description: 'Please select a JPG image file only',
        variant: 'destructive'
      })
      return
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: 'File too large',
        description: 'Please select an image smaller than 5MB',
        variant: 'destructive'
      })
      return
    }

    setIsUploading(true)
    
    try {
      // Upload file to Supabase Storage
      const fileExt = file.name.split('.').pop() || 'jpg' // Default to jpg if no extension
      const fileName = `${user.id}/avatar.${fileExt}`
      
      const { error: uploadError } = await supabase.storage
        .from('avatars')
        .upload(fileName, file, {
          upsert: true
        })

      if (uploadError) throw uploadError

      // Get public URL for the uploaded file
      const { data: { publicUrl } } = supabase.storage
        .from('avatars')
        .getPublicUrl(fileName)

      // Update profile with new avatar URL
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ avatar_url: publicUrl })
        .eq('id', user.id)

      if (updateError) throw updateError

      // Update local state
      setLocalAvatarUrl(publicUrl)
      setAvatarTimestamp(Date.now())
      
      // Update parent component state
      onAvatarUpdate(publicUrl)
      
      // Dispatch event for header to update avatar - using a custom event with timestamp
      const timestamp = Date.now();
      window.dispatchEvent(new CustomEvent('profileUpdated', {
        detail: { 
          type: 'avatar',
          url: publicUrl,
          timestamp: timestamp
        }
      }));

      // Also store the update in localStorage as a fallback mechanism
      localStorage.setItem('lastProfileUpdate', JSON.stringify({
        type: 'avatar',
        url: publicUrl,
        timestamp: timestamp
      }));

      toast({
        title: 'Profile picture updated',
        description: 'Your profile picture has been successfully updated'
      })
    } catch (error) {
      console.error('Error uploading profile picture:', error)
      toast({
        title: 'Upload failed',
        description: 'Failed to update profile picture. Please try again.',
        variant: 'destructive'
      })
    } finally {
      setIsUploading(false)
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const handleRemovePicture = async () => {
    if (!user) return

    try {
      // Remove avatar URL from profile
      const { error } = await supabase
        .from('profiles')
        .update({ avatar_url: null })
        .eq('id', user.id)

      if (error) throw error

      // Update local state
      setLocalAvatarUrl(null)
      setAvatarTimestamp(Date.now())
      
      // Update parent component state
      onAvatarUpdate(null)
      
      // Dispatch event for header to update avatar - using a custom event with timestamp
      const timestamp = Date.now();
      window.dispatchEvent(new CustomEvent('profileUpdated', {
        detail: { 
          type: 'avatar',
          url: null,
          timestamp: timestamp
        }
      }));

      // Also store the update in localStorage as a fallback mechanism
      localStorage.setItem('lastProfileUpdate', JSON.stringify({
        type: 'avatar',
        url: null,
        timestamp: timestamp
      }));

      toast({
        title: 'Profile picture removed',
        description: 'Your profile picture has been successfully removed'
      })
    } catch (error) {
      console.error('Error removing profile picture:', error)
      toast({
        title: 'Removal failed',
        description: 'Failed to remove profile picture. Please try again.',
        variant: 'destructive'
      })
    }
  }

  const triggerFileSelect = () => {
    fileInputRef.current?.click()
  }

  return (
    <div className="flex flex-col items-center gap-3">
      <Avatar className="w-16 h-16">
        {localAvatarUrl ? (
          <AvatarImage 
            src={`${localAvatarUrl}?v=${avatarTimestamp}`} 
            alt="Profile picture" 
          />
        ) : (
          <AvatarFallback className="bg-gray-100">
            <User className="w-8 h-8 text-gray-500" />
          </AvatarFallback>
        )}
      </Avatar>
      
      <div className="flex gap-2">
        <Button 
          variant="outline" 
          onClick={triggerFileSelect}
          disabled={isUploading}
          className="h-8 px-2 text-xs"
        >
          {isUploading ? 'Uploading...' : 'Upload'}
        </Button>
        
        {localAvatarUrl && (
          <Button 
            variant="outline" 
            onClick={handleRemovePicture}
            disabled={isUploading}
            className="h-8 px-2 text-xs"
          >
            Remove
          </Button>
        )}
      </div>
      
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        accept="image/jpeg"
        onChange={handleFileChange}
      />
      
      <p className="text-xs text-muted-foreground text-center">
        JPG files only, up to 5MB
      </p>
    </div>
  )
}