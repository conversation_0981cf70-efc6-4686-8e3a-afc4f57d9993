# ONKO Web App - Implementation Patterns and Best Practices

This document outlines the consistent implementation patterns used throughout the ONKO web application to ensure maintainability, scalability, and a consistent user experience.

## 1. Data Fetching with React Query

### Custom Hooks
All data fetching is implemented through custom React Query hooks:
- Products: `useAllProducts`, `useProducts`, `useInvalidateProducts`
- Expenses: `useAllExpenses`, `useInvalidateExpenses`

### Query Configuration
```typescript
{
  queryKey: ['entityName', userId],
  staleTime: 5 * 60 * 1000, // 5 minutes
  gcTime: 10 * 60 * 1000, // 10 minutes
  refetchOnWindowFocus: false,
  retry: 2,
  retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000)
}
```

### Error Handling
- Proper error boundaries with user-friendly messages
- Specific error handling for network, timeout, and authentication issues
- Retry mechanisms with exponential backoff

## 2. Component Structure

### Tab Components
Each major section is divided into tab components:
- Main data view (ProductsTab, ExpensesTab)
- Analytics dashboard (AnalyticsTab)
- Configuration (SettingsTab)

### Loading States
- Skeleton loading components for all data-fetching UI
- Consistent loading indicators
- Proper handling of initial load vs. refresh states

### Error States
- User-friendly error messages
- Retry functionality
- Specific error handling based on error type

## 3. Data Filtering and Contextual Integration

### Filter Components
- Dedicated filter components for each data type
- Support for multiple filter criteria (text search, categories, date ranges, etc.)
- URL synchronization for filter persistence

### Contextual Filtering
- Ability to pass filtered data between components
- Support for filtered views in analytics and settings

## 4. UI/UX Consistency

### Export Functionality
- CSV export for all data tables
- Proper data formatting and escaping
- Metadata inclusion in exports

### Refresh Mechanisms
- Manual refresh buttons
- Event-based auto-refresh (custom events)
- Real-time updates with Supabase subscriptions

### Responsive Design
- Mobile-first approach
- Collapsible sections for smaller screens
- Touch-friendly controls

## 5. Data Management

### Real-time Updates
- Supabase real-time subscriptions for automatic data updates
- Proper cleanup of subscriptions
- Event-driven refresh mechanisms

### Bulk Operations
- Support for bulk delete operations
- Confirmation dialogs for destructive actions
- Success/error feedback

### Detailed Views
- Modal or detail views for data items
- Consistent presentation of data
- Action buttons for common operations

## 6. Navigation Structure

### Sidebar Organization
- Expandable menu groups for related functionality
- Automatic expansion based on current route
- Visual indication of active pages

### URL Structure
- Consistent hierarchical URL structure
- Descriptive page names
- Support for deep linking

## 7. Implementation Examples

### Products Section
The products section demonstrates the full implementation pattern:
- ProductsTab: Main product catalog with filtering and bulk operations
- InventoryTab: Specialized inventory view with stock management
- AnalyticsTab: Data visualization with charts and insights
- SettingsTab: Configuration options and data management
- Purchase Orders: Separate section for procurement management

### Expenses Section
Updated to match the products pattern:
- ExpensesTab: Main expense tracking with filtering
- AnalyticsTab: Expense categorization and visualization
- SettingsTab: Configuration and data export options

## 8. Best Practices

### Performance
- Proper caching with React Query
- Debounced invalidation to prevent excessive requests
- Efficient data transformation in hooks

### Maintainability
- Separation of concerns (data fetching, UI, business logic)
- Consistent component structure
- Clear prop interfaces

### User Experience
- Immediate feedback for user actions
- Clear error messaging
- Intuitive navigation
- Consistent interaction patterns

## 9. Future Development Guidelines

All new sections should follow these established patterns:
1. Create dedicated React Query hooks for data fetching
2. Implement tab-based navigation for related functionality
3. Include proper loading and error states
4. Add filtering capabilities where applicable
5. Provide export functionality
6. Implement real-time updates when beneficial
7. Follow responsive design principles
8. Maintain consistency with existing UI components