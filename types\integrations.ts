export interface Integration {
  id: string;
  organization_id: string;
  platform: string;
  store_url: string | null;
  consumer_key: string | null;
  consumer_secret: string | null;
  secret_name: string | null;
  status: 'connected' | 'disconnected';
  created_at: string;
  updated_at?: string;
}

export interface SyncedOrder {
  id: string;
  organization_id: string;
  integration_id: string;
  external_order_id: string;
  onko_order_id: string | null;
  synced_at: string;
}

export interface ConnectWooCommerceParams {
  storeUrl: string;
  consumerKey: string;
  consumerSecret: string;
}

// New interface for Vault integration
export interface VaultIntegration extends Integration {
  api_key: string | null;
  vault_url: string | null;
  account_id: string | null;
}