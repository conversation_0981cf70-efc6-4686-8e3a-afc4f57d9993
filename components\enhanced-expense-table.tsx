'use client'

import { useState, useMemo } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  ChevronUp,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  Download,
  Trash2,
  Edit3,
  Eye,
  FileSpreadsheet,
  Check,
  X
} from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'
import { type ExpenseRow } from '@/lib/supabase'
import { useCurrency } from '@/lib/currency'
import { cn } from '@/lib/utils'

// Sort configuration
type SortField = 'expense_date' | 'amount' | 'category' | 'vendor' | 'description' | 'created_at'
type SortDirection = 'asc' | 'desc'

interface SortConfig {
  field: SortField
  direction: SortDirection
}

// Pagination configuration
interface PaginationConfig {
  currentPage: number
  pageSize: number
  totalItems: number
  totalPages: number
}

interface EnhancedExpenseTableProps {
  expenses: ExpenseRow[]
  onExpenseEdit?: (expense: ExpenseRow) => void
  onExpenseDelete?: (expenseIds: string[]) => void
  onExpenseView?: (expense: ExpenseRow) => void
  showBulkActions?: boolean
  defaultPageSize?: number
}

export function EnhancedExpenseTable({
  expenses,
  onExpenseEdit,
  onExpenseDelete,
  onExpenseView,
  showBulkActions = false,
  defaultPageSize = 10
}: EnhancedExpenseTableProps) {
  const [sortConfig, setSortConfig] = useState<SortConfig>({ 
    field: 'expense_date', 
    direction: 'desc' 
  })
  const [pagination, setPagination] = useState({
    currentPage: 1,
    pageSize: defaultPageSize
  })
  const [isExporting, setIsExporting] = useState(false)
  
  const { toast } = useToast()
  const { formatCurrency } = useCurrency()

  // Sort expenses
  const sortedExpenses = useMemo(() => {
    return [...expenses].sort((a, b) => {
      let aValue: any = a[sortConfig.field]
      let bValue: any = b[sortConfig.field]

      // Handle date fields
      if (sortConfig.field === 'expense_date' || sortConfig.field === 'created_at') {
        aValue = new Date(aValue).getTime()
        bValue = new Date(bValue).getTime()
      }

      // Handle string fields
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase()
        bValue = bValue?.toLowerCase() || ''
      }

      // Handle null/undefined values
      if (aValue === null || aValue === undefined) aValue = ''
      if (bValue === null || bValue === undefined) bValue = ''

      if (aValue < bValue) {
        return sortConfig.direction === 'asc' ? -1 : 1
      }
      if (aValue > bValue) {
        return sortConfig.direction === 'asc' ? 1 : -1
      }
      return 0
    })
  }, [expenses, sortConfig])

  // Paginate expenses
  const paginationConfig: PaginationConfig = useMemo(() => {
    const totalItems = sortedExpenses.length
    const totalPages = Math.ceil(totalItems / pagination.pageSize)
    
    return {
      currentPage: pagination.currentPage,
      pageSize: pagination.pageSize,
      totalItems,
      totalPages
    }
  }, [sortedExpenses.length, pagination])

  const paginatedExpenses = useMemo(() => {
    const startIndex = (pagination.currentPage - 1) * pagination.pageSize
    const endIndex = startIndex + pagination.pageSize
    return sortedExpenses.slice(startIndex, endIndex)
  }, [ sortedExpenses, pagination])

  // Handle sorting
  const handleSort = (field: SortField) => {
    setSortConfig(current => ({
      field,
      direction: current.field === field && current.direction === 'asc' ? 'desc' : 'asc'
    }))
  }

  // Handle pagination
  const handlePageChange = (page: number) => {
    setPagination(current => ({
      ...current,
      currentPage: Math.max(1, Math.min(page, paginationConfig.totalPages))
    }))
  }

  const handlePageSizeChange = (pageSize: number) => {
    setPagination(current => ({
      currentPage: 1,
      pageSize
    }))
  }

  // Export functions
  const exportToCSV = () => {
    setIsExporting(true)
    
    try {
      // Create CSV header with metadata
      const metadataLines = [
        '# Expense Report',
        `# Generated on: ${new Date().toISOString()}`,
        `# Total Expenses: ${sortedExpenses.length}`,
        `# Total Amount: ${sortedExpenses.reduce((sum, exp) => sum + exp.amount, 0).toFixed(2)}`,
        `# Filtered from ${expenses.length} total expenses`,
        '#',
        '# Report Data:'
      ]
      
      // CSV header row
      const headers = ['Expense ID', 'Date', 'Description', 'Category', 'Vendor', 'Payment Method', 'Amount', 'Created At']
      
      // CSV data rows with proper escaping
      const dataRows = sortedExpenses.map(expense => {
        const expenseDate = new Date(expense.expense_date || expense.created_at)
        const createdDate = new Date(expense.created_at)
        
        return [
          expense.expense_id || 'N/A',
          expenseDate.toLocaleDateString('en-US'),
          `"${(expense.description || '').replace(/"/g, '""')}"`, // Escape quotes
          expense.category || '',
          `"${(expense.vendor || '').replace(/"/g, '""')}"`, // Escape quotes
          expense.payment_method || '',
          expense.amount.toFixed(2),
          createdDate.toISOString()
        ].join(',')
      })
      
      // Combine all parts
      const csvContent = [
        ...metadataLines,
        headers.join(','),
        ...dataRows
      ].join('\n')

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `expenses-export-${new Date().toISOString().split('T')[0]}.csv`
      link.style.display = 'none'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      toast({
        title: "CSV Export Complete",
        description: `Successfully exported ${sortedExpenses.length} expenses to CSV format.`,
      })
    } catch (error) {
      console.error('CSV export error:', error)
      toast({
        title: "CSV Export Failed",
        description: "Failed to export expenses. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsExporting(false)
    }
  }

// PDF export functionality removed to match product table design

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  // Get sort icon
  const getSortIcon = (field: SortField) => {
    if (sortConfig.field !== field) {
      return (
        <div className="flex flex-col items-center justify-center ml-1 opacity-50">
          <ChevronUp className="h-3 w-3" />
          <ChevronDown className="h-3 w-3 -mt-1" />
        </div>
      )
    }
    
    if (sortConfig.direction === 'asc') {
      return <ChevronUp className="h-4 w-4 text-blue-600 ml-1" />
    } else {
      return <ChevronDown className="h-4 w-4 text-blue-600 ml-1" />
    }
  }

  return (
    <>
      {expenses.length === 0 ? (
        <div className="text-center py-12 border border-gray-200 rounded-lg bg-white">
          <p className="text-muted-foreground text-lg">No expenses to display.</p>
          <p className="text-sm text-muted-foreground mt-2">Add your first expense to get started.</p>
        </div>
      ) : (
        <>
          {/* Table Container */}
          <div className="border border-gray-200 rounded-t-lg overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 bg-white">
                <thead className="bg-gray-50">
                  <tr>
                    <th 
                      scope="col" 
                      className="text-left py-3 px-6 font-medium text-gray-900 cursor-pointer hover:bg-gray-100 transition-colors text-xs"
                      onClick={() => handleSort('expense_date')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>Date</span>
                        {getSortIcon('expense_date')}
                      </div>
                    </th>
                    
                    <th 
                      scope="col" 
                      className="text-left py-3 px-6 font-medium text-gray-900 cursor-pointer hover:bg-gray-100 transition-colors text-xs"
                      onClick={() => handleSort('description')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>Description</span>
                        {getSortIcon('description')}
                      </div>
                    </th>
                    
                    <th 
                      scope="col" 
                      className="text-left py-3 px-6 font-medium text-gray-900 cursor-pointer hover:bg-gray-100 transition-colors text-xs"
                      onClick={() => handleSort('category')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>Category</span>
                        {getSortIcon('category')}
                      </div>
                    </th>
                    
                    <th 
                      scope="col" 
                      className="text-left py-3 px-6 font-medium text-gray-900 cursor-pointer hover:bg-gray-100 transition-colors text-xs"
                      onClick={() => handleSort('vendor')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>Vendor</span>
                        {getSortIcon('vendor')}
                      </div>
                    </th>
                    
                    <th 
                      scope="col" 
                      className="text-left py-3 px-6 font-medium text-gray-900 text-xs"
                    >
                      Payment Method
                    </th>
                    
                    <th 
                      scope="col" 
                      className="text-left py-3 px-6 font-medium text-gray-900 cursor-pointer hover:bg-gray-100 transition-colors text-xs"
                      onClick={() => handleSort('amount')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>Amount</span>
                        {getSortIcon('amount')}
                      </div>
                    </th>
                    
                    <th scope="col" className="text-center py-3 px-6 font-medium text-gray-900 w-32 text-xs">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {paginatedExpenses.map((expense, index) => (
                    <tr 
                      key={expense.id} 
                      className="transition-colors bg-white hover:bg-gray-50"
                    >
                      <td className="py-4 px-6 text-xs text-gray-700">
                        {formatDate(expense.expense_date || expense.created_at)}
                      </td>
                      
                      <td className="py-4 px-6">
                        <div className="max-w-[220px]">
                          <div className="font-medium text-gray-900 truncate text-xs" title={expense.description || 'No description'}>
                            {expense.description}
                          </div>
                          {expense.expense_id && (
                            <div className="text-xs text-gray-500 space-y-1 mt-1">
                              ID: {expense.expense_id}
                            </div>
                          )}
                        </div>
                      </td>
                      
                      <td className="py-4 px-6">
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 border border-gray-200">
                          {expense.category}
                        </span>
                      </td>
                      
                      <td className="py-4 px-6 text-xs text-gray-700">
                        {expense.vendor || '-'}
                      </td>
                      
                      <td className="py-4 px-6">
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 border border-gray-200">
                          {expense.payment_method || 'N/A'}
                        </span>
                      </td>
                      
                      <td className="py-4 px-6 text-left">
                        <div className="font-semibold text-gray-900 text-xs">
                          {formatCurrency(expense.amount)}
                        </div>
                      </td>
                      
                      <td className="py-4 px-6 text-center">
                        <div className="flex items-center justify-center relative w-full">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" className="w-48">
                              <DropdownMenuItem
                                onClick={() => onExpenseView?.(expense)}
                                className="cursor-pointer text-xs py-2"
                              >
                                <Eye className="h-4 w-4 mr-2" />
                                <span>View</span>
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => onExpenseEdit?.(expense)}
                                className="cursor-pointer text-xs py-2"
                              >
                                <Edit3 className="h-4 w-4 mr-2" />
                                <span>Edit</span>
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={() => {
                                  if (window.confirm(`Are you sure you want to delete this expense?\n\nExpense: ${expense.description || 'No description'}\nAmount: ${formatCurrency(expense.amount)}`)) {
                                    onExpenseDelete?.([expense.id])
                                  }
                                }}
                                className="cursor-pointer text-red-600 focus:text-red-600 focus:bg-red-50 text-xs py-2"
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                <span>Delete</span>
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Pagination */}
          <div className="flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 p-4 border border-gray-200 border-t-0 rounded-b-lg bg-gray-50">
            {/* Left: Items per page selector */}
            <div className="flex items-center gap-2 text-xs text-gray-600">
              <span>Show</span>
              <select
                value={pagination.pageSize}
                onChange={(e) => handlePageSizeChange(Number(e.target.value))}
                className="rounded border border-gray-200 px-2 py-1 text-xs h-7"
              >
                <option value={5}>5</option>
                <option value={10}>10</option>
                <option value={20}>20</option>
                <option value={50}>50</option>
              </select>
              <span>per page</span>
            </div>
            
            {/* Right: Pagination Controls and Stats */}
            <div className="flex items-center w-full md:w-auto justify-end">
              {/* Item Range and Navigation */}
              <div className="flex items-center gap-2">
                <p className="text-xs text-gray-600">
                  <span className="hidden sm:inline">Showing </span>
                  <span className="font-medium">{Math.min((pagination.currentPage - 1) * pagination.pageSize + 1, paginationConfig.totalItems)}-{Math.min(pagination.currentPage * pagination.pageSize, paginationConfig.totalItems)}</span>
                  <span className="hidden sm:inline"> of </span>
                  <span className="sm:hidden">/</span>
                  <span className="font-medium">{paginationConfig.totalItems}</span>
                </p>
                
                <div className="flex items-center rounded-md border border-gray-200 overflow-hidden">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(pagination.currentPage - 1)}
                    disabled={pagination.currentPage <= 1}
                    className="h-7 w-7 p-0 rounded-none border-r border-gray-200"
                  >
                    <ChevronLeft className="h-3.5 w-3.5 text-gray-600" />
                  </Button>
                  
                  <div className="hidden sm:flex">
                    {Array.from({ length: Math.min(3, paginationConfig.totalPages) }, (_, i) => {
                      const page = Math.max(1, pagination.currentPage - 1) + i
                      if (page > paginationConfig.totalPages) return null
                      
                      return (
                        <Button
                          key={page}
                          variant={page === pagination.currentPage ? "default" : "ghost"}
                          size="sm"
                          onClick={() => handlePageChange(page)}
                          className={cn(
                            "h-7 w-7 p-0 rounded-none border-r border-gray-200 text-xs",
                            page === pagination.currentPage ? "bg-blue-600 hover:bg-blue-700 text-white" : "text-gray-700"
                          )}
                        >
                          {page}
                        </Button>
                      )
                    })}
                  </div>
                  
                  <div className="sm:hidden flex items-center justify-center h-7 w-7 text-xs font-medium border-r border-gray-200">
                    {pagination.currentPage}
                  </div>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(pagination.currentPage + 1)}
                    disabled={pagination.currentPage >= paginationConfig.totalPages}
                    className="h-7 w-7 p-0 rounded-none"
                  >
                    <ChevronRight className="h-3.5 w-3.5 text-gray-600" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </>
  )
}