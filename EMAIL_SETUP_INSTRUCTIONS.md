# Email Setup Instructions

## Current Implementation

The application currently has a placeholder implementation for sending invitation emails. The system logs email content to the console but does not send actual emails.

## How Email Sending Works

1. When an invitation is sent or resent, the system creates an invitation record in the database
2. The [sendInvitationEmail](file:///Qoder/onko/lib/team-management.ts#L368-L434) function is called to simulate email sending
3. Email content is logged to the console for debugging purposes

## Setting Up Real Email Sending

To enable actual email sending, you'll need to integrate with an email service provider. Here are the recommended approaches:

### Option 1: Supabase Email Service (Recommended for Development)

Supabase provides an email testing service called Inbucket that's enabled by default in development.

1. Access the Inbucket interface at http://localhost:54324
2. All emails sent through the application will appear here for testing

### Option 2: SendGrid Integration

1. Sign up for a SendGrid account at https://sendgrid.com/
2. Create an API key in the SendGrid dashboard
3. Install the SendGrid package:
   ```bash
   npm install @sendgrid/mail
   ```
4. Update the [sendInvitationEmail](file:///Qoder/onko/lib/team-management.ts#L368-L434) function in [lib/team-management.ts](file:///Qoder/onko/lib/team-management.ts):
   ```typescript
   import sgMail from '@sendgrid/mail';
   
   // Add this at the top of the function
   sgMail.setApiKey(process.env.SENDGRID_API_KEY || '');
   
   // Replace the console.log section with:
   const msg = {
     to: invitation.email,
     from: '<EMAIL>', // Replace with your verified sender
     subject: `You've been invited to join ${organizationName}`,
     text: `Hello,
     
     You've been invited to join ${organizationName} as a ${invitation.role}.
     
     To accept this invitation, please click on the following link:
     ${typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000'}/accept-invitation?token=${invitation.id}
     
     This invitation will expire in 7 days.
     
     If you did not expect this invitation, you can safely ignore this email.
     
     Best regards,
     The ${organizationName} Team`,
     html: `<p>Hello,</p>
     
     <p>You've been invited to join <strong>${organizationName}</strong> as a <strong>${invitation.role}</strong>.</p>
     
     <p>To accept this invitation, please click on the following link:</p>
     <p><a href="${typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000'}/accept-invitation?token=${invitation.id}">Accept Invitation</a></p>
     
     <p>This invitation will expire in 7 days.</p>
     
     <p>If you did not expect this invitation, you can safely ignore this email.</p>
     
     <p>Best regards,<br>
     The ${organizationName} Team</p>`,
   };
   
   try {
     await sgMail.send(msg);
     console.log('Email sent successfully');
   } catch (error) {
     console.error('Error sending email:', error);
     throw error;
   }
   ```

### Option 3: SMTP Integration

1. Configure your SMTP settings in environment variables:
   ```
   SMTP_HOST=your-smtp-host.com
   SMTP_PORT=587
   SMTP_USER=your-username
   SMTP_PASS=your-password
   SMTP_FROM=<EMAIL>
   ```
2. Install nodemailer:
   ```bash
   npm install nodemailer
   ```
3. Update the [sendInvitationEmail](file:///Qoder/onko/lib/team-management.ts#L368-L434) function in [lib/team-management.ts](file:///Qoder/onko/lib/team-management.ts):
   ```typescript
   import nodemailer from 'nodemailer';
   
   // Replace the console.log section with:
   const transporter = nodemailer.createTransporter({
     host: process.env.SMTP_HOST,
     port: parseInt(process.env.SMTP_PORT || '587'),
     secure: false, // true for 465, false for other ports
     auth: {
       user: process.env.SMTP_USER,
       pass: process.env.SMTP_PASS,
     },
   });
   
   const mailOptions = {
     from: process.env.SMTP_FROM,
     to: invitation.email,
     subject: `You've been invited to join ${organizationName}`,
     text: `Hello,
     
     You've been invited to join ${organizationName} as a ${invitation.role}.
     
     To accept this invitation, please click on the following link:
     ${typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000'}/accept-invitation?token=${invitation.id}
     
     This invitation will expire in 7 days.
     
     If you did not expect this invitation, you can safely ignore this email.
     
     Best regards,
     The ${organizationName} Team`,
     html: `<p>Hello,</p>
     
     <p>You've been invited to join <strong>${organizationName}</strong> as a <strong>${invitation.role}</strong>.</p>
     
     <p>To accept this invitation, please click on the following link:</p>
     <p><a href="${typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000'}/accept-invitation?token=${invitation.id}">Accept Invitation</a></p>
     
     <p>This invitation will expire in 7 days.</p>
     
     <p>If you did not expect this invitation, you can safely ignore this email.</p>
     
     <p>Best regards,<br>
     The ${organizationName} Team</p>`,
   };
   
   try {
     await transporter.sendMail(mailOptions);
     console.log('Email sent successfully');
   } catch (error) {
     console.error('Error sending email:', error);
     throw error;
   }
   ```

## Testing Email Functionality

1. Start your development server: `npm run dev`
2. Navigate to the Team Settings page
3. Send an invitation to a test email address
4. Check the console logs for the email content
5. If using Inbucket, visit http://localhost:54324 to see the email
6. For production, verify that emails are being sent through your email service

## Troubleshooting

1. **Emails not appearing in Inbucket**: Make sure the Inbucket service is running and accessible
2. **Emails not sending in production**: Verify your email service credentials and configuration
3. **Email links not working**: Check that your SITE_URL environment variable is correctly set