import { useEffect, useRef } from 'react'
import { useAuth } from '@/contexts/auth-context'

export function useTabFocusRefresher() {
  const { forceSessionAndDataRefresh } = useAuth()
  const lastRefreshRef = useRef<number>(Date.now())
  const isRefreshingRef = useRef<boolean>(false)

  useEffect(() => {
    const handleVisibilityChange = async () => {
      // Only proceed if the tab is becoming visible
      if (document.visibilityState !== 'visible') {
        return
      }

      // Check if we're already refreshing
      if (isRefreshingRef.current) {
        console.log('Skipping refresh - already refreshing')
        return
      }

      // Check if enough time has passed since last refresh (60 seconds minimum)
      const now = Date.now()
      const timeSinceLastRefresh = now - lastRefreshRef.current
      
      if (timeSinceLastRefresh < 60000) {
        console.log(`Skipping refresh - only ${timeSinceLastRefresh}ms since last refresh (minimum 60s)`)
        return
      }

      try {
        isRefreshingRef.current = true
        console.log('Tab became visible, initiating smart refresh')
        await forceSessionAndDataRefresh()
        lastRefreshRef.current = Date.now()
        console.log('Smart refresh completed successfully')
      } catch (error) {
        console.error('Error during smart refresh:', error)
        // Even if there's an error, update the last refresh time to prevent continuous retries
        lastRefreshRef.current = Date.now()
      } finally {
        isRefreshingRef.current = false
      }
    }

    // Add event listener for visibility change
    document.addEventListener('visibilitychange', handleVisibilityChange)

    // Cleanup function to remove event listener
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [forceSessionAndDataRefresh])
}