'use client'

import * as React from "react"
import { 
  Card, 
  Card<PERSON>ontent, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card"
import { cn } from "@/lib/utils"

interface MetricCardProps extends React.HTMLAttributes<HTMLDivElement> {
  title: string
  value: React.ReactNode
  subtitle?: string
  icon?: React.ReactNode
}

const MetricCard = React.forwardRef<HTMLDivElement, MetricCardProps>(
  ({ title, value, subtitle, icon, className, ...props }, ref) => (
    <Card 
      ref={ref} 
      className={cn("shadow-md border border-gray-200 bg-white rounded-lg", className)}
      {...props}
    >
      <CardHeader className="flex flex-row items-center justify-between space-y-0 p-6 pb-2">
        <CardTitle className="text-sm font-medium text-gray-800">{title}</CardTitle>
        {icon && (
          <div className="h-6 w-6 rounded-md bg-blue-50 flex items-center justify-center">
            {icon}
          </div>
        )}
      </CardHeader>
      <CardContent className="p-6 pt-0">
        <div className="text-2xl font-bold text-gray-900">{value}</div>
        {subtitle && (
          <p className="text-xs text-gray-500 mt-1">
            {subtitle}
          </p>
        )}
      </CardContent>
    </Card>
  )
)

MetricCard.displayName = "MetricCard"

export { MetricCard, type MetricCardProps }