'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Card, CardContent } from '@/components/ui/card'
import {
  CheckCircle,
  AlertTriangle,
  Info,
  Clock,
  Archive,
  Trash2,
  EyeOff,
  ExternalLink,
  X,
  Calendar,
  Tag,
  User,
  FileText,
  Link
} from 'lucide-react'
import { format, parseISO } from 'date-fns'
import { cn } from '@/lib/utils'

interface Notification {
  id: string
  title: string
  message: string
  type: string
  created_at: string
  is_read: boolean
  is_archived: boolean
  priority?: 'low' | 'medium' | 'high' | 'urgent'
  category?: string
  related_entity_type?: string
  related_entity_id?: string
}

interface NotificationDetailModalProps {
  notification: Notification | null
  isOpen: boolean
  onClose: () => void
  onMarkAsRead: (id: string) => void
  onArchive: (id: string) => void
  onDelete: (id: string) => void
}

export function NotificationDetailModal({
  notification,
  isOpen,
  onClose,
  onMarkAsRead,
  onArchive,
  onDelete
}: NotificationDetailModalProps) {
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  if (!notification) return null

  // Get enhanced notification icon with background
  const getNotificationIconProps = (type: string) => {
    switch (type) {
      case 'success':
        return {
          Icon: CheckCircle,
          bgClass: 'bg-green-100',
          iconClass: 'text-green-600',
          borderClass: 'border-green-200'
        }
      case 'warning':
        return {
          Icon: AlertTriangle,
          bgClass: 'bg-yellow-100',
          iconClass: 'text-yellow-600',
          borderClass: 'border-yellow-200'
        }
      case 'product_deleted':
        return {
          Icon: X,
          bgClass: 'bg-red-100',
          iconClass: 'text-red-600',
          borderClass: 'border-red-200'
        }
      case 'overdue_invoice':
        return {
          Icon: AlertTriangle,
          bgClass: 'bg-red-100',
          iconClass: 'text-red-600',
          borderClass: 'border-red-200'
        }
      case 'new_order':
        return {
          Icon: Info,
          bgClass: 'bg-blue-100',
          iconClass: 'text-blue-600',
          borderClass: 'border-blue-200'
        }
      case 'info':
      default:
        return {
          Icon: Info,
          bgClass: 'bg-blue-100',
          iconClass: 'text-blue-600',
          borderClass: 'border-blue-200'
        }
    }
  }

  // Get priority badge styling
  const getPriorityBadge = (priority?: string) => {
    if (!priority) return null
    
    const variants = {
      low: { variant: 'secondary' as const, text: 'Low Priority', color: 'text-gray-600', bgColor: 'bg-gray-100' },
      medium: { variant: 'default' as const, text: 'Medium Priority', color: 'text-blue-600', bgColor: 'bg-blue-100' },
      high: { variant: 'destructive' as const, text: 'High Priority', color: 'text-orange-600', bgColor: 'bg-orange-100' },
      urgent: { variant: 'destructive' as const, text: 'Urgent', color: 'text-red-600', bgColor: 'bg-red-100' }
    }
    
    const config = variants[priority as keyof typeof variants]
    if (!config) return null
    
    return (
      <Badge variant={config.variant} className={cn("text-sm px-3 py-1", config.color, config.bgColor)}>
        {config.text}
      </Badge>
    )
  }

  // Handle navigation to related entity
  const handleNavigateToEntity = () => {
    if (notification.related_entity_type && notification.related_entity_id) {
      const routes = {
        product: `/dashboard/products/${notification.related_entity_id}`,
        order: `/dashboard/orders/${notification.related_entity_id}`,
        invoice: `/dashboard/invoices/${notification.related_entity_id}`,
        customer: `/dashboard/customers/${notification.related_entity_id}`
      }
      
      const route = routes[notification.related_entity_type as keyof typeof routes]
      if (route) {
        onClose()
        router.push(route)
      }
    }
  }

  const handleAction = async (action: () => void) => {
    setIsLoading(true)
    try {
      await action()
      onClose()
    } catch (error) {
      console.error('Action failed:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const iconProps = getNotificationIconProps(notification.type)
  const { Icon, bgClass, iconClass, borderClass } = iconProps

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="space-y-4">
          <div className="flex items-start gap-4">
            {/* Notification Icon */}
            <div className={cn(
              "flex-shrink-0 w-16 h-16 rounded-full flex items-center justify-center border-2",
              bgClass,
              borderClass
            )}>
              <Icon className={cn("w-8 h-8", iconClass)} />
            </div>

            <div className="flex-1 min-w-0">
              <DialogTitle className="text-xl font-bold text-gray-900 mb-2">
                {notification.title}
              </DialogTitle>
              
              <div className="flex items-center gap-3 mb-3">
                {!notification.is_read && (
                  <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                    Unread
                  </Badge>
                )}
                {getPriorityBadge(notification.priority)}
                {notification.category && (
                  <Badge variant="outline" className="text-sm">
                    {notification.category}
                  </Badge>
                )}
              </div>

              <div className="flex items-center gap-4 text-sm text-gray-600">
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  <span>{format(parseISO(notification.created_at), 'MMM d, yyyy h:mm a')}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Tag className="w-4 h-4" />
                  <span className="capitalize">{notification.type.replace('_', ' ')}</span>
                </div>
              </div>
            </div>
          </div>
        </DialogHeader>

        <Separator />

        {/* Notification Content */}
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
              <FileText className="w-5 h-5" />
              Message
            </h3>
            <Card>
              <CardContent className="p-4">
                <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">
                  {notification.message}
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Metadata */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
              <Info className="w-5 h-5" />
              Details
            </h3>
            <Card>
              <CardContent className="p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <div>
                      <span className="text-sm font-medium text-gray-700">Notification ID:</span>
                      <p className="text-sm text-gray-600 font-mono">{notification.id}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-700">Type:</span>
                      <p className="text-sm text-gray-600 capitalize">{notification.type.replace('_', ' ')}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-700">Status:</span>
                      <p className="text-sm text-gray-600">
                        {notification.is_archived ? 'Archived' : notification.is_read ? 'Read' : 'Unread'}
                      </p>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div>
                      <span className="text-sm font-medium text-gray-700">Created:</span>
                      <p className="text-sm text-gray-600">
                        {format(parseISO(notification.created_at), 'EEEE, MMMM d, yyyy')}
                      </p>
                      <p className="text-sm text-gray-500">
                        {format(parseISO(notification.created_at), 'h:mm:ss a')}
                      </p>
                    </div>
                    {notification.priority && (
                      <div>
                        <span className="text-sm font-medium text-gray-700">Priority:</span>
                        <p className="text-sm text-gray-600 capitalize">{notification.priority}</p>
                      </div>
                    )}
                    {notification.category && (
                      <div>
                        <span className="text-sm font-medium text-gray-700">Category:</span>
                        <p className="text-sm text-gray-600">{notification.category}</p>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Related Entity */}
          {notification.related_entity_type && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                <Link className="w-5 h-5" />
                Related Item
              </h3>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-700 capitalize">
                        {notification.related_entity_type}
                      </p>
                      <p className="text-sm text-gray-600 font-mono">
                        ID: {notification.related_entity_id}
                      </p>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleNavigateToEntity}
                      className="flex items-center gap-2"
                    >
                      <ExternalLink className="w-4 h-4" />
                      View Details
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>

        <Separator />

        {/* Action Buttons */}
        <div className="flex items-center justify-between pt-2">
          <div className="flex items-center gap-2">
            {!notification.is_read && (
              <Button
                variant="outline"
                onClick={() => handleAction(() => onMarkAsRead(notification.id))}
                disabled={isLoading}
                className="flex items-center gap-2"
              >
                <EyeOff className="w-4 h-4" />
                Mark as Read
              </Button>
            )}
            
            <Button
              variant="outline"
              onClick={() => handleAction(() => onArchive(notification.id))}
              disabled={isLoading}
              className="flex items-center gap-2"
            >
              <Archive className="w-4 h-4" />
              Archive
            </Button>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={() => handleAction(() => onDelete(notification.id))}
              disabled={isLoading}
              className="flex items-center gap-2 text-red-600 border-red-300 hover:bg-red-50"
            >
              <Trash2 className="w-4 h-4" />
              Delete
            </Button>
            
            <Button variant="default" onClick={onClose}>
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
