import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query'
import { 
  getPurchaseOrders, 
  getPurchaseOrderById, 
  deletePurchaseOrder,
  updatePurchaseOrderItemReceived
} from '@/components/products/purchase-orders/purchase-order-service'
import type { PurchaseOrder, PurchaseOrderFilters, ExtendedPurchaseOrder } from '@/components/products/purchase-orders/types'

// Debounce function to prevent rapid requests
function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  return function (...args: Parameters<T>) {
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(() => func(...args), wait)
  }
}

/**
 * Custom hook to fetch purchase orders with React Query
 * @param organizationId - The organization ID to fetch purchase orders for
 * @param filters - Filters to apply to the purchase orders query
 * @returns React Query result object with purchase orders data
 */
export function usePurchaseOrders(organizationId: string | undefined, filters: PurchaseOrderFilters) {
  return useQuery<PurchaseOrder[], Error>({
    queryKey: ['purchaseOrders', organizationId, filters],
    queryFn: async () => {
      if (!organizationId) {
        throw new Error('Organization ID is required')
      }
      
      // Convert date objects to strings for the API
      const apiFilters = {
        ...filters,
        dateRange: {
          from: filters.dateRange.from ? filters.dateRange.from.toISOString() : undefined,
          to: filters.dateRange.to ? filters.dateRange.to.toISOString() : undefined
        }
      }
      
      const data = await getPurchaseOrders(organizationId, apiFilters)
      return data || []
    },
    enabled: !!organizationId,
    staleTime: 2 * 60 * 1000, // 2 minutes for responsive updates
    gcTime: 5 * 60 * 1000, // 5 minutes garbage collection time
    refetchOnMount: true,
    refetchOnWindowFocus: true, // Important for tab switching
    refetchOnReconnect: true,
    refetchInterval: false,
    refetchIntervalInBackground: false,
    retry: 3, // Increased retry count
    retryDelay: (attemptIndex) => {
      return Math.min(500 * Math.pow(2, attemptIndex), 5000)
    },
    networkMode: 'online' // Handle offline scenarios
  })
}

/**
 * Custom hook to fetch a single purchase order with React Query
 * @param id - The purchase order ID to fetch
 * @param organizationId - The organization ID to fetch purchase order for
 * @returns React Query result object with purchase order data
 */
export function usePurchaseOrder(id: string | undefined, organizationId: string | undefined) {
  return useQuery<ExtendedPurchaseOrder, Error>({
    queryKey: ['purchaseOrder', id, organizationId],
    queryFn: async () => {
      if (!id || !organizationId) {
        throw new Error('Purchase order ID and organization ID are required')
      }
      
      const data = await getPurchaseOrderById(id, organizationId)
      if (!data) {
        throw new Error('Purchase order not found')
      }
      return data
    },
    enabled: !!id && !!organizationId,
    staleTime: 2 * 60 * 1000, // 2 minutes for responsive updates
    gcTime: 5 * 60 * 1000, // 5 minutes garbage collection time
    refetchOnMount: true,
    refetchOnWindowFocus: true, // Important for tab switching
    refetchOnReconnect: true,
    refetchInterval: false,
    refetchIntervalInBackground: false,
    retry: 3, // Increased retry count
    retryDelay: (attemptIndex) => {
      return Math.min(500 * Math.pow(2, attemptIndex), 5000)
    },
    networkMode: 'online' // Handle offline scenarios
  })
}

/**
 * Custom hook to delete a purchase order with React Query mutation
 * @returns React Query mutation object for deleting purchase orders
 */
export function useDeletePurchaseOrder() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async ({ id, organizationId }: { id: string; organizationId: string }) => {
      return await deletePurchaseOrder(id, organizationId)
    },
    onSuccess: () => {
      // Invalidate purchase orders queries to trigger refetching
      queryClient.invalidateQueries({ queryKey: ['purchaseOrders'] })
    },
    retry: 3, // Standardized retry count
    retryDelay: (attemptIndex) => {
      return Math.min(1000 * 2 ** attemptIndex, 30000)
    }
  })
}

/**
 * Custom hook to update purchase order item received quantity with React Query mutation
 * @returns React Query mutation object for updating purchase order items
 */
export function useUpdatePurchaseOrderItemReceived() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async ({ 
      itemId, 
      organizationId, 
      quantityReceived 
    }: { 
      itemId: string; 
      organizationId: string; 
      quantityReceived: number 
    }) => {
      return await updatePurchaseOrderItemReceived(itemId, organizationId, quantityReceived)
    },
    onSuccess: (_, variables) => {
      // Invalidate both purchase orders and individual purchase order queries
      queryClient.invalidateQueries({ queryKey: ['purchaseOrders'] })
      queryClient.invalidateQueries({ queryKey: ['purchaseOrder', variables.itemId] })
    },
    retry: 3, // Standardized retry count
    retryDelay: (attemptIndex) => {
      return Math.min(1000 * 2 ** attemptIndex, 30000)
    }
  })
}

/**
 * Custom hook to invalidate purchase order queries and trigger refetching with debouncing
 * @returns Object with functions to invalidate queries
 */
export function useInvalidatePurchaseOrders() {
  const queryClient = useQueryClient()
  
  // Debounced invalidate functions
  const debouncedInvalidatePurchaseOrders = debounce(() => {
    queryClient.invalidateQueries({ queryKey: ['purchaseOrders'] })
  }, 1000) // 1 second debounce
  
  const debouncedInvalidatePurchaseOrder = debounce((id: string) => {
    queryClient.invalidateQueries({ queryKey: ['purchaseOrder', id] })
  }, 1000) // 1 second debounce
  
  return {
    invalidatePurchaseOrders: debouncedInvalidatePurchaseOrders,
    invalidatePurchaseOrder: debouncedInvalidatePurchaseOrder
  }
}