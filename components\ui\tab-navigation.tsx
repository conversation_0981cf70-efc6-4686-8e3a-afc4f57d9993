'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import { LucideIcon } from 'lucide-react'
import { typographyClasses } from '@/lib/design-tokens'

export interface TabConfig {
  id: string
  label: string
  icon?: LucideIcon // Made optional
  description?: string
  content: React.ReactNode
}

interface TabNavigationProps {
  tabs: TabConfig[]
  defaultTab?: string
  onTabChange?: (tabId: string) => void
  isMobile?: boolean
  className?: string
  enableRouting?: boolean // Enable URL-based routing
  basePath?: string // Base path for routing (e.g., '/dashboard/expenses')
}

export function TabNavigation({ 
  tabs, 
  defaultTab, 
  onTabChange, 
  isMobile = false,
  className,
  enableRouting = false,
  basePath = '/dashboard/expenses'
}: TabNavigationProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [activeTab, setActiveTab] = useState(defaultTab || tabs[0]?.id || '')

  // Initialize active tab from URL if routing is enabled
  useEffect(() => {
    if (enableRouting) {
      const urlTab = searchParams.get('tab')
      if (urlTab && tabs.some(tab => tab.id === urlTab)) {
        setActiveTab(urlTab)
        onTabChange?.(urlTab)
      } else if (defaultTab) {
        setActiveTab(defaultTab)
        // Update URL to reflect default tab
        const newUrl = new URL(window.location.href)
        newUrl.searchParams.set('tab', defaultTab)
        router.replace(newUrl.toString(), { scroll: false })
      }
    }
  }, [enableRouting, searchParams, defaultTab, tabs, router, onTabChange])

  // Handle browser back/forward navigation
  useEffect(() => {
    if (!enableRouting) return

    const handlePopState = () => {
      const urlParams = new URLSearchParams(window.location.search)
      const urlTab = urlParams.get('tab')
      
      if (urlTab && tabs.some(tab => tab.id === urlTab)) {
        setActiveTab(urlTab)
        onTabChange?.(urlTab)
      } else if (defaultTab) {
        setActiveTab(defaultTab)
        onTabChange?.(defaultTab)
      }
    }

    // Listen for browser navigation events
    window.addEventListener('popstate', handlePopState)
    
    return () => {
      window.removeEventListener('popstate', handlePopState)
    }
  }, [enableRouting, tabs, defaultTab, onTabChange])

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId)
    onTabChange?.(tabId)
    
    // Update URL if routing is enabled
    if (enableRouting) {
      const newUrl = new URL(window.location.href)
      newUrl.searchParams.set('tab', tabId)
      router.replace(newUrl.toString(), { scroll: false })
    }
  }

  const activeTabConfig = tabs.find(tab => tab.id === activeTab)

  if (isMobile) {
    return (
      <div className={cn("space-y-4", className)}>
        {/* Mobile Tab Navigation with Enhanced Horizontal Scroll */}
        <div className="bg-white rounded-md shadow-sm overflow-hidden">
          <div 
            className="flex overflow-x-auto scrollbar-hide pb-2 pt-1 px-1 snap-x snap-mandatory"
            style={{
              scrollBehavior: 'smooth',
              WebkitOverflowScrolling: 'touch', // Improve iOS scrolling
              msOverflowStyle: 'none', // Hide scrollbar in IE
              scrollbarWidth: 'none' // Hide scrollbar in Firefox
            }}
            role="tablist"
            aria-label="Page tabs"
          >
            {tabs.map((tab, index) => {
              const isActive = activeTab === tab.id
              
              return (
                <button
                  key={tab.id}
                  onClick={(e) => {
                    e.stopPropagation()
                    handleTabChange(tab.id)
                  }}
                  role="tab"
                  aria-selected={isActive}
                  aria-controls={`tabpanel-${tab.id}`}
                  tabIndex={isActive ? 0 : -1}
                  className={cn(
                    "flex-shrink-0 h-9 px-4 text-sm font-medium whitespace-nowrap rounded-md transition-all duration-200",
                    "snap-center touch-manipulation select-none",
                    "focus:outline-none focus:ring-0",
                    "mx-0.5 my-1",
                    isActive 
                      ? "bg-gray-100 text-gray-900" 
                      : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                  )}
                >
                  {tab.label}
                </button>
              )
            })}
          </div>
        </div>

        {/* Active Tab Content with proper ARIA */}
        <div 
          id={`tabpanel-${activeTabConfig?.id}`}
          role="tabpanel"
          aria-labelledby={`tab-${activeTabConfig?.id}`}
          tabIndex={0}
          className="pt-2"
        >
          {activeTabConfig?.content}
        </div>
      </div>
    )
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Desktop Tab Navigation with Enhanced Accessibility */}
      <div className="bg-white rounded-md shadow-sm overflow-hidden">
        <nav 
          className="flex"
          role="tablist"
          aria-label="Page tabs"
        >
          {tabs.map((tab, index) => {
            const isActive = activeTab === tab.id
            
            return (
              <button
                key={tab.id}
                onClick={(e) => {
                  e.stopPropagation()
                  handleTabChange(tab.id)
                }}
                role="tab"
                aria-selected={isActive}
                aria-controls={`tabpanel-${tab.id}`}
                id={`tab-${tab.id}`}
                tabIndex={isActive ? 0 : -1}
                onKeyDown={(e) => {
                  // Handle keyboard navigation
                  if (e.key === 'ArrowRight') {
                    e.preventDefault()
                    const nextIndex = (index + 1) % tabs.length
                    handleTabChange(tabs[nextIndex].id)
                  } else if (e.key === 'ArrowLeft') {
                    e.preventDefault()
                    const prevIndex = index === 0 ? tabs.length - 1 : index - 1
                    handleTabChange(tabs[prevIndex].id)
                  } else if (e.key === 'Home') {
                    e.preventDefault()
                    handleTabChange(tabs[0].id)
                  } else if (e.key === 'End') {
                    e.preventDefault()
                    handleTabChange(tabs[tabs.length - 1].id)
                  }
                }}
                className={cn(
                  "px-6 py-3 text-sm font-medium transition-all duration-200",
                  "focus:outline-none focus:ring-0",
                  isActive
                    ? "text-gray-900 bg-white border-b-2 border-blue-600"
                    : "text-gray-600 hover:text-gray-900 hover:bg-gray-50 border-b-2 border-transparent"
                )}
              >
                {tab.label}
                {tab.description && (
                  <span className="sr-only">{tab.description}</span>
                )}
              </button>
            )
          })}
        </nav>
      </div>

      {/* Active Tab Content with proper ARIA */}
      <div 
        id={`tabpanel-${activeTabConfig?.id}`}
        role="tabpanel"
        aria-labelledby={`tab-${activeTabConfig?.id}`}
        tabIndex={0}
      >
        {activeTabConfig?.content}
      </div>
    </div>
  )
}