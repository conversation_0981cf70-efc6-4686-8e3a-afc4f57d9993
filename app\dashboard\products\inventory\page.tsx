'use client'

import { useState, useEffect, useRef } from 'react'
import { InventoryTab } from '@/components/products/products-catalog/inventory-tab'
import { usePersistedState } from '@/lib/use-persisted-state'
import { type ProductFilters, INITIAL_PRODUCT_FILTERS } from '@/components/products/products-catalog/product-filters'
import { Button } from '@/components/ui/button'
import { FileSpreadsheet, RefreshCw } from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'
import { PageHeader } from '@/components/ui/page-header'

export default function InventoryPage() {
  const [isMobile, setIsMobile] = useState(false)
  const { toast } = useToast()
  const hasAttemptedRefresh = useRef(false)
  
  // Shared state for filters (for contextual analytics) - with persistence
  const [globalFilters, setGlobalFilters] = usePersistedState<ProductFilters>(
    'products-filters', 
    INITIAL_PRODUCT_FILTERS
  )
  const [filteredProductIds, setFilteredProductIds] = useState<string[]>([])
  const [selectedProducts, setSelectedProducts] = useState<string[]>([])
  const [isExporting, setIsExporting] = useState(false)

  // Mobile detection
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Handle tab visibility change to trigger refresh when tab becomes visible
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && !hasAttemptedRefresh.current) {
        hasAttemptedRefresh.current = true
        // Dispatch refresh event to trigger inventory refresh
        window.dispatchEvent(new CustomEvent('refreshInventoryData'))
        // Reset the flag after a delay to allow future refreshes
        setTimeout(() => {
          hasAttemptedRefresh.current = false
        }, 5000)
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [])

  // Refresh inventory data
  const handleRefresh = () => {
    // This will trigger a refresh in the InventoryTab component
    window.dispatchEvent(new CustomEvent('refreshInventoryData'))
    toast({
      title: "Refresh initiated",
      description: "Inventory data refresh has been initiated.",
    })
  }

  // Export to CSV function
  const exportToCSV = () => {
    setIsExporting(true)
    // Dispatch event to trigger export in InventoryTab
    window.dispatchEvent(new CustomEvent('exportInventoryCSV'))
  }

  return (
    <div className="flex flex-col gap-4">
      {/* Page Header with Title and Actions */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <PageHeader 
          title="Inventory" 
          description="Track stock levels, manage adjustments, and prevent stockouts"
        />
        
        <div className="flex flex-col sm:flex-row sm:items-center gap-2">
          {/* Refresh Button */}
          <Button 
            variant="outline" 
            size="sm" 
            className="flex items-center gap-1.5 h-8 px-3"
            onClick={handleRefresh}
          >
            <RefreshCw className="h-4 w-4" />
            <span>Refresh</span>
          </Button>
          
          {/* Export Button */}
          <Button 
            variant="outline" 
            size="sm" 
            className="flex items-center gap-1.5 h-8 px-3"
            onClick={exportToCSV}
          >
            <FileSpreadsheet className="h-4 w-4" />
            <span>Export CSV</span>
          </Button>
        </div>
      </div>

      <InventoryTab />
    </div>
  )
}
