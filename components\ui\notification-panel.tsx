'use client'

import { useState, useEffect, useRef } from 'react'
import { Bell, X, CheckCircle, AlertTriangle, Info } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { formatDistanceToNow } from 'date-fns'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { NotificationService } from '@/lib/notification-service'
import { centralizedNotificationService } from '@/lib/centralized-notification-service'
import Link from 'next/link'
import { toast } from '@/components/ui/use-toast'
import { useAuth } from '@/contexts/auth-context'

interface Notification {
  id: string
  title: string
  message: string
  type: 'success' | 'warning' | 'info' | 'product_deleted' | 'overdue_invoice' | 'new_order' | 'stock_adjustment' | 'low_stock' | 'out_of_stock' | 'purchase_order' | 'expense_added' | 'product_added'
  created_at: string
  is_read: boolean
}

export function NotificationPanel() {
  const [isOpen, setIsOpen] = useState(false)
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  const supabase = createClientComponentClient()
  const notificationService = new NotificationService()
  const unsubscribeRef = useRef<(() => void) | null>(null)
  const unsubscribeCustomEventRef = useRef<(() => void) | null>(null)
  const lastVisibilityChangeRef = useRef<number>(Date.now())
  const { organizationId } = useAuth()
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen])

  // Load notifications and count
  useEffect(() => {
    loadNotifications()
    loadUnreadCount()

    // Set up subscription using centralized service
    if (organizationId) {
      // Clean up any existing subscription first
      if (unsubscribeRef.current) {
        unsubscribeRef.current()
        unsubscribeRef.current = null
      }
      
      unsubscribeRef.current = centralizedNotificationService.subscribe(
        organizationId,
        (newNotification: any) => {
          // Add new notification to the top of the list only if it doesn't already exist
          setNotifications(prev => {
            // Check if notification already exists
            const exists = prev.some(notification => notification.id === newNotification.id)
            if (!exists) {
              return [newNotification, ...prev]
            }
            return prev
          })
          // Update unread count for new notifications
          if (!newNotification.is_read) {
            setUnreadCount(prev => prev + 1)
          }
        },
        (updatedNotification: any) => {
          // Update notification in the list
          setNotifications(prev => 
            prev.map(notification => 
              notification.id === updatedNotification.id 
                ? updatedNotification 
                : notification
            )
          )
          
          // Update unread count if this notification was marked as read
          if (updatedNotification.is_read) {
            setUnreadCount(prev => Math.max(0, prev - 1))
          }
        },
        (deletedNotification: any) => {
          // Remove deleted notification from the list
          setNotifications(prev => 
            prev.filter(notification => notification.id !== deletedNotification.id)
          )
          
          // Update unread count if this notification was unread
          if (!deletedNotification.is_read) {
            setUnreadCount(prev => Math.max(0, prev - 1))
          }
        }
      )
    }

    // Subscribe to custom events for "mark all as read"
    if (unsubscribeCustomEventRef.current) {
      unsubscribeCustomEventRef.current()
      unsubscribeCustomEventRef.current = null
    }
    
    unsubscribeCustomEventRef.current = centralizedNotificationService.subscribeToEvent(
      'notifications-marked-as-read',
      () => {
        // Update all notifications to read
        setNotifications(prev => 
          prev.map(notification => ({ ...notification, is_read: true }))
        )
        setUnreadCount(0)
      }
    )

    // Handle visibility change to refresh when tab becomes visible
    const handleVisibilityChange = () => {
      const now = Date.now()
      // Debounce visibility changes
      if (now - lastVisibilityChangeRef.current < 1000) {
        return
      }
      
      lastVisibilityChangeRef.current = now
      
      if (document.visibilityState === 'visible') {
        console.debug('Notification panel: Tab became visible, refreshing data')
        // When tab becomes visible, refresh the data
        loadUnreadCount()
        // Only reconnect if we're not already subscribed
        const status = centralizedNotificationService.getSubscriptionStatus()
        if (organizationId && !status.isSubscribed) {
          centralizedNotificationService.reconnect(organizationId)
        }
      }
    }

    // Handle window focus
    const handleFocus = () => {
      console.debug('Notification panel: Window focused, refreshing data')
      loadUnreadCount()
      // Only reconnect if we're not already subscribed
      const status = centralizedNotificationService.getSubscriptionStatus()
      if (organizationId && !status.isSubscribed) {
        centralizedNotificationService.reconnect(organizationId)
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('focus', handleFocus)

    // Set up periodic refresh of unread count (every 30 seconds)
    const refreshInterval = setInterval(() => {
      // Only log in development mode to reduce console noise
      if (process.env.NODE_ENV === 'development') {
        console.log('NotificationPanel: Refreshing unread count')
      }
      loadUnreadCount()
    }, 30000)

    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current()
        unsubscribeRef.current = null
      }
      if (unsubscribeCustomEventRef.current) {
        unsubscribeCustomEventRef.current()
        unsubscribeCustomEventRef.current = null
      }
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('focus', handleFocus)
      clearInterval(refreshInterval)
    }
  }, [organizationId])

  const loadNotifications = async () => {
    try {
      if (organizationId) {
        const notifications = await notificationService.getNotifications(organizationId, 5)
        setNotifications(notifications)
      }
    } catch (error) {
      console.error('Error loading notifications:', error)
    }
  }

  const loadUnreadCount = async () => {
    try {
      if (organizationId) {
        const count = await notificationService.getUnreadNotificationCount(organizationId)
        setUnreadCount(count)
      }
    } catch (error) {
      console.error('Error loading unread count:', error)
    }
  }

  const markAsRead = async (id: string) => {
    try {
      await notificationService.markAsRead(id)
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === id 
            ? { ...notification, is_read: true } 
            : notification
        )
      )
      setUnreadCount(prev => Math.max(0, prev - 1))
    } catch (error) {
      console.error('Error marking notification as read:', error)
    }
  }

  const markAllAsRead = async () => {
    try {
      if (organizationId) {
        // Update database
        await notificationService.markAllAsRead(organizationId)
        
        // Update local state immediately for instant feedback
        setNotifications(prev => 
          prev.map(notification => ({ ...notification, is_read: true }))
        )
        setUnreadCount(0)
        
        // Broadcast the change to other components
        centralizedNotificationService.broadcastEvent('notifications-marked-as-read', { organizationId })
        
        // Show confirmation toast
        toast({
          title: "All notifications marked as read",
          description: "All your notifications have been marked as read."
        })
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error)
      toast({
        title: "Error",
        description: "Failed to mark all notifications as read. Please try again.",
        variant: "destructive"
      })
    }
  }

  const getNotificationDisplayInfo = (notification: Notification) => {
    // For specific notification types, show only the type as title with a brief description
    switch (notification.type) {
      case 'new_order':
        return {
          title: 'New Order Created',
          description: 'A new order has been placed',
          showMessage: false
        };
      case 'stock_adjustment':
        return {
          title: 'Stock Adjustment',
          description: 'Inventory levels have been updated',
          showMessage: false
        };
      case 'low_stock':
        return {
          title: 'Low Stock Alert',
          description: 'Product inventory is running low',
          showMessage: false
        };
      case 'out_of_stock':
        return {
          title: 'Out of Stock',
          description: 'Product is no longer available',
          showMessage: false
        };
      case 'purchase_order':
        return {
          title: 'Purchase Order',
          description: 'A new purchase order was created',
          showMessage: false
        };
      case 'expense_added':
        return {
          title: 'Expense Added',
          description: 'A new expense entry was recorded',
          showMessage: false
        };
      case 'product_added':
        return {
          title: 'Product Added',
          description: 'A new product was added to inventory',
          showMessage: false
        };
      case 'product_deleted':
        return {
          title: 'Product Deleted',
          description: 'A product was removed from inventory',
          showMessage: false
        };
      default:
        // For other types, show the original title and message
        return {
          title: notification.title,
          description: notification.message,
          showMessage: false
        };
    }
  }

  const getNotificationIconProps = (type: string) => {
    switch (type) {
      case 'success':
        return {
          Icon: CheckCircle,
          bgClass: 'bg-green-100',
          iconClass: 'text-green-500',
        };
      case 'warning':
        return {
          Icon: AlertTriangle,
          bgClass: 'bg-yellow-100',
          iconClass: 'text-yellow-500',
        };
      case 'product_deleted':
        return {
          Icon: X,
          bgClass: 'bg-red-100',
          iconClass: 'text-red-500',
        };
      case 'overdue_invoice':
         return {
          Icon: AlertTriangle,
          bgClass: 'bg-red-100',
          iconClass: 'text-red-500',
        };
      case 'new_order':
        return {
          Icon: Info,
          bgClass: 'bg-blue-100',
          iconClass: 'text-blue-500',
        };
      case 'stock_adjustment':
        return {
          Icon: Info,
          bgClass: 'bg-purple-100',
          iconClass: 'text-purple-500',
        };
      case 'low_stock':
        return {
          Icon: AlertTriangle,
          bgClass: 'bg-yellow-100',
          iconClass: 'text-yellow-500',
        };
      case 'out_of_stock':
        return {
          Icon: X,
          bgClass: 'bg-red-100',
          iconClass: 'text-red-500',
        };
      case 'purchase_order':
        return {
          Icon: Info,
          bgClass: 'bg-indigo-100',
          iconClass: 'text-indigo-500',
        };
      case 'expense_added':
        return {
          Icon: Info,
          bgClass: 'bg-green-100',
          iconClass: 'text-green-500',
        };
      case 'product_added':
        return {
          Icon: CheckCircle,
          bgClass: 'bg-green-100',
          iconClass: 'text-green-500',
        };
      case 'info':
      default:
        return {
          Icon: Info,
          bgClass: 'bg-blue-100',
          iconClass: 'text-blue-500',
        };
    }
  }

  return (
    <div className="relative" ref={dropdownRef}>
      <Button
        variant="ghost"
        size="sm"
        className="rounded-full relative hover:bg-gray-100 focus:ring-0 p-2"
        onClick={() => setIsOpen(!isOpen)}
        aria-label="Notifications"
      >
        <Bell className="h-5 w-5 text-gray-600" />
        {unreadCount > 0 && (
          <span className="absolute top-0 right-0 h-2 w-2 rounded-full bg-red-500" />
        )}
      </Button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-72 bg-white rounded-xl shadow-lg border border-slate-200 z-50 overflow-hidden">
          <div className="p-3 border-b border-slate-100 flex items-center justify-between">
            <h3 className="text-xs font-semibold text-gray-900">Notifications</h3>
            {unreadCount > 0 && (
              <button
                className="text-xs font-medium text-blue-600 hover:text-blue-800 focus:outline-none"
                onClick={markAllAsRead}
              >
                Mark all as read
              </button>
            )}
          </div>

          <div className="max-h-96 overflow-y-auto">
            {notifications.length === 0 ? (
              <div className="p-6 text-center text-xs text-gray-500">
                No notifications
              </div>
            ) : (
              <ul className="divide-y divide-slate-100">
                {notifications.map((notification) => {
                  const { Icon, bgClass, iconClass } = getNotificationIconProps(notification.type);
                  const displayInfo = getNotificationDisplayInfo(notification);
                  // Format the timestamp and remove "about" if present
                  const timestamp = formatDistanceToNow(new Date(notification.created_at), { addSuffix: true });
                  const cleanTimestamp = timestamp.replace(/^about\s+/, '');
                  
                  return (
                    <li 
                      key={notification.id}
                      className="p-2.5 hover:bg-slate-50 transition-colors duration-150"
                    >
                      <div className="flex items-start gap-2">
                        <div className={`flex-shrink-0 mt-0.5 rounded-full w-6 h-6 flex items-center justify-center ${bgClass}`}>
                          <Icon className={`h-3 w-3 ${iconClass}`} />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-xs font-medium text-slate-900 truncate">
                            {displayInfo.title}
                          </p>
                          <p className="mt-0.5 text-xs text-slate-500 truncate">
                            {displayInfo.description}
                          </p>
                          <p className="mt-1 text-[0.65rem] text-slate-400">
                            {cleanTimestamp}
                          </p>
                        </div>
                        {!notification.is_read && (
                          <div className="mt-1 w-1.5 h-1.5 rounded-full bg-blue-500 flex-shrink-0"></div>
                        )}
                      </div>
                    </li>
                  );
                })}
              </ul>
            )}
          </div>

          <div className="border-t border-slate-100 p-2 bg-slate-50">
            <Link 
              href="/dashboard/notifications" 
              className="block text-center text-xs font-medium text-blue-600 hover:text-blue-800 py-1 rounded-lg hover:bg-white transition-colors duration-150"
              onClick={() => setIsOpen(false)}
            >
              View all notifications
            </Link>
          </div>
        </div>
      )}
    </div>
  )
}
