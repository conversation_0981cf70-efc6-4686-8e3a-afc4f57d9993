'use client'

import { useState, useEffect, useRef } from 'react'
import { Bell, X, CheckCircle, AlertTriangle, Info, Clock, Archive, Trash2, ExternalLink, ChevronRight } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { formatDistanceToNow, isToday, isYesterday, format } from 'date-fns'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { NotificationService } from '@/lib/notification-service'
import { centralizedNotificationService } from '@/lib/centralized-notification-service'
import Link from 'next/link'
import { toast } from '@/components/ui/use-toast'
import { useAuth } from '@/contexts/auth-context'
import { cn } from '@/lib/utils'
import { useRouter } from 'next/navigation'

interface Notification {
  id: string
  title: string
  message: string
  type: 'success' | 'warning' | 'info' | 'product_deleted' | 'overdue_invoice' | 'new_order' | 'stock_adjustment' | 'low_stock' | 'out_of_stock' | 'purchase_order' | 'expense_added' | 'product_added'
  created_at: string
  is_read: boolean
  is_archived?: boolean
  priority?: 'low' | 'medium' | 'high' | 'urgent'
  category?: string
}

interface NotificationGroup {
  title: string
  notifications: Notification[]
}

export function NotificationPanel() {
  const [isOpen, setIsOpen] = useState(false)
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  const [isLoading, setIsLoading] = useState(false)
  const [hoveredNotification, setHoveredNotification] = useState<string | null>(null)
  const supabase = createClientComponentClient()
  const notificationService = new NotificationService()
  const unsubscribeRef = useRef<(() => void) | null>(null)
  const unsubscribeCustomEventRef = useRef<(() => void) | null>(null)
  const lastVisibilityChangeRef = useRef<number>(Date.now())
  const { organizationId } = useAuth()
  const router = useRouter()
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen])

  // Load notifications and count
  useEffect(() => {
    loadNotifications()
    loadUnreadCount()

    // Set up subscription using centralized service
    if (organizationId) {
      // Clean up any existing subscription first
      if (unsubscribeRef.current) {
        unsubscribeRef.current()
        unsubscribeRef.current = null
      }
      
      unsubscribeRef.current = centralizedNotificationService.subscribe(
        organizationId,
        (newNotification: any) => {
          // Add new notification to the top of the list only if it doesn't already exist
          setNotifications(prev => {
            // Check if notification already exists
            const exists = prev.some(notification => notification.id === newNotification.id)
            if (!exists) {
              return [newNotification, ...prev]
            }
            return prev
          })
          // Update unread count for new notifications
          if (!newNotification.is_read) {
            setUnreadCount(prev => prev + 1)
          }
        },
        (updatedNotification: any) => {
          // Update notification in the list
          setNotifications(prev => 
            prev.map(notification => 
              notification.id === updatedNotification.id 
                ? updatedNotification 
                : notification
            )
          )
          
          // Update unread count if this notification was marked as read
          if (updatedNotification.is_read) {
            setUnreadCount(prev => Math.max(0, prev - 1))
          }
        },
        (deletedNotification: any) => {
          // Remove deleted notification from the list
          setNotifications(prev => 
            prev.filter(notification => notification.id !== deletedNotification.id)
          )
          
          // Update unread count if this notification was unread
          if (!deletedNotification.is_read) {
            setUnreadCount(prev => Math.max(0, prev - 1))
          }
        }
      )
    }

    // Subscribe to custom events for "mark all as read"
    if (unsubscribeCustomEventRef.current) {
      unsubscribeCustomEventRef.current()
      unsubscribeCustomEventRef.current = null
    }
    
    unsubscribeCustomEventRef.current = centralizedNotificationService.subscribeToEvent(
      'notifications-marked-as-read',
      () => {
        // Update all notifications to read
        setNotifications(prev => 
          prev.map(notification => ({ ...notification, is_read: true }))
        )
        setUnreadCount(0)
      }
    )

    // Handle visibility change to refresh when tab becomes visible
    const handleVisibilityChange = () => {
      const now = Date.now()
      // Debounce visibility changes
      if (now - lastVisibilityChangeRef.current < 1000) {
        return
      }
      
      lastVisibilityChangeRef.current = now
      
      if (document.visibilityState === 'visible') {
        console.debug('Notification panel: Tab became visible, refreshing data')
        // When tab becomes visible, refresh the data
        loadUnreadCount()
        // Only reconnect if we're not already subscribed
        const status = centralizedNotificationService.getSubscriptionStatus()
        if (organizationId && !status.isSubscribed) {
          centralizedNotificationService.reconnect(organizationId)
        }
      }
    }

    // Handle window focus
    const handleFocus = () => {
      console.debug('Notification panel: Window focused, refreshing data')
      loadUnreadCount()
      // Only reconnect if we're not already subscribed
      const status = centralizedNotificationService.getSubscriptionStatus()
      if (organizationId && !status.isSubscribed) {
        centralizedNotificationService.reconnect(organizationId)
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('focus', handleFocus)

    // Set up periodic refresh of unread count (every 30 seconds)
    const refreshInterval = setInterval(() => {
      // Only log in development mode to reduce console noise
      if (process.env.NODE_ENV === 'development') {
        console.log('NotificationPanel: Refreshing unread count')
      }
      loadUnreadCount()
    }, 30000)

    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current()
        unsubscribeRef.current = null
      }
      if (unsubscribeCustomEventRef.current) {
        unsubscribeCustomEventRef.current()
        unsubscribeCustomEventRef.current = null
      }
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('focus', handleFocus)
      clearInterval(refreshInterval)
    }
  }, [organizationId])

  const loadNotifications = async () => {
    try {
      setIsLoading(true)
      if (organizationId) {
        const notifications = await notificationService.getNotifications(organizationId, 8) // Increased limit
        setNotifications(notifications)
      }
    } catch (error) {
      console.error('Error loading notifications:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Group notifications by time periods
  const groupNotifications = (notifications: Notification[]): NotificationGroup[] => {
    const groups: NotificationGroup[] = []
    const today: Notification[] = []
    const yesterday: Notification[] = []
    const thisWeek: Notification[] = []
    const older: Notification[] = []

    notifications.forEach(notification => {
      const date = new Date(notification.created_at)
      if (isToday(date)) {
        today.push(notification)
      } else if (isYesterday(date)) {
        yesterday.push(notification)
      } else if (Date.now() - date.getTime() < 7 * 24 * 60 * 60 * 1000) {
        thisWeek.push(notification)
      } else {
        older.push(notification)
      }
    })

    if (today.length > 0) groups.push({ title: 'Today', notifications: today })
    if (yesterday.length > 0) groups.push({ title: 'Yesterday', notifications: yesterday })
    if (thisWeek.length > 0) groups.push({ title: 'This Week', notifications: thisWeek })
    if (older.length > 0) groups.push({ title: 'Older', notifications: older })

    return groups
  }

  const loadUnreadCount = async () => {
    try {
      if (organizationId) {
        const count = await notificationService.getUnreadNotificationCount(organizationId)
        setUnreadCount(count)
      }
    } catch (error) {
      console.error('Error loading unread count:', error)
    }
  }

  const markAsRead = async (id: string) => {
    try {
      await notificationService.markAsRead(id)
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === id 
            ? { ...notification, is_read: true } 
            : notification
        )
      )
      setUnreadCount(prev => Math.max(0, prev - 1))
    } catch (error) {
      console.error('Error marking notification as read:', error)
    }
  }

  const markAllAsRead = async () => {
    try {
      if (organizationId) {
        // Update database
        await notificationService.markAllAsRead(organizationId)

        // Update local state immediately for instant feedback
        setNotifications(prev =>
          prev.map(notification => ({ ...notification, is_read: true }))
        )
        setUnreadCount(0)

        // Broadcast the change to other components
        centralizedNotificationService.broadcastEvent('notifications-marked-as-read', { organizationId })

        // Show confirmation toast
        toast({
          title: "All notifications marked as read",
          description: "All your notifications have been marked as read."
        })
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error)
      toast({
        title: "Error",
        description: "Failed to mark all notifications as read. Please try again.",
        variant: "destructive"
      })
    }
  }

  // Navigate to notification page with specific notification highlighted
  const navigateToNotification = (notificationId: string) => {
    setIsOpen(false)
    router.push(`/dashboard/notifications?highlight=${notificationId}`)
  }

  // Quick action handlers
  const handleQuickMarkAsRead = async (notificationId: string, event: React.MouseEvent) => {
    event.stopPropagation()
    try {
      await notificationService.markAsRead(notificationId)
      setNotifications(prev =>
        prev.map(notification =>
          notification.id === notificationId
            ? { ...notification, is_read: true }
            : notification
        )
      )
      setUnreadCount(prev => Math.max(0, prev - 1))
    } catch (error) {
      console.error('Error marking notification as read:', error)
    }
  }

  const handleQuickArchive = async (notificationId: string, event: React.MouseEvent) => {
    event.stopPropagation()
    try {
      await notificationService.archiveNotification(notificationId)
      setNotifications(prev => prev.filter(n => n.id !== notificationId))
      toast({
        title: "Notification archived",
        description: "The notification has been archived."
      })
    } catch (error) {
      console.error('Error archiving notification:', error)
    }
  }

  const getNotificationDisplayInfo = (notification: Notification) => {
    // For specific notification types, show only the type as title with a brief description
    switch (notification.type) {
      case 'new_order':
        return {
          title: 'New Order Created',
          description: 'A new order has been placed',
          showMessage: false
        };
      case 'stock_adjustment':
        return {
          title: 'Stock Adjustment',
          description: 'Inventory levels have been updated',
          showMessage: false
        };
      case 'low_stock':
        return {
          title: 'Low Stock Alert',
          description: 'Product inventory is running low',
          showMessage: false
        };
      case 'out_of_stock':
        return {
          title: 'Out of Stock',
          description: 'Product is no longer available',
          showMessage: false
        };
      case 'purchase_order':
        return {
          title: 'Purchase Order',
          description: 'A new purchase order was created',
          showMessage: false
        };
      case 'expense_added':
        return {
          title: 'Expense Added',
          description: 'A new expense entry was recorded',
          showMessage: false
        };
      case 'product_added':
        return {
          title: 'Product Added',
          description: 'A new product was added to inventory',
          showMessage: false
        };
      case 'product_deleted':
        return {
          title: 'Product Deleted',
          description: 'A product was removed from inventory',
          showMessage: false
        };
      default:
        // For other types, show the original title and message
        return {
          title: notification.title,
          description: notification.message,
          showMessage: false
        };
    }
  }

  const getNotificationIconProps = (type: string) => {
    switch (type) {
      case 'success':
        return {
          Icon: CheckCircle,
          bgClass: 'bg-green-100',
          iconClass: 'text-green-500',
        };
      case 'warning':
        return {
          Icon: AlertTriangle,
          bgClass: 'bg-yellow-100',
          iconClass: 'text-yellow-500',
        };
      case 'product_deleted':
        return {
          Icon: X,
          bgClass: 'bg-red-100',
          iconClass: 'text-red-500',
        };
      case 'overdue_invoice':
         return {
          Icon: AlertTriangle,
          bgClass: 'bg-red-100',
          iconClass: 'text-red-500',
        };
      case 'new_order':
        return {
          Icon: Info,
          bgClass: 'bg-blue-100',
          iconClass: 'text-blue-500',
        };
      case 'stock_adjustment':
        return {
          Icon: Info,
          bgClass: 'bg-purple-100',
          iconClass: 'text-purple-500',
        };
      case 'low_stock':
        return {
          Icon: AlertTriangle,
          bgClass: 'bg-yellow-100',
          iconClass: 'text-yellow-500',
        };
      case 'out_of_stock':
        return {
          Icon: X,
          bgClass: 'bg-red-100',
          iconClass: 'text-red-500',
        };
      case 'purchase_order':
        return {
          Icon: Info,
          bgClass: 'bg-indigo-100',
          iconClass: 'text-indigo-500',
        };
      case 'expense_added':
        return {
          Icon: Info,
          bgClass: 'bg-green-100',
          iconClass: 'text-green-500',
        };
      case 'product_added':
        return {
          Icon: CheckCircle,
          bgClass: 'bg-green-100',
          iconClass: 'text-green-500',
        };
      case 'info':
      default:
        return {
          Icon: Info,
          bgClass: 'bg-blue-100',
          iconClass: 'text-blue-500',
        };
    }
  }

  // Get priority badge styling
  const getPriorityBadge = (priority?: string) => {
    if (!priority) return null

    const variants = {
      low: { variant: 'secondary' as const, text: 'Low' },
      medium: { variant: 'default' as const, text: 'Medium' },
      high: { variant: 'destructive' as const, text: 'High' },
      urgent: { variant: 'destructive' as const, text: 'Urgent' }
    }

    const config = variants[priority as keyof typeof variants]
    if (!config) return null

    return (
      <Badge variant={config.variant} className="text-xs px-1.5 py-0.5">
        {config.text}
      </Badge>
    )
  }

  // Format timestamp for display
  const formatNotificationTime = (dateString: string) => {
    const date = new Date(dateString)
    if (isToday(date)) {
      return format(date, 'h:mm a')
    } else if (isYesterday(date)) {
      return 'Yesterday'
    } else {
      return format(date, 'MMM d')
    }
  }

  // Group notifications for display
  const groupedNotifications = groupNotifications(notifications)

  return (
    <div className="relative" ref={dropdownRef}>
      <Button
        variant="ghost"
        size="sm"
        className={cn(
          "rounded-full relative transition-all duration-200 p-2",
          "hover:bg-gray-100 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
          isOpen && "bg-gray-100"
        )}
        onClick={() => setIsOpen(!isOpen)}
        aria-label={`Notifications${unreadCount > 0 ? ` (${unreadCount} unread)` : ''}`}
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <Bell className={cn(
          "h-5 w-5 transition-colors duration-200",
          unreadCount > 0 ? "text-blue-600" : "text-gray-600"
        )} />
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 h-5 w-5 rounded-full bg-red-500 flex items-center justify-center">
            <span className="text-white text-xs font-medium">
              {unreadCount > 99 ? '99+' : unreadCount}
            </span>
          </span>
        )}
      </Button>

      {isOpen && (
        <div className={cn(
          "absolute right-0 mt-3 w-96 bg-white rounded-2xl shadow-xl border border-gray-200 z-50",
          "transform transition-all duration-200 ease-out",
          "animate-in slide-in-from-top-2 fade-in-0"
        )}>
          {/* Header */}
          <div className="p-4 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <h3 className="text-lg font-semibold text-gray-900">Notifications</h3>
                {unreadCount > 0 && (
                  <Badge variant="secondary" className="text-xs">
                    {unreadCount} new
                  </Badge>
                )}
              </div>
              <div className="flex items-center gap-2">
                {unreadCount > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-xs text-blue-600 hover:text-blue-800 hover:bg-blue-50"
                    onClick={markAllAsRead}
                  >
                    Mark all read
                  </Button>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => setIsOpen(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="max-h-[32rem] overflow-y-auto">
            {isLoading ? (
              <div className="p-6">
                <div className="space-y-3">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="flex items-start gap-3">
                      <div className="w-10 h-10 bg-gray-200 rounded-full animate-pulse" />
                      <div className="flex-1 space-y-2">
                        <div className="h-4 bg-gray-200 rounded animate-pulse" />
                        <div className="h-3 bg-gray-200 rounded w-3/4 animate-pulse" />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : notifications.length === 0 ? (
              <div className="p-8 text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                  <Bell className="w-8 h-8 text-gray-400" />
                </div>
                <h4 className="text-sm font-medium text-gray-900 mb-1">No notifications</h4>
                <p className="text-xs text-gray-500">You're all caught up! Check back later for updates.</p>
              </div>
            ) : (
              <div className="py-2">
                {groupedNotifications.map((group, groupIndex) => (
                  <div key={group.title} className={groupIndex > 0 ? "mt-4" : ""}>
                    <div className="px-4 py-2 bg-gray-50">
                      <h4 className="text-xs font-semibold text-gray-700 uppercase tracking-wide">
                        {group.title}
                      </h4>
                    </div>
                    <div className="space-y-1">
                      {group.notifications.map((notification, index) => {
                        const { Icon, bgClass, iconClass } = getNotificationIconProps(notification.type);
                        const displayInfo = getNotificationDisplayInfo(notification);

                        return (
                          <div
                            key={notification.id}
                            className={cn(
                              "group relative px-4 py-3 cursor-pointer transition-all duration-200 transform hover:scale-[1.01]",
                              "hover:bg-gray-50 border-l-4 hover:shadow-sm",
                              !notification.is_read
                                ? "bg-blue-50/50 border-l-blue-500"
                                : "border-l-transparent hover:border-l-gray-200",
                              hoveredNotification === notification.id && "bg-gray-50 shadow-sm"
                            )}
                            style={{
                              animationDelay: `${index * 50}ms`,
                              animation: 'fadeInUp 0.3s ease-out forwards'
                            }}
                            onClick={() => navigateToNotification(notification.id)}
                            onMouseEnter={() => setHoveredNotification(notification.id)}
                            onMouseLeave={() => setHoveredNotification(null)}
                          >
                            <div className="flex items-start gap-3">
                              <div className={cn(
                                "flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center",
                                bgClass
                              )}>
                                <Icon className={cn("h-5 w-5", iconClass)} />
                              </div>

                              <div className="flex-1 min-w-0">
                                <div className="flex items-start justify-between gap-2">
                                  <div className="flex-1 min-w-0">
                                    <p className="text-sm font-medium text-gray-900 truncate">
                                      {displayInfo.title}
                                    </p>
                                    <p className="text-xs text-gray-600 mt-0.5 line-clamp-2">
                                      {displayInfo.description}
                                    </p>
                                  </div>

                                  <div className="flex items-center gap-2 flex-shrink-0">
                                    {getPriorityBadge(notification.priority)}
                                    {!notification.is_read && (
                                      <div className="w-2 h-2 bg-blue-500 rounded-full" />
                                    )}
                                  </div>
                                </div>

                                <div className="flex items-center justify-between mt-2">
                                  <span className="text-xs text-gray-500">
                                    {formatNotificationTime(notification.created_at)}
                                  </span>

                                  {/* Quick Actions */}
                                  <div className={cn(
                                    "flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity",
                                    hoveredNotification === notification.id && "opacity-100"
                                  )}>
                                    {!notification.is_read && (
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        className="h-6 w-6 p-0 hover:bg-blue-100"
                                        onClick={(e) => handleQuickMarkAsRead(notification.id, e)}
                                        title="Mark as read"
                                      >
                                        <CheckCircle className="h-3 w-3" />
                                      </Button>
                                    )}
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      className="h-6 w-6 p-0 hover:bg-gray-200"
                                      onClick={(e) => handleQuickArchive(notification.id, e)}
                                      title="Archive"
                                    >
                                      <Archive className="h-3 w-3" />
                                    </Button>
                                    <ChevronRight className="h-3 w-3 text-gray-400" />
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Footer */}
          {notifications.length > 0 && (
            <div className="border-t border-gray-100 p-3 bg-gray-50/50">
              <Link
                href="/dashboard/notifications"
                className={cn(
                  "flex items-center justify-center gap-2 w-full py-2 px-4",
                  "text-sm font-medium text-blue-600 hover:text-blue-700",
                  "bg-white hover:bg-blue-50 rounded-lg border border-gray-200",
                  "transition-all duration-200 hover:shadow-sm"
                )}
                onClick={() => setIsOpen(false)}
              >
                View all notifications
                <ExternalLink className="h-4 w-4" />
              </Link>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
