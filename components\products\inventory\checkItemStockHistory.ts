'use client'

import { createSupabaseClient } from '@/lib/supabase'
import { type InventoryItem } from './types'

export async function checkItemStockHistory(
  item: InventoryItem,
  itemsWithHistory: Record<string, boolean>,
  setItemsWithHistory: (items: Record<string, boolean>) => void,
  organizationId: string
): Promise<boolean> {
  // Check if we've already checked this item
  if (itemsWithHistory.hasOwnProperty(item.id)) {
    return itemsWithHistory[item.id];
  }

  try {
    const supabase = createSupabaseClient();
    
    if (!organizationId) {
      return false;
    }

    // Query stock history records
    let query = supabase
      .from('stock_history')
      .select('id', { count: 'exact', head: true })
      .eq('organization_id', organizationId)
      .limit(1);

    // Filter by product or variant
    if (item.variantId) {
      query = query.eq('variant_id', item.variantId);
    } else {
      query = query.eq('product_id', item.productId);
    }

    const { count, error } = await query;

    if (error) throw error;

    const hasHistory = (count || 0) > 0;
    
    // Update state
    setItemsWithHistory({
      ...itemsWithHistory,
      [item.id]: hasHistory
    });

    return hasHistory;
  } catch (error) {
    console.error('Error checking stock history:', error);
    return false;
  }
}