# Vault Integration Guide

This guide will help you properly set up and configure the Vault integration with your Onko account.

## Overview

The Vault integration allows you to securely store and manage your financial data within Onko. Vault provides a secure, encrypted storage solution for sensitive financial information.

## Prerequisites

1. A Vault account with API access
2. Administrator access to your Vault account
3. API Key with appropriate permissions
4. SSL certificate (HTTPS) for secure connections

## Supported Features

The Vault integration currently supports:
- Secure storage of financial data
- Automatic data synchronization
- Encrypted data transmission
- Access control and permissions management

## Step 1: Obtain Vault API Credentials

1. Log in to your Vault account
2. Navigate to **Settings > API Access**
3. Generate a new API Key with the following permissions:
   - Read financial data
   - Write financial data
   - Manage accounts
4. Save the API Key in a secure location

## Step 2: Configure Onko Integration

1. In Onko, go to **Settings > Integrations**
2. Find the Vault integration card
3. Click **"Connect"**
4. Fill in the form:
   - **Vault URL**: Your Vault API endpoint (e.g., `https://api.vault.com`)
   - **API Key**: The key you generated in Step 1
   - **Account ID** (Optional): Your Vault account identifier
5. Click **"Test Connection"** to verify your credentials
6. If successful, click **"Connect Vault"**

## Step 3: Sync Data

After connecting your Vault:
1. Click **"Sync Data"** on the Vault integration card
2. This will initiate the initial data synchronization
3. Subsequent syncs can be performed manually or scheduled

## Security Considerations

### Credential Storage

Vault credentials are stored securely using the Supabase Vault pattern:
- Actual secrets are stored in a secure location
- Database only contains references to the secrets
- Secrets are never exposed in API responses

### Data Encryption

All data transmitted between Onko and Vault is encrypted using industry-standard protocols:
- TLS 1.3 for data in transit
- AES-256 encryption for data at rest
- Regular security audits and compliance checks

## Troubleshooting

### Common Issues and Solutions

#### 1. "Invalid API Key" Error
- Verify your API key is correct and has not expired
- Ensure the key has the necessary permissions
- Check that you're using the correct Vault URL

#### 2. "Connection Timeout" Error
- Verify your Vault URL is accessible
- Check your network connectivity
- Ensure your firewall is not blocking the connection

#### 3. "Insufficient Permissions" Error
- Verify your API key has the required permissions
- Contact your Vault administrator to adjust permissions

#### 4. Data Not Syncing
- Check the connection status
- Verify your account has data to sync
- Review the sync logs for errors

### Checking Connection Status

1. In the Vault integration card, click **"Check"** next to Connection Test
2. Review the connection status message
3. If there are issues, follow the troubleshooting steps above

## Best Practices

### API Key Management

1. Rotate your API keys regularly (every 90 days)
2. Use different keys for different environments (development, staging, production)
3. Monitor key usage and revoke unused keys
4. Store keys securely and never commit them to version control

### Data Sync

1. Schedule regular syncs to keep data up-to-date
2. Monitor sync logs for errors or warnings
3. Perform manual syncs after significant data changes
4. Review synced data for accuracy and completeness

## API Endpoints

The integration uses the following Vault API endpoints:
- `GET /api/v1/accounts` - For account verification
- `GET /api/v1/transactions` - For transaction data sync
- `POST /api/v1/transactions` - For creating new transactions
- `PUT /api/v1/transactions/{id}` - For updating existing transactions

## Support

If you continue to experience issues, please contact support with:
- Your Vault URL
- Error messages you're seeing
- Steps you've already taken to troubleshoot
- Screenshots of connection settings if relevant

## FAQ

### Q: How often is data synced?
A: Data sync frequency depends on your plan. By default, data is synced every 24 hours. You can also trigger manual syncs at any time.

### Q: What data is synced?
A: The following data is synced:
- Account information
- Transaction history
- Balance information
- Account metadata

### Q: Can I edit synced data in Onko?
A: Yes, you can edit synced data in Onko. However, changes made in Vault after the initial sync will not overwrite your changes in Onko.

### Q: Is my data secure?
A: Yes, all data is encrypted both in transit and at rest. We follow industry best practices for data security and privacy.

### Q: What happens if the connection fails?
A: If the connection fails, the integration will retry several times. If it continues to fail, you'll receive a notification to check your connection settings.