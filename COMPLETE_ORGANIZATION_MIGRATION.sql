-- =============================================
-- COMPLETE ORGANIZATION MODEL MIGRATION
-- =============================================

-- First, ensure organization_preferences table exists
CREATE TABLE IF NOT EXISTS organization_preferences (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  organization_id uuid REFERENCES organizations(id) ON DELETE CASCADE NOT NULL,
  email_notifications BOOLEAN DEFAULT true,
  push_notifications BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
  UNIQUE(organization_id)
);

CREATE INDEX IF NOT EXISTS idx_organization_preferences_org_id ON organization_preferences(organization_id);

ALTER TABLE organization_preferences ENABLE ROW LEVEL SECURITY;

-- Create organization-based policies for preferences
DROP POLICY IF EXISTS "Organizations can view their own preferences" ON organization_preferences;
CREATE POLICY "Organizations can view their own preferences" ON organization_preferences
  FOR SELECT USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

DROP POLICY IF EXISTS "Organization admins can update preferences" ON organization_preferences;
CREATE POLICY "Organization admins can update preferences" ON organization_preferences
  FOR UPDATE USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );

DROP POLICY IF EXISTS "Organization admins can insert preferences" ON organization_preferences;
CREATE POLICY "Organization admins can insert preferences" ON organization_preferences
  FOR INSERT WITH CHECK (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );

GRANT ALL ON organization_preferences TO authenticated;

-- =============================================
-- MIGRATE CATEGORIES TABLE
-- =============================================
ALTER TABLE categories 
ADD COLUMN IF NOT EXISTS organization_id uuid REFERENCES organizations(id) ON DELETE CASCADE;

CREATE INDEX IF NOT EXISTS idx_categories_organization_id ON categories(organization_id);

UPDATE categories 
SET organization_id = (
  SELECT organization_id 
  FROM organization_members 
  WHERE user_id = categories.user_id 
  LIMIT 1
)
WHERE organization_id IS NULL;

ALTER TABLE categories 
ALTER COLUMN organization_id SET NOT NULL;

ALTER TABLE categories 
DROP COLUMN IF EXISTS user_id;

-- Update RLS policies for categories
DROP POLICY IF EXISTS "Users can view their categories" ON categories;
DROP POLICY IF EXISTS "Users can insert categories" ON categories;
DROP POLICY IF EXISTS "Users can update their categories" ON categories;
DROP POLICY IF EXISTS "Users can delete their categories" ON categories;

CREATE POLICY "Organization members can view categories" ON categories
  FOR SELECT USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can insert categories" ON categories
  FOR INSERT WITH CHECK (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can update categories" ON categories
  FOR UPDATE USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can delete categories" ON categories
  FOR DELETE USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

GRANT ALL ON categories TO authenticated;

-- =============================================
-- MIGRATE STOCK_MOVEMENTS TABLE
-- =============================================
ALTER TABLE stock_movements 
ADD COLUMN IF NOT EXISTS organization_id uuid REFERENCES organizations(id) ON DELETE CASCADE;

CREATE INDEX IF NOT EXISTS idx_stock_movements_organization_id ON stock_movements(organization_id);

UPDATE stock_movements 
SET organization_id = (
  SELECT organization_id 
  FROM organization_members 
  WHERE user_id = stock_movements.user_id 
  LIMIT 1
)
WHERE organization_id IS NULL;

ALTER TABLE stock_movements 
ALTER COLUMN organization_id SET NOT NULL;

ALTER TABLE stock_movements 
DROP COLUMN IF EXISTS user_id;

-- Update RLS policies for stock_movements
DROP POLICY IF EXISTS "Users can view their stock movements" ON stock_movements;
DROP POLICY IF EXISTS "Users can insert stock movements" ON stock_movements;
DROP POLICY IF EXISTS "Users can update their stock movements" ON stock_movements;
DROP POLICY IF EXISTS "Users can delete their stock movements" ON stock_movements;

CREATE POLICY "Organization members can view stock movements" ON stock_movements
  FOR SELECT USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can insert stock movements" ON stock_movements
  FOR INSERT WITH CHECK (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can update stock movements" ON stock_movements
  FOR UPDATE USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can delete stock movements" ON stock_movements
  FOR DELETE USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

GRANT ALL ON stock_movements TO authenticated;

-- =============================================
-- MIGRATE STOCK_HISTORY TABLE
-- =============================================
ALTER TABLE stock_history 
ADD COLUMN IF NOT EXISTS organization_id uuid REFERENCES organizations(id) ON DELETE CASCADE;

CREATE INDEX IF NOT EXISTS idx_stock_history_organization_id ON stock_history(organization_id);

UPDATE stock_history 
SET organization_id = (
  SELECT organization_id 
  FROM organization_members 
  WHERE user_id = stock_history.user_id 
  LIMIT 1
)
WHERE organization_id IS NULL;

ALTER TABLE stock_history 
ALTER COLUMN organization_id SET NOT NULL;

ALTER TABLE stock_history 
DROP COLUMN IF EXISTS user_id;

-- Update RLS policies for stock_history
DROP POLICY IF EXISTS "Users can view their stock history" ON stock_history;
DROP POLICY IF EXISTS "Users can insert stock history" ON stock_history;
DROP POLICY IF EXISTS "Users can update their stock history" ON stock_history;
DROP POLICY IF EXISTS "Users can delete their stock history" ON stock_history;

CREATE POLICY "Organization members can view stock history" ON stock_history
  FOR SELECT USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can insert stock history" ON stock_history
  FOR INSERT WITH CHECK (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can update stock history" ON stock_history
  FOR UPDATE USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can delete stock history" ON stock_history
  FOR DELETE USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

GRANT ALL ON stock_history TO authenticated;

-- =============================================
-- MIGRATE PRODUCT_ATTRIBUTES TABLE
-- =============================================
ALTER TABLE product_attributes 
ADD COLUMN IF NOT EXISTS organization_id uuid REFERENCES organizations(id) ON DELETE CASCADE;

CREATE INDEX IF NOT EXISTS idx_product_attributes_organization_id ON product_attributes(organization_id);

UPDATE product_attributes 
SET organization_id = (
  SELECT organization_id 
  FROM organization_members 
  WHERE user_id = product_attributes.user_id 
  LIMIT 1
)
WHERE organization_id IS NULL;

ALTER TABLE product_attributes 
ALTER COLUMN organization_id SET NOT NULL;

ALTER TABLE product_attributes 
DROP COLUMN IF EXISTS user_id;

-- Update RLS policies for product_attributes
DROP POLICY IF EXISTS "Users can view their product attributes" ON product_attributes;
DROP POLICY IF EXISTS "Users can insert product attributes" ON product_attributes;
DROP POLICY IF EXISTS "Users can update their product attributes" ON product_attributes;
DROP POLICY IF EXISTS "Users can delete their product attributes" ON product_attributes;

CREATE POLICY "Organization members can view product attributes" ON product_attributes
  FOR SELECT USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can insert product attributes" ON product_attributes
  FOR INSERT WITH CHECK (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can update product attributes" ON product_attributes
  FOR UPDATE USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can delete product attributes" ON product_attributes
  FOR DELETE USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

GRANT ALL ON product_attributes TO authenticated;

-- =============================================
-- MIGRATE PRODUCT_ATTRIBUTE_VALUES TABLE
-- =============================================
ALTER TABLE product_attribute_values 
ADD COLUMN IF NOT EXISTS organization_id uuid REFERENCES organizations(id) ON DELETE CASCADE;

CREATE INDEX IF NOT EXISTS idx_product_attribute_values_organization_id ON product_attribute_values(organization_id);

UPDATE product_attribute_values 
SET organization_id = (
  SELECT organization_id 
  FROM organization_members 
  WHERE user_id = product_attribute_values.user_id 
  LIMIT 1
)
WHERE organization_id IS NULL;

ALTER TABLE product_attribute_values 
ALTER COLUMN organization_id SET NOT NULL;

ALTER TABLE product_attribute_values 
DROP COLUMN IF EXISTS user_id;

-- Update RLS policies for product_attribute_values
DROP POLICY IF EXISTS "Users can view their product attribute values" ON product_attribute_values;
DROP POLICY IF EXISTS "Users can insert product attribute values" ON product_attribute_values;
DROP POLICY IF EXISTS "Users can update their product attribute values" ON product_attribute_values;
DROP POLICY IF EXISTS "Users can delete their product attribute values" ON product_attribute_values;

CREATE POLICY "Organization members can view product attribute values" ON product_attribute_values
  FOR SELECT USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can insert product attribute values" ON product_attribute_values
  FOR INSERT WITH CHECK (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can update product attribute values" ON product_attribute_values
  FOR UPDATE USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can delete product attribute values" ON product_attribute_values
  FOR DELETE USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

GRANT ALL ON product_attribute_values TO authenticated;

-- =============================================
-- UPDATE TRIGGERS FOR ORGANIZATION MODEL
-- =============================================

-- Update stock adjustment notification function to use organization_id
CREATE OR REPLACE FUNCTION create_stock_adjustment_notification()
RETURNS TRIGGER AS $$
DECLARE
  product_name TEXT;
  product_sku TEXT;
  organization_id_var uuid;
  notification_title TEXT;
  notification_message TEXT;
BEGIN
  -- Only proceed if stock quantity actually changed
  IF OLD.stock_quantity IS DISTINCT FROM NEW.stock_quantity THEN
    -- Get product information
    SELECT p.name, p.base_sku, p.organization_id INTO product_name, product_sku, organization_id_var
    FROM products p
    WHERE p.id = NEW.id;
    
    -- Create notification title and message
    notification_title := 'Stock Adjustment: ' || COALESCE(product_name, 'Unknown Product');
    notification_message := 'Stock for "' || COALESCE(product_name, 'Unknown Product') || 
                           '" (SKU: ' || COALESCE(product_sku, 'N/A') || 
                           ') was adjusted from ' || COALESCE(OLD.stock_quantity, 0) || 
                           ' to ' || COALESCE(NEW.stock_quantity, 0) || '.';
    
    -- Insert notification
    INSERT INTO notifications (
      organization_id,
      type,
      title,
      message,
      related_entity_type,
      related_entity_id,
      created_at
    ) VALUES (
      organization_id_var,
      'stock_adjustment',
      notification_title,
      notification_message,
      'product',
      NEW.id,
      NOW()
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Update variant stock adjustment notification function to use organization_id
CREATE OR REPLACE FUNCTION create_variant_stock_adjustment_notification()
RETURNS TRIGGER AS $$
DECLARE
  product_name TEXT;
  variant_name TEXT;
  variant_sku TEXT;
  organization_id_var uuid;
  notification_title TEXT;
  notification_message TEXT;
BEGIN
  -- Only proceed if stock quantity actually changed
  IF OLD.stock_quantity IS DISTINCT FROM NEW.stock_quantity THEN
    -- Get product and variant information
    SELECT p.name, pv.variant_name, pv.sku, p.organization_id INTO product_name, variant_name, variant_sku, organization_id_var
    FROM product_variants pv
    JOIN products p ON pv.product_id = p.id
    WHERE pv.id = NEW.id;
    
    -- Create notification title and message
    notification_title := 'Stock Adjustment: ' || COALESCE(product_name, 'Unknown Product') || 
                         ' - ' || COALESCE(variant_name, 'Unknown Variant');
    notification_message := 'Stock for "' || COALESCE(product_name, 'Unknown Product') || 
                           ' - ' || COALESCE(variant_name, 'Unknown Variant') || 
                           '" (SKU: ' || COALESCE(variant_sku, 'N/A') || 
                           ') was adjusted from ' || COALESCE(OLD.stock_quantity, 0) || 
                           ' to ' || COALESCE(NEW.stock_quantity, 0) || '.';
    
    -- Insert notification
    INSERT INTO notifications (
      organization_id,
      type,
      title,
      message,
      related_entity_type,
      related_entity_id,
      created_at
    ) VALUES (
      organization_id_var,
      'stock_adjustment',
      notification_title,
      notification_message,
      'variant',
      NEW.id,
      NOW()
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- REFRESH SCHEMA CACHE
-- =============================================
-- This helps ensure that the Supabase client recognizes the new schema
SELECT COUNT(*) FROM categories;
SELECT COUNT(*) FROM stock_movements;
SELECT COUNT(*) FROM stock_history;
SELECT COUNT(*) FROM product_attributes;
SELECT COUNT(*) FROM product_attribute_values;
SELECT COUNT(*) FROM organization_preferences;
