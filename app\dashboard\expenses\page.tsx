'use client'

import { useState, useEffect } from 'react'
import { usePersistedState } from '@/lib/use-persisted-state'
import { type ExpenseFilters, INITIAL_FILTERS } from '@/components/expenses/expense-table-header'
import { ExpensesTab } from '@/components/expenses/expenses-tab'
import { AddExpenseModal } from '@/components/expenses/AddExpenseModal'
import { Button } from '@/components/ui/button'
import { FileSpreadsheet } from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'
import { PageHeader } from '@/components/ui/page-header'

export default function ExpensesPage() {
  const [isMobile, setIsMobile] = useState(false)
  const [isAddExpenseModalOpen, setIsAddExpenseModalOpen] = useState(false)
  const [isExporting, setIsExporting] = useState(false)
  
  // Shared state for filters across tabs (for contextual analytics) - with persistence
  const [globalFilters, setGlobalFilters] = usePersistedState<ExpenseFilters>(
    'expenses-filters', 
    INITIAL_FILTERS
  )
  const [filteredExpenses, setFilteredExpenses] = useState<any[]>([])
  const { toast } = useToast()

  // Mobile detection
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  const handleExpenseAdded = () => {
    // Close the modal after successful expense addition
    setIsAddExpenseModalOpen(false)
  }

  // Export to CSV function
  const exportToCSV = () => {
    setIsExporting(true)
    
    try {
      // Create CSV header with metadata
      const metadataLines = [
        '# Expense Report',
        `# Generated on: ${new Date().toISOString()}`,
        `# Total Expenses: ${filteredExpenses.length}`,
        `# Total Amount: ${filteredExpenses.reduce((sum, exp) => sum + (exp.amount || 0), 0).toFixed(2)}`,
        '#',
        '# Report Data:'
      ];
      
      // CSV header row
      const headers = ['Expense ID', 'Date', 'Description', 'Category', 'Vendor', 'Payment Method', 'Amount', 'Created At'];
      
      // CSV data rows with proper escaping
      const dataRows = filteredExpenses.map(expense => {
        const expenseDate = new Date(expense.expense_date || expense.created_at);
        const createdDate = new Date(expense.created_at);
        
        const row = [
          expense.expense_id || 'N/A',
          expenseDate.toLocaleDateString('en-US'),
          '"' + (expense.description || '').replace(/"/g, '""') + '"', // Escape quotes
          expense.category || '',
          '"' + (expense.vendor || '').replace(/"/g, '""') + '"', // Escape quotes
          expense.payment_method || '',
          (expense.amount || 0).toFixed(2),
          createdDate.toISOString()
        ].join(',')
        
        return row;
      })
      
      // Combine all parts
      const csvContent = [
        ...metadataLines,
        headers.join(','),
        ...dataRows
      ].join('\n')

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `expenses-export-${new Date().toISOString().split('T')[0]}.csv`
      link.style.display = 'none'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      toast({
        title: "CSV Export Complete",
        description: `Successfully exported ${filteredExpenses.length} expenses to CSV format.`,
      })
    } catch (error) {
      console.error('CSV export error:', error)
      toast({
        title: "CSV Export Failed",
        description: "Failed to export expenses. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsExporting(false)
    }
  }

  return (
    <div className="flex flex-col gap-4">
      {/* Page Header with Title and Actions */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <PageHeader 
          title="Expenses" 
          description="Manage your business expenses"
        />
        
        <div className="flex flex-col sm:flex-row sm:items-center gap-2">
          {/* Export Button */}
          <Button 
            variant="outline" 
            size="sm" 
            className="flex items-center gap-1.5 h-8 px-3"
            onClick={exportToCSV}
            disabled={isExporting || filteredExpenses.length === 0}
          >
            <FileSpreadsheet className="h-4 w-4" />
            <span>Export CSV</span>
          </Button>
          
          {/* Add Expense Button */}
          <Button
            className="flex items-center gap-1.5 h-8 px-3 bg-blue-600 hover:bg-blue-700"
            onClick={() => setIsAddExpenseModalOpen(true)}
          >
            <span>+ Add Expense</span>
          </Button>
        </div>
      </div>
      
      {/* Main Content - Expenses Tab without inline Add Expense Form */}
      <ExpensesTab 
        isMobile={isMobile} 
        onFiltersChange={setGlobalFilters}
        onFilteredExpensesChange={(expenses) => setFilteredExpenses(expenses)}
      />
      
      {/* Add Expense Modal */}
      <AddExpenseModal
        open={isAddExpenseModalOpen}
        onOpenChange={setIsAddExpenseModalOpen}
        onExpenseAdded={handleExpenseAdded}
      />
    </div>
  )
}