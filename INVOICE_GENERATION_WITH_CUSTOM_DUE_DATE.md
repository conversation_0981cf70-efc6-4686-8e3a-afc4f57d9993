# Invoice Generation with Custom Due Date Implementation

## Overview
This document describes the implementation of the custom due date feature for invoice generation from orders. The feature allows users to specify a custom due date when generating invoices, with a default of 30 days from the current date if no date is provided.

## Components Updated

### 1. Database Function
The `generate_invoice_from_order` function has been updated to accept an optional `p_due_date` parameter:

```sql
CREATE OR REPLACE FUNCTION generate_invoice_from_order(
  p_order_id uuid,
  p_due_date timestamptz DEFAULT NULL
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_invoice_id uuid;
  v_order record;
BEGIN
  -- Get the order details
  SELECT 
    id,
    user_id,
    customer_id,
    order_number,
    total
  INTO v_order
  FROM orders
  WHERE id = p_order_id;
  
  -- Check if order exists
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Order not found with id: %', p_order_id;
  END IF;
  
  -- Generate a unique invoice number
  DECLARE
    date_part text := to_char(now(), 'YYYYMMDD');
    random_part text := lpad(floor(random() * 9000 + 1000)::text, 4, '0');
    invoice_number text := 'INV-' || date_part || '-' || random_part;
  BEGIN
    -- Insert the new invoice
    INSERT INTO invoices (
      user_id,
      order_id,
      customer_id,
      invoice_number,
      issue_date,
      due_date,
      status,
      total
    ) VALUES (
      v_order.user_id,
      v_order.id,
      v_order.customer_id,
      invoice_number,
      now(),
      COALESCE(p_due_date, now() + interval '30 days'),
      'Draft',
      v_order.total
    ) RETURNING id INTO v_invoice_id;
  END;
  
  RETURN v_invoice_id;
END;
$$;
```

### 2. GenerateInvoiceModal Component
A new modal component was created at `components/sales/orders/GenerateInvoiceModal.tsx` with:
- Title showing the order number
- DatePicker for selecting a custom due date (pre-populated with 30 days from now)
- Checkbox for "Mark as Sent immediately"
- Confirm & Generate and Cancel buttons

### 3. EnhancedOrderTable Component
The [EnhancedOrderTable.tsx](file:///x:/Qoder/onko/components/sales/orders/EnhancedOrderTable.tsx) component was updated to:
- Import and use the [GenerateInvoiceModal](file:///x:/Qoder/onko/components/sales/orders/GenerateInvoiceModal.tsx#L17-L31)
- Add state management for the modal visibility and selected order
- Replace the direct invoice generation with opening the modal
- Handle the confirmation from the modal to generate the invoice with the selected parameters

### 4. useGenerateInvoice Hook
The [useGenerateInvoice](file:///x:/Qoder/onko/hooks/use-invoices.ts#L162-L213) hook in [hooks/use-invoices.ts](file:///x:/Qoder/onko/hooks/use-invoices.ts) was updated to:
- Accept optional `dueDate` and `markAsSent` parameters
- Pass these parameters to the RPC function call
- Handle the "Mark as Sent" functionality by updating the invoice status after creation

## Implementation Details

### Database Function Logic
1. The function accepts an optional `p_due_date` parameter
2. If provided, it uses this date as the invoice due date
3. If not provided, it defaults to 30 days from the current date using `now() + interval '30 days'`
4. The due date is set using `COALESCE(p_due_date, now() + interval '30 days')`

### Frontend Workflow
1. User clicks "Generate Invoice" in the order table
2. The [GenerateInvoiceModal](file:///x:/Qoder/onko/components/sales/orders/GenerateInvoiceModal.tsx#L17-L31) opens with:
   - Order number in the title
   - DatePicker pre-populated with 30 days from now
   - Checkbox for "Mark as Sent immediately"
3. User can modify the due date or leave the default
4. User can check "Mark as Sent immediately" if desired
5. User clicks "Confirm & Generate"
6. The frontend calls the updated `generate_invoice_from_order` RPC function with the selected parameters
7. If "Mark as Sent" was checked, the frontend additionally updates the invoice status to "Sent"

## Testing
The implementation has been tested to ensure:
- The modal opens correctly when clicking "Generate Invoice"
- The default due date is 30 days from now
- Custom due dates are properly passed to the backend
- The "Mark as Sent" functionality works correctly
- Error handling is properly implemented
- The invoice is generated with the correct due date in all scenarios

## Future Enhancements
- Add validation to ensure due dates are not in the past
- Implement a calendar view for selecting due dates
- Add the ability to set recurring invoices