'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { getSupabaseClient } from '@/lib/supabase';
import { Loader2 } from 'lucide-react';

// Force light mode for this page
const lightModeStyles = `
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 47.4% 11.2%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 47.4% 11.2%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
  }
  
  .dark, [data-theme="dark"] {
    display: none !important;
  }
`;

export default function AcceptInvitationPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { toast } = useToast();
  
  // Inject light mode styles
  useEffect(() => {
    // Add data-theme attribute to force light mode
    document.documentElement.setAttribute('data-theme', 'light');
    
    const styleElement = document.createElement('style');
    styleElement.innerHTML = lightModeStyles;
    document.head.appendChild(styleElement);
    
    // Clean up the style element when the component unmounts
    return () => {
      document.documentElement.removeAttribute('data-theme');
      if (styleElement.parentNode) {
        styleElement.parentNode.removeChild(styleElement);
      }
    };
  }, []);
  
  const [token, setToken] = useState<string | null>(null);
  const [invitationData, setInvitationData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    fullName: '',
    password: '',
    confirmPassword: ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Get token from URL params
  useEffect(() => {
    const tokenParam = searchParams.get('token');
    if (tokenParam) {
      setToken(tokenParam);
      validateInvitation(tokenParam);
    } else {
      setError('No invitation token provided. Please use a valid invitation link.');
      setLoading(false);
    }
  }, [searchParams, router, toast]);

  // Validate invitation token
  const validateInvitation = async (token: string) => {
    try {
      setError(null);
      const supabase = getSupabaseClient();
      
      // Call the accept_invitation function to validate the token
      const { data, error } = await supabase.rpc('accept_invitation', { token });
      
      if (error) {
        throw new Error(error.message);
      }
      
      if (!data.success) {
        throw new Error(data.message);
      }
      
      console.log('Function response data:', data);
      setInvitationData(data);
    } catch (err: any) {
      console.error('Error validating invitation:', err);
      setError(err.message || 'Failed to validate invitation. The invitation may be invalid or expired.');
    } finally {
      setLoading(false);
    }
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.fullName.trim()) {
      newErrors.fullName = 'Full name is required';
    }
    
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }
    
    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm() || !token || !invitationData) {
      return;
    }
    
    setProcessing(true);
    
    try {
      const supabase = getSupabaseClient();
      
      // Create the user account
      const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
        email: invitationData.email,
        password: formData.password,
        options: {
          data: {
            first_name: formData.fullName.split(' ')[0],
            last_name: formData.fullName.split(' ').slice(1).join(' ') || '',
            display_name: formData.fullName,
            business_name: invitationData.organization_name
          }
        }
      });
      
      if (signUpError) {
        throw new Error(signUpError.message);
      }
      
      if (!signUpData.user) {
        throw new Error('Failed to create user account');
      }
      
      // Complete the invitation process
      const { data: completeData, error: completeError } = await supabase.rpc('complete_invitation', {
        token,
        user_id: signUpData.user.id,
        full_name: formData.fullName
      });
      
      if (completeError) {
        throw new Error(completeError.message);
      }
      
      if (!completeData.success) {
        throw new Error(completeData.message);
      }
      
      toast({
        title: 'Success',
        description: 'Invitation accepted successfully! You can now sign in with your credentials.'
      });
      
      // Redirect to sign in page
      router.push('/auth/signin');
    } catch (err: any) {
      console.error('Error accepting invitation:', err);
      toast({
        title: 'Error',
        description: err.message || 'Failed to accept invitation. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setProcessing(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50" data-theme="light">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-4 text-gray-600">Validating invitation...</p>
          {token && (
            <p className="mt-2 text-sm text-gray-500">Token: {token.substring(0, 8)}...</p>
          )}
        </div>
      </div>
    );
  }

  // Show error message if validation failed
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4" data-theme="light">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl">Invitation Error</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Error</h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>{error}</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="text-center space-y-3">
              <Button onClick={() => router.push('/auth/signin')}>
                Back to Sign In
              </Button>
              <div className="text-sm text-gray-500">
                <p>Debug info:</p>
                <p className="font-mono text-xs mt-1">
                  {token ? `Token: ${token.substring(0, 8)}...` : 'No token provided'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show invitation form if data is valid
  if (invitationData) {
    // Log the invitation data for debugging
    console.log('Invitation data received:', invitationData);
    
    // Ensure we have a valid organization name
    let organizationName = 'your organization';
    if (invitationData.organization_name && 
        typeof invitationData.organization_name === 'string' && 
        invitationData.organization_name.trim().length > 0) {
      organizationName = invitationData.organization_name.trim();
    }
    
    // Log the organization name we're going to display
    console.log('Displaying organization name:', organizationName);
    
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4" data-theme="light">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl">You're Invited!</CardTitle>
            <CardDescription>
              You've been invited to join <strong>{organizationName}</strong>
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={invitationData.email}
                  readOnly
                  className="bg-gray-100"
                />
              </div>
              
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="fullName">Full Name</Label>
                  <Input
                    id="fullName"
                    name="fullName"
                    type="text"
                    value={formData.fullName}
                    onChange={handleInputChange}
                    placeholder="Enter your full name"
                    required
                  />
                  {errors.fullName && <p className="text-sm text-red-500">{errors.fullName}</p>}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  <Input
                    id="password"
                    name="password"
                    type="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    placeholder="Create a password"
                    required
                  />
                  {errors.password && <p className="text-sm text-red-500">{errors.password}</p>}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">Confirm Password</Label>
                  <Input
                    id="confirmPassword"
                    name="confirmPassword"
                    type="password"
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    placeholder="Confirm your password"
                    required
                  />
                  {errors.confirmPassword && <p className="text-sm text-red-500">{errors.confirmPassword}</p>}
                </div>
              </form>
            </div>
          </CardContent>
          <CardFooter>
            <Button 
              className="w-full" 
              onClick={handleSubmit as any}
              disabled={processing}
            >
              {processing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                'Accept Invitation & Create Account'
              )}
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  // Fallback - should not reach here
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4" data-theme="light">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl">Unexpected Error</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Unexpected Error</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>An unexpected error occurred. Please try again or contact support.</p>
                </div>
              </div>
            </div>
          </div>
          <div className="text-center space-y-3">
            <Button onClick={() => router.push('/auth/signin')}>
              Back to Sign In
            </Button>
            <div className="text-sm text-gray-500">
              <p>Debug info:</p>
              <p className="font-mono text-xs mt-1">
                {token ? `Token: ${token.substring(0, 8)}...` : 'No token provided'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}