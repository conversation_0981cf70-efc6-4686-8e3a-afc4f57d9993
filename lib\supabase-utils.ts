import { getSupabaseClient } from '@/lib/supabase'
import { dataEnrichmentService } from '@/lib/supabase-enrichment'

/**
 * Utility function to fetch related data for sales records
 * This avoids issues with Supabase schema cache and relationship syntax
 * @param sales - Array of sales records
 * @returns Promise with sales records including customer names
 */
export async function enrichSalesWithCustomerData(sales: any[]) {
  const supabase = getSupabaseClient()
  return dataEnrichmentService.enrichSalesWithCustomerData(supabase, sales)
}

/**
 * Utility function to fetch a single sale with customer data
 * @param sale - Sale record
 * @returns Promise with sale record including customer name
 */
export async function enrichSaleWithCustomerData(sale: any) {
  const supabase = getSupabaseClient()
  return dataEnrichmentService.enrichSaleWithCustomerData(supabase, sale)
}

/**
 * Generic utility function to enrich records with related data
 * @param records - Array of records to enrich
 * @param relatedTable - Name of the related table
 * @param foreignKey - Foreign key field in the main records
 * @param relatedKey - Key field in the related table (default: 'id')
 * @param selectFields - Fields to select from the related table
 * @param resultMap - Function to transform related records into a map
 * @returns Promise with enriched records
 */
export async function enrichRecordsWithRelatedData<T>(
  records: T[],
  relatedTable: string,
  foreignKey: string,
  relatedKey: string = 'id',
  selectFields: string,
  resultMap: (relatedRecords: any[]) => Record<string, any>
): Promise<T[]> {
  const supabase = getSupabaseClient()
  return dataEnrichmentService.enrichRecordsWithRelatedData(
    supabase,
    records,
    relatedTable,
    foreignKey,
    relatedKey,
    selectFields,
    resultMap
  )
}

/**
 * Utility function to fetch related data for products with categories and variants
 * This avoids issues with Supabase schema cache and relationship syntax
 * @param products - Array of product records
 * @returns Promise with products including category names and variants
 */
export async function enrichProductsWithRelatedData(products: any[]) {
  const supabase = getSupabaseClient()
  return dataEnrichmentService.enrichProductsWithRelatedData(supabase, products)
}

/**
 * Utility function to fetch a single product with related data
 * @param product - Product record
 * @returns Promise with product record including category name and variants
 */
export async function enrichProductWithRelatedData(product: any) {
  const supabase = getSupabaseClient()
  return dataEnrichmentService.enrichProductWithRelatedData(supabase, product)
}