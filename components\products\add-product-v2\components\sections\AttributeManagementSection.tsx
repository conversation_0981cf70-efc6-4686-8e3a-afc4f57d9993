import React, { useState, useEffect } from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { SimpleDropdown } from '@/components/ui/simple-dropdown'
import { Plus, X } from 'lucide-react'
import { useProductForm } from '../../context/ProductFormContext'
import { FormField } from '../shared/FormField'
import { useAuth } from '@/contexts/auth-context'
import { getProductAttributes, createProductAttribute, getProductAttributeValues, createProductAttributeValue } from '@/lib/supabase'

interface AttributeDefinition {
  id: string
  name: string
  attributeId?: string // New field to store the database ID of the attribute
  values: string[]
}

interface VariantCombination {
  id: string
  attributes: { [key: string]: string }
  sku: string
  enabled: boolean
}

interface Props {
  onVariantsGenerated?: (variants: VariantCombination[]) => void
}

export function AttributeManagementSection({ onVariantsGenerated }: Props) {
  const { formData, updateFormData } = useProductForm()
  const { user } = useAuth()
  const [attributes, setAttributes] = useState<AttributeDefinition[]>([])
  const [newAttributeName, setNewAttributeName] = useState('')
  const [newAttributeValue, setNewAttributeValue] = useState('')
  const [editingAttribute, setEditingAttribute] = useState<string | null>(null)
  const [variantCombinations, setVariantCombinations] = useState<VariantCombination[]>([])
  const [userAttributes, setUserAttributes] = useState<Array<{ value: string; label: string }>>([])
  const [loadingAttributes, setLoadingAttributes] = useState(true)
  const [attributeValuesCache, setAttributeValuesCache] = useState<Record<string, Array<{ value: string; label: string }>>>({});

  // Load user's saved attributes
  useEffect(() => {
    const loadUserAttributes = async () => {
      if (!user?.id) return
      
      try {
        setLoadingAttributes(true)
        const attributesData = await getProductAttributes(user.id)
        
        // Only use user-defined attributes from database
        const userDefinedAttributes = attributesData.map(attr => ({ value: attr.name, label: attr.name }))
        
        setUserAttributes(userDefinedAttributes)
      } catch (error) {
        console.error('Error loading user attributes:', error)
        setUserAttributes([])
      } finally {
        setLoadingAttributes(false)
      }
    }
    
    loadUserAttributes()
  }, [user?.id])

  // Load attribute values when an attribute is being edited
  useEffect(() => {
    const loadAttributeValues = async () => {
      if (!editingAttribute || !user?.id) return;
      
      const attribute = attributes.find(attr => attr.id === editingAttribute);
      if (!attribute) return;
      
      // Only load values from database for user-defined attributes
      if (!attribute.attributeId) {
        return;
      }
      
      // Check if we already have the values in cache
      if (attributeValuesCache[attribute.attributeId]) return;
      
      try {
        const values = await getProductAttributeValues(user.id, attribute.attributeId);
        setAttributeValuesCache(prev => ({
          ...prev,
          [attribute.attributeId!]: values
        }));
      } catch (error) {
        console.error('Error loading attribute values:', error);
      }
    };
    
    loadAttributeValues();
  }, [editingAttribute, attributes, user?.id, attributeValuesCache]);

  // Add new attribute
  const addAttribute = async () => {
    if (!newAttributeName.trim()) return
    
    // Check if attribute already exists by name
    const existingAttribute = attributes.find(attr => attr.name === newAttributeName.trim());
    if (existingAttribute) {
      // Attribute already exists, just set it as selected and start editing
      setEditingAttribute(existingAttribute.id);
      setNewAttributeName('');
      return;
    }
    
    // Check if this attribute already exists in user attributes (database)
    const existingUserAttribute = userAttributes.find(attr => attr.label === newAttributeName.trim());
    
    let attributeId = '';
    let newAttributeId = `attr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Save to database for all new attributes
    if (user?.id && !existingUserAttribute) {
      try {
        const result = await createProductAttribute(user.id, newAttributeName.trim())
        attributeId = result.id;
        // Refresh the user attributes list
        const attributesData = await getProductAttributes(user.id)
        const userDefinedAttributes = attributesData.map(attr => ({ value: attr.name, label: attr.name }))
        setUserAttributes(userDefinedAttributes)
      } catch (error: any) {
        // Handle duplicate key error specifically
        if (error.code === '23505') {
          // Only log in development mode
          if (process.env.NODE_ENV === 'development') {
            console.warn('Attribute already exists in database:', newAttributeName.trim());
          }
          // Try to get the existing attribute
          try {
            const attributesData = await getProductAttributes(user!.id);
            const foundAttribute = attributesData.find(attr => attr.name === newAttributeName.trim());
            if (foundAttribute) {
              attributeId = foundAttribute.id;
            }
          } catch (fetchError) {
            // Only log in development mode
            if (process.env.NODE_ENV === 'development') {
              console.error('Error fetching existing attribute:', fetchError);
            }
          }
        } else {
          // Only log in development mode
          if (process.env.NODE_ENV === 'development') {
            console.error('Error saving attribute:', error);
          }
        }
      }
    } else if (existingUserAttribute) {
      // If attribute exists in user attributes, try to get its ID
      try {
        const attributesData = await getProductAttributes(user!.id);
        const foundAttribute = attributesData.find(attr => attr.name === newAttributeName.trim());
        if (foundAttribute) {
          attributeId = foundAttribute.id;
        }
      } catch (error) {
        // Only log in development mode
        if (process.env.NODE_ENV === 'development') {
          console.error('Error fetching existing attribute:', error);
        }
      }
    }
    
    const newAttribute: AttributeDefinition = {
      id: newAttributeId, // Use unique ID instead of Date.now().toString()
      name: newAttributeName.trim(),
      attributeId: attributeId || undefined, // Store the database ID if available
      values: []
    }
    
    setAttributes(prev => [...prev, newAttribute])
    setNewAttributeName('')
    setEditingAttribute(newAttribute.id)
  }

  // Add value to attribute
  const addValueToAttribute = async (attributeId: string, value: string) => {
    if (!value.trim()) return
    
    // Find the attribute
    const attribute = attributes.find(attr => attr.id === attributeId);
    if (!attribute) return;
    
    // Check if value already exists locally
    if (attribute.values.includes(value.trim())) {
      // Only log if we're in development mode
      if (process.env.NODE_ENV === 'development') {
        console.log('Value already exists locally:', value.trim());
      }
      return;
    }
    
    // Save to database if it's a user-defined attribute and we have a user
    let saveSuccess = true;
    if (attribute.attributeId && user?.id) {
      try {
        const result = await createProductAttributeValue(user.id, attribute.attributeId, value.trim());
        if (result === null) {
          // Value already exists in database - only log in development
          if (process.env.NODE_ENV === 'development') {
            console.log('Value already exists in database:', value.trim());
          }
        }
        
        // Update the cache with the new value (if it was successfully added or if it already existed)
        setAttributeValuesCache(prev => {
          const currentValues = prev[attribute.attributeId!] || [];
          // Check if value already exists to avoid duplicates
          if (!currentValues.some(v => v.value === value.trim())) {
            return {
              ...prev,
              [attribute.attributeId!]: [...currentValues, { value: value.trim(), label: value.trim() }]
            };
          }
          return prev;
        });
      } catch (error: any) {
        // Only log the full error in development mode
        if (process.env.NODE_ENV === 'development') {
          console.error('Error saving attribute value:', error);
        } else {
          // In production, log a simplified message
          console.error('Error saving attribute value:', error.message || 'Unknown error');
        }
        saveSuccess = false;
        // Even if saving to database fails, we can still add it locally
      }
    }
    
    // Only update local state if either:
    // 1. Database operation succeeded or wasn't needed
    // 2. Database operation failed but we still want to add it locally
    if (saveSuccess || !attribute.attributeId) {
      setAttributes(prev => prev.map(attr => 
        attr.id === attributeId 
          ? { ...attr, values: [...attr.values, value.trim()] }
          : attr
      ));
    }
  }

  // Remove value from attribute
  const removeValueFromAttribute = (attributeId: string, valueIndex: number) => {
    setAttributes(prev => prev.map(attr => 
      attr.id === attributeId 
        ? { ...attr, values: attr.values.filter((_, i) => i !== valueIndex) }
        : attr
    ))
  }

  // Remove attribute
  const removeAttribute = (attributeId: string) => {
    setAttributes(prev => prev.filter(attr => attr.id !== attributeId))
    generateVariantCombinations(attributes.filter(attr => attr.id !== attributeId))
  }

  // Generate all possible variant combinations
  const generateVariantCombinations = (attrs: AttributeDefinition[] = attributes) => {
    if (attrs.length === 0 || attrs.some(attr => attr.values.length === 0)) {
      setVariantCombinations([])
      return
    }

    const combinations: VariantCombination[] = []
    
    function generateCombos(index: number, currentCombo: { [key: string]: string }) {
      if (index === attrs.length) {
        const comboId = `${Object.values(currentCombo).join('-').toLowerCase().replace(/\s+/g, '-')}-${index}-${Date.now()}`
        const sku = `${formData.base_sku}-${Object.values(currentCombo).map(v => v.substring(0, 2).toUpperCase()).join('')}`
        
        combinations.push({
          id: comboId,
          attributes: { ...currentCombo },
          sku,
          enabled: true
        })
        return
      }

      const attr = attrs[index]
      for (const value of attr.values) {
        generateCombos(index + 1, { ...currentCombo, [attr.name]: value })
      }
    }

    generateCombos(0, {})
    setVariantCombinations(combinations)

    // Notify parent component
    if (onVariantsGenerated) {
      onVariantsGenerated(combinations)
    }
  }

  // Generate variants when attributes change
  React.useEffect(() => {
    generateVariantCombinations()
  }, [attributes, formData.base_sku])

  // Update form data with attributes
  React.useEffect(() => {
    updateFormData('variant_attributes', attributes.map(attr => ({
      name: attr.name,
      values: attr.values
    })))
  }, [attributes, updateFormData])

  return (
    <div className="space-y-6">
      {/* Attribute Definition */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm">Product Attributes</CardTitle>
          <p className="text-xs text-gray-600">Define the attributes that will create your product variants</p>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Add New Attribute */}
          <div className="flex gap-2">
            <div className="flex-1">
              <SimpleDropdown
                options={userAttributes}
                value={newAttributeName}
                onValueChange={setNewAttributeName}
                onAddCustomOption={(option) => {
                  setNewAttributeName(option.value);
                }}
                placeholder="Select or add attribute name..."
                allowCustom={true}
                customPlaceholder="Add custom attribute..."
                className="h-8 text-xs"
              />
            </div>
            <Button
              type="button"
              onClick={addAttribute}
              disabled={!newAttributeName.trim() || loadingAttributes}
              className="h-8 px-3 text-xs"
            >
              <Plus className="h-3 w-3 mr-1" />
              Add Attribute
            </Button>
          </div>

          {/* Existing Attributes */}
          {attributes.map((attribute) => (
            <div key={attribute.id} className="border rounded-md p-3 space-y-3">
              <div className="flex items-center justify-between">
                <h4 className="font-medium text-sm">{attribute.name}</h4>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeAttribute(attribute.id)}
                  className="h-6 w-6 p-0 text-red-600 hover:text-red-700"
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>

              {/* Attribute Values */}
              <div className="space-y-2">
                <div className="flex flex-wrap gap-1">
                  {attribute.values.map((value, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {value}
                      <button
                        onClick={() => removeValueFromAttribute(attribute.id, index)}
                        className="ml-1 hover:text-red-600"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>

                {/* Add Value Input */}
                {editingAttribute === attribute.id && (
                  <div className="flex gap-2">
                    <div className="flex-1">
                      <SimpleDropdown
                        options={attribute.attributeId ? (attributeValuesCache[attribute.attributeId] || []) : []}
                        value={newAttributeValue}
                        onValueChange={async (value) => {
                          setNewAttributeValue(value);
                          // If this is a new value not in the options, automatically add it
                          const currentOptions = attribute.attributeId ? (attributeValuesCache[attribute.attributeId] || []) : [];
                          if (value && !currentOptions.some(option => option.value === value)) {
                            // Automatically add the value when a new value is entered
                            await addValueToAttribute(attribute.id, value);
                            setNewAttributeValue('');
                          }
                        }}
                        onAddCustomOption={async (option) => {
                          setNewAttributeValue(option.value);
                          // Automatically add the value when custom option is added
                          await addValueToAttribute(attribute.id, option.value);
                          setNewAttributeValue('');
                        }}
                        placeholder={`Select or add ${attribute.name.toLowerCase()} value...`}
                        allowCustom={true}
                        customPlaceholder={`Add ${attribute.name.toLowerCase()} value...`}
                        className="h-7 text-xs"
                      />
                    </div>
                    <Button
                      type="button"
                      size="sm"
                      onClick={() => {
                        addValueToAttribute(attribute.id, newAttributeValue)
                        setNewAttributeValue('')
                      }}
                      disabled={!newAttributeValue.trim()}
                      className="h-7 px-2 text-xs"
                    >
                      Add
                    </Button>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setEditingAttribute(null)
                        setNewAttributeValue('')
                      }}
                      className="h-7 px-2 text-xs"
                    >
                      Done
                    </Button>
                  </div>
                )}

                {editingAttribute !== attribute.id && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setEditingAttribute(attribute.id)}
                    className="h-7 px-2 text-xs"
                  >
                    <Plus className="h-3 w-3 mr-1" />
                    Add Values
                  </Button>
                )}
              </div>
            </div>
          ))}

          {attributes.length === 0 && (
            <div className="text-center py-8 bg-gray-50 border border-gray-200 rounded-md">
              <div className="text-gray-500 mb-2">
                <svg className="w-8 h-8 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="font-medium text-gray-900 text-sm mb-1">No Attributes Defined</h3>
              <p className="text-xs text-gray-600">
                Add attributes like Size, Color, or Material to create product variants
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Variant Preview */}
      {variantCombinations.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Generated Variants ({variantCombinations.length})</CardTitle>
            <p className="text-xs text-gray-600">Preview of all possible variant combinations</p>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {variantCombinations.map((variant) => (
                <div key={variant.id} className="flex items-center justify-between p-2 bg-gray-50 rounded border">
                  <div className="flex items-center gap-2">
                    <div className="flex flex-wrap gap-1">
                      {Object.entries(variant.attributes).map(([attr, value]) => (
                        <Badge key={attr} variant="outline" className="text-xs">
                          {attr}: {value}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <div className="text-xs font-mono text-gray-600">{variant.sku}</div>
                </div>
              ))}
            </div>
            
            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded">
              <p className="text-xs text-blue-700">
                💡 These variants will be created when you save the product. You can configure individual pricing and inventory in the next step.
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}