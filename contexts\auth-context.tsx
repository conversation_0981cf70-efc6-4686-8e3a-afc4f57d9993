'use client'

import { createContext, useContext, useEffect, useState, useMemo, useCallback } from 'react'
import { User, AuthError } from '@supabase/supabase-js'
import { getSupabaseClient } from '@/lib/supabase'
import { useRouter } from 'next/navigation'
import { QueryClient } from '@tanstack/react-query'

interface AuthContextType {
  user: User | null
  userRole: string | null
  organizationId: string | null
  loading: boolean
  isOnline: boolean
  signIn: (email: string, password: string) => Promise<{ error: AuthError | null }>
  signUp: (email: string, password: string, metadata?: any) => Promise<{ error: AuthError | null }>
  signOut: () => Promise<void>
  resetPassword: (email: string) => Promise<{ error: AuthError | null }>
  updatePassword: (newPassword: string) => Promise<{ error: AuthError | null }>
  forceSessionAndDataRefresh: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export function AuthProvider({ children, queryClient }: { children: React.ReactNode, queryClient: QueryClient }) {
  const [user, setUser] = useState<User | null>(null)
  const [userRole, setUserRole] = useState<string | null>(null)
  const [organizationId, setOrganizationId] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)
  const [isOnline, setIsOnline] = useState(true)
  const router = useRouter()

  // Fetch user role and organization when user changes
  const fetchUserOrganizationData = useCallback(async (userId: string) => {
    try {
      const supabase = getSupabaseClient()
      
      // Get organization membership for the user
      const { data: memberData, error: memberError } = await supabase
        .from('organization_members')
        .select('organization_id, role')
        .eq('user_id', userId)
        .maybeSingle()
      
      if (!memberError && memberData) {
        setOrganizationId(memberData.organization_id)
        setUserRole(memberData.role)
        return
      }
      
      // If no organization membership found, check if user is an owner
      const { data: ownerData, error: ownerError } = await supabase
        .from('organizations')
        .select('id')
        .eq('owner_id', userId)
        .maybeSingle()
      
      if (!ownerError && ownerData) {
        setOrganizationId(ownerData.id)
        setUserRole('owner')
        return
      }
      
      // Default to null if no organization data found
      setOrganizationId(null)
      setUserRole('staff') // Default to staff if no role found
    } catch (error) {
      console.error('Error fetching user organization data:', error)
      setOrganizationId(null)
      setUserRole('staff') // Default to staff if error
    }
  }, [])

  // Handle auth state changes with proper session recovery
  const handleAuthStateChange = useCallback(async (event: string, session: any) => {
    console.log('Auth state change:', event, session?.user?.id)
    
    const newUser = session?.user ?? null
    setUser(newUser)
    
    // Fetch user organization data if user is authenticated
    if (newUser) {
      await fetchUserOrganizationData(newUser.id)
    } else {
      setOrganizationId(null)
      setUserRole(null)
    }
    
    setLoading(false)
    
    // Handle navigation - only redirect if needed
    if (event === 'SIGNED_IN' && newUser) {
      const currentPath = typeof window !== 'undefined' ? window.location.pathname : ''
      // If user has no organization, redirect to create organization page
      if (!currentPath.includes('/auth/create-organization')) {
        // Check if user has an organization
        const supabase = getSupabaseClient()
        const { data: memberData } = await supabase
          .from('organization_members')
          .select('organization_id')
          .eq('user_id', newUser.id)
          .maybeSingle()
        
        const { data: ownerData } = await supabase
          .from('organizations')
          .select('id')
          .eq('owner_id', newUser.id)
          .maybeSingle()
        
        if (!memberData && !ownerData) {
          router.push('/auth/create-organization')
          return
        }
      }
      
      if (!currentPath.startsWith('/dashboard') && !currentPath.includes('/auth/')) {
        router.push('/dashboard')
      }
    } else if (event === 'SIGNED_OUT' || (!newUser && !loading)) {
      const currentPath = typeof window !== 'undefined' ? window.location.pathname : ''
      if (currentPath.startsWith('/dashboard')) {
        router.push('/auth/signin')
      }
    }
    
    // Always invalidate queries on auth state change to ensure fresh data
    if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
      console.log('Invalidating queries due to auth state change:', event)
      try {
        await queryClient.invalidateQueries()
        console.log('Queries invalidated successfully')
      } catch (error) {
        console.error('Error invalidating queries:', error)
      }
    }
  }, [router, queryClient, loading, fetchUserOrganizationData])

  // Handle tab visibility change with proper session refresh
  const handleVisibilityChange = useCallback(async () => {
    if (document.visibilityState === 'visible') {
      console.log('Tab became visible - checking session')
      
      try {
        const supabase = getSupabaseClient()
        
        // Force session refresh to ensure we have the latest state
        const { data, error } = await supabase.auth.refreshSession()
        
        if (error) {
          console.error('Error refreshing session on tab focus:', error)
          // If refresh fails, get current session
          const { data: sessionData } = await supabase.auth.getSession()
          const newUser = sessionData?.session?.user ?? null
          setUser(newUser)
          
          // Fetch user organization data if user is authenticated
          if (newUser) {
            await fetchUserOrganizationData(newUser.id)
          } else {
            setOrganizationId(null)
            setUserRole(null)
          }
        } else {
          console.log('Session refreshed successfully on tab focus')
          const newUser = data?.session?.user ?? null
          setUser(newUser)
          
          // Fetch user organization data if user is authenticated
          if (newUser) {
            await fetchUserOrganizationData(newUser.id)
          } else {
            setOrganizationId(null)
            setUserRole(null)
          }
        }
        
        // Always invalidate queries when tab becomes visible to ensure fresh data
        console.log('Invalidating queries due to tab visibility change')
        await queryClient.invalidateQueries()
        
      } catch (error) {
        console.error('Error handling visibility change:', error)
      }
    }
  }, [queryClient, fetchUserOrganizationData])

  // Network status handling
  useEffect(() => {
    const handleOnline = () => {
      console.log('Network back online')
      setIsOnline(true)
      // Refresh session and invalidate queries when coming back online
      handleVisibilityChange()
    }
    
    const handleOffline = () => {
      console.log('Network went offline')
      setIsOnline(false)
    }

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)
    
    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [handleVisibilityChange])

  // Main auth effect - simplified and more reliable
  useEffect(() => {
    const supabase = getSupabaseClient()
    let mounted = true

    // Get initial session
    const initializeAuth = async () => {
      try {
        console.log('Initializing auth...')
        const { data: { session }, error } = await supabase.auth.getSession()
        
        if (error) {
          console.error('Error getting initial session:', error)
        }
        
        if (mounted) {
          const newUser = session?.user ?? null
          setUser(newUser)
          
          // Fetch user organization data if user is authenticated
          if (newUser) {
            await fetchUserOrganizationData(newUser.id)
          } else {
            setOrganizationId(null)
            setUserRole(null)
          }
          
          console.log('Auth initialized, user:', newUser?.id)
          setLoading(false)
        }
      } catch (error) {
        console.error('Error initializing auth:', error)
        if (mounted) {
          setUser(null)
          setOrganizationId(null)
          setUserRole(null)
          setLoading(false)
        }
      }
    }

    initializeAuth()

    // Set up auth state change listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      console.log('Auth state changed:', event, session?.user?.id)
      if (mounted) {
        handleAuthStateChange(event, session)
      }
    })

    // Set up visibility change listener
    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      mounted = false
      subscription.unsubscribe()
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [handleAuthStateChange, handleVisibilityChange, fetchUserOrganizationData])

  const signIn = async (email: string, password: string) => {
    try {
      const supabase = getSupabaseClient()
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })
      return { error }
    } catch (error) {
      console.error('Sign in error:', error)
      return { error: error as AuthError }
    }
  }

  const signUp = async (email: string, password: string, metadata?: any) => {
    try {
      const supabase = getSupabaseClient()
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: metadata,
        },
      })
      return { error }
    } catch (error) {
      console.error('Sign up error:', error)
      return { error: error as AuthError }
    }
  }

  const signOut = async () => {
    try {
      const supabase = getSupabaseClient()
      // Clear React Query cache before signing out
      queryClient.clear()
      await supabase.auth.signOut()
      setOrganizationId(null)
      setUserRole(null)
    } catch (error) {
      console.error('Sign out error:', error)
    }
  }

  const resetPassword = async (email: string) => {
    try {
      const supabase = getSupabaseClient()
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`,
      })
      return { error }
    } catch (error) {
      console.error('Reset password error:', error)
      return { error: error as AuthError }
    }
  }

  const updatePassword = async (newPassword: string) => {
    try {
      const supabase = getSupabaseClient()
      const { error } = await supabase.auth.updateUser({
        password: newPassword,
      })
      return { error }
    } catch (error) {
      console.error('Update password error:', error)
      return { error: error as AuthError }
    }
  }

  const forceSessionAndDataRefresh = async () => {
    try {
      console.log('Force session and data refresh initiated')
      
      const supabase = getSupabaseClient()
      const { data, error } = await supabase.auth.refreshSession()
      
      if (error) {
        console.error('Error refreshing session:', error)
        // Even if refresh fails, try to get current session
        const { data: sessionData } = await supabase.auth.getSession()
        const newUser = sessionData?.session?.user ?? null
        setUser(newUser)
        
        // Fetch user organization data if user is authenticated
        if (newUser) {
          await fetchUserOrganizationData(newUser.id)
        } else {
          setOrganizationId(null)
          setUserRole(null)
        }
      } else {
        console.log('Session refreshed successfully')
        const newUser = data?.session?.user ?? null
        setUser(newUser)
        
        // Fetch user organization data if user is authenticated
        if (newUser) {
          await fetchUserOrganizationData(newUser.id)
        } else {
          setOrganizationId(null)
          setUserRole(null)
        }
      }
      
      // Always invalidate all queries to force fresh data
      console.log('Invalidating all queries...')
      await queryClient.invalidateQueries()
      console.log('All queries invalidated successfully')
      
    } catch (error) {
      console.error('Error in forceSessionAndDataRefresh:', error)
    }
  }

  // Memoize context value
  const value = useMemo(() => ({
    user,
    userRole,
    organizationId,
    loading,
    isOnline,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updatePassword,
    forceSessionAndDataRefresh
  }), [user, userRole, organizationId, loading, isOnline])

  if (loading) {
    return <div className="flex items-center justify-center min-h-screen">Loading...</div>
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}