// Date formatting utilities based on user preferences

export type DateFormat = 'MM/dd/yyyy' | 'dd/MM/yyyy' | 'yyyy-MM-dd' | 'MMMM d, yyyy'
export type TimeFormat = '12-hour' | '24-hour'
export type FirstDayOfWeek = 'sunday' | 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday'

export interface DateFormatPreferences {
  dateFormat: DateFormat
  timeFormat: TimeFormat
  firstDayOfWeek: FirstDayOfWeek
}

// Default preferences
export const DEFAULT_DATE_FORMAT_PREFERENCES: DateFormatPreferences = {
  dateFormat: 'MM/dd/yyyy',
  timeFormat: '12-hour',
  firstDayOfWeek: 'sunday'
}

// Format a date according to user preferences
export function formatUserDate(date: Date, preferences: DateFormatPreferences): string {
  const { dateFormat } = preferences
  
  switch (dateFormat) {
    case 'MM/dd/yyyy':
      return `${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getDate().toString().padStart(2, '0')}/${date.getFullYear()}`
    case 'dd/MM/yyyy':
      return `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()}`
    case 'yyyy-MM-dd':
      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
    case 'MMMM d, yyyy':
      const months = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
      ]
      return `${months[date.getMonth()]} ${date.getDate()}, ${date.getFullYear()}`
    default:
      return date.toLocaleDateString()
  }
}

// Format time according to user preferences
export function formatUserTime(date: Date, preferences: DateFormatPreferences): string {
  const { timeFormat } = preferences
  
  if (timeFormat === '12-hour') {
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    })
  } else {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    })
  }
}

// Format both date and time
export function formatUserDateTime(date: Date, preferences: DateFormatPreferences): string {
  return `${formatUserDate(date, preferences)} at ${formatUserTime(date, preferences)}`
}

// Get the first day of the week as a number (0 = Sunday, 1 = Monday, etc.)
export function getFirstDayOfWeekNumber(preferences: DateFormatPreferences): number {
  const { firstDayOfWeek } = preferences
  
  switch (firstDayOfWeek) {
    case 'sunday': return 0
    case 'monday': return 1
    case 'tuesday': return 2
    case 'wednesday': return 3
    case 'thursday': return 4
    case 'friday': return 5
    case 'saturday': return 6
    default: return 0
  }
}