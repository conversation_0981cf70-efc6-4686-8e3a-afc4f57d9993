import { 
  createSupabaseClient,
  getTableColumns, 
  verifyColumnExists, 
  listAllTables, 
  getTableInfo, 
  findProductTables, 
  forceRefreshSchema 
} from './supabase'

/**
 * Database Management Utilities
 * 
 * These functions allow you to manage your Supabase database directly
 * from your Next.js application. Use with caution in production!
 */

interface TableColumn {
  name: string
  type: string
  nullable?: boolean
  defaultValue?: string
  isPrimaryKey?: boolean
  isUnique?: boolean
  references?: {
    table: string
    column: string
    onDelete?: 'CASCADE' | 'SET NULL' | 'RESTRICT'
  }
}

interface CreateTableOptions {
  tableName: string
  columns: TableColumn[]
  enableRLS?: boolean
  policies?: string[]
}

/**
 * Create a new table with columns and constraints
 */
export async function createTable(options: CreateTableOptions) {
  // This function still needs direct Supabase access for DDL operations
  // Keeping the existing implementation
  const supabase = createSupabaseClient()
  
  try {
    // Build the CREATE TABLE SQL
    let sql = `CREATE TABLE IF NOT EXISTS ${options.tableName} (\n`
    
    const columnDefinitions = options.columns.map(col => {
      let def = `  ${col.name} ${col.type}`
      
      if (col.isPrimaryKey) def += ' PRIMARY KEY'
      if (!col.nullable && !col.isPrimaryKey) def += ' NOT NULL'
      if (col.defaultValue) def += ` DEFAULT ${col.defaultValue}`
      if (col.isUnique && !col.isPrimaryKey) def += ' UNIQUE'
      if (col.references) {
        def += ` REFERENCES ${col.references.table}(${col.references.column})`
        if (col.references.onDelete) {
          def += ` ON DELETE ${col.references.onDelete}`
        }
      }
      
      return def
    })
    
    sql += columnDefinitions.join(',\n')
    sql += '\n);'
    
    // Execute the CREATE TABLE statement
    const { error } = await supabase.rpc('exec_sql', { sql })
    
    if (error) {
      console.error('Error creating table:', error)
      return { success: false, error }
    }
    
    // Enable RLS if requested
    if (options.enableRLS) {
      const rlsResult = await enableRLS(options.tableName)
      if (!rlsResult.success) {
        return { success: false, error: rlsResult.error }
      }
    }
    
    // Create policies if provided
    if (options.policies && options.policies.length > 0) {
      for (const policy of options.policies) {
        const policyResult = await createPolicy(options.tableName, policy)
        if (!policyResult.success) {
          console.warn(`Failed to create policy for ${options.tableName}:`, policyResult.error)
        }
      }
    }
    
    return { success: true }
    
  } catch (error) {
    console.error('Error in createTable:', error)
    return { success: false, error }
  }
}

/**
 * Add a new column to an existing table
 */
export async function addColumn(tableName: string, column: TableColumn) {
  // This function still needs direct Supabase access for DDL operations
  // Keeping the existing implementation
  const supabase = createSupabaseClient()
  
  try {
    let sql = `ALTER TABLE ${tableName} ADD COLUMN IF NOT EXISTS ${column.name} ${column.type}`
    
    if (!column.nullable) sql += ' NOT NULL'
    if (column.defaultValue) sql += ` DEFAULT ${column.defaultValue}`
    if (column.isUnique) sql += ' UNIQUE'
    if (column.references) {
      sql += ` REFERENCES ${column.references.table}(${column.references.column})`
      if (column.references.onDelete) {
        sql += ` ON DELETE ${column.references.onDelete}`
      }
    }
    
    sql += ';'
    
    const { error } = await supabase.rpc('exec_sql', { sql })
    
    if (error) {
      console.error('Error adding column:', error)
      return { success: false, error }
    }
    
    return { success: true }
    
  } catch (error) {
    console.error('Error in addColumn:', error)
    return { success: false, error }
  }
}

/**
 * Enable Row Level Security on a table
 */
export async function enableRLS(tableName: string) {
  // This function still needs direct Supabase access for DDL operations
  // Keeping the existing implementation
  const supabase = createSupabaseClient()
  
  try {
    const { error } = await supabase.rpc('exec_sql', {
      sql: `ALTER TABLE ${tableName} ENABLE ROW LEVEL SECURITY;`
    })
    
    if (error) {
      console.error('Error enabling RLS:', error)
      return { success: false, error }
    }
    
    return { success: true }
    
  } catch (error) {
    console.error('Error in enableRLS:', error)
    return { success: false, error }
  }
}

/**
 * Create a Row Level Security policy
 */
export async function createPolicy(tableName: string, policySQL: string) {
  // This function still needs direct Supabase access for DDL operations
  // Keeping the existing implementation
  const supabase = createSupabaseClient()
  
  try {
    const { error } = await supabase.rpc('exec_sql', { sql: policySQL })
    
    if (error) {
      console.error('Error creating policy:', error)
      return { success: false, error }
    }
    
    return { success: true }
    
  } catch (error) {
    console.error('Error in createPolicy:', error)
    return { success: false, error }
  }
}

/**
 * Execute raw SQL (use with extreme caution!)
 */
export async function executeSQL(sql: string) {
  // This function still needs direct Supabase access for DDL operations
  // Keeping the existing implementation
  const supabase = createSupabaseClient()
  
  try {
    const { data, error } = await supabase.rpc('exec_sql', { sql })
    
    if (error) {
      console.error('Error executing SQL:', error)
      return { success: false, error }
    }
    
    return { success: true, data }
    
  } catch (error) {
    console.error('Error in executeSQL:', error)
    return { success: false, error }
  }
}

/**
 * Create a function in the database
 */
export async function createFunction(functionSQL: string) {
  return executeSQL(functionSQL)
}

/**
 * Create an index on a table
 */
export async function createIndex(tableName: string, columnName: string, indexName?: string) {
  const indexNameFinal = indexName || `idx_${tableName}_${columnName}`
  const sql = `CREATE INDEX IF NOT EXISTS ${indexNameFinal} ON ${tableName}(${columnName});`
  
  return executeSQL(sql)
}

/**
 * Get information about table columns
 */
export async function getTableColumnsWrapper(tableName: string) {
  try {
    const data = await getTableColumns(tableName)
    return { success: true, data }
  } catch (error) {
    console.error('Error getting table columns:', error)
    return { success: false, error }
  }
}

/**
 * Verify if a specific column exists in a table
 */
export async function verifyColumnExistsWrapper(tableName: string, columnName: string) {
  try {
    const result = await verifyColumnExists(tableName, columnName)
    return result
  } catch (error) {
    console.error('Error in verifyColumnExists:', error)
    return { success: false, error }
  }
}

/**
 * List all tables in the public schema
 */
export async function listAllTablesWrapper() {
  try {
    const data = await listAllTables()
    return { success: true, data }
  } catch (error) {
    console.error('Error listing tables:', error)
    return { success: false, error }
  }
}

/**
 * Get detailed information about a specific table
 */
export async function getTableInfoWrapper(tableName: string) {
  try {
    const result = await getTableInfo(tableName)
    return { success: true, data: result.data, tableName: result.tableName }
  } catch (error) {
    console.error(`Error getting info for table ${tableName}:`, error)
    return { success: false, error }
  }
}

/**
 * Find all tables with 'product' in their name
 */
export async function findProductTablesWrapper() {
  try {
    const data = await findProductTables()
    return { success: true, data }
  } catch (error) {
    console.error('Error finding product tables:', error)
    return { success: false, error }
  }
}

/**
 * Force refresh the Supabase schema cache
 */
export async function forceRefreshSchemaWrapper() {
  try {
    const result = await forceRefreshSchema()
    return result
  } catch (error) {
    console.error('Error in forceRefreshSchema:', error)
    return { success: false, error }
  }
}

// Add a more robust verifyColumnExists function
export async function verifyColumnExistsRobust(tableName: string, columnName: string) {
  try {
    console.log(`Verifying column ${columnName} exists in table ${tableName}...`)
    
    // First, try to query the column directly
    try {
      const result = await verifyColumnExists(tableName, columnName)
      
      if (!result.success) {
        throw result.error
      }
      
      if (!result.exists) {
        // If we get a schema cache error, try to refresh and retry
        console.log(`Schema cache issue detected for ${tableName}.${columnName}, attempting refresh...`)
        await forceRefreshSchemaWrapper()
        
        // Retry the query after refresh
        const retryResult = await verifyColumnExists(tableName, columnName)
        
        if (!retryResult.success) {
          console.error(`Retry failed for ${tableName}.${columnName}:`, retryResult.error)
          return { success: true, exists: false, error: retryResult.error }
        } else {
          console.log(`Retry successful for ${tableName}.${columnName}`)
          return { success: true, exists: true, data: retryResult.data }
        }
      } else {
        console.log(`Column ${columnName} exists in table ${tableName}`)
        return { success: true, exists: true, data: result.data }
      }
    } catch (directError) {
      console.error(`Direct query error for ${tableName}.${columnName}:`, directError)
      throw directError
    }
  } catch (error) {
    console.error('Error in verifyColumnExistsRobust:', error)
    return { success: false, error }
  }
}

// Example usage functions for common operations
export const examples = {
  /**
   * Example: Create a simple blog posts table
   */
  createBlogTable: () => createTable({
    tableName: 'blog_posts',
    columns: [
      { name: 'id', type: 'uuid', isPrimaryKey: true, defaultValue: 'gen_random_uuid()' },
      { name: 'user_id', type: 'uuid', nullable: false, references: { table: 'auth.users', column: 'id', onDelete: 'CASCADE' } },
      { name: 'title', type: 'text', nullable: false },
      { name: 'content', type: 'text' },
      { name: 'published', type: 'boolean', defaultValue: 'false' },
      { name: 'created_at', type: 'timestamp with time zone', defaultValue: "timezone('utc'::text, now())" },
      { name: 'updated_at', type: 'timestamp with time zone', defaultValue: "timezone('utc'::text, now())" }
    ],
    enableRLS: true,
    policies: [
      `CREATE POLICY "Users can view own posts" ON blog_posts FOR SELECT USING (auth.uid() = user_id);`,
      `CREATE POLICY "Users can create own posts" ON blog_posts FOR INSERT WITH CHECK (auth.uid() = user_id);`,
      `CREATE POLICY "Users can update own posts" ON blog_posts FOR UPDATE USING (auth.uid() = user_id);`,
      `CREATE POLICY "Users can delete own posts" ON blog_posts FOR DELETE USING (auth.uid() = user_id);`
    ]
  }),
  
  /**
   * Example: Add a new column to products table
   */
  addDiscountColumn: () => addColumn('products', {
    name: 'discount_percentage',
    type: 'decimal(5,2)',
    defaultValue: '0.00'
  }),
  
  /**
   * Example: Create an index for better performance
   */
  createProductsIndex: () => createIndex('products', 'user_id'),
}