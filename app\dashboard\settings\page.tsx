'use client'

import React, { useState } from 'react'
import { ProfileSettings } from '@/components/settings/profile-settings'
import { OrganizationSettings } from '@/components/settings/organization-settings'
import { BillingSettings } from '@/components/settings/billing-settings'
import { SecuritySettings } from '@/components/settings/security-settings'
import { NotificationSettings } from '@/components/settings/notification-settings'
import { ThemeSettings } from '@/components/settings/theme-settings'
import { TeamSettings } from '@/components/settings/team-settings'
import IntegrationsSettings from '@/components/settings/IntegrationsSettings'
import { useAuth } from '@/contexts/auth-context'
import { useCurrency } from '@/lib/currency'
import { getSupabaseClient } from '@/lib/supabase'
import { useTheme } from '@/contexts/theme-context'
import { 
  User, 
  Building, 
  CreditCard, 
  Bell,
  Palette,
  Shield,
  Users,
  Plug
} from 'lucide-react'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import { PermissionGuard } from '@/components/auth/PermissionGuard'
import { PageHeader } from '@/components/ui/page-header'

interface SettingsSection {
  id: string
  title: string
  icon: React.ReactNode
  component: React.ReactNode
}

export default function SettingsPage() {
  const { user } = useAuth()
  const { userCurrency } = useCurrency()
  const { theme, setTheme } = useTheme()
  const supabase = getSupabaseClient()
  
  const [activeSection, setActiveSection] = useState('profile')
  
  // Form states
  const [businessName, setBusinessName] = useState('')
  const [avatarUrl, setAvatarUrl] = useState<string | null>(null)
  const [firstName, setFirstName] = useState('')
  const [lastName, setLastName] = useState('')
  const [displayName, setDisplayName] = useState('')
  const [jobTitle, setJobTitle] = useState('')
  const [profileLoaded, setProfileLoaded] = useState(false)
  
  const settingsSections: SettingsSection[] = [
    {
      id: 'profile',
      title: 'Profile',
      icon: <User className="h-4 w-4" />,
      component: (
        <ProfileSettings
          initialAvatarUrl={avatarUrl}
          initialFirstName={firstName}
          initialLastName={lastName}
          initialDisplayName={displayName}
          initialJobTitle={jobTitle}
          initialBusinessName={businessName}
          onAvatarUpdate={setAvatarUrl}
          onPersonalInfoUpdate={(info) => {
            setFirstName(info.firstName)
            setLastName(info.lastName)
            setDisplayName(info.displayName)
            setJobTitle(info.jobTitle)
          }}
        />
      )
    },
    {
      id: 'team',
      title: 'Team',
      icon: <Users className="h-4 w-4" />,
      component: <TeamSettings />
    },
    {
      id: 'security',
      title: 'Security',
      icon: <Shield className="h-4 w-4" />,
      component: <SecuritySettings />
    },
    {
      id: 'organization',
      title: 'Organization',
      icon: <Building className="h-4 w-4" />,
      component: (
        <PermissionGuard allowedRoles={['owner']}>
          <OrganizationSettings />
        </PermissionGuard>
      )
    },
    {
      id: 'integrations',
      title: 'Integrations',
      icon: <Plug className="h-4 w-4" />,
      component: <IntegrationsSettings />
    },
    {
      id: 'billing',
      title: 'Billing',
      icon: <CreditCard className="h-4 w-4" />,
      component: <BillingSettings />
    },
    {
      id: 'notifications',
      title: 'Notifications',
      icon: <Bell className="h-4 w-4" />,
      component: <NotificationSettings />
    },
    {
      id: 'appearance',
      title: 'Appearance',
      icon: <Palette className="h-4 w-4" />,
      component: <ThemeSettings 
        onThemeChange={setTheme} 
        currentTheme={theme} 
      />
    }
  ]

  const activeSectionData = settingsSections.find(section => section.id === activeSection) || settingsSections[0]

  return (
    <div className="space-y-6">
      <PageHeader 
        title="Settings"
        description="Manage your account settings and preferences"
      />

      <div className="flex flex-col lg:flex-row gap-6">
        {/* Left Column - Navigation */}
        <div className="lg:w-1/4">
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
            <div className="p-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Settings</h2>
            </div>
            <nav className="p-2">
              <ul className="space-y-1">
                {settingsSections.map((section) => (
                  <li key={section.id}>
                    <button
                      onClick={() => setActiveSection(section.id)}
                      className={cn(
                        "w-full flex items-center gap-3 rounded-md px-3 py-2 text-sm transition-colors",
                        activeSection === section.id
                          ? "bg-slate-100 text-gray-900 font-medium"
                          : "text-gray-700 hover:bg-gray-50 hover:text-gray-900"
                      )}
                    >
                      {section.icon}
                      {section.title}
                    </button>
                  </li>
                ))}
              </ul>
            </nav>
          </div>
        </div>

        {/* Right Column - Content */}
        <div className="lg:w-3/4">
          <Card>
            <CardHeader>
              <h2 className="text-lg font-medium text-gray-900">
                {activeSectionData.title}
              </h2>
            </CardHeader>
            <CardContent>
              {activeSectionData.component}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}