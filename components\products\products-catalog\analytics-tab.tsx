'use client'

import { useState, useEffect, useMemo, useCallback } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { useToast } from '@/components/ui/use-toast'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { MetricCard } from '@/components/ui/metric-card'
import { PageHeader } from '@/components/ui/page-header'
import {
  createSupabaseClient,
  type ProductRow,
  type ProductVariantRow,
} from '@/lib/supabase'
import { useAllProducts, type ExtendedProductRow } from '@/hooks/use-products'
import { useAuth } from '@/contexts/auth-context'
import { useCurrency } from '@/lib/currency'
import { cn } from '@/lib/utils'
import { type ProductFilters } from '@/components/products/products-catalog/product-filters'
import {
  Calendar,
  Download,
  ChevronDown,
  FileSpreadsheet,
  FileText,
} from 'lucide-react'
import { BestSellingItems } from './sales-performance'

// Chart.js imports
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
  ArcElement,
  BarElement,
} from 'chart.js'
import { Line, Doughnut, Bar } from 'react-chartjs-2'

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
  ArcElement,
  BarElement
)

// Local copy of Skeleton component to avoid import issues
function Skeleton({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn('animate-pulse rounded-md bg-muted', className)}
      {...props}
    />
  )
}

interface ProductWithVariants extends ProductRow {
  variants?: ProductVariantRow[]
  category_name?: string | null
}

interface AnalyticsTabProps {
  isMobile?: boolean
  className?: string
  contextualFilters?: any // ProductFilters type
  filteredProductIds?: string[]
}

export function AnalyticsTab({
  filteredProductIds = [],
  className,
}: AnalyticsTabProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [isExporting, setIsExporting] = useState(false)
  const [dateRange, setDateRange] = useState({
    from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
    to: new Date(),
  })
  const [categoryChartMode, setCategoryChartMode] = useState<
    'revenue' | 'profit'
  >('revenue')
  const [topProductsCount, setTopProductsCount] = useState<number | 'all'>(5)

  const { toast } = useToast()
  const { user, organizationId } = useAuth()
  const { userCurrency, formatCurrency } = useCurrency()

  // Use React Query to fetch products
  const {
    data: productsData = [],
    isLoading: isLoadingProducts,
    error: productError,
  } = useAllProducts(organizationId || undefined)
  const products = productsData as ExtendedProductRow[]

  // Listen for export events
  useEffect(() => {
    const handleExportCSV = () => {
      exportToCSV()
    }

    window.addEventListener('exportAnalyticsCSV', handleExportCSV)
    return () => {
      window.removeEventListener('exportAnalyticsCSV', handleExportCSV)
    }
  }, [])

  // Process analytics data
  const analyticsData = useMemo(() => {
    // For now, we'll create simplified analytics data
    // In a real implementation, this would be more complex with time-based data

    // Category breakdown
    const categoryMap = new Map<
      string,
      { count: number; value: number; color: string }
    >()
    let totalInventoryValue = 0
    let totalProducts = products.length
    let lowStockProducts = 0
    let outOfStockProducts = 0

    // Margin analysis data
    let totalMargin = 0
    let totalCost = 0
    let totalRevenue = 0
    const topMarginProducts = [] as {
      name: string
      margin: number
      value: number
    }[]
    const lowMarginProducts = [] as {
      name: string
      margin: number
      value: number
    }[]

    products.forEach((product, index) => {
      const categoryName = (product as any).category_name || 'Uncategorized'
      let stockQty, unitValue, totalValue, cost, revenue, profit, margin

      // Handle variable products differently from simple products
      if (
        product.has_variants &&
        product.variants &&
        product.variants.length > 0
      ) {
        // For variable products, calculate totals from all variants
        stockQty = product.variants.reduce(
          (sum, variant) => sum + (variant.stock_quantity || 0),
          0
        )

        // Calculate total inventory value from variants
        totalValue = product.variants.reduce((sum, variant) => {
          const variantPrice = variant.effective_price || variant.price || 0
          const variantQuantity = variant.stock_quantity || 0
          return sum + variantPrice * variantQuantity
        }, 0)

        // Calculate weighted average cost from variants - using cost_adjustment for variants
        // For variants, the total cost is base_cost + packaging_cost + cost_adjustment
        let totalVariantCost = 0
        let totalVariantRevenue = 0

        product.variants.forEach((variant) => {
          const variantQuantity = variant.stock_quantity || 0
          const variantPrice = variant.effective_price || variant.price || 0
          const variantCost =
            (product.base_cost || 0) +
            (product.packaging_cost || 0) +
            (variant.cost_adjustment || 0)

          totalVariantCost += variantCost * variantQuantity
          totalVariantRevenue += variantPrice * variantQuantity
        })

        cost = totalVariantCost
        revenue = totalVariantRevenue
        profit = revenue - cost
        // Calculate margin as percentage
        margin = cost > 0 ? (profit / cost) * 100 : 0

        // Check stock status for variants
        const hasOutOfStock = product.variants.some(
          (variant) => (variant.stock_quantity || 0) === 0
        )
        const hasLowStock = product.variants.some((variant) => {
          const qty = variant.stock_quantity || 0
          const threshold =
            variant.low_stock_threshold || product.low_stock_threshold || 10
          return qty > 0 && qty <= threshold
        })

        if (hasOutOfStock) {
          outOfStockProducts++
        } else if (hasLowStock) {
          lowStockProducts++
        }
      } else {
        // For simple products, use existing logic
        stockQty = product.stock_quantity || 0
        const lowStockThreshold = product.low_stock_threshold || 10
        unitValue = product.effective_price || product.price || 0
        totalValue = unitValue * stockQty

        if (stockQty === 0) outOfStockProducts++
        else if (stockQty <= lowStockThreshold) lowStockProducts++

        // Margin calculations for simple products
        cost =
          ((product.base_cost || 0) + (product.packaging_cost || 0)) * stockQty
        revenue = unitValue * stockQty
        profit = revenue - cost
        margin = cost > 0 ? (profit / cost) * 100 : 0
      }

      totalInventoryValue += totalValue
      totalMargin += profit
      totalCost += cost
      totalRevenue += revenue

      // Top margin products
      if (margin > 0) {
        topMarginProducts.push({
          name: product.name,
          margin,
          value: totalValue,
        })
      }

      // Low margin products
      if (margin < 15) {
        lowMarginProducts.push({
          name: product.name,
          margin,
          value: totalValue,
        })
      }

      if (categoryMap.has(categoryName)) {
        const current = categoryMap.get(categoryName)!
        categoryMap.set(categoryName, {
          count: current.count + 1,
          value: current.value + totalValue,
          color: current.color,
        })
      } else {
        categoryMap.set(categoryName, {
          count: 1,
          value: totalValue,
          color: `hsl(${(index * 30) % 360}, 70%, 50%)`,
        })
      }
    })

    // Calculate average margin
    const averageMargin = totalCost > 0 ? (totalMargin / totalCost) * 100 : 0

    // Sort top and low margin products
    topMarginProducts.sort((a, b) => b.margin - a.margin)
    lowMarginProducts.sort((a, b) => a.margin - b.margin)

    // Margin distribution (simplified ranges)
    const marginDistribution = [
      {
        range: '0-10%',
        count: lowMarginProducts.filter((p) => p.margin >= 0 && p.margin < 10)
          .length,
        color: '#EF4444',
      },
      {
        range: '10-15%',
        count: lowMarginProducts.filter((p) => p.margin >= 10 && p.margin < 15)
          .length,
        color: '#F59E0B',
      },
      {
        range: '15-25%',
        count: topMarginProducts.filter((p) => p.margin >= 15 && p.margin < 25)
          .length,
        color: '#3B82F6',
      },
      {
        range: '25%+',
        count: topMarginProducts.filter((p) => p.margin >= 25).length,
        color: '#10B981',
      },
    ]

    // Convert to array and sort by value
    const categoryBreakdown = Array.from(categoryMap.entries())
      .map(([category, data]) => ({
        category,
        count: data.count,
        value: data.value,
        percentage:
          totalInventoryValue > 0
            ? (data.value / totalInventoryValue) * 100
            : 0,
        color: data.color,
      }))
      .sort((a, b) => b.value - a.value)

    // Top selling products (by stock value)
    const topProducts = [...products]
      .map((product) => {
        let value, stock, productMargin

        if (
          product.has_variants &&
          product.variants &&
          product.variants.length > 0
        ) {
          // For variable products, calculate total value from variants
          value = product.variants.reduce((sum, variant) => {
            const variantPrice = variant.effective_price || variant.price || 0
            const variantQuantity = variant.stock_quantity || 0
            return sum + variantPrice * variantQuantity
          }, 0)

          // Total stock across all variants
          stock = product.variants.reduce(
            (sum, variant) => sum + (variant.stock_quantity || 0),
            0
          )

          // Calculate weighted average margin for variable product
          let totalVariantCost = 0
          let totalVariantRevenue = 0

          product.variants.forEach((variant) => {
            const variantQuantity = variant.stock_quantity || 0
            const variantPrice = variant.effective_price || variant.price || 0
            const variantCost =
              (product.base_cost || 0) +
              (product.packaging_cost || 0) +
              (variant.cost_adjustment || 0)

            totalVariantCost += variantCost * variantQuantity
            totalVariantRevenue += variantPrice * variantQuantity
          })

          const totalCost = totalVariantCost
          const totalRevenue = totalVariantRevenue
          const profit = totalRevenue - totalCost
          productMargin = totalCost > 0 ? (profit / totalCost) * 100 : 0
        } else {
          // For simple products, use existing logic
          value =
            (product.effective_price || product.price || 0) *
            (product.stock_quantity || 0)
          stock = product.stock_quantity || 0
          // Use the calculated margin instead of the stored profit_margin
          const cost =
            ((product.base_cost || 0) + (product.packaging_cost || 0)) * stock
          const revenue = value
          const profit = revenue - cost
          productMargin = cost > 0 ? (profit / cost) * 100 : 0
        }

        return {
          name: product.name,
          sku: product.base_sku || 'N/A',
          value,
          stock,
          margin: productMargin,
        }
      })
      .sort((a, b) => b.value - a.value)
      .slice(0, 5)

    // Stock status distribution
    const stockStatusData = [
      {
        status: 'In Stock',
        count: totalProducts - lowStockProducts - outOfStockProducts,
        color: '#10B981',
      },
      { status: 'Low Stock', count: lowStockProducts, color: '#F59E0B' },
      { status: 'Out of Stock', count: outOfStockProducts, color: '#EF4444' },
    ]

    // Count variable vs simple products
    const variableProductsCount = products.filter((p) => p.has_variants).length
    const simpleProductsCount = products.length - variableProductsCount

    // Count total variants
    const totalVariants = products.reduce((sum, product) => {
      if (product.has_variants && product.variants) {
        return sum + product.variants.length
      }
      return sum
    }, 0)

    // Average variants per variable product
    const avgVariantsPerProduct =
      variableProductsCount > 0 ? totalVariants / variableProductsCount : 0

    return {
      totalProducts,
      totalInventoryValue,
      lowStockProducts,
      outOfStockProducts,
      categoryBreakdown,
      topProducts,
      stockStatusData,
      // Margin analysis data
      totalRevenue,
      totalMargin,
      totalCost,
      averageMargin,
      marginDistribution,
      topMarginProducts,
      lowMarginProducts,
      // Variant-specific data
      variableProductsCount,
      simpleProductsCount,
      totalVariants,
      avgVariantsPerProduct,
    }
  }, [products, userCurrency, formatCurrency])

  // Generate chart data for performance over time
  const performanceChartData = useMemo(() => {
    // Generate mock data for the last 12 months
    const months = []
    const revenueData = []
    const profitData = []

    for (let i = 11; i >= 0; i--) {
      const date = new Date()
      date.setMonth(date.getMonth() - i)
      months.push(
        date.toLocaleDateString('en-US', { month: 'short', year: '2-digit' })
      )

      // Generate mock revenue and profit data
      const baseRevenue =
        analyticsData.totalRevenue * (0.7 + Math.random() * 0.6)
      const baseProfit = analyticsData.totalMargin * (0.7 + Math.random() * 0.6)

      revenueData.push(Math.max(0, baseRevenue))
      profitData.push(Math.max(0, baseProfit))
    }

    return {
      labels: months,
      datasets: [
        {
          label: 'Revenue',
          data: revenueData,
          borderColor: 'rgb(59, 130, 246)',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          borderWidth: 2,
          fill: true,
          tension: 0.4,
        },
        {
          label: 'Profit',
          data: profitData,
          borderColor: 'rgb(16, 185, 129)',
          backgroundColor: 'transparent',
          borderWidth: 2,
          fill: false,
          tension: 0.4,
        },
      ],
    }
  }, [analyticsData.totalRevenue, analyticsData.totalMargin])

  // Margin distribution chart data
  const marginDistributionChartData = {
    labels: analyticsData.marginDistribution.map((item) => item.range),
    datasets: [
      {
        label: 'Product Count',
        data: analyticsData.marginDistribution.map((item) => item.count),
        backgroundColor: analyticsData.marginDistribution.map(
          (item) => item.color
        ),
        borderColor: analyticsData.marginDistribution.map(
          (item) => item.color
        ),
        borderWidth: 1,
      },
    ],
  }

  // Margin distribution chart options
  const marginDistributionChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          usePointStyle: true,
          padding: 20,
        },
      },
      tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        titleColor: '#1e293b',
        bodyColor: '#1e293b',
        borderColor: '#e2e8f0',
        borderWidth: 1,
        padding: 12,
        callbacks: {
          label: function (context: any) {
            return `${context.dataset.label}: ${context.raw} products`
          },
        },
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          color: '#94a3b8',
        },
      },
      y: {
        grid: {
          color: '#f1f5f9',
        },
        ticks: {
          color: '#94a3b8',
        },
      },
    },
  }

  // Chart options for performance chart
  const performanceChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          usePointStyle: true,
          padding: 20,
        },
      },
      tooltip: {
        mode: 'index' as const,
        intersect: false,
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        titleColor: '#1e293b',
        bodyColor: '#1e293b',
        borderColor: '#e2e8f0',
        borderWidth: 1,
        padding: 12,
        displayColors: true,
        callbacks: {
          label: function (context: any) {
            return `${context.dataset.label}: ${formatCurrency(context.parsed.y)}`
          },
        },
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          color: '#94a3b8',
        },
      },
      y: {
        grid: {
          color: '#f1f5f9',
        },
        ticks: {
          color: '#94a3b8',
          callback: function (value: any) {
            return `$${value >= 1000 ? (value / 1000).toFixed(0) + 'k' : value}`
          },
        },
      },
    },
    interaction: {
      mode: 'nearest' as const,
      axis: 'x' as const,
      intersect: false,
    },
  }

  // Stock status donut chart data
  const stockStatusChartData = {
    labels: analyticsData.stockStatusData.map((item) => item.status),
    datasets: [
      {
        data: analyticsData.stockStatusData.map((item) => item.count),
        backgroundColor: analyticsData.stockStatusData.map(
          (item) => item.color
        ),
        borderWidth: 0,
      },
    ],
  }

  // Stock status donut chart options
  const stockStatusChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom' as const,
        labels: {
          usePointStyle: true,
          padding: 20,
        },
      },
      tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        titleColor: '#1e293b',
        bodyColor: '#1e293b',
        borderColor: '#e2e8f0',
        borderWidth: 1,
        padding: 12,
        callbacks: {
          label: function (context: any) {
            return `${context.label}: ${context.raw} products`
          },
        },
      },
    },
    cutout: '70%',
  }

  // Calculate category profit data
  const categoryProfitData = useMemo(() => {
    // Calculate profit for each category
    const categoryProfitMap = new Map<string, number>()

    products.forEach((product) => {
      const categoryName = (product as any).category_name || 'Uncategorized'
      let profit = 0

      if (
        product.has_variants &&
        product.variants &&
        product.variants.length > 0
      ) {
        // For variable products, calculate profit from all variants
        let totalVariantCost = 0
        let totalVariantRevenue = 0

        product.variants.forEach((variant) => {
          const variantQuantity = variant.stock_quantity || 0
          const variantPrice = variant.effective_price || variant.price || 0
          const variantCost =
            (product.base_cost || 0) +
            (product.packaging_cost || 0) +
            (variant.cost_adjustment || 0)

          totalVariantCost += variantCost * variantQuantity
          totalVariantRevenue += variantPrice * variantQuantity
        })

        profit = totalVariantRevenue - totalVariantCost
      } else {
        // For simple products, calculate profit
        const stockQty = product.stock_quantity || 0
        const unitPrice = product.effective_price || product.price || 0
        const unitCost =
          (product.base_cost || 0) + (product.packaging_cost || 0)

        const revenue = unitPrice * stockQty
        const cost = unitCost * stockQty
        profit = revenue - cost
      }

      if (categoryProfitMap.has(categoryName)) {
        categoryProfitMap.set(
          categoryName,
          categoryProfitMap.get(categoryName)! + profit
        )
      } else {
        categoryProfitMap.set(categoryName, profit)
      }
    })

    // Convert to array and sort by value
    return Array.from(categoryProfitMap.entries())
      .map(([category, profit]) => ({
        category,
        profit,
      }))
      .sort((a, b) => b.profit - a.profit)
  }, [products])

  // Top categories bar chart data (based on selected mode)
  const topCategoriesChartData = useMemo(() => {
    if (categoryChartMode === 'revenue') {
      return {
        labels: analyticsData.categoryBreakdown
          .slice(0, 5)
          .map((item) => item.category),
        datasets: [
          {
            label: 'Category Value',
            data: analyticsData.categoryBreakdown
              .slice(0, 5)
              .map((item) => item.value),
            backgroundColor: '#64748b',
            borderRadius: 4,
          },
        ],
      }
    } else {
      return {
        labels: categoryProfitData.slice(0, 5).map((item) => item.category),
        datasets: [
          {
            label: 'Category Profit',
            data: categoryProfitData.slice(0, 5).map((item) => item.profit),
            backgroundColor: '#64748b',
            borderRadius: 4,
          },
        ],
      }
    }
  }, [analyticsData.categoryBreakdown, categoryProfitData, categoryChartMode])

  // Top categories bar chart options
  const topCategoriesChartOptions = {
    indexAxis: 'y' as const,
    responsive: true,
    maintainAspectRatio: false,
    layout: {
      padding: {
        top: 0,
        right: 0,
        bottom: 0,
        left: 0
      }
    },
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        titleColor: '#1e293b',
        bodyColor: '#1e293b',
        borderColor: '#e2e8f0',
        borderWidth: 1,
        padding: 6,
        callbacks: {
          label: function (context: any) {
            return `${formatCurrency(context.raw)}`
          },
        },
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          color: '#94a3b8',
          callback: function (value: any) {
            return `$${value >= 1000 ? (value / 1000).toFixed(0) + 'k' : value}`
          },
          padding: 0
        },
      },
      y: {
        grid: {
          display: false,
        },
        ticks: {
          color: '#94a3b8',
          padding: 0
        },
      },
    },
  }

  // Export functions
  const exportToCSV = useCallback(() => {
    setIsExporting(true)

    try {
      const currentDate = new Date().toISOString().split('T')[0]

      // Create comprehensive analytics CSV
      const lines = [
        '# Product Analytics Report',
        `# Generated on: ${new Date().toISOString()}`,
        '#',
        '# Summary Data:',
        'Metric,Value',
        `Inventory Value,${analyticsData.totalCost.toFixed(2)}`,
        `Potential Revenue,${analyticsData.totalRevenue.toFixed(2)}`,
        `Potential Profit,${analyticsData.totalMargin.toFixed(2)}`,
        `Average Margin,${analyticsData.averageMargin.toFixed(2)}%`,
        `Variable Products,${analyticsData.variableProductsCount}`,
        `Simple Products,${analyticsData.simpleProductsCount}`,
        `Total Variants,${analyticsData.totalVariants}`,
        `Avg. Variants per Variable Product,${analyticsData.avgVariantsPerProduct.toFixed(2)}`,
        '#',
        '# Category Breakdown:',
        'Category,Count,Value,Percentage',
        ...analyticsData.categoryBreakdown.map(
          (cat) =>
            `"${cat.category}",${cat.count},${cat.value.toFixed(2)},${cat.percentage.toFixed(2)}%`
        ),
        '#',
        '# Top Products:',
        'Product,Value,Stock,Margin %',
        ...analyticsData.topProducts.map(
          (prod) =>
            `"${prod.name}",${prod.value.toFixed(2)},${prod.stock},${prod.margin.toFixed(2)}%`
        ),
      ]

      const csvContent = lines.join('\n')
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `product-analytics-${currentDate}.csv`
      link.style.display = 'none'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      toast({
        title: 'Analytics Export Complete',
        description: 'Analytics report has been downloaded as CSV.',
      })
    } catch (error) {
      console.error('Analytics CSV export error:', error)
      toast({
        title: 'Export Failed',
        description: 'Failed to export analytics. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsExporting(false)
    }
  }, [analyticsData, toast])

  const exportToPDF = useCallback(() => {
    setIsExporting(true)

    try {
      // Dynamic import to avoid SSR issues
      import('jspdf').then(({ default: jsPDF }) => {
        import('jspdf-autotable').then(() => {
          try {
            const doc = new jsPDF()
            const currentDate = new Date().toLocaleDateString()

            // Set document properties
            doc.setProperties({
              title: `Product Analytics Report`,
              subject: 'Business Product Analytics',
              creator: 'ONKO Product Management',
            })

            // Add header
            doc.setFontSize(20)
            doc.text('Product Analytics Report', 20, 25)

            doc.setFontSize(12)
            doc.text(`Generated on: ${currentDate}`, 20, 35)

            // Summary section
            doc.setFontSize(14)
            doc.text('Summary', 20, 60)

            const summaryData = [
              ['Inventory Value', formatCurrency(analyticsData.totalCost)],
              ['Potential Revenue', formatCurrency(analyticsData.totalRevenue)],
              ['Potential Profit', formatCurrency(analyticsData.totalMargin)],
              ['Average Margin', `${analyticsData.averageMargin.toFixed(2)}%`],
            ]

            ;(doc as any).autoTable({
              head: [['Metric', 'Value']],
              body: summaryData,
              startY: 65,
              styles: { fontSize: 10 },
              headStyles: { fillColor: [59, 130, 246] },
            })

            // Category breakdown
            const finalY = (doc as any).lastAutoTable.finalY || 65
            doc.setFontSize(14)
            doc.text('Category Breakdown', 20, finalY + 20)

            const categoryData = analyticsData.categoryBreakdown.map((cat) => [
              cat.category,
              cat.count.toString(),
              formatCurrency(cat.value),
              `${cat.percentage.toFixed(1)}%`,
            ])

            ;(doc as any).autoTable({
              head: [['Category', 'Count', 'Value', 'Percentage']],
              body: categoryData,
              startY: finalY + 25,
              styles: { fontSize: 10 },
              headStyles: { fillColor: [59, 130, 246] },
            })

            // Save PDF
            doc.save(
              `product-analytics-${new Date().toISOString().split('T')[0]}.pdf`
            )

            toast({
              title: 'Analytics PDF Export Complete',
              description: 'Analytics report has been downloaded as PDF.',
            })
          } catch (error) {
            console.error('Analytics PDF export error:', error)
            toast({
              title: 'PDF Export Failed',
              description:
                'Failed to generate analytics PDF. Please try again.',
              variant: 'destructive',
            })
          } finally {
            setIsExporting(false)
          }
        })
      })
    } catch (error) {
      console.error('PDF library loading error:', error)
      toast({
        title: 'PDF Export Failed',
        description: 'Failed to load PDF library. Please try again.',
        variant: 'destructive',
      })
      setIsExporting(false)
    }
  }, [analyticsData, formatCurrency, toast])

  if (isLoading) {
    return (
      <div className="min-h-screen">
        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <div className="text-center space-y-2">
              <div className="text-muted-foreground">Loading analytics...</div>
              <div className="text-xs text-muted-foreground">
                Processing product data and calculations
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className={cn('min-h-screen', className)}>
      {/* Page Header */}
      <PageHeader 
        title="Product Analytics" 
        description="Track and analyze your product performance"
      />
      
      {/* Main Grid Layout */}
      <div className="grid grid-cols-12 gap-6">
        {/* KPI Cards - Row 1 */}
        <div className="col-span-12 md:col-span-3">
          <MetricCard
            title="Inventory Value"
            value={formatCurrency(analyticsData.totalCost)}
            subtitle="Total cost of inventory"
            icon={
              <div className="h-6 w-6 rounded-md bg-blue-50 flex items-center justify-center">
                <div className="w-3 h-3 rounded-full bg-blue-500"></div>
              </div>
            }
          />
        </div>

        <div className="col-span-12 md:col-span-3">
          <MetricCard
            title="Potential Revenue"
            value={formatCurrency(analyticsData.totalRevenue)}
            subtitle="Total potential revenue"
            icon={
              <div className="h-6 w-6 rounded-md bg-green-50 flex items-center justify-center">
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
              </div>
            }
          />
        </div>

        <div className="col-span-12 md:col-span-3">
          <MetricCard
            title="Potential Profit"
            value={formatCurrency(analyticsData.totalMargin)}
            subtitle="Total potential profit"
            icon={
              <div className="h-6 w-6 rounded-md bg-yellow-50 flex items-center justify-center">
                <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
              </div>
            }
          />
        </div>

        <div className="col-span-12 md:col-span-3">
          <MetricCard
            title="Average Margin"
            value={`${analyticsData.averageMargin.toFixed(2)}%`}
            subtitle="Average profit margin"
            icon={
              <div className="h-6 w-6 rounded-md bg-red-50 flex items-center justify-center">
                <div className="w-3 h-3 rounded-full bg-red-500"></div>
              </div>
            }
          />
        </div>

        {/* Sales Performance Metrics - New Row */}
        <div className="col-span-12 md:col-span-6">
          <BestSellingItems />
        </div>

        {/* Top Categories Chart - Spanning 6 columns to match BestSellingItems */}
        <div className="col-span-12 md:col-span-6">
          <Card className="bg-white border-slate-200 shadow-sm h-full">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div>
                <CardTitle className="text-lg">Top Categories</CardTitle>
                <CardDescription className="text-xs">
                  {categoryChartMode === 'revenue' ? 'By Revenue' : 'By Profit'}
                </CardDescription>
              </div>
              <Select
                value={categoryChartMode}
                onValueChange={(value: 'revenue' | 'profit') =>
                  setCategoryChartMode(value)
                }
              >
                <SelectTrigger className="w-[100px] h-7 text-xs">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="revenue" className="text-xs">
                    By Revenue
                  </SelectItem>
                  <SelectItem value="profit" className="text-xs">
                    By Profit
                  </SelectItem>
                </SelectContent>
              </Select>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="h-40">
                <Bar
                  data={topCategoriesChartData}
                  options={topCategoriesChartOptions}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* New Margin Distribution Chart - Spanning 4 columns */}
        <div className="col-span-12 md:col-span-4">
          <Card className="bg-white border-slate-200 shadow-sm h-full">
            <CardHeader>
              <CardTitle>Margin Distribution</CardTitle>
              <CardDescription>Product count by margin range</CardDescription>
            </CardHeader>
            <CardContent className="h-48 pt-0">
              <Bar
                data={marginDistributionChartData}
                options={marginDistributionChartOptions}
              />
            </CardContent>
          </Card>
        </div>

        {/* Stock Status Donut Chart - Spanning 4 columns */}
        <div className="col-span-12 md:col-span-4">
          <Card className="bg-white border-slate-200 shadow-sm h-full">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Stock Status</CardTitle>
              <CardDescription className="text-xs">
                Current inventory availability
              </CardDescription>
            </CardHeader>
            <CardContent className="h-48 pt-0">
              <Doughnut
                data={stockStatusChartData}
                options={stockStatusChartOptions}
              />
            </CardContent>
          </Card>
        </div>

        {/* Performance Over Time Chart - Full Width */}
        <div className="col-span-12">
          <Card className="bg-white border-slate-200 shadow-sm">
            <CardHeader>
              <CardTitle>Performance Over Time</CardTitle>
              <CardDescription>Revenue and profit trends</CardDescription>
            </CardHeader>
            <CardContent className="h-80">
              <Line
                data={performanceChartData}
                options={performanceChartOptions}
              />
            </CardContent>
          </Card>
        </div>

        {/* Top Products Table - Full Width at Bottom */}
        <div className="col-span-12">
          <Card className="bg-white border-slate-200 shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div>
                <CardTitle className="text-lg">
                  Top Products by Revenue
                </CardTitle>
                <CardDescription className="text-xs">
                  Highest performing products
                </CardDescription>
              </div>
              <Select
                value={topProductsCount.toString()}
                onValueChange={(value) =>
                  setTopProductsCount(value === 'all' ? 'all' : parseInt(value))
                }
              >
                <SelectTrigger className="w-[100px] h-7 text-xs">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5" className="text-xs">
                    5 Items
                  </SelectItem>
                  <SelectItem value="10" className="text-xs">
                    10 Items
                  </SelectItem>
                  <SelectItem value="all" className="text-xs">
                    All Items
                  </SelectItem>
                </SelectContent>
              </Select>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="bg-slate-100">
                      <th className="text-left py-2 px-3 font-medium text-xs">
                        Product Name
                      </th>
                      <th className="text-left py-2 px-3 font-medium text-xs">
                        SKU
                      </th>
                      <th className="text-right py-2 px-3 font-medium text-xs">
                        Revenue
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {analyticsData.topProducts
                      .slice(
                        0,
                        topProductsCount === 'all'
                          ? undefined
                          : topProductsCount
                      )
                      .map((product, index) => (
                        <tr
                          key={index}
                          className="border-b border-slate-100 last:border-0"
                        >
                          <td className="py-2 px-3">{product.name}</td>
                          <td className="py-2 px-3 text-muted-foreground text-xs">
                            {product.sku}
                          </td>
                          <td className="py-2 px-3 text-right font-medium text-xs">
                            {formatCurrency(product.value)}
                          </td>
                        </tr>
                      ))}
                    {analyticsData.topProducts.length === 0 && (
                      <tr>
                        <td
                          colSpan={3}
                          className="py-4 px-3 text-center text-muted-foreground text-xs"
                        >
                          No products available
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
