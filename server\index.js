const http = require('http');
const { parse } = require('url');

// Import all route handlers
const apiRoutes = require('./routes/api');
const authRoutes = require('./routes/auth');
const pageRoutes = require('./routes/pages');

// Import configuration (this will initialize Supabase)
const supabaseConfig = require('./config/supabase');

class ONKOServer {
  constructor() {
    this.server = null;
    this.port = 3002;
  }

  async start() {
    this.server = http.createServer((req, res) => {
      this.handleRequest(req, res);
    });

    this.server.listen(this.port, () => {
      console.log('\n🚀 ONKO Server Started Successfully!');
      console.log('=====================================');
      console.log('🌐 Server running at: http://localhost:3002');
      console.log('📱 Main App: http://localhost:3002/');
      console.log('🔑 Sign In: http://localhost:3002/auth/signin');
      console.log('📊 Dashboard: http://localhost:3002/dashboard');
      console.log('💰 Expenses: http://localhost:3002/dashboard/expenses');
      console.log('=====================================');
      console.log('✅ Modular Architecture: Implemented');
      console.log('✅ Separation of Concerns: Complete');
      console.log('✅ Production Ready: True');
      console.log('✅ Database: Supabase Live Database Only');
      console.log('=====================================\n');
    });

    // Handle server errors
    this.server.on('error', (error) => {
      if (error.code === 'EADDRINUSE') {
        console.error(`❌ Port ${this.port} is already in use`);
        console.log('💡 Try stopping the existing server or use a different port');
      } else {
        console.error('❌ Server error:', error);
      }
    });

    // Graceful shutdown
    process.on('SIGTERM', () => this.shutdown());
    process.on('SIGINT', () => this.shutdown());
  }

  async handleRequest(req, res) {
    const parsedUrl = parse(req.url, true);
    const { pathname } = parsedUrl;
    
    // Log requests for debugging
    console.log(`${new Date().toISOString()} - ${req.method} ${pathname}`);
    
    try {
      // Route requests to appropriate handlers
      if (pathname.startsWith('/api/auth/')) {
        console.log('🔐 Routing to auth handler:', pathname);
        await authRoutes.handleRequest(req, res, pathname);
      } else if (pathname.startsWith('/api/')) {
        console.log('🌐 Routing to API handler:', pathname);
        await apiRoutes.handleRequest(req, res, pathname);
      } else {
        console.log('📄 Routing to page handler:', pathname);
        // Handle page routes (includes static files)
        pageRoutes.handleRequest(req, res, pathname);
      }
    } catch (error) {
      console.error('❌ Request handling error:', error);
      this.handleServerError(res, error);
    }
  }

  handleServerError(res, error) {
    if (!res.headersSent) {
      res.writeHead(500, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        success: false,
        error: 'Internal server error',
        timestamp: new Date().toISOString()
      }));
    }
  }

  shutdown() {
    console.log('\n🛑 Shutting down ONKO server gracefully...');
    
    if (this.server) {
      this.server.close(() => {
        console.log('✅ Server closed successfully');
        process.exit(0);
      });
    } else {
      process.exit(0);
    }
  }
}

// Create and start the server
const onkoServer = new ONKOServer();

// Handle startup errors
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  onkoServer.shutdown();
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  onkoServer.shutdown();
});

// Start the server
onkoServer.start().catch((error) => {
  console.error('❌ Failed to start server:', error);
  process.exit(1);
});

module.exports = ONKOServer;