'use client'

import { useState } from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Search, Filter, SlidersHorizontal } from 'lucide-react'
import { cn } from '@/lib/utils'

interface CustomerTableHeaderProps {
  searchTerm: string
  onSearchChange: (value: string) => void
  onFilterClick: () => void
  activeFilterCount?: number
  isMobile?: boolean
  className?: string
}

export function CustomerTableHeader({
  searchTerm,
  onSearchChange,
  onFilterClick,
  activeFilterCount = 0,
  isMobile = false,
  className
}: CustomerTableHeaderProps) {
  return (
    <div className={cn("flex flex-col sm:flex-row gap-2", className)}>
      <div className="relative flex-1">
        <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-gray-500" />
        <Input
          placeholder="Search customers..."
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
          className="pl-8 h-8 text-xs"
        />
      </div>
      
      <div className="flex items-center gap-2">
        <Button 
          variant="outline" 
          size="sm" 
          className="h-8 text-xs relative"
          onClick={onFilterClick}
        >
          {isMobile ? (
            <>
              <Filter className="h-3.5 w-3.5 mr-2" />
              <span className="font-medium">Filters</span>
              {activeFilterCount > 0 && (
                <span className="ml-1.5 flex items-center justify-center w-4 h-4 rounded-full bg-red-500 text-white text-xs font-medium">
                  {activeFilterCount}
                </span>
              )}
            </>
          ) : (
            <>
              <SlidersHorizontal className="h-3.5 w-3.5" />
              {activeFilterCount > 0 && (
                <span className="absolute -top-1 -right-1 flex items-center justify-center w-4 h-4 rounded-full bg-red-500 text-white text-[10px] font-medium">
                  {activeFilterCount}
                </span>
              )}
            </>
          )}
        </Button>
      </div>
    </div>
  )
}