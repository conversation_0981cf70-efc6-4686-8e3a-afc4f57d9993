# WooCommerce Integration Guide

This guide will help you properly set up and troubleshoot the WooCommerce integration with your Onko account.

## Overview

The WooCommerce integration allows you to automatically sync orders from your WooCommerce store to Onko, eliminating the need for manual data entry.

## Prerequisites

1. A WooCommerce store with REST API enabled
2. Administrator access to your WooCommerce store
3. SSL certificate (HTTPS) for your store
4. WooCommerce version 3.5 or higher

## Step 1: Enable REST API in WooCommerce

1. Log in to your WordPress admin panel
2. Go to **WooCommerce > Settings > Advanced > REST API**
3. Make sure the REST API is enabled (it should be by default)

## Step 2: Create API Credentials

1. In your WordPress admin panel, go to **WooCommerce > Settings > Advanced > REST API**
2. Click **"Add Key"**
3. Fill in the details:
   - **Description**: `Onko Integration`
   - **User**: Select an admin user
   - **Permissions**: `Read/Write`
4. Click **"Generate API Key"**
5. **Save the Consumer Key and Consumer Secret in a secure location**

## Step 3: Configure Onko Integration

1. In Onko, go to **Settings > Integrations**
2. Find the WooCommerce integration card
3. Click **"Connect"**
4. Fill in the form:
   - **Store URL**: Your WooCommerce store URL (e.g., `https://yourstore.com`)
   - **Consumer Key**: The key you generated in Step 2
   - **Consumer Secret**: The secret you generated in Step 2
5. Click **"Test Connection"** to verify your credentials
6. If successful, click **"Connect Store"**

## Step 4: Register Webhook

After connecting your store:
1. Click **"Register Webhook"** on the WooCommerce integration card
2. This will set up automatic order syncing when new orders are created in WooCommerce

## Troubleshooting

### Common Issues and Solutions

#### 1. "400 Bad Request" Error
This typically means there's an issue with your credentials or store URL:
- **Verify your store URL** is correct and accessible
- **Confirm your API credentials** are correct and have Read/Write permissions
- **Check that your store has SSL (HTTPS)**

#### 2. "Invalid Store URL" Error
- Make sure you're using the full URL including `https://`
- Ensure there are no trailing slashes
- Verify the URL is publicly accessible

#### 3. "Invalid API Credentials" Error
- Ensure your Consumer Key starts with `ck_` and Consumer Secret starts with `cs_`
- Verify the credentials have Read/Write permissions
- Check that the user associated with the credentials has admin privileges

#### 4. Webhook Registration Failed
- Ensure your store URL is publicly accessible
- Check that your WooCommerce REST API is properly configured
- Verify your server isn't blocking external requests

#### 5. Orders Not Syncing
- Verify the webhook is properly registered
- Check the webhook status in your WooCommerce settings
- Look at the WooCommerce webhook delivery logs for errors

### Checking Webhook Status

1. In your WordPress admin panel, go to **WooCommerce > Settings > Advanced > Webhooks**
2. Look for the "ONKO Order Created" webhook
3. Ensure its status is "Active"
4. Check the delivery logs for any errors

### Testing Webhook Delivery

1. In your WordPress admin panel, go to **WooCommerce > Settings > Advanced > Webhooks**
2. Find the "ONKO Order Created" webhook
3. Click on it to view details
4. Scroll down to the "Recent Deliveries" section
5. Check the delivery status and response

## Security Notes

- Never share your API credentials
- Use Read/Write permissions only when necessary
- Regularly rotate your API keys for security
- The Consumer Secret is stored securely and never displayed in the UI

## Advanced Configuration

### Custom Webhook Secret

If you need to use a custom webhook secret:
1. When registering the webhook, the system will use your Consumer Secret as the webhook secret
2. You can change this in WooCommerce settings if needed.

### Multiple WooCommerce Stores

Currently, Onko supports one WooCommerce integration per organization. If you need to connect multiple stores:
1. Create separate organizations for each store
2. Set up the integration in each organization

## API Endpoints

The integration uses the following WooCommerce REST API endpoints:
- `GET /wp-json/wc/v3/system_status` - For connection testing
- `POST /wp-json/wc/v3/webhooks` - For webhook registration
- `GET /wp-json/wc/v3/webhooks/{id}` - For webhook status checking

## Support

If you continue to experience issues, please contact support with:
- Your store URL
- Error messages you're seeing
- Steps you've already taken to troubleshoot
- Screenshots of webhook settings if relevant

## FAQ

### Q: How often are orders synced?
A: Orders are synced in real-time when they are created in WooCommerce through the webhook.

### Q: What order data is synced?
A: The following data is synced:
- Order number
- Order date
- Customer information
- Line items (products and quantities)
- Shipping and tax information
- Order status

### Q: Can I sync historical orders?
A: Currently, only new orders are synced automatically. For historical orders, you would need to manually create them in Onko.

### Q: What happens if the webhook fails?
A: WooCommerce will retry the webhook delivery several times. If it continues to fail, the webhook may be disabled. You can re-enable it manually.

### Q: Can I edit synced orders in Onko?
A: Yes, you can edit synced orders in Onko. However, changes made in WooCommerce after the initial sync will not overwrite your changes in Onko.