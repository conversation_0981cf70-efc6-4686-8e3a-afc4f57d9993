import React from 'react'
import { Input } from '@/components/ui/input'
import { useProductForm } from '../../context/ProductFormContext'
import { SectionHeader } from '../shared/SectionHeader'
import { FormField } from '../shared/FormField'
import { useCurrency } from '@/lib/currency'
import { AttributeManagementSection } from './AttributeManagementSection'
import { VariantConfigurationSection } from './VariantConfigurationSection'

export function PricingSection() {
  const { formData, updateFormData, errors } = useProductForm()
  const { formatCurrency } = useCurrency()
  const [generatedVariants, setGeneratedVariants] = React.useState<Array<{
    id: string
    attributes: { [key: string]: string }
    sku: string
    enabled: boolean
  }>>([])

  // Handle variant generation from AttributeManagementSection
  const handleVariantsGenerated = React.useCallback((variants: Array<{
    id: string
    attributes: { [key: string]: string }
    sku: string
    enabled: boolean
  }>) => {
    setGeneratedVariants(variants)
  }, [])

  return (
    <div className="max-w-4xl">
      <SectionHeader
        title={formData.has_variants ? "Variants" : "Pricing & Costs"}
      />

      <div className="space-y-4">
        {/* Simple Product Pricing */}
        {!formData.has_variants && (
          <>
            {/* Cost Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                label="Base Cost"
                required
                error={errors.base_cost}
              >
                <Input
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.base_cost || ''}
                  onChange={(e) => updateFormData('base_cost', parseFloat(e.target.value) || 0)}
                  placeholder="0.00"
                  className="h-8 text-xs placeholder:text-xs"
                />
              </FormField>

              <FormField
                label="Packaging Cost"
                error={errors.packaging_cost}
              >
                <Input
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.packaging_cost || ''}
                  onChange={(e) => updateFormData('packaging_cost', parseFloat(e.target.value) || 0)}
                  placeholder="0.00"
                  className="h-8 text-xs placeholder:text-xs"
                />
              </FormField>
            </div>

            {/* Selling Price */}
            <FormField
              label="Selling Price"
              error={errors.price}
            >
              <Input
                type="number"
                step="0.01"
                min="0"
                value={formData.price || ''}
                onChange={(e) => updateFormData('price', parseFloat(e.target.value) || null)}
                placeholder="0.00"
                className="h-8 text-xs placeholder:text-xs"
              />
            </FormField>

            {/* Sale Price (Optional) */}
            <FormField
              label="Sale Price (Optional)"
              error={errors.sale_price}
            >
              <Input
                type="number"
                step="0.01"
                min="0"
                value={formData.sale_price || ''}
                onChange={(e) => updateFormData('sale_price', parseFloat(e.target.value) || null)}
                placeholder="0.00"
                className="h-8 text-xs placeholder:text-xs"
              />
            </FormField>

            {/* Cost Summary */}
            {(formData.base_cost > 0 || formData.packaging_cost > 0) && (
              <div className="mt-6 p-3 bg-gray-50 border border-gray-200 rounded-md">
                <h4 className="font-medium text-gray-900 text-xs mb-2">Cost Summary</h4>
                <div className="space-y-1 text-xs">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Base Cost:</span>
                    <span className="font-mono">{formatCurrency(formData.base_cost || 0)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Packaging Cost:</span>
                    <span className="font-mono">{formatCurrency(formData.packaging_cost || 0)}</span>
                  </div>
                  <div className="flex justify-between border-t pt-1 font-medium">
                    <span>Total Cost:</span>
                    <span className="font-mono">{formatCurrency((formData.base_cost || 0) + (formData.packaging_cost || 0))}</span>
                  </div>
                  {formData.price && (
                    <div className="flex justify-between text-green-600">
                      <span>Profit Margin:</span>
                      <span className="font-mono">
                        {formatCurrency(formData.price - ((formData.base_cost || 0) + (formData.packaging_cost || 0)))}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            )}
          </>
        )}

        {/* Variable Product Variant Management */}
        {formData.has_variants && (
          <div className="space-y-6">
            <AttributeManagementSection onVariantsGenerated={handleVariantsGenerated} />
            <VariantConfigurationSection generatedVariants={generatedVariants} />
          </div>
        )}
      </div>
    </div>
  )
}