'use client';

import { useState } from 'react';
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogFooter 
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, CheckCircle, Info } from 'lucide-react';
import { useConnectVault } from '@/hooks/use-connect-vault';

interface VaultConnectionModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
  isConnected: boolean;
}

export default function VaultConnectionModal({
  open,
  onOpenChange,
  onSuccess,
  isConnected
}: VaultConnectionModalProps) {
  const { toast } = useToast();
  const [vaultUrl, setVaultUrl] = useState('');
  const [apiKey, setApiKey] = useState('');
  const [accountId, setAccountId] = useState('');
  const [connectionTestResult, setConnectionTestResult] = useState<{success: boolean; message: string} | null>(null);
  
  const { mutate: connectVault, isPending } = useConnectVault({
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Vault connected successfully',
      });
      
      onSuccess();
      // Reset form
      setVaultUrl('');
      setApiKey('');
      setAccountId('');
      setConnectionTestResult(null);
    },
    onError: (error: Error) => {
      toast({
        title: 'Connection Failed',
        description: error.message || 'Failed to connect to Vault',
        variant: 'destructive',
      });
    }
  });

  const handleTestConnection = async () => {
    setConnectionTestResult(null);
    
    if (!vaultUrl || !apiKey) {
      toast({
        title: 'Validation Error',
        description: 'Please fill in all required fields',
        variant: 'destructive',
      });
      return;
    }

    try {
      // Validate URL format
      try {
        new URL(vaultUrl);
      } catch {
        toast({
          title: 'Validation Error',
          description: 'Please enter a valid Vault URL',
          variant: 'destructive',
        });
        return;
      }

      // In a real implementation, this would test connection to the Vault API
      // For now, we'll simulate this with a timeout
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Simulate successful connection
      setConnectionTestResult({
        success: true,
        message: 'Successfully connected to Vault'
      });
    } catch (error: any) {
      setConnectionTestResult({
        success: false,
        message: `Connection failed: ${error.message || 'Unknown error'}`
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!vaultUrl || !apiKey) {
      toast({
        title: 'Validation Error',
        description: 'Please fill in all required fields',
        variant: 'destructive',
      });
      return;
    }

    // Validate URL format
    try {
      new URL(vaultUrl);
    } catch {
      toast({
        title: 'Validation Error',
        description: 'Please enter a valid Vault URL',
        variant: 'destructive',
      });
      return;
    }

    connectVault({
      vaultUrl,
      apiKey,
      accountId: accountId || undefined
    });
  };

  return (
    <Dialog open={open} onOpenChange={(isOpen) => {
      onOpenChange(isOpen);
      // Reset form when dialog is closed
      if (!isOpen) {
        setVaultUrl('');
        setApiKey('');
        setAccountId('');
        setConnectionTestResult(null);
      }
    }}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Connect to Vault</DialogTitle>
          <DialogDescription>
            Enter your Vault credentials to connect your financial storage with Onko.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="vault-url">Vault URL *</Label>
              <Input
                id="vault-url"
                placeholder="https://your-vault.com"
                value={vaultUrl}
                onChange={(e) => setVaultUrl(e.target.value)}
                disabled={isPending}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="api-key">API Key *</Label>
              <Input
                id="api-key"
                type="password"
                placeholder="Enter your API key"
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                disabled={isPending}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="account-id">Account ID (Optional)</Label>
              <Input
                id="account-id"
                placeholder="Enter your account ID"
                value={accountId}
                onChange={(e) => setAccountId(e.target.value)}
                disabled={isPending}
              />
            </div>
            
            <Alert variant="default">
              <Info className="h-4 w-4" />
              <AlertDescription>
                Make sure your Vault API key has the necessary permissions for financial data access.
              </AlertDescription>
            </Alert>
            
            {connectionTestResult && (
              <Alert variant={connectionTestResult.success ? "default" : "destructive"}>
                {connectionTestResult.success ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  <AlertCircle className="h-4 w-4" />
                )}
                <AlertDescription>
                  {connectionTestResult.message}
                </AlertDescription>
              </Alert>
            )}
          </div>
          <DialogFooter className="gap-2">
            <Button 
              type="button" 
              variant="outline" 
              onClick={handleTestConnection}
              disabled={isPending}
            >
              Test Connection
            </Button>
            <Button type="submit" disabled={isPending}>
              {isPending ? 'Connecting...' : 'Connect Vault'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}