# WooCommerce Integration Documentation

## Overview
This document explains how the WooCommerce integration works in the Onko application, covering both the frontend components and backend logic.

## Frontend Components

### IntegrationsSettings.tsx
This component displays the integrations dashboard in the settings area. It shows available integrations as cards and manages the connection status.

Key features:
- Displays a grid of available integrations
- Shows connection status for each integration
- Provides Connect/Manage buttons based on connection status
- Opens the WooCommerceConnectionModal when needed

### WooCommerceConnectionModal.tsx
This modal component handles the connection process for WooCommerce stores.

Key features:
- Collects store URL, consumer key, and consumer secret
- Validates inputs before submission
- Calls the `useConnectWooCommerce` hook to establish the connection
- Provides user feedback through toast notifications

### useConnectWooCommerce Hook
A React Query mutation hook that handles the connection process.

Key features:
- Calls the Supabase RPC function `connect_woocommerce`
- Handles success and error states
- Provides loading states for UI feedback

## Backend Logic

### connect_woocommerce SQL Function
A PostgreSQL function that handles the connection process on the backend.

Key features:
- Validates user organization membership
- Checks for existing integrations
- Creates or updates integration records
- Securely stores credentials
- Returns structured JSON responses

### Database Schema
The integration uses two main tables:

1. `integrations` - Stores connection details for third-party services
2. `synced_orders` - Tracks imported orders to prevent duplicates

## Security Considerations

### Credential Storage
Credentials are stored securely using the Supabase Vault pattern:
- Actual secrets are stored in a secure location
- Database only contains references to the secrets
- Secrets are never exposed in API responses

### Access Control
- Row Level Security (RLS) policies ensure users only access their organization's data
- All operations are performed within the context of the authenticated user
- Organization membership is verified before any operations

## Error Handling

The integration includes comprehensive error handling:
- Input validation for all required fields
- Connection testing before saving credentials
- Graceful error messages for users
- Detailed error logging for debugging

## Testing

The integration includes unit tests for:
- Component rendering
- User interactions
- Error states
- Success flows

## Future Enhancements

Planned improvements:
- Real API validation of WooCommerce credentials
- Enhanced error handling with specific error types
- Improved UI with loading states and progress indicators
- Additional integrations beyond WooCommerce