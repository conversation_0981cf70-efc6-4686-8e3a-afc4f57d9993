'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { useToast } from '@/components/ui/use-toast'
import { Plus, RefreshCw } from 'lucide-react'
import { PurchaseOrderFilterBar } from '@/components/products/purchase-orders/PurchaseOrderFilterBar'
import { PurchaseOrderTable } from '@/components/products/purchase-orders/PurchaseOrderTable'
import { PageHeader } from '@/components/ui/page-header'
import { 
  getSuppliers 
} from '@/components/products/purchase-orders/purchase-order-service'
import { useAuth } from '@/contexts/auth-context'
import { 
  PurchaseOrder, 
  PurchaseOrderFilters, 
  INITIAL_PURCHASE_ORDER_FILTERS 
} from '@/components/products/purchase-orders/types'
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog'
import { PurchaseOrderModal } from '@/components/products/modals/PurchaseOrderModal'
import { getSupabaseClient } from '@/lib/supabase'
import { Skeleton } from '@/components/ui/skeleton'
import { useAllProducts, type ExtendedProductRow } from '@/hooks/use-products'
import { useRouter } from 'next/navigation'
// Import the new React Query hooks
import { usePurchaseOrders, useDeletePurchaseOrder, useInvalidatePurchaseOrders } from '@/hooks/use-purchase-orders'

export default function PurchaseOrdersPage() {
  const [suppliers, setSuppliers] = useState<string[]>([])
  const [filters, setFilters] = useState<PurchaseOrderFilters>(INITIAL_PURCHASE_ORDER_FILTERS)
  const [isAddModalOpen, setIsAddModalOpen] = useState(false)
  const { user, organizationId } = useAuth()
  const { toast } = useToast()
  const router = useRouter()
  
  // Use React Query to fetch products
  const { data: productsData = [], isLoading: isLoadingProducts, error: productError } = useAllProducts(organizationId || undefined)
  const products = productsData as ExtendedProductRow[]
  
  // Use React Query to fetch purchase orders
  const { 
    data: purchaseOrders = [], 
    isLoading: isLoadingPurchaseOrders, 
    error: purchaseOrderError,
    refetch: refetchPurchaseOrders
  } = usePurchaseOrders(organizationId || undefined, filters)
  
  // Use React Query mutation for deleting purchase orders
  const { mutate: deletePurchaseOrder } = useDeletePurchaseOrder()
  
  // Use invalidation hook for manual refetching
  const { invalidatePurchaseOrders } = useInvalidatePurchaseOrders()
  
  // Fetch suppliers
  const fetchSuppliers = async () => {
    if (!organizationId) return
    
    try {
      const supplierList = await getSuppliers(organizationId)
      setSuppliers(supplierList)
    } catch (error) {
      console.error('Error fetching suppliers:', error)
    }
  }
  
  // Handle delete with React Query mutation
  const handleDelete = async (id: string) => {
    if (!organizationId) return
    
    deletePurchaseOrder(
      { id, organizationId },
      {
        onSuccess: () => {
          toast({
            title: "Success",
            description: "Purchase order deleted successfully."
          })
        },
        onError: (error) => {
          console.error('Error deleting purchase order:', error)
          toast({
            title: "Error",
            description: "Failed to delete purchase order. Please try again.",
            variant: "destructive"
          })
        }
      }
    )
  }
  
  // Handle filter changes
  const handleFiltersChange = (newFilters: PurchaseOrderFilters) => {
    setFilters(newFilters)
  }
  
  // Fetch suppliers on mount
  useEffect(() => {
    fetchSuppliers()
  }, [organizationId])
  
  // Set up real-time subscription for purchase orders
  useEffect(() => {
    if (!organizationId) return
    
    const supabase = getSupabaseClient()
    
    const purchaseOrdersChannel = supabase
      .channel('purchase_orders_changes')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'purchase_orders',
          filter: `organization_id=eq.${organizationId}`
        },
        () => {
          invalidatePurchaseOrders()
          fetchSuppliers()
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'purchase_orders',
          filter: `organization_id=eq.${organizationId}`
        },
        () => {
          invalidatePurchaseOrders()
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'DELETE',
          schema: 'public',
          table: 'purchase_orders',
          filter: `organization_id=eq.${organizationId}`
        },
        () => {
          invalidatePurchaseOrders()
        }
      )
      .subscribe()
    
    // Cleanup subscription on unmount
    return () => {
      supabase.removeChannel(purchaseOrdersChannel)
    }
  }, [organizationId, invalidatePurchaseOrders])
  
  const loading = isLoadingPurchaseOrders || isLoadingProducts
  
  if (loading) {
    return (
      <div className="flex flex-col gap-4">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <Skeleton className="h-6 w-48 mb-2" />
            <Skeleton className="h-4 w-64" />
          </div>
          <Skeleton className="h-8 w-40" />
        </div>
        
        <Skeleton className="h-12 w-full" />
        
        <div className="border rounded-lg">
          <div className="p-4 border-b">
            <Skeleton className="h-6 w-32" />
          </div>
          <div className="divide-y">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="p-4">
                <div className="flex justify-between">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-4 w-24" />
                </div>
                <div className="flex justify-between mt-2">
                  <Skeleton className="h-3 w-24" />
                  <Skeleton className="h-3 w-20" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }
  
  if (productError || purchaseOrderError) {
    const error = productError || purchaseOrderError
    let userFriendlyMessage = "Failed to load data. Please try again."
    
    // Provide more specific error messages based on the error type
    if (error?.message.includes("NetworkError") || error?.message.includes("Failed to fetch")) {
      userFriendlyMessage = "Network connection error. Please check your internet connection and try again."
    } else if (error?.message.includes("ERR_INSUFFICIENT_RESOURCES")) {
      userFriendlyMessage = "System resource limit reached. Please refresh the page or try again in a few minutes."
    } else if (error?.message.includes("timeout")) {
      userFriendlyMessage = "Request timed out. Please try again."
    } else if (error?.message.includes("401") || error?.message.includes("403")) {
      userFriendlyMessage = "Authentication error. Please sign in again."
    } else if (error?.message.includes("500")) {
      userFriendlyMessage = "Server error. Please try again later."
    }
    
    return (
      <div className="flex flex-col gap-4">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <PageHeader 
            title="Purchase Orders" 
            description="Create, track, and manage all your orders with suppliers"
          />
          <Button 
            className="flex items-center gap-1.5 h-8 px-3 bg-blue-600 hover:bg-blue-700"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4" />
            <span>Refresh</span>
          </Button>
        </div>
        
        <div className="flex items-center justify-center py-12">
          <div className="text-center max-w-md">
            <div className="text-red-500 mb-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
              <h3 className="text-lg font-medium">Unable to Load Data</h3>
            </div>
            <p className="text-muted-foreground text-sm mb-4">{userFriendlyMessage}</p>
            <Button 
              onClick={() => window.location.reload()} 
              variant="default" 
              size="sm"
            >
              Refresh Page
            </Button>
            <p className="text-xs text-muted-foreground mt-4">
              Error: {error?.message}
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col gap-4">
      {/* Page Header with Title and Actions */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <PageHeader 
          title="Purchase Orders" 
          description="Create, track, and manage all your orders with suppliers"
        />
        
        <Button 
          className="flex items-center gap-1.5 h-8 px-3 bg-blue-600 hover:bg-blue-700"
          onClick={() => setIsAddModalOpen(true)}
        >
          <Plus className="h-4 w-4" />
          <span>New Purchase Order</span>
        </Button>
      </div>
      
      {/* Filter Bar */}
      <PurchaseOrderFilterBar 
        filters={filters} 
        onFiltersChange={handleFiltersChange}
        suppliers={suppliers}
      />
      
      {/* Purchase Orders Table */}
      <PurchaseOrderTable 
        purchaseOrders={purchaseOrders} 
        onDelete={handleDelete} 
      />
      
      {/* Add Purchase Order Modal */}
      <PurchaseOrderModal 
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        selectedItem={null}
        products={products}
        mode="purchase-orders"
      />
    </div>
  )
}