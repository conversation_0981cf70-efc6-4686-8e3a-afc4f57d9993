'use client'

import { useState } from 'react'
import { format } from 'date-fns'
import { CalendarIcon } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Calendar } from '@/components/ui/calendar'
import { cn } from '@/lib/utils'
import { DateRange } from 'react-day-picker'

interface DateRangeSelectorProps {
  dateRange: DateRange | undefined
  onDateChange: (dateRange: DateRange | undefined) => void
}

export function DateRangeSelector({ dateRange, onDateChange }: DateRangeSelectorProps) {
  const [isCustomPopoverOpen, setIsCustomPopoverOpen] = useState(false)

  const getPresetLabel = () => {
    if (!dateRange?.from) {
      return 'Select a date range'
    }

    if (!dateRange.to) {
      return format(dateRange.from, 'MMM d, yyyy')
    }

    // Check for preset ranges
    const today = new Date()
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1)
    const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0)
    
    const firstDayOfLastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1)
    const lastDayOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0)
    
    const threeMonthsAgo = new Date(today.getFullYear(), today.getMonth() - 3, 1)
    const firstDayOfYear = new Date(today.getFullYear(), 0, 1)
    const lastDayOfYear = new Date(today.getFullYear(), 11, 31)

    if (
      dateRange.from.getTime() === firstDayOfMonth.getTime() &&
      dateRange.to.getTime() === lastDayOfMonth.getTime()
    ) {
      return 'This Month'
    }

    if (
      dateRange.from.getTime() === firstDayOfLastMonth.getTime() &&
      dateRange.to.getTime() === lastDayOfLastMonth.getTime()
    ) {
      return 'Last Month'
    }

    if (
      dateRange.from.getTime() === threeMonthsAgo.getTime() &&
      dateRange.to.getTime() === lastDayOfMonth.getTime()
    ) {
      return 'Last 3 Months'
    }

    if (
      dateRange.from.getTime() === firstDayOfYear.getTime() &&
      dateRange.to.getTime() === lastDayOfYear.getTime()
    ) {
      return 'This Year'
    }

    // Custom range
    return `${format(dateRange.from, 'MMM d, yyyy')} - ${format(dateRange.to, 'MMM d, yyyy')}`
  }

  const handlePresetSelect = (preset: string) => {
    const today = new Date()
    
    switch (preset) {
      case 'this-month':
        const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1)
        const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0)
        onDateChange({ from: firstDayOfMonth, to: lastDayOfMonth })
        break
      case 'last-month':
        const firstDayOfLastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1)
        const lastDayOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0)
        onDateChange({ from: firstDayOfLastMonth, to: lastDayOfLastMonth })
        break
      case 'last-3-months':
        const threeMonthsAgo = new Date(today.getFullYear(), today.getMonth() - 3, 1)
        const lastDayOfCurrentMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0)
        onDateChange({ from: threeMonthsAgo, to: lastDayOfCurrentMonth })
        break
      case 'this-year':
        const firstDayOfYear = new Date(today.getFullYear(), 0, 1)
        const lastDayOfYear = new Date(today.getFullYear(), 11, 31)
        onDateChange({ from: firstDayOfYear, to: lastDayOfYear })
        break
      case 'custom':
        setIsCustomPopoverOpen(true)
        return // Don't close the dropdown
    }
  }

  const handleCustomRangeSelect = (range: DateRange | undefined) => {
    if (range?.from) {
      onDateChange(range)
      setIsCustomPopoverOpen(false)
    }
  }

  return (
    <div className="flex items-center">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            id="date-range"
            variant="outline"
            className={cn(
              "w-full sm:w-auto justify-start text-left font-normal",
              !dateRange && "text-muted-foreground"
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {getPresetLabel()}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-56">
          <DropdownMenuItem onSelect={() => handlePresetSelect('this-month')}>
            This Month
          </DropdownMenuItem>
          <DropdownMenuItem onSelect={() => handlePresetSelect('last-month')}>
            Last Month
          </DropdownMenuItem>
          <DropdownMenuItem onSelect={() => handlePresetSelect('last-3-months')}>
            Last 3 Months
          </DropdownMenuItem>
          <DropdownMenuItem onSelect={() => handlePresetSelect('this-year')}>
            This Year
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <Popover open={isCustomPopoverOpen} onOpenChange={setIsCustomPopoverOpen}>
            <PopoverTrigger asChild>
              <DropdownMenuItem
                onSelect={(e) => {
                  e.preventDefault() // Prevent dropdown from closing
                  handlePresetSelect('custom')
                }}
              >
                Custom Range
              </DropdownMenuItem>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                initialFocus
                mode="range"
                defaultMonth={dateRange?.from}
                selected={dateRange}
                onSelect={handleCustomRangeSelect}
                numberOfMonths={2}
              />
            </PopoverContent>
          </Popover>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}