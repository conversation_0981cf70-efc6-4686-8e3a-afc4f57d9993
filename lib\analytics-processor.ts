/**
 * Analytics data processing utilities
 * Handles category breakdown, period data calculation, and analytics aggregation
 */

import { type ExpenseRow } from '@/lib/supabase'
import { 
  type PeriodType, 
  getPeriodRanges, 
  getPeriodLabels, 
  generateTrendData,
  filterExpensesByDateRange,
  calculatePercentageChange
} from './period-calculations'

// Memoization cache for expensive calculations
const calculationCache = new Map<string, any>()
const CACHE_TTL = 5 * 60 * 1000 // 5 minutes

// Category colors for visualization
const CATEGORY_COLORS = [
  '#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6', 
  '#06B6D4', '#EC4899', '#84CC16'
]

export interface PeriodData {
  period: string
  amount: number
  count: number
  change?: number
}

export interface CategoryData {
  category: string
  amount: number
  count: number
  percentage: number
  color: string
}

export interface AnalyticsData {
  currentPeriod: PeriodData
  previousPeriod: PeriodData
  currentQuarter: PeriodData
  currentYear: PeriodData
  topCategories: CategoryData[]
  trendData: Array<{ period: string; amount: number; count: number }>
}

/**
 * Generate cache key for memoization
 */
function getCacheKey(expenses: ExpenseRow[], selectedPeriod: PeriodType): string {
  const expenseIds = expenses.map(e => e.id).sort().join(',')
  return `${expenseIds}-${selectedPeriod}`
}

/**
 * Check if cached data is still valid
 */
function isCacheValid(timestamp: number): boolean {
  return Date.now() - timestamp < CACHE_TTL
}

/**
 * Calculate category breakdown for a specific period
 */
function calculateCategoryBreakdown(expenses: ExpenseRow[], totalAmount: number): CategoryData[] {
  const categoryTotals = new Map<string, { amount: number; count: number }>()
  
  expenses.forEach(expense => {
    const category = expense.category || 'Uncategorized'
    const current = categoryTotals.get(category) || { amount: 0, count: 0 }
    categoryTotals.set(category, {
      amount: current.amount + expense.amount,
      count: current.count + 1
    })
  })

  return Array.from(categoryTotals.entries())
    .map(([category, data], index) => ({
      category,
      amount: data.amount,
      count: data.count,
      percentage: totalAmount > 0 ? (data.amount / totalAmount) * 100 : 0,
      color: CATEGORY_COLORS[index % CATEGORY_COLORS.length]
    }))
    .sort((a, b) => b.amount - a.amount)
    .slice(0, 5)
}

/**
 * Memoized category breakdown calculation
 */
function calculateCategoryBreakdownMemoized(expenses: ExpenseRow[], totalAmount: number): CategoryData[] {
  const cacheKey = `categories-${expenses.map(e => e.id).join(',')}-${totalAmount}`
  const cached = calculationCache.get(cacheKey)
  
  if (cached && isCacheValid(cached.timestamp)) {
    return cached.data
  }

  const result = calculateCategoryBreakdown(expenses, totalAmount)
  calculationCache.set(cacheKey, { data: result, timestamp: Date.now() })
  
  // Clean old cache entries
  if (calculationCache.size > 100) {
    const oldKeys = Array.from(calculationCache.keys()).slice(0, 50)
    oldKeys.forEach(key => calculationCache.delete(key))
  }
  
  return result
}

/**
 * Calculate period data for specific date range
 */
function calculatePeriodData(
  expenses: ExpenseRow[], 
  start: Date, 
  end: Date, 
  label: string
): PeriodData {
  const periodExpenses = filterExpensesByDateRange(expenses, start, end)
  const amount = periodExpenses.reduce((sum, exp) => sum + exp.amount, 0)
  
  return {
    period: label,
    amount,
    count: periodExpenses.length
  }
}

/**
 * Process analytics data for a specific period type (with memoization)
 */
export function processAnalyticsData(
  expenses: ExpenseRow[], 
  selectedPeriod: PeriodType
): AnalyticsData {
  // Check cache first
  const cacheKey = getCacheKey(expenses, selectedPeriod)
  const cached = calculationCache.get(cacheKey)
  
  if (cached && isCacheValid(cached.timestamp)) {
    return cached.data
  }

  if (!expenses.length) {
    const labels = getPeriodLabels(selectedPeriod)
    const emptyResult = {
      currentPeriod: { period: labels.current, amount: 0, count: 0 },
      previousPeriod: { period: labels.previous, amount: 0, count: 0 },
      currentQuarter: { period: 'This Quarter', amount: 0, count: 0 },
      currentYear: { period: 'This Year', amount: 0, count: 0 },
      topCategories: [],
      trendData: []
    }
    
    calculationCache.set(cacheKey, { data: emptyResult, timestamp: Date.now() })
    return emptyResult
  }

  const ranges = getPeriodRanges(selectedPeriod)
  const labels = getPeriodLabels(selectedPeriod)

  // Calculate period data
  const currentPeriodData = calculatePeriodData(
    expenses, 
    ranges.current.start, 
    ranges.current.end, 
    labels.current
  )
  
  const previousPeriodData = calculatePeriodData(
    expenses, 
    ranges.previous.start, 
    ranges.previous.end, 
    labels.previous
  )

  const currentQuarterData = calculatePeriodData(
    expenses, 
    ranges.currentQuarter.start, 
    ranges.currentQuarter.end, 
    'This Quarter'
  )

  const currentYearData = calculatePeriodData(
    expenses, 
    ranges.currentYear.start, 
    ranges.currentYear.end, 
    'This Year'
  )

  // Calculate percentage change for current period
  const change = calculatePercentageChange(currentPeriodData.amount, previousPeriodData.amount)
  currentPeriodData.change = change

  // Calculate category breakdown for the selected period (memoized)
  const selectedPeriodExpenses = filterExpensesByDateRange(
    expenses, 
    ranges.current.start, 
    ranges.current.end
  )
  const topCategories = calculateCategoryBreakdownMemoized(selectedPeriodExpenses, currentPeriodData.amount)

  // Generate trend data
  const trendData = generateTrendData(selectedPeriod, expenses)

  const result = {
    currentPeriod: currentPeriodData,
    previousPeriod: previousPeriodData,
    currentQuarter: currentQuarterData,
    currentYear: currentYearData,
    topCategories,
    trendData
  }
  
  // Cache the result
  calculationCache.set(cacheKey, { data: result, timestamp: Date.now() })
  
  return result
}

/**
 * Get primary data based on selected period
 */
export function getPrimaryData(analyticsData: AnalyticsData | null, selectedPeriod: PeriodType): PeriodData | null {
  if (!analyticsData) return null
  
  switch (selectedPeriod) {
    case 'month':
      return analyticsData.currentPeriod
    case 'quarter':
      return analyticsData.currentQuarter
    case 'year':
      return analyticsData.currentYear
    default:
      return analyticsData.currentPeriod
  }
}

/**
 * Generate contextual insights for Quick Insights section
 */
export function generateQuickInsights(
  analyticsData: AnalyticsData | null, 
  selectedPeriod: PeriodType
): Array<{ label: string; value: string; trend?: 'up' | 'down' | 'neutral' }> {
  if (!analyticsData) return []

  const primaryData = getPrimaryData(analyticsData, selectedPeriod)
  if (!primaryData) return []

  const insights = []

  // Top category insight
  if (analyticsData.topCategories.length > 0) {
    insights.push({
      label: 'Top Category:',
      value: analyticsData.topCategories[0].category
    })
  }

  // Average per expense
  if (primaryData.count > 0) {
    const average = primaryData.amount / primaryData.count
    insights.push({
      label: 'Average per Expense:',
      value: average.toFixed(2)
    })
  }

  // Period comparison
  if (primaryData.change !== undefined) {
    const changeLabel = selectedPeriod === 'month' 
      ? 'This Month vs Last:' 
      : selectedPeriod === 'quarter' 
        ? 'This Quarter vs Last:' 
        : 'This Year vs Last:'
    
    insights.push({
      label: changeLabel,
      value: `${primaryData.change >= 0 ? '+' : ''}${primaryData.change.toFixed(1)}%`,
      trend: (primaryData.change > 0 ? 'up' : primaryData.change < 0 ? 'down' : 'neutral') as 'up' | 'down' | 'neutral'
    })
  }

  return insights
}