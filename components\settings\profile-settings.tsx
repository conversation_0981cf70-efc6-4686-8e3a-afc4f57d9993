'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useToast } from '@/components/ui/use-toast'
import { createSupabaseClient } from '@/lib/supabase'
import { useAuth } from '@/contexts/auth-context'
import { ProfilePictureManager } from '@/components/profile/profile-picture-manager'
import { PasswordManager } from '@/components/profile/password-manager'

interface ProfileSettingsProps {
  initialAvatarUrl: string | null
  initialFirstName: string
  initialLastName: string
  initialDisplayName: string
  initialJobTitle: string
  initialBusinessName: string
  onAvatarUpdate: (avatarUrl: string | null) => void
  onPersonalInfoUpdate: (info: {
    firstName: string
    lastName: string
    displayName: string
    jobTitle: string
  }) => void
}

interface ProfileUpdate {
  type: string;
  url: string | null;
  timestamp: number;
}

export function ProfileSettings({ 
  initialAvatarUrl,
  initialFirstName,
  initialLastName,
  initialDisplayName,
  initialJobTitle,
  initialBusinessName,
  onAvatarUpdate,
  onPersonalInfoUpdate
}: ProfileSettingsProps) {
  const { user } = useAuth()
  const { toast } = useToast()
  const supabase = createSupabaseClient()
  
  // Form states
  const [avatarUrl, setAvatarUrl] = useState<string | null>(initialAvatarUrl)
  const [firstName, setFirstName] = useState(initialFirstName)
  const [lastName, setLastName] = useState(initialLastName)
  const [displayName, setDisplayName] = useState(initialDisplayName)
  const [jobTitle, setJobTitle] = useState(initialJobTitle)
  const [isLoading, setIsLoading] = useState(false)
  const [lastUpdateTime, setLastUpdateTime] = useState<number>(0)
  const [hasChanges, setHasChanges] = useState(false)
  
  // Track initial values for comparison
  const [initialValues] = useState({
    firstName: initialFirstName,
    lastName: initialLastName,
    displayName: initialDisplayName,
    jobTitle: initialJobTitle
  })
  
  // Check for changes whenever form fields change
  useEffect(() => {
    const hasFormChanges = 
      firstName !== initialValues.firstName ||
      lastName !== initialValues.lastName ||
      displayName !== initialValues.displayName ||
      jobTitle !== initialValues.jobTitle;
    
    setHasChanges(hasFormChanges);
  }, [firstName, lastName, displayName, jobTitle, initialValues]);
  
  // Update local state when props change but don't trigger re-renders for every keystroke
  useEffect(() => {
    // Only update if there's a significant change from parent component
    if (initialAvatarUrl !== avatarUrl) {
      setAvatarUrl(initialAvatarUrl)
    }
  }, [initialAvatarUrl])

  // Listen for profile updates
  useEffect(() => {
    const handleProfileUpdate = (event: Event) => {
      if (event instanceof CustomEvent) {
        const updateData = event.detail as ProfileUpdate;
        
        // Only process avatar updates here, not form field updates
        if (updateData && updateData.timestamp > lastUpdateTime && updateData.type === 'avatar') {
          setAvatarUrl(updateData.url)
          setLastUpdateTime(updateData.timestamp)
        }
      }
    }

    window.addEventListener('profileUpdated', handleProfileUpdate)
    
    return () => {
      window.removeEventListener('profileUpdated', handleProfileUpdate)
    }
  }, [lastUpdateTime])
  
  // Check localStorage on component mount
  useEffect(() => {
    try {
      const lastUpdate = localStorage.getItem('lastProfileUpdate')
      if (lastUpdate) {
        const updateData: ProfileUpdate = JSON.parse(lastUpdate)
        if (updateData && updateData.timestamp > lastUpdateTime && updateData.type === 'avatar') {
          setAvatarUrl(updateData.url)
          setLastUpdateTime(updateData.timestamp)
        }
      }
    } catch (error) {
      console.error('Error parsing stored profile update:', error)
    }
  }, [])

  const handleAvatarUpdate = (newAvatarUrl: string | null) => {
    setAvatarUrl(newAvatarUrl)
    onAvatarUpdate(newAvatarUrl)
    setLastUpdateTime(new Date().getTime())
  }

  const handlePersonalInfoUpdate = async () => {
    // Only proceed if there are actual changes
    if (!hasChanges) return;
    
    setIsLoading(true)
    try {
      const { error } = await supabase
        .from('profiles')
        .update({ 
          first_name: firstName.trim(),
          last_name: lastName.trim(),
          display_name: displayName.trim(),
          job_title: jobTitle.trim()
        })
        .eq('id', user?.id)

      if (error) {
        toast({
          title: "Update Failed",
          description: `Failed to update personal information: ${error.message}`,
          variant: "destructive",
        })
      } else {
        // Update parent state and track new initial values
        onPersonalInfoUpdate({
          firstName,
          lastName,
          displayName,
          jobTitle
        })
        
        // Update initial values to reflect the new state
        initialValues.firstName = firstName;
        initialValues.lastName = lastName;
        initialValues.displayName = displayName;
        initialValues.jobTitle = jobTitle;
        
        // Reset changes flag
        setHasChanges(false);
        
        // Dispatch event to update display name in header
        const timestamp = new Date().getTime();
        window.dispatchEvent(new CustomEvent('profileUpdated', {
          detail: { 
            type: 'personalInfo',
            firstName,
            lastName,
            displayName,
            jobTitle,
            timestamp
          }
        }));
        
        setLastUpdateTime(timestamp)

        toast({
          title: "Personal Information Updated",
          description: "Your personal information has been updated successfully.",
        })
      }
    } catch (error) {
      toast({
        title: "Unexpected Error",
        description: "Something went wrong. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6 max-w-3xl">
      {/* Profile Picture Section */}
      <Card>
        <CardHeader>
          <CardTitle>Profile Picture</CardTitle>
        </CardHeader>
        <CardContent>
          <ProfilePictureManager 
            currentAvatarUrl={avatarUrl} 
            onAvatarUpdate={handleAvatarUpdate} 
          />
        </CardContent>
      </Card>

      {/* Account Information Section */}
      <Card>
        <CardHeader>
          <CardTitle>Account Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium text-gray-700">Email</Label>
                <p className="mt-1 text-sm text-gray-900">{user?.email || 'Not available'}</p>
                {user?.email_confirmed_at ? (
                  <p className="text-xs text-green-600 mt-1">Verified</p>
                ) : (
                  <p className="text-xs text-amber-600 mt-1">Not verified</p>
                )}
              </div>
              
              <div>
                <Label className="text-sm font-medium text-gray-700">Account Created</Label>
                <p className="mt-1 text-sm text-gray-900">
                  {user?.created_at 
                    ? new Date(user.created_at).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                      })
                    : 'Not available'}
                </p>
              </div>
              
              <div>
                <Label className="text-sm font-medium text-gray-700">Last Sign In</Label>
                <p className="mt-1 text-sm text-gray-900">
                  {user?.last_sign_in_at 
                    ? new Date(user.last_sign_in_at).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })
                    : 'Never'}
                </p>
              </div>
              
              <div>
                <Label className="text-sm font-medium text-gray-700">Account Type</Label>
                <p className="mt-1 text-sm text-gray-900">Standard Account</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Personal Information Section */}
      <Card>
        <CardHeader>
          <CardTitle>Personal Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="first-name" className="text-sm font-medium text-gray-700">First Name</Label>
                <Input
                  id="first-name"
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                  placeholder="Your first name"
                  className="text-sm h-9"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="last-name" className="text-sm font-medium text-gray-700">Last Name</Label>
                <Input
                  id="last-name"
                  value={lastName}
                  onChange={(e) => setLastName(e.target.value)}
                  placeholder="Your last name"
                  className="text-sm h-9"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="display-name" className="text-sm font-medium text-gray-700">Display Name</Label>
              <Input
                id="display-name"
                value={displayName}
                onChange={(e) => setDisplayName(e.target.value)}
                placeholder="Name displayed on your profile"
                className="text-sm h-9"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="job-title" className="text-sm font-medium text-gray-700">Job Title</Label>
              <Input
                id="job-title"
                value={jobTitle}
                onChange={(e) => setJobTitle(e.target.value)}
                placeholder="Your job title"
                className="text-sm h-9"
              />
            </div>
            
            <div className="pt-2">
              <Button 
                onClick={handlePersonalInfoUpdate}
                disabled={isLoading || !hasChanges}
                variant={hasChanges ? "default" : "outline"}
                className="h-9 px-3 text-sm"
              >
                {isLoading ? 'Updating...' : 'Update Personal Information'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Security Section */}
      <Card>
        <CardHeader>
          <CardTitle>Security</CardTitle>
        </CardHeader>
        <CardContent>
          <PasswordManager />
        </CardContent>
      </Card>
    </div>
  )
}