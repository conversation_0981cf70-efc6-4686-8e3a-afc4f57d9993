-- Function to accept an invitation and create a new user account
-- This function validates the invitation token and returns the necessary information
-- for the application to create the user account and complete the invitation process

create or replace function accept_invitation(
  token uuid
)
returns json
language plpgsql
security definer
as $$
declare
  invitation_record record;
  org_name text;
  org_count integer;
begin
  -- Validate input parameter
  if token is null then
    return json_build_object('success', false, 'message', 'Missing invitation token');
  end if;
  
  -- Find the invitation by token
  select * into invitation_record 
  from invitations 
  where id = token 
    and status = 'pending' 
    and expires_at > now();
  
  -- Check if invitation exists and is valid
  if not found then
    return json_build_object('success', false, 'message', 'Invalid or expired invitation token');
  end if;
  
  -- Get organization name for response
  select name into org_name 
  from organizations 
  where id = invitation_record.organization_id;
  
  -- Check if we found the organization
  GET DIAGNOSTICS org_count = ROW_COUNT;
  
  -- Handle case where organization name is not found
  if org_name is null or org_name = '' or org_count = 0 then
    -- Try a more explicit query to debug the issue
    select name into org_name 
    from organizations 
    where id = invitation_record.organization_id 
    and name is not null 
    and name != '';
    
    GET DIAGNOSTICS org_count = ROW_COUNT;
    
    if org_name is null or org_name = '' or org_count = 0 then
      org_name := 'My Organization';
    end if;
  end if;
  
  -- Return invitation details for the frontend to display
  return json_build_object(
    'success', true,
    'message', 'Invitation validated successfully',
    'email', invitation_record.email,
    'organization_name', org_name,
    'role', invitation_record.role,
    'organization_id', invitation_record.organization_id,
    'invited_by', invitation_record.invited_by,
    'token', token
  );
  
exception
  when others then
    return json_build_object('success', false, 'message', 'Error processing invitation: ' || sqlerrm);
end;
$$;

-- Function to complete the invitation process after user account creation
create or replace function complete_invitation(
  token uuid,
  user_id uuid,
  full_name text
)
returns json
language plpgsql
security definer
as $$
declare
  invitation_record record;
  org_name text;
  org_count integer;
begin
  -- Validate input parameters
  if token is null or user_id is null or full_name is null then
    return json_build_object('success', false, 'message', 'Missing required parameters');
  end if;
  
  -- Find the invitation by token
  select * into invitation_record 
  from invitations 
  where id = token 
    and status = 'pending' 
    and expires_at > now();
  
  -- Check if invitation exists and is valid
  if not found then
    return json_build_object('success', false, 'message', 'Invalid or expired invitation token');
  end if;
  
  -- Get organization name for response
  select name into org_name 
  from organizations 
  where id = invitation_record.organization_id;
  
  -- Check if we found the organization
  GET DIAGNOSTICS org_count = ROW_COUNT;
  
  -- Handle case where organization name is not found
  if org_name is null or org_name = '' or org_count = 0 then
    -- Try a more explicit query to debug the issue
    select name into org_name 
    from organizations 
    where id = invitation_record.organization_id 
    and name is not null 
    and name != '';
    
    GET DIAGNOSTICS org_count = ROW_COUNT;
    
    if org_name is null or org_name = '' or org_count = 0 then
      org_name := 'the organization';
    end if;
  end if;
  
  -- Update user's profile with full name
  update profiles 
  set 
    first_name = split_part(full_name, ' ', 1),
    last_name = case 
      when position(' ' in full_name) > 0 
      then substring(full_name from position(' ' in full_name) + 1) 
      else '' 
    end,
    display_name = full_name
  where id = user_id;
  
  -- Create organization member record
  insert into organization_members (
    organization_id,
    user_id,
    role,
    invited_by,
    accepted_at,
    name
  )
  values (
    invitation_record.organization_id,
    user_id,
    invitation_record.role,
    invitation_record.invited_by,
    now(),
    full_name
  );
  
  -- Update invitation status
  update invitations 
  set status = 'accepted', accepted_at = now()
  where id = token;
  
  -- Return success response
  return json_build_object(
    'success', true,
    'message', 'Invitation accepted successfully',
    'organization_name', org_name
  );
  
exception
  when others then
    return json_build_object('success', false, 'message', 'Error completing invitation: ' || sqlerrm);
end;
$$;