'use client'

import { useState, useEffect } from 'react'
import { 
  <PERSON><PERSON>, 
  <PERSON>alogContent, 
  Dialog<PERSON>eader, 
  DialogTitle
} from '@/components/ui/dialog'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { getSupabaseClient } from '@/lib/supabase'
import { useToast } from '@/components/ui/use-toast'
import { format } from 'date-fns'
import { type InventoryItem } from '../inventory/types'
import { useAuth } from '@/contexts/auth-context'

interface StockHistoryRecord {
  id: string
  created_at: string
  change_reason: string
  change_notes: string | null
  previous_quantity: number
  new_quantity: number
  quantity_change: number
  source_type: string | null
  source_reference: string | null
  created_by_name: string | null
}

interface StockHistoryModalProps {
  isOpen: boolean
  onClose: () => void
  selectedItem: InventoryItem | null
}

const REASON_LABELS: Record<string, string> = {
  'initial_stock_correction': 'Initial Stock Correction',
  'shipment_received': 'Shipment Received',
  'stock_take': 'Stock Take / Cycle Count',
  'damaged_goods': 'Damaged Goods',
  'lost_stolen': 'Lost / Stolen',
  'customer_return': 'Customer Return',
  'other': 'Other'
}

export function StockHistoryModal({ isOpen, onClose, selectedItem }: StockHistoryModalProps) {
  const [historyRecords, setHistoryRecords] = useState<StockHistoryRecord[]>([])
  const [loading, setLoading] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalRecords, setTotalRecords] = useState(0)
  const recordsPerPage = 10
  
  const { toast } = useToast()
  const { organizationId } = useAuth()
  // Use getSupabaseClient instead of createSupabaseClient to ensure we get a fresh client
  const supabase = getSupabaseClient()

  // Fetch stock history when selectedItem changes
  useEffect(() => {
    if (isOpen && selectedItem && organizationId) {
      fetchStockHistory()
    }
  }, [isOpen, selectedItem, currentPage, organizationId])

  const fetchStockHistory = async () => {
    if (!selectedItem || !organizationId) return
    
    setLoading(true)
    
    try {
      // Calculate the range for pagination
      const from = (currentPage - 1) * recordsPerPage
      const to = from + recordsPerPage - 1
      
      // Query stock history records
      let query = supabase
        .from('stock_history')
        .select(`
          id,
          created_at,
          change_reason,
          change_notes,
          previous_quantity,
          new_quantity,
          quantity_change,
          source_type,
          source_reference,
          created_by_name
        `, { count: 'exact' })
        .eq('organization_id', organizationId)
        .order('created_at', { ascending: false })
      
      // Filter by product or variant
      if (selectedItem.variantId) {
        query = query.eq('variant_id', selectedItem.variantId)
      } else {
        query = query.eq('product_id', selectedItem.productId)
      }
      
      // Apply pagination
      query = query.range(from, to)
      
      const { data, error, count } = await query
      
      if (error) throw error
      
      setHistoryRecords(data || [])
      setTotalRecords(count || 0)
    } catch (error) {
      console.error('Error fetching stock history:', error)
      toast({
        title: 'Error',
        description: 'Failed to load stock history records',
        variant: 'destructive'
      })
      setHistoryRecords([])
      setTotalRecords(0)
    } finally {
      setLoading(false)
    }
  }

  // Format reason for display
  const formatReason = (reason: string) => {
    return REASON_LABELS[reason] || reason
  }

  // Format quantity change for display
  const formatQuantityChange = (change: number) => {
    if (change > 0) return `+${change}`
    return change.toString()
  }

  // Get badge variant based on quantity change
  const getChangeVariant = (change: number) => {
    if (change > 0) return 'default'
    if (change < 0) return 'destructive'
    return 'secondary'
  }

  // Calculate total pages
  const totalPages = Math.ceil(totalRecords / recordsPerPage)

  // Handle page change
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage)
    }
  }

  // Early return if not open or no selected item
  if (!isOpen || !selectedItem) {
    return null
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-3xl max-h-[80vh] overflow-y-auto rounded-lg">
        <DialogHeader>
          <DialogTitle className="text-base font-semibold text-gray-900 dark:text-gray-100">
            Stock History
          </DialogTitle>
          <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            <div className="font-medium text-gray-900 dark:text-gray-100">{selectedItem.name}</div>
            <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">SKU: {selectedItem.sku}</div>
          </div>
        </DialogHeader>
        
        <div className="py-2">
          {loading ? (
            <div className="flex justify-center items-center h-32">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            </div>
          ) : historyRecords.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <p>No stock history records found</p>
            </div>
          ) : (
            <>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="text-xs font-medium text-gray-700 dark:text-gray-300 w-1/4">Date/Time</TableHead>
                      <TableHead className="text-xs font-medium text-gray-700 dark:text-gray-300">Source/User</TableHead>
                      <TableHead className="text-xs font-medium text-gray-700 dark:text-gray-300">Event/Reason</TableHead>
                      <TableHead className="text-xs font-medium text-gray-700 dark:text-gray-300 text-center">Quantity Change</TableHead>
                      <TableHead className="text-xs font-medium text-gray-700 dark:text-gray-300 text-center">Resulting Stock</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {historyRecords.map((record) => (
                      <TableRow key={record.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                        <TableCell className="py-3 text-xs text-gray-900 dark:text-gray-100">
                          {format(new Date(record.created_at), 'MMM d, yyyy h:mm a')}
                        </TableCell>
                        <TableCell className="py-3 text-xs text-gray-900 dark:text-gray-100">
                          {record.created_by_name || record.source_reference || record.source_type || 'Manual'}
                        </TableCell>
                        <TableCell className="py-3 text-xs text-gray-900 dark:text-gray-100">
                          {formatReason(record.change_reason)}
                          {record.change_notes && (
                            <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                              {record.change_notes}
                            </div>
                          )}
                        </TableCell>
                        <TableCell className="py-3 text-center">
                          <Badge variant={getChangeVariant(record.quantity_change)} className="text-xs font-medium">
                            {formatQuantityChange(record.quantity_change)}
                          </Badge>
                        </TableCell>
                        <TableCell className="py-3 text-center text-xs text-gray-900 dark:text-gray-100">
                          {record.new_quantity}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
              
              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between border-t border-gray-200 dark:border-gray-700 px-4 py-3 sm:px-6">
                  <div className="flex flex-1 justify-between sm:hidden">
                    <button
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                      className="relative inline-flex items-center rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
                    >
                      Previous
                    </button>
                    <button
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                      className="relative ml-3 inline-flex items-center rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
                    >
                      Next
                    </button>
                  </div>
                  <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                    <div>
                      <p className="text-sm text-gray-700 dark:text-gray-300">
                        Showing <span className="font-medium">{(currentPage - 1) * recordsPerPage + 1}</span> to{' '}
                        <span className="font-medium">
                          {Math.min(currentPage * recordsPerPage, totalRecords)}
                        </span>{' '}
                        of <span className="font-medium">{totalRecords}</span> results
                      </p>
                    </div>
                    <div>
                      <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                        <button
                          onClick={() => handlePageChange(currentPage - 1)}
                          disabled={currentPage === 1}
                          className="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 dark:text-gray-500 ring-1 ring-inset ring-gray-300 dark:ring-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 focus:z-20 focus:outline-offset-0 disabled:opacity-50"
                        >
                          <span className="sr-only">Previous</span>
                          <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fillRule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clipRule="evenodd" />
                          </svg>
                        </button>
                        
                        {/* Page numbers */}
                        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                          let pageNum;
                          if (totalPages <= 5) {
                            pageNum = i + 1;
                          } else if (currentPage <= 3) {
                            pageNum = i + 1;
                          } else if (currentPage >= totalPages - 2) {
                            pageNum = totalPages - 4 + i;
                          } else {
                            pageNum = currentPage - 2 + i;
                          }
                          
                          return (
                            <button
                              key={pageNum}
                              onClick={() => handlePageChange(pageNum)}
                              className={`relative inline-flex items-center px-4 py-2 text-sm font-semibold ${
                                pageNum === currentPage
                                  ? 'z-10 bg-blue-600 text-white focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600'
                                  : 'text-gray-900 dark:text-gray-100 ring-1 ring-inset ring-gray-300 dark:ring-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 focus:z-20 focus:outline-offset-0'
                              }`}
                            >
                              {pageNum}
                            </button>
                          );
                        })}
                        
                        <button
                          onClick={() => handlePageChange(currentPage + 1)}
                          disabled={currentPage === totalPages}
                          className="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 dark:text-gray-500 ring-1 ring-inset ring-gray-300 dark:ring-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 focus:z-20 focus:outline-offset-0 disabled:opacity-50"
                        >
                          <span className="sr-only">Next</span>
                          <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fillRule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clipRule="evenodd" />
                          </svg>
                        </button>
                      </nav>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}