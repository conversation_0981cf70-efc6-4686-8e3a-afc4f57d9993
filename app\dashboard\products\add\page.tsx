'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Package } from 'lucide-react'

export default function AddProductPage() {
  const router = useRouter()

  useEffect(() => {
    // Redirect to the main products page where the new add product modal is available
    router.push('/dashboard/products')
  }, [router])

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="px-4 py-3 border-b border-gray-200">
          <h3 className="text-base font-medium text-gray-900">Redirecting...</h3>
        </div>
        <div className="p-4">
          <Card className="max-w-2xl border-0 shadow-none">
            <CardHeader className="p-0">
              <CardTitle className="flex items-center space-x-2 text-base">
                <Package className="h-5 w-5" />
                <span>Redirecting to Products</span>
              </CardTitle>
              <CardDescription className="text-sm">
                Redirecting to the products page where you can add new products
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0 pt-4">
              <div className="flex items-center justify-center py-8">
                <div className="text-center">
                  <Package className="mx-auto h-12 w-12 text-gray-400 mb-4 animate-spin" />
                  <h3 className="text-base font-medium text-gray-900 mb-2">Redirecting...</h3>
                  <p className="text-sm text-gray-500">
                    Taking you to the products page where you can add new products
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}