'use client'

import { useState, useEffect, useMemo } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { MetricCard } from '@/components/ui/metric-card'
import { 
  Package, 
  DollarSign, 
  ShoppingCart, 
  TrendingUp,
  Plus,
  ShoppingCartIcon,
  FileText,
  Box,
  Loader2
} from 'lucide-react'
import { 
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js'
import { Bar } from 'react-chartjs-2'
import { useAuth } from '@/contexts/auth-context'
import { useCurrency } from '@/lib/currency'
import { useAllExpenses } from '@/hooks/use-expenses'
import { useAllProducts, type ExtendedProductRow } from '@/hooks/use-products'
import { useProfile } from '@/hooks/use-profile'
import { useAllOrders } from '@/hooks/use-orders'
import { PageHeader } from '@/components/ui/page-header'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
)

interface DashboardMetrics {
  totalRevenue: number
  netProfit: number
  lowStockItems: number
  openOrders: number
}

interface ProductVariant {
  stock_quantity?: number | null
  low_stock_threshold?: number | null
}

export default function DashboardPage() {
  const { user, organizationId } = useAuth()
  const { formatCurrency } = useCurrency()
  
  // Redirect to signin if user is not authenticated
  const router = useRouter()
  useEffect(() => {
    if (!user && !organizationId) {
      router.replace('/auth/signin')
    }
  }, [user, organizationId, router])
  
  // Use React Query hooks for data fetching
  const { data: expenses = [], isLoading: expensesLoading } = useAllExpenses(organizationId || undefined)
  const { data: productsData = [], isLoading: productsLoading } = useAllProducts(organizationId || undefined)
  const { data: profile, isLoading: profileLoading } = useProfile(user?.id)
  const { data: orders = [], isLoading: ordersLoading } = useAllOrders(organizationId || undefined) as { data: Array<{status: string, total: number, order_date: string}> | undefined, isLoading: boolean }
  
  const products = productsData as ExtendedProductRow[]
  
  // Show loading state while critical data is loading
  const isLoading = expensesLoading || productsLoading || profileLoading || ordersLoading
  
  // Handle case where user is not authenticated
  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center gap-2">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="text-lg">Redirecting...</span>
        </div>
      </div>
    )
  }
  
  // Calculate metrics based on fetched data
  const metrics = useMemo<DashboardMetrics>(() => {
    // Calculate total revenue from orders
    const totalRevenue = orders?.reduce((sum, order) => sum + (order.total || 0), 0) || 0
    
    // Calculate total expenses
    const totalExpenses = expenses?.reduce((sum, expense) => sum + (expense.amount || 0), 0) || 0
    
    // Calculate net profit (revenue minus expenses)
    const netProfit = totalRevenue - totalExpenses
    
    // Calculate low stock items
    let lowStockItems = 0
    products?.forEach((product: ExtendedProductRow) => {
      if (product.has_variants && product.variants) {
        // For variable products, check variants
        product.variants.forEach((variant: ProductVariant) => {
          const stockQty = variant.stock_quantity || 0
          const lowStockThreshold = variant.low_stock_threshold || 10
          if (stockQty > 0 && stockQty <= lowStockThreshold) {
            lowStockItems++
          }
        })
      } else {
        // For simple products, check the product itself
        const stockQty = product.stock_quantity || 0
        const lowStockThreshold = product.low_stock_threshold || 10
        if (stockQty > 0 && stockQty <= lowStockThreshold) {
          lowStockItems++
        }
      }
    })
    
    // Calculate open orders (only orders with pending statuses)
    const openOrders = orders?.filter(order => 
      order.status === 'Pending payment'
    ).length || 0
    
    return {
      totalRevenue,
      netProfit,
      lowStockItems,
      openOrders
    }
  }, [expenses, products, orders])
  
  // Calculate recent expenses (last 5) using useMemo instead of useEffect
  const recentExpenses = useMemo(() => {
    if (expenses && expenses.length > 0) {
      return expenses.slice(0, 5)
    }
    return []
  }, [expenses])
  
  // Calculate recent orders (last 5) using useMemo
  const recentOrders = useMemo(() => {
    if (orders && orders.length > 0) {
      return orders.slice(0, 5)
    }
    return []
  }, [orders])
  
  // Generate sales vs expenses data for the last 6 months
  const salesVsExpensesData = useMemo(() => {
    // Calculate actual data from the orders and expenses
    const months = []
    const salesData = []
    const expensesData = []
    
    // Get the last 6 months
    for (let i = 5; i >= 0; i--) {
      const date = new Date()
      date.setMonth(date.getMonth() - i)
      const month = date.toLocaleString('default', { month: 'short' })
      const year = date.getFullYear()
      months.push(`${month} ${year}`)
      
      // Calculate sales for this month
      const monthSales = orders?.filter(order => {
        if (!order.order_date) return false
        const orderDate = new Date(order.order_date)
        return orderDate.getMonth() === date.getMonth() && orderDate.getFullYear() === year
      }).reduce((sum, order) => sum + (order.total || 0), 0) || 0
      
      salesData.push(monthSales)
      
      // Calculate expenses for this month
      const monthExpenses = expenses?.filter(expense => {
        if (!expense.expense_date) return false
        const expenseDate = new Date(expense.expense_date)
        return expenseDate.getMonth() === date.getMonth() && expenseDate.getFullYear() === year
      }).reduce((sum, expense) => sum + (expense.amount || 0), 0) || 0
      
      expensesData.push(monthExpenses)
    }
    
    return {
      labels: months,
      datasets: [
        {
          label: 'Sales',
          data: salesData,
          backgroundColor: 'rgba(59, 130, 246, 0.8)',
          borderColor: 'rgba(59, 130, 246, 1)',
          borderWidth: 1,
        },
        {
          label: 'Expenses',
          data: expensesData,
          backgroundColor: 'rgba(156, 163, 175, 0.8)',
          borderColor: 'rgba(156, 163, 175, 1)',
          borderWidth: 1,
        },
      ],
    }
  }, [orders, expenses])
  
  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            return `${context.dataset.label}: ${formatCurrency(context.parsed.y)}`
          }
        }
      }
    },
    scales: {
      y: {
        ticks: {
          callback: function(value: any) {
            return formatCurrency(value)
          }
        }
      }
    }
  }
  
  return (
    <div className="space-y-6">
      {/* Standard Page Header */}
      <PageHeader 
        title="Dashboard" 
        description="Your business command center"
      />
      
      {/* 12-Column Grid Layout */}
      <div className="grid grid-cols-12 gap-6">
        {/* KPI Cards - Top Row */}
        <div className="col-span-12 md:col-span-3">
          <MetricCard
            title="Total Revenue"
            value={isLoading ? '...' : formatCurrency(metrics.totalRevenue)}
            subtitle="All-time revenue"
            icon={<DollarSign className="h-4 w-4 text-primary" />}
          />
        </div>
        
        <div className="col-span-12 md:col-span-3">
          <MetricCard
            title="Net Profit"
            value={isLoading ? '...' : formatCurrency(metrics.netProfit)}
            subtitle="Revenue minus expenses"
            icon={<TrendingUp className="h-4 w-4 text-primary" />}
          />
        </div>
        
        <div className="col-span-12 md:col-span-3">
          <MetricCard
            title="Low Stock Items"
            value={isLoading ? '...' : metrics.lowStockItems}
            subtitle="Items need restocking"
            icon={<ShoppingCart className="h-4 w-4 text-primary" />}
          />
        </div>
        
        <div className="col-span-12 md:col-span-3">
          <MetricCard
            title="Pending Orders"
            value={isLoading ? '...' : metrics.openOrders}
            subtitle="Orders awaiting completion"
            icon={<Package className="h-4 w-4 text-primary" />}
          />
        </div>
        
        {/* Sales vs Expenses Chart - Spans 8 columns */}
        <div className="col-span-12 lg:col-span-8">
          <Card className="shadow-md border border-gray-200 bg-white rounded-lg">
            <CardHeader>
              <CardTitle className="text-lg">Sales vs. Expenses</CardTitle>
              <CardDescription>
                Last 6 months comparison
              </CardDescription>
            </CardHeader>
            <CardContent className="h-80">
              <Bar data={salesVsExpensesData} options={chartOptions} />
            </CardContent>
          </Card>
        </div>
        
        {/* Quick Actions - Spans 4 columns */}
        <div className="col-span-12 lg:col-span-4">
          <Card className="shadow-md border border-gray-200 bg-white rounded-lg h-full">
            <CardHeader>
              <CardTitle className="text-lg">Quick Actions</CardTitle>
              <CardDescription>
                Common tasks
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button variant="outline" className="w-full justify-start">
                <Plus className="h-4 w-4 mr-2" />
                New Order
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Plus className="h-4 w-4 mr-2" />
                New Expense
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Plus className="h-4 w-4 mr-2" />
                New Product
              </Button>
            </CardContent>
          </Card>
        </div>
        
        {/* Recent Sales - Spans 8 columns */}
        <div className="col-span-12 lg:col-span-8">
          <Card className="shadow-md border border-gray-200 bg-white rounded-lg">
            <CardHeader>
              <CardTitle className="text-lg">Recent Sales</CardTitle>
              <CardDescription>
                Last 5 orders
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-3">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="flex justify-between items-center">
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                    </div>
                  ))}
                </div>
              ) : recentOrders.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="text-xs">Customer</TableHead>
                      <TableHead className="text-xs">Order #</TableHead>
                      <TableHead className="text-xs">Status</TableHead>
                      <TableHead className="text-right text-xs">Total</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {recentOrders.map((order: any) => (
                      <TableRow key={order.id}>
                        <TableCell className="text-xs py-2">
                          {order.customer_name || 'N/A'}
                        </TableCell>
                        <TableCell className="text-xs py-2 font-medium">
                          #{order.order_number}
                        </TableCell>
                        <TableCell className="text-xs py-2">
                          <span className="inline-flex items-center rounded-full bg-gray-100 px-2 py-1 text-xs font-medium text-gray-800">
                            {order.status}
                          </span>
                        </TableCell>
                        <TableCell className="text-right text-xs py-2 font-medium">
                          {formatCurrency(order.total || 0)}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <p className="text-sm text-gray-500 py-4 text-center">No recent orders</p>
              )}
            </CardContent>
          </Card>
        </div>
        
        {/* Recent Expenses - Spans 4 columns */}
        <div className="col-span-12 lg:col-span-4">
          <Card className="shadow-md border border-gray-200 bg-white rounded-lg h-full">
            <CardHeader>
              <CardTitle className="text-lg">Recent Expenses</CardTitle>
              <CardDescription>
                Last 5 expenses
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-3">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="flex justify-between items-center">
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                    </div>
                  ))}
                </div>
              ) : recentExpenses.length > 0 ? (
                <div className="space-y-3">
                  {recentExpenses.map((expense: any) => (
                    <div key={expense.id} className="flex justify-between items-center">
                      <div>
                        <p className="text-sm font-medium">{expense.description}</p>
                        <p className="text-xs text-gray-500">
                          {expense.expense_date ? new Date(expense.expense_date).toLocaleDateString() : 'N/A'}
                        </p>
                      </div>
                      <p className="text-sm font-medium text-red-600">
                        {formatCurrency(expense.amount || 0)}
                      </p>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500 py-4 text-center">No recent expenses</p>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}