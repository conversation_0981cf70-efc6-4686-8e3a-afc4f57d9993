class SignUpTemplate {
  generate() {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - ONKO</title>
    <!-- TODO: Replace with PostCSS Tailwind in production - CDN is for development only -->
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full space-y-8 p-8">
        <div class="text-center">
            <h1 class="text-4xl font-bold text-gray-900 mb-2">ONKO</h1>
            <p class="text-gray-600 mb-8">Business Management Platform</p>
            <h2 class="text-2xl font-bold text-gray-900">Sign Up</h2>
            <p class="text-gray-600">Create your ONKO account to get started</p>
        </div>
        
        <form id="signupForm" class="space-y-6">
            <div>
                <label class="block text-sm font-medium text-gray-700">Business Name</label>
                <input id="businessName" type="text" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="Your Business Name" required>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Email</label>
                <input id="email" type="email" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="<EMAIL>" required>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Password</label>
                <input id="password" type="password" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="Enter your password" required>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Confirm Password</label>
                <input id="confirmPassword" type="password" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="Confirm your password" required>
            </div>
            <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">Sign Up</button>
        </form>
        
        <div class="text-center space-y-2">
            <a href="/auth/signin" class="text-sm text-blue-600 hover:underline">Already have an account? Sign in</a>
        </div>
        
        <div class="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <p class="text-sm text-green-800">
                <strong>✅ Production Ready:</strong> Real user registration with profile creation and secure authentication.
            </p>
        </div>

        <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p class="text-sm text-blue-800">
                <strong>Demo:</strong> Click "Sign Up" to see the registration flow!
            </p>
        </div>
    </div>
    
    <script>
        // Add error handling for debugging
        window.onerror = function(msg, url, line, col, error) {
            console.error('JavaScript Error:', msg, 'at', url, ':', line);
            alert('JavaScript Error: ' + msg);
        };
        
        console.log('Script starting to load...');
        
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOMContentLoaded event fired');
            console.log('Signup page loaded');
            
            const form = document.getElementById('signupForm');
            if (!form) {
                console.error('Form not found!');
                alert('Form not found! Check the HTML.');
                return;
            }
            console.log('Form found:', form);
            
            form.addEventListener('submit', async function(e) {
                console.log('Form submit event triggered');
                e.preventDefault();
                console.log('Form submitted');
                
                const businessName = document.getElementById('businessName').value;
                const email = document.getElementById('email').value;
                const password = document.getElementById('password').value;
                const confirmPassword = document.getElementById('confirmPassword').value;
                const submitButton = e.target.querySelector('button[type="submit"]');
                
                console.log('Form data:', { businessName, email, password: '***', confirmPassword: '***' });
                
                // Client-side validation
                if (!businessName || !email || !password || !confirmPassword) {
                    alert('Please fill in all fields');
                    return;
                }
                
                if (password !== confirmPassword) {
                    alert('Passwords do not match');
                    return;
                }
                
                if (password.length < 6) {
                    alert('Password must be at least 6 characters long');
                    return;
                }
                
                // Disable button and show loading state
                submitButton.disabled = true;
                submitButton.textContent = 'Creating Account...';
                console.log('Calling signup API...');
                
                try {
                    // Call our auth API endpoint
                    console.log('About to fetch...');
                    const response = await fetch('/api/auth/signup', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            email: email,
                            password: password,
                            businessName: businessName
                        })
                    });
                    
                    console.log('Fetch completed. Response status:', response.status);
                    const result = await response.json();
                    console.log('API response:', result);
                    
                    if (result.success) {
                        // Show success message and redirect to confirmation page
                        console.log('Signup successful, showing confirmation');
                        showConfirmationMessage(email);
                    } else {
                        console.error('Signup failed:', result.error);
                        alert('Registration failed: ' + result.error);
                    }
                } catch (error) {
                    console.error('Registration error:', error);
                    alert('Network error occurred. Please try again. Error: ' + error.message);
                } finally {
                    // Re-enable button
                    submitButton.disabled = false;
                    submitButton.textContent = 'Sign Up';
                }
            });
            
            console.log('Event listener attached successfully');
        });
        
        console.log('Script finished loading...');
        
        function showConfirmationMessage(email) {
            // Replace the form with confirmation message
            document.body.innerHTML = 
                '<div class="bg-gray-50 min-h-screen flex items-center justify-center">' +
                '<div class="max-w-md w-full space-y-8 p-8">' +
                '<div class="text-center">' +
                '<div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">' +
                '<svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">' +
                '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>' +
                '</svg>' +
                '</div>' +
                '<h1 class="text-4xl font-bold text-gray-900 mb-2">ONKO</h1>' +
                '<h2 class="text-2xl font-bold text-gray-900 mb-4">Account Created!</h2>' +
                '<p class="text-gray-600 mb-6">We have sent a confirmation email to:</p>' +
                '<p class="text-lg font-medium text-blue-600 mb-6">' + email + '</p>' +
                '</div>' +
                '<div class="bg-blue-50 border border-blue-200 rounded-lg p-6">' +
                '<h3 class="text-lg font-medium text-blue-900 mb-2">Next Steps:</h3>' +
                '<ol class="text-sm text-blue-800 space-y-2">' +
                '<li>1. Check your email inbox (and spam folder)</li>' +
                '<li>2. Click the confirmation link in the email</li>' +
                '<li>3. Return here to sign in to your account</li>' +
                '</ol>' +
                '</div>' +
                '<div class="text-center space-y-4">' +
                '<button onclick="resendConfirmation()" class="text-sm text-blue-600 hover:underline">' +
                'Did not receive the email? Resend confirmation' +
                '</button>' +
                '<div>' +
                '<a href="/auth/signin" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">' +
                'Go to Sign In' +
                '</a>' +
                '</div>' +
                '</div>' +
                '<div class="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">' +
                '<p class="text-sm text-green-800">' +
                '<strong>✅ Email Confirmation Required:</strong> For security, you must confirm your email address before accessing your ONKO account.' +
                '</p>' +
                '</div>' +
                '</div>' +
                '</div>';
        }
        
        function resendConfirmation() {
            // Future implementation: call resend confirmation API
            alert('Confirmation email resent! Please check your inbox.');
        }
    </script>
</body>
</html>
    `;
  }
}

module.exports = new SignUpTemplate();