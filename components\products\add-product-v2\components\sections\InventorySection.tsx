import React from 'react'
import { Input } from '@/components/ui/input'
import { useProductForm } from '../../context/ProductFormContext'
import { SectionHeader } from '../shared/SectionHeader'
import { FormField } from '../shared/FormField'

export function InventorySection() {
  const { formData, updateFormData, errors } = useProductForm()

  return (
    <div className="max-w-4xl">
      <SectionHeader
        title="Inventory Management"
        description="Stock levels and inventory settings"
      />

      <div className="space-y-4">
        {/* Simple Product Inventory */}
        {!formData.has_variants && (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                label="Stock Quantity"
                required
                error={errors.stock_quantity}
              >
                <Input
                  type="number"
                  min="0"
                  value={formData.stock_quantity || ''}
                  onChange={(e) => updateFormData('stock_quantity', parseInt(e.target.value) || 0)}
                  placeholder="0"
                  className="h-8 text-xs placeholder:text-xs"
                />
              </FormField>

              <FormField
                label="Low Stock Threshold"
                error={errors.low_stock_threshold}
              >
                <Input
                  type="number"
                  min="0"
                  value={formData.low_stock_threshold || ''}
                  onChange={(e) => updateFormData('low_stock_threshold', parseInt(e.target.value) || 10)}
                  placeholder="10"
                  className="h-8 text-xs placeholder:text-xs"
                />
              </FormField>
            </div>

            {/* Stock Status Indicator */}
            {formData.stock_quantity !== undefined && (
              <div className="mt-4 p-3 bg-gray-50 border border-gray-200 rounded-md">
                <h4 className="font-medium text-gray-900 text-xs mb-2">Stock Status</h4>
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${
                    formData.stock_quantity === 0
                      ? 'bg-red-500'
                      : formData.stock_quantity <= (formData.low_stock_threshold || 10)
                      ? 'bg-yellow-500'
                      : 'bg-green-500'
                  }`} />
                  <span className="text-xs text-gray-600">
                    {formData.stock_quantity === 0
                      ? 'Out of Stock'
                      : formData.stock_quantity <= (formData.low_stock_threshold || 10)
                      ? 'Low Stock'
                      : 'In Stock'
                    }
                  </span>
                  <span className="text-xs text-gray-500">
                    ({formData.stock_quantity} units)
                  </span>
                </div>
              </div>
            )}
          </>
        )}

        {/* Variable Product Info */}
        {formData.has_variants && (
          <div className="text-center py-8 bg-blue-50 border border-blue-200 rounded-md">
            <div className="text-blue-500 mb-2">
              <svg className="w-8 h-8 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M5 3a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2V5a2 2 0 00-2-2H5zm0 2h10v7h-2l-1-2H8l-1 2H5V5z" clipRule="evenodd" />
              </svg>
            </div>
            <h3 className="font-medium text-blue-900 text-sm mb-1">Variable Product Inventory</h3>
            <p className="text-xs text-blue-700 mb-4">
              For variable products, you'll manage inventory for each variant separately after creating the base product.
            </p>
            <div className="text-xs text-blue-600 bg-blue-100 rounded px-3 py-2 inline-block">
              📦 Each variant will have its own stock quantity and low stock threshold
            </div>
          </div>
        )}

        {/* Inventory Tips */}
        <div className="mt-6 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
          <div className="flex items-start gap-2">
            <div className="text-yellow-500 mt-0.5">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div>
              <h4 className="font-medium text-yellow-900 text-xs mb-1">
                Inventory Tips
              </h4>
              <ul className="text-xs text-yellow-700 space-y-1">
                <li>• Set realistic low stock thresholds to avoid stockouts</li>
                <li>• You can adjust inventory levels later in the Products table</li>
                <li>• Stock movements will be tracked automatically</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
