'use client'

import { useState, useEffect, useMemo, useCallback, useRef } from 'react'
import { use<PERSON><PERSON>er, useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { useSessionState } from '@/lib/use-persisted-state'
import { 
  Search, 
  Filter, 
  X, 
  SlidersHorizontal,
  RotateCcw
} from 'lucide-react'
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Label } from '@/components/ui/label'
import { DatePicker } from '@/components/ui/date-picker'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { type ProductRow } from '@/lib/supabase'
import { useCurrency } from '@/lib/currency'
import { cn } from '@/lib/utils'
import { type ProductFilters, INITIAL_PRODUCT_FILTERS } from './product-filters'

// Extended ProductRow type with category_name property (added during data transformation)
// Updated to match organization-based model
type ExtendedProductRow = {
  id: string
  organization_id: string
  category_id: string | null
  name: string
  description: string | null
  brand: string | null
  supplier: string | null
  base_sku: string | null
  has_variants: boolean
  track_inventory: boolean
  is_active: boolean
  base_cost: number | null
  packaging_cost: number | null
  price: number | null
  size: string | null
  color: string | null
  stock_quantity: number | null
  low_stock_threshold: number | null
  barcode: string | null
  image_url: string | null
  sale_price: number | null
  sale_start_date: string | null
  sale_end_date: string | null
  batch_reference: string | null
  purchase_date: string | null
  notes: string | null
  total_cost: number | null
  effective_price: number | null
  is_on_sale: boolean | null
  profit_amount: number | null
  profit_margin: number | null
  created_at: string
  updated_at: string
  category_name?: string | null
}

interface ProductTableHeaderProps {
  products: ExtendedProductRow[]
  filters: ProductFilters
  onFiltersChange: (filters: ProductFilters) => void
  onFilteredProductsChange: (filteredProducts: ExtendedProductRow[]) => void
  isMobile?: boolean
  className?: string
  enableFilterUrlSync?: boolean // Enable URL sync for filters
  categories?: { value: string; label: string }[] // Add categories prop
}

export function ProductTableHeader({
  products,
  filters,
  onFiltersChange,
  onFilteredProductsChange,
  isMobile = false,
  className,
  enableFilterUrlSync = false,
  categories = [] // Destructure categories prop with default empty array
}: ProductTableHeaderProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [searchDebounce, setSearchDebounce] = useState(filters.searchQuery)
  const [isFilterSheetOpen, setIsFilterSheetOpen] = useSessionState('product-filter-sheet-open', false)
  const [isExpanded, setIsExpanded] = useState(false)
  
  const { formatCurrency } = useCurrency()
  
  // Create a stable reference to onFilteredProductsChange to prevent infinite loops
  const onFilteredProductsChangeRef = useRef(onFilteredProductsChange);
  
  // Update ref when callback changes
  useEffect(() => {
    onFilteredProductsChangeRef.current = onFilteredProductsChange;
  }, [onFilteredProductsChange]);

  // Initialize with all products - only run once on mount or when products change significantly
  useEffect(() => {
    if (products.length > 0) {
      onFilteredProductsChangeRef.current(products);
    }
  }, [products]); // Removed onFilteredProductsChange from dependencies

  // Sync filter changes with URL parameters (optional deep linking support)
  useEffect(() => {
    if (!enableFilterUrlSync) return
    
    const currentUrl = new URL(window.location.href)
    const hasActiveFilters = 
      filters.searchQuery.trim() || 
      filters.categories.length > 0 || 
      filters.suppliers.length > 0 ||
      filters.stockStatus.length > 0 ||
      filters.productType.length > 0 ||
      filters.saleStatus.length > 0 ||
      filters.priceRange.min !== null || 
      filters.priceRange.max !== null
    
    if (hasActiveFilters) {
      // Encode filters in URL for deep linking
      if (filters.searchQuery.trim()) {
        currentUrl.searchParams.set('search', filters.searchQuery)
      } else {
        currentUrl.searchParams.delete('search')
      }
      
      if (filters.categories.length > 0) {
        currentUrl.searchParams.set('categories', filters.categories.join(','))
      } else {
        currentUrl.searchParams.delete('categories')
      }
      
      if (filters.suppliers.length > 0) {
        currentUrl.searchParams.set('suppliers', filters.suppliers.join(','))
      } else {
        currentUrl.searchParams.delete('suppliers')
      }
    } else {
      // Clear filter parameters when no filters are active
      currentUrl.searchParams.delete('search')
      currentUrl.searchParams.delete('categories')
      currentUrl.searchParams.delete('suppliers')
    }
    
    // Update URL without triggering navigation
    if (currentUrl.toString() !== window.location.href) {
      router.replace(currentUrl.toString(), { scroll: false })
    }
  }, [filters, enableFilterUrlSync, router])

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      onFiltersChange({
        ...filters,
        searchQuery: searchDebounce
      })
    }, 300)
    
    return () => clearTimeout(timer)
  }, [searchDebounce, filters, onFiltersChange])

  // Extract unique options from products
  const availableOptions = useMemo(() => {
    const suppliers = new Set<string>()
    
    products.forEach(product => {
      if (product.supplier) suppliers.add(product.supplier)
    })
    
    return {
      categories: categories, // Use the passed categories prop
      suppliers: Array.from(suppliers).map(supplier => ({ value: supplier, label: supplier })),
      stockStatus: [
        { value: 'in_stock', label: 'In Stock' },
        { value: 'low_stock', label: 'Low Stock' },
        { value: 'out_of_stock', label: 'Out of Stock' },
      ],
      productType: [
        { value: 'simple', label: 'Simple Product' },
        { value: 'variable', label: 'Variable Product' },
      ],
      saleStatus: [
        { value: 'on_sale', label: 'On Sale' },
        { value: 'regular', label: 'Regular Price' },
      ]
    }
  }, [products, categories])

  // Filter products based on current filters
  const filteredProducts = useMemo(() => {
    let result = products as ExtendedProductRow[]

    // Text search
    if (filters.searchQuery.trim()) {
      const query = filters.searchQuery.toLowerCase()
      result = result.filter(product => 
        product.name?.toLowerCase().includes(query) ||
        product.description?.toLowerCase().includes(query) ||
        product.brand?.toLowerCase().includes(query) ||
        product.base_sku?.toLowerCase().includes(query) ||
        product.supplier?.toLowerCase().includes(query)
      )
    }

    // Category filter
    if (filters.categories.length > 0) {
      result = result.filter(product => 
        product.category_name && filters.categories.includes(product.category_name)
      )
    }

    // Supplier filter
    if (filters.suppliers.length > 0) {
      result = result.filter(product => 
        product.supplier && filters.suppliers.includes(product.supplier)
      )
    }

    // Stock status filter
    if (filters.stockStatus.length > 0) {
      result = result.filter(product => {
        const stockQty = product.stock_quantity || 0
        const lowStockThreshold = product.low_stock_threshold || 10
        
        let status: 'in_stock' | 'low_stock' | 'out_of_stock'
        if (stockQty === 0) {
          status = 'out_of_stock'
        } else if (stockQty <= lowStockThreshold) {
          status = 'low_stock'
        } else {
          status = 'in_stock'
        }
        
        return filters.stockStatus.includes(status)
      })
    }

    // Product type filter
    if (filters.productType.length > 0) {
      result = result.filter(product => {
        const type = product.has_variants ? 'variable' : 'simple'
        return filters.productType.includes(type as any)
      })
    }

    // Sale status filter
    if (filters.saleStatus.length > 0) {
      result = result.filter(product => {
        const isOnSale = product.is_on_sale || false
        const status = isOnSale ? 'on_sale' : 'regular'
        return filters.saleStatus.includes(status as any)
      })
    }

    // Price range filter
    if (filters.priceRange.min !== null || filters.priceRange.max !== null) {
      result = result.filter(product => {
        const price = product.effective_price || product.price || 0
        
        if (filters.priceRange.min !== null && price < filters.priceRange.min) {
          return false
        }
        
        if (filters.priceRange.max !== null && price > filters.priceRange.max) {
          return false
        }
        
        return true
      })
    }

    return result
  }, [products, filters])

  // Update filtered products when they change (using ref to avoid infinite loops)
  useEffect(() => {
    onFilteredProductsChangeRef.current(filteredProducts)
  }, [filteredProducts]) // Removed onFilteredProductsChange from dependencies
  
  // Count active filters
  const activeFilterCount = useMemo(() => {
    let count = 0
    if (filters.searchQuery.trim()) count++
    if (filters.categories.length > 0) count += filters.categories.length
    if (filters.suppliers.length > 0) count += filters.suppliers.length
    if (filters.stockStatus.length > 0) count += filters.stockStatus.length
    if (filters.productType.length > 0) count += filters.productType.length
    if (filters.saleStatus.length > 0) count += filters.saleStatus.length
    if (filters.priceRange.min !== null || filters.priceRange.max !== null) count++
    return count
  }, [filters])

  // Clear all filters
  const clearAllFilters = useCallback(() => {
    setSearchDebounce('')
    onFiltersChange(INITIAL_PRODUCT_FILTERS)
  }, [onFiltersChange])

  // Multi-select toggle handlers
  const handleCategoryToggle = useCallback((category: string) => {
    const newCategories = filters.categories.includes(category)
      ? filters.categories.filter(c => c !== category)
      : [...filters.categories, category]
    
    onFiltersChange({
      ...filters,
      categories: newCategories
    })
  }, [filters, onFiltersChange])

  const handleSupplierToggle = useCallback((supplier: string) => {
    const newSuppliers = filters.suppliers.includes(supplier)
      ? filters.suppliers.filter(s => s !== supplier)
      : [...filters.suppliers, supplier]
    
    onFiltersChange({
      ...filters,
      suppliers: newSuppliers
    })
  }, [filters, onFiltersChange])

  const handleStockStatusToggle = useCallback((status: 'in_stock' | 'low_stock' | 'out_of_stock') => {
    const newStatuses = filters.stockStatus.includes(status)
      ? filters.stockStatus.filter(s => s !== status)
      : [...filters.stockStatus, status]
    
    onFiltersChange({
      ...filters,
      stockStatus: newStatuses
    })
  }, [filters, onFiltersChange])

  const handleProductTypeToggle = useCallback((type: 'simple' | 'variable') => {
    const newTypes = filters.productType.includes(type)
      ? filters.productType.filter(t => t !== type)
      : [...filters.productType, type]
    
    onFiltersChange({
      ...filters,
      productType: newTypes
    })
  }, [filters, onFiltersChange])

  const handleSaleStatusToggle = useCallback((status: 'on_sale' | 'regular') => {
    const newStatuses = filters.saleStatus.includes(status)
      ? filters.saleStatus.filter(s => s !== status)
      : [...filters.saleStatus, status]
    
    onFiltersChange({
      ...filters,
      saleStatus: newStatuses
    })
  }, [filters, onFiltersChange])

  // Generate active filter badges
  const getActiveFilterBadges = useCallback(() => {
    const badges = []
    
    if (filters.searchQuery.trim()) {
      badges.push(
        <Badge key="search" className="bg-blue-50 text-blue-700 border-none text-xs py-1 px-2 flex items-center gap-1.5">
          Search: "{filters.searchQuery}"
          <Button 
            variant="ghost" 
            size="sm" 
            className="ml-1 h-3.5 w-3.5 p-0 hover:bg-blue-100"
            onClick={() => {
              setSearchDebounce('')
              onFiltersChange({ ...filters, searchQuery: '' })
            }}
          >
            <X className="h-3 w-3 cursor-pointer" />
          </Button>
        </Badge>
      )
    }
    
    // Add other filter badges as needed
    if (filters.categories.length > 0) {
      badges.push(
        <Badge key="categories" className="bg-blue-50 text-blue-700 border-none text-xs py-1 px-2 flex items-center gap-1.5">
          Categories: {filters.categories.join(', ')}
          <Button 
            variant="ghost" 
            size="sm" 
            className="ml-1 h-3.5 w-3.5 p-0 hover:bg-blue-100"
            onClick={() => onFiltersChange({ ...filters, categories: [] })}
          >
            <X className="h-3 w-3 cursor-pointer" />
          </Button>
        </Badge>
      )
    }
    
    if (filters.suppliers.length > 0) {
      badges.push(
        <Badge key="suppliers" className="bg-blue-50 text-blue-700 border-none text-xs py-1 px-2 flex items-center gap-1.5">
          Suppliers: {filters.suppliers.join(', ')}
          <Button 
            variant="ghost" 
            size="sm" 
            className="ml-1 h-3.5 w-3.5 p-0 hover:bg-blue-100"
            onClick={() => onFiltersChange({ ...filters, suppliers: [] })}
          >
            <X className="h-3 w-3 cursor-pointer" />
          </Button>
        </Badge>
      )
    }
    
    if (filters.stockStatus.length > 0) {
      badges.push(
        <Badge key="stock-status" className="bg-blue-50 text-blue-700 border-none text-xs py-1 px-2 flex items-center gap-1.5">
          Stock Status: {filters.stockStatus.join(', ')}
          <Button 
            variant="ghost" 
            size="sm" 
            className="ml-1 h-3.5 w-3.5 p-0 hover:bg-blue-100"
            onClick={() => onFiltersChange({ ...filters, stockStatus: [] })}
          >
            <X className="h-3 w-3 cursor-pointer" />
          </Button>
        </Badge>
      )
    }
    
    if (filters.productType.length > 0) {
      badges.push(
        <Badge key="product-type" className="bg-blue-50 text-blue-700 border-none text-xs py-1 px-2 flex items-center gap-1.5">
          Product Type: {filters.productType.join(', ')}
          <Button 
            variant="ghost" 
            size="sm" 
            className="ml-1 h-3.5 w-3.5 p-0 hover:bg-blue-100"
            onClick={() => onFiltersChange({ ...filters, productType: [] })}
          >
            <X className="h-3 w-3 cursor-pointer" />
          </Button>
        </Badge>
      )
    }
    
    if (filters.saleStatus.length > 0) {
      badges.push(
        <Badge key="sale-status" className="bg-blue-50 text-blue-700 border-none text-xs py-1 px-2 flex items-center gap-1.5">
          Sale Status: {filters.saleStatus.join(', ')}
          <Button 
            variant="ghost" 
            size="sm" 
            className="ml-1 h-3.5 w-3.5 p-0 hover:bg-blue-100"
            onClick={() => onFiltersChange({ ...filters, saleStatus: [] })}
          >
            <X className="h-3 w-3 cursor-pointer" />
          </Button>
        </Badge>
      )
    }
    
    if (filters.priceRange.min !== null || filters.priceRange.max !== null) {
      const rangeText = `${filters.priceRange.min !== null ? `$${filters.priceRange.min}` : 'Any'} - ${filters.priceRange.max !== null ? `$${filters.priceRange.max}` : 'Any'}`
      badges.push(
        <Badge key="price-range" className="bg-blue-50 text-blue-700 border-none text-xs py-1 px-2 flex items-center gap-1.5">
          Price Range: {rangeText}
          <Button 
            variant="ghost" 
            size="sm" 
            className="ml-1 h-3.5 w-3.5 p-0 hover:bg-blue-100"
            onClick={() => onFiltersChange({ ...filters, priceRange: { min: null, max: null } })}
          >
            <X className="h-3 w-3 cursor-pointer" />
          </Button>
        </Badge>
      )
    }
    
    return badges
  }, [filters, onFiltersChange])

  return (
    <div className={cn("space-y-2", className)}>
      {/* Top tier: Always visible filters */}
      <div className="flex flex-col sm:flex-row gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-gray-500" />
          <Input
            placeholder="Search products..."
            value={searchDebounce}
            onChange={(e) => setSearchDebounce(e.target.value)}
            className={cn(
              "pl-8 h-8 text-xs",
              isMobile && "text-sm min-h-[40px] touch-manipulation"
            )}
            autoComplete="off"
            spellCheck="false"
            autoCapitalize="none"
            autoCorrect="off"
          />
        </div>
        <div className="flex items-center gap-2">
          {!isMobile && (
            <Button 
              variant="outline" 
              size="sm" 
              className="h-8 text-xs relative"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              <SlidersHorizontal className="h-3.5 w-3.5" />
              {activeFilterCount > 0 && (
                <span className="absolute -top-1 -right-1 flex items-center justify-center w-4 h-4 rounded-full bg-red-500 text-white text-[10px] font-medium">
                  {activeFilterCount}
                </span>
              )}
            </Button>
          )}
          
          {isMobile ? (
            <Sheet open={isFilterSheetOpen} onOpenChange={setIsFilterSheetOpen}>
              <SheetTrigger asChild>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="relative touch-manipulation h-8 px-3 text-xs border-gray-300"
                >
                  <SlidersHorizontal className="h-3.5 w-3.5 mr-2" />
                  <span className="font-medium">Filters</span>
                  {activeFilterCount > 0 && (
                    <span className="ml-1.5 flex items-center justify-center w-4 h-4 rounded-full bg-red-500 text-white text-xs font-medium">
                      {activeFilterCount}
                    </span>
                  )}
                </Button>
              </SheetTrigger>
              <SheetContent 
                side="bottom" 
                className="h-[85vh] overflow-y-auto bg-background/95 backdrop-blur-sm"
                onOpenAutoFocus={(e) => e.preventDefault()}
              >
                <SheetHeader className="text-left pb-3 border-b">
                  <SheetTitle className="text-base font-semibold">Filter Products</SheetTitle>
                </SheetHeader>
                <div className="py-3 space-y-4">
                  {/* Categories filter */}
                  <div className="space-y-2">
                    <Label className="text-xs font-medium text-foreground">Categories</Label>
                    <div className="border border-border rounded-md bg-background/50 shadow-xs">
                      <div className="max-h-32 overflow-y-auto p-2 space-y-1">
                        {availableOptions.categories.length > 0 ? (
                          availableOptions.categories.map(category => (
                            <label
                              key={category.value}
                              className={cn(
                                "flex items-center space-x-2 p-1.5 rounded transition-all cursor-pointer group",
                                "hover:bg-accent/50 hover:text-accent-foreground text-xs"
                              )}
                            >
                              <input
                                type="checkbox"
                                checked={filters.categories.includes(category.value)}
                                onChange={() => handleCategoryToggle(category.value)}
                                className="h-3 rounded border-border text-primary focus:ring-primary/20 focus:ring-1"
                              />
                              <span className="font-medium">{category.label}</span>
                            </label>
                          ))
                        ) : (
                          <div className="text-center py-3 text-muted-foreground">
                            <p className="text-xs">No categories available</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  {/* Suppliers filter */}
                  <div className="space-y-2">
                    <Label className="text-xs font-medium text-foreground">Suppliers</Label>
                    <div className="border border-border rounded-md bg-background/50 shadow-xs">
                      <div className="max-h-32 overflow-y-auto p-2 space-y-1">
                        {availableOptions.suppliers.length > 0 ? (
                          availableOptions.suppliers.map(supplier => (
                            <label
                              key={supplier.value}
                              className={cn(
                                "flex items-center space-x-2 p-1.5 rounded transition-all cursor-pointer group",
                                "hover:bg-accent/50 hover:text-accent-foreground text-xs"
                              )}
                            >
                              <input
                                type="checkbox"
                                checked={filters.suppliers.includes(supplier.value)}
                                onChange={() => handleSupplierToggle(supplier.value)}
                                className="h-3 rounded border-border text-primary focus:ring-primary/20 focus:ring-1"
                              />
                              <span className="font-medium">{supplier.label}</span>
                            </label>
                          ))
                        ) : (
                          <div className="text-center py-3 text-muted-foreground">
                            <p className="text-xs">No suppliers available</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  {/* Stock Status filter */}
                  <div className="space-y-2">
                    <Label className="text-xs font-medium text-foreground">Stock Status</Label>
                    <div className="border border-border rounded-md bg-background/50 shadow-xs">
                      <div className="p-2 space-y-1">
                        {availableOptions.stockStatus.map(status => (
                          <label
                            key={status.value}
                            className={cn(
                              "flex items-center space-x-2 p-1.5 rounded transition-all cursor-pointer group",
                              "hover:bg-accent/50 hover:text-accent-foreground text-xs"
                            )}
                          >
                            <input
                              type="checkbox"
                              checked={filters.stockStatus.includes(status.value as any)}
                              onChange={() => handleStockStatusToggle(status.value as any)}
                              className="h-3 rounded border-border text-primary focus:ring-primary/20 focus:ring-1"
                            />
                            <span className="font-medium">{status.label}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>
                  
                  {/* Product Type filter */}
                  <div className="space-y-2">
                    <Label className="text-xs font-medium text-foreground">Product Type</Label>
                    <div className="border border-border rounded-md bg-background/50 shadow-xs">
                      <div className="p-2 space-y-1">
                        {availableOptions.productType.map(type => (
                          <label
                            key={type.value}
                            className={cn(
                              "flex items-center space-x-2 p-1.5 rounded transition-all cursor-pointer group",
                              "hover:bg-accent/50 hover:text-accent-foreground text-xs"
                            )}
                          >
                            <input
                              type="checkbox"
                              checked={filters.productType.includes(type.value as any)}
                              onChange={() => handleProductTypeToggle(type.value as any)}
                              className="h-3 rounded border-border text-primary focus:ring-primary/20 focus:ring-1"
                            />
                            <span className="font-medium">{type.label}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>
                  
                  {/* Sale Status filter */}
                  <div className="space-y-2">
                    <Label className="text-xs font-medium text-foreground">Sale Status</Label>
                    <div className="border border-border rounded-md bg-background/50 shadow-xs">
                      <div className="p-2 space-y-1">
                        {availableOptions.saleStatus.map(status => (
                          <label
                            key={status.value}
                            className={cn(
                              "flex items-center space-x-2 p-1.5 rounded transition-all cursor-pointer group",
                              "hover:bg-accent/50 hover:text-accent-foreground text-xs"
                            )}
                          >
                            <input
                              type="checkbox"
                              checked={filters.saleStatus.includes(status.value as any)}
                              onChange={() => handleSaleStatusToggle(status.value as any)}
                              className="h-3 rounded border-border text-primary focus:ring-primary/20 focus:ring-1"
                            />
                            <span className="font-medium">{status.label}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>
                  
                  {/* Price Range filter */}
                  <div className="space-y-2">
                    <Label className="text-xs font-medium text-foreground">Price Range</Label>
                    <div className="space-y-2 p-2 border border-border rounded-md bg-background/50 shadow-xs">
                      <div className="flex space-x-2">
                        <div className="flex-1">
                          <Label className="text-xs font-medium text-muted-foreground uppercase tracking-wide sr-only">Minimum</Label>
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="Min"
                            value={filters.priceRange.min || ''}
                            onChange={(e) => onFiltersChange({
                              ...filters,
                              priceRange: { 
                                ...filters.priceRange, 
                                min: e.target.value ? parseFloat(e.target.value) : null 
                              }
                            })}
                            className="w-full h-8 text-xs px-2 py-1"
                          />
                        </div>
                        <div className="flex-1">
                          <Label className="text-xs font-medium text-muted-foreground uppercase tracking-wide sr-only">Maximum</Label>
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="Max"
                            value={filters.priceRange.max || ''}
                            onChange={(e) => onFiltersChange({
                              ...filters,
                              priceRange: { 
                                ...filters.priceRange, 
                                max: e.target.value ? parseFloat(e.target.value) : null 
                              }
                            })}
                            className="w-full h-8 text-xs px-2 py-1"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="sticky bottom-0 bg-background/95 backdrop-blur-sm pt-3 border-t">
                  <div className="flex gap-2">
                    <Button 
                      onClick={clearAllFilters} 
                      variant="outline" 
                      size="sm"
                      className="flex-1 touch-manipulation h-9 px-3 text-xs border-gray-300"
                      disabled={activeFilterCount === 0}
                    >
                      <RotateCcw className="h-3.5 w-3.5 mr-1.5" />
                      Clear All {activeFilterCount > 0 && `(${activeFilterCount})`}
                    </Button>
                    <Button 
                      onClick={() => setIsFilterSheetOpen(false)}
                      size="sm"
                      className="flex-1 touch-manipulation h-9 font-medium text-xs"
                    >
                      Apply Filters
                    </Button>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          ) : null}
        </div>
      </div>
      
      {/* Active filters indicator */}
      {activeFilterCount > 0 && (
        <div className="flex flex-wrap gap-1.5">
          {getActiveFilterBadges()}
        </div>
      )}
      
      {/* Second tier: Expandable advanced filters */}
      {!isMobile && isExpanded && (
        <div className="border border-gray-200 rounded-lg p-4 bg-white">
          <div className="text-xs font-medium mb-3 pb-2 border-b border-gray-100">Advanced Filters</div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
            {/* Category filter - CHANGED TO SELECT */}
            <div className="space-y-1">
              <Label className="text-xs font-medium">Categories</Label>
              <Select 
                value={filters.categories[0] || ""} 
                onValueChange={(value) => onFiltersChange({
                  ...filters,
                  categories: value ? [value] : []
                })}
              >
                <SelectTrigger className="h-8 text-xs">
                  <SelectValue placeholder="Select categories" />
                </SelectTrigger>
                <SelectContent>
                  {availableOptions.categories.map(category => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            {/* Supplier filter - CHANGED TO SELECT */}
            <div className="space-y-1">
              <Label className="text-xs font-medium">Suppliers</Label>
              <Select 
                value={filters.suppliers[0] || ""} 
                onValueChange={(value) => onFiltersChange({
                  ...filters,
                  suppliers: value ? [value] : []
                })}
              >
                <SelectTrigger className="h-8 text-xs">
                  <SelectValue placeholder="Select suppliers" />
                </SelectTrigger>
                <SelectContent>
                  {availableOptions.suppliers.map(supplier => (
                    <SelectItem key={supplier.value} value={supplier.value}>
                      {supplier.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            {/* Stock Status filter - CHANGED TO SELECT */}
            <div className="space-y-1">
              <Label className="text-xs font-medium">Stock Status</Label>
              <Select 
                value={filters.stockStatus[0] || ""} 
                onValueChange={(value) => onFiltersChange({
                  ...filters,
                  stockStatus: value ? [value as 'in_stock' | 'low_stock' | 'out_of_stock'] : []
                })}
              >
                <SelectTrigger className="h-8 text-xs">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  {availableOptions.stockStatus.map(status => (
                    <SelectItem key={status.value} value={status.value}>
                      {status.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            {/* Product Type filter - CHANGED TO SELECT */}
            <div className="space-y-1">
              <Label className="text-xs font-medium">Product Type</Label>
              <Select 
                value={filters.productType[0] || ""} 
                onValueChange={(value) => onFiltersChange({
                  ...filters,
                  productType: value ? [value as 'simple' | 'variable'] : []
                })}
              >
                <SelectTrigger className="h-8 text-xs">
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  {availableOptions.productType.map(type => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            {/* Sale Status filter - CHANGED TO SELECT */}
            <div className="space-y-1">
              <Label className="text-xs font-medium">Sale Status</Label>
              <Select 
                value={filters.saleStatus[0] || ""} 
                onValueChange={(value) => onFiltersChange({
                  ...filters,
                  saleStatus: value ? [value as 'on_sale' | 'regular'] : []
                })}
              >
                <SelectTrigger className="h-8 text-xs">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  {availableOptions.saleStatus.map(status => (
                    <SelectItem key={status.value} value={status.value}>
                      {status.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            {/* Price Range filter - KEEP AS IS but remove container */}
            <div className="space-y-1">
              <Label className="text-xs font-medium">Price Range</Label>
              <div className="flex space-x-2">
                <div className="flex-1">
                  <Label className="text-xs font-medium text-muted-foreground uppercase tracking-wide sr-only">Minimum</Label>
                  <Input
                    type="number"
                    step="0.01"
                    placeholder="Min"
                    value={filters.priceRange.min || ''}
                    onChange={(e) => onFiltersChange({
                      ...filters,
                      priceRange: { 
                        ...filters.priceRange, 
                        min: e.target.value ? parseFloat(e.target.value) : null 
                      }
                    })}
                    className="w-full h-8 text-xs px-2 py-1"
                  />
                </div>
                <div className="flex-1">
                  <Label className="text-xs font-medium text-muted-foreground uppercase tracking-wide sr-only">Maximum</Label>
                  <Input
                    type="number"
                    step="0.01"
                    placeholder="Max"
                    value={filters.priceRange.max || ''}
                    onChange={(e) => onFiltersChange({
                      ...filters,
                      priceRange: { 
                        ...filters.priceRange, 
                        max: e.target.value ? parseFloat(e.target.value) : null 
                      }
                    })}
                    className="w-full h-8 text-xs px-2 py-1"
                  />
                </div>
              </div>
            </div>
            
            {/* Empty placeholders to maintain grid structure */}
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
          </div>
          
          {/* Action buttons */}
          <div className="mt-4 flex justify-end gap-2">
            <Button 
              variant="outline" 
              size="sm" 
              className="h-7 text-xs"
              onClick={clearAllFilters}
              disabled={activeFilterCount === 0}
            >
              <RotateCcw className="h-3 w-3 mr-1.5" />
              Reset
            </Button>
            <Button 
              size="sm" 
              className="h-7 text-xs"
              onClick={() => setIsExpanded(false)}
            >
              Apply Filters
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}