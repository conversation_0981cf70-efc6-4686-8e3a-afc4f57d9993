# Supabase Storage Configuration

## Overview
This document describes how storage is set up in the ONKO application, particularly for storing user profile pictures and company logos.

## Storage Buckets
The application uses the following storage buckets:

### 1. Avatars Bucket
- **Name**: `avatars`
- **Purpose**: Stores user profile pictures
- **Access**: Public bucket with controlled access via Row Level Security (RLS)
- **File structure**: Files are stored in user-specific folders using the pattern `{user_id}/avatar.{extension}`
- **File size limit**: 5MB
- **Allowed file types**: Image files (JPEG, PNG, GIF, WebP)

### 2. Company Logos Bucket
- **Name**: `company-logos`
- **Purpose**: Stores company logos
- **Access**: Public bucket with controlled access via Row Level Security (RLS)
- **File structure**: Files are stored in user-specific folders using the pattern `{user_id}/company-logo.{extension}`
- **File size limit**: 5MB
- **Allowed file types**: Image files (JPEG, PNG, GIF, WebP, SVG)

## Setup
The storage configuration is set up using the following scripts:

### Avatars Bucket Setup
- Script: `scripts/setup-avatars-bucket.js`
- Purpose: Creates and configures the avatars bucket
- To run:
  ```bash
  node scripts/setup-avatars-bucket.js
  ```

### Company Logos Bucket Setup
- Script: `scripts/setup-company-logos-bucket.js`
- Purpose: Creates and configures the company-logos bucket
- To run:
  ```bash
  node scripts/setup-company-logos-bucket.js
  ```

Each script:
- Creates the bucket if it doesn't exist
- Updates bucket settings (public access, file size limits, allowed MIME types)

Additionally, you need to set up the storage policies through the Supabase dashboard UI as described in `STORAGE_SETUP_INSTRUCTIONS.md`.

## Implementation in the Application

### Profile Picture Upload
The profile picture upload is implemented in `components/profile/profile-picture-manager.tsx`. The component:

1. Allows users to upload an image file
2. Validates the file type and size
3. Uploads the file to Supabase Storage in the avatars bucket
4. Gets the public URL for the uploaded file
5. Updates the user's profile with the new avatar URL

### Company Logo Upload
The company logo upload is implemented in `components/settings/company-settings.tsx`. The component:

1. Allows users to upload an image file
2. Validates the file type and size
3. Uploads the file to Supabase Storage in the company-logos bucket
4. Gets the public URL for the uploaded file
5. Updates the user's profile with the company information including the logo URL

## Troubleshooting

### "Bucket not found" Error
If you encounter a "Bucket not found" error when uploading profile pictures or company logos:

1. Verify that the required buckets exist in your Supabase project
   - Go to the Supabase dashboard > Storage > Buckets
   - Check if the "avatars" and "company-logos" buckets exist

2. If they don't exist, run the setup scripts:
   ```bash
   node scripts/setup-avatars-bucket.js
   node scripts/setup-company-logos-bucket.js
   ```

3. Alternatively, manually create the buckets:
   - Go to the Supabase dashboard > Storage > Buckets
   - Click "New Bucket"
   - For avatars:
     - Name: "avatars"
     - Public bucket: Yes
     - Set file size limit to 5MB
     - Allow MIME types: image/jpeg, image/png, image/gif, image/webp
   - For company-logos:
     - Name: "company-logos"
     - Public bucket: Yes
     - Set file size limit to 5MB
     - Allow MIME types: image/jpeg, image/png, image/gif, image/webp, image/svg+xml

### "new row violates row-level security policy" Error
This error occurs when the storage policies are not properly configured. In Supabase, storage policies need to be set up manually through the dashboard UI:

1. Follow the instructions in `STORAGE_SETUP_INSTRUCTIONS.md` to set up the policies through the Supabase dashboard
2. The policies control who can upload, download, update, and delete files in the buckets

### Other Storage Issues
For other storage-related issues:

1. Check the browser console for specific error messages
2. Verify your Supabase credentials in .env.local
3. Check if the user is properly authenticated
4. Inspect the storage policies in the Supabase dashboard