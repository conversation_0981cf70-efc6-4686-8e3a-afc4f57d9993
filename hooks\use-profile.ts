import { useQuery } from '@tanstack/react-query'
import { getSupabaseClient } from '@/lib/supabase'
import type { ProfileRow } from '@/lib/supabase'

/**
 * Custom hook to fetch user profile data with React Query
 * @param userId - The user ID to fetch profile for
 * @returns React Query result object with profile data
 */
export function useProfile(userId: string | undefined) {
  return useQuery<ProfileRow, Error>({
    queryKey: ['profile', userId],
    queryFn: async () => {
      if (!userId) {
        throw new Error('User ID is required')
      }
      
      const supabase = getSupabaseClient()
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()
      
      if (error) {
        console.error('Error fetching profile:', error)
        throw error
      }
      
      return data
    },
    enabled: !!userId,
    staleTime: 2 * 60 * 1000, // 2 minutes for responsive updates
    gcTime: 5 * 60 * 1000, // 5 minutes garbage collection time
    refetchOnMount: true,
    refetchOnWindowFocus: true, // Important for tab switching
    refetchOnReconnect: true,
    refetchInterval: false,
    refetchIntervalInBackground: false,
    retry: 3, // Increased retry count
    retryDelay: (attemptIndex) => {
      return Math.min(1000 * 2 ** attemptIndex, 30000)
    },
    networkMode: 'online' // Handle offline scenarios
  })
}