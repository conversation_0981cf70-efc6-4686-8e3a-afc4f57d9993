import { useState, useEffect, useRef } from 'react'
import { createSupabaseClient } from '@/lib/supabase'
import { NotificationService } from '@/lib/notification-service'
import { centralizedNotificationService } from '@/lib/centralized-notification-service'
import { useAuth } from '@/contexts/auth-context'

interface Notification {
  id: string
  title: string
  message: string
  type: string
  created_at: string
  is_read: boolean
}

export function useNotifications() {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  const [loading, setLoading] = useState(true)
  const supabase = createSupabaseClient()
  const notificationService = new NotificationService()
  const lastVisibilityChangeRef = useRef<number>(Date.now())
  const { organizationId } = useAuth()

  useEffect(() => {
    loadNotifications()
    loadUnreadCount()

    // Set up subscription using centralized service
    let unsubscribe: (() => void) | null = null
    if (organizationId) {
      unsubscribe = centralizedNotificationService.subscribe(
        organizationId,
        (newNotification: any) => {
          // Add new notification to the top of the list only if it doesn't already exist
          setNotifications(prev => {
            // Check if notification already exists
            const exists = prev.some(notification => notification.id === newNotification.id)
            if (!exists) {
              return [newNotification, ...prev]
            }
            return prev
          })
          setUnreadCount(prev => prev + 1)
        },
        (updatedNotification: any) => {
          // Update notification in the list
          setNotifications(prev => 
            prev.map(notification => 
              notification.id === updatedNotification.id 
                ? updatedNotification 
                : notification
            )
          )
          
          // Update unread count if this notification was marked as read
          if (updatedNotification.is_read) {
            setUnreadCount(prev => Math.max(0, prev - 1))
          }
        },
        (deletedNotification: any) => {
          // Remove notification from the list
          setNotifications(prev => 
            prev.filter(notification => notification.id !== deletedNotification.id)
          )
          
          // Update unread count if this notification was unread
          if (!deletedNotification.is_read) {
            setUnreadCount(prev => Math.max(0, prev - 1))
          }
        }
      )
    }

    // Subscribe to custom events
    const unsubscribeCustom = centralizedNotificationService.subscribeToEvent(
      'notifications-marked-as-read',
      () => {
        // Update all notifications to read
        setNotifications(prev => 
          prev.map(notification => ({ ...notification, is_read: true }))
        )
        setUnreadCount(0)
      }
    )

    // Handle visibility change to refresh when tab becomes visible
    const handleVisibilityChange = () => {
      const now = Date.now()
      // Debounce visibility changes
      if (now - lastVisibilityChangeRef.current < 1000) {
        return
      }
      
      lastVisibilityChangeRef.current = now
      
      if (document.visibilityState === 'visible') {
        console.debug('useNotifications: Tab became visible, refreshing data')
        // When tab becomes visible, refresh the data
        loadUnreadCount()
        // Only reconnect if we're not already subscribed
        const status = centralizedNotificationService.getSubscriptionStatus()
        if (organizationId && !status.isSubscribed) {
          centralizedNotificationService.reconnect(organizationId)
        }
      }
    }

    // Handle window focus
    const handleFocus = () => {
      console.debug('useNotifications: Window focused, refreshing data')
      loadUnreadCount()
      // Only reconnect if we're not already subscribed
      const status = centralizedNotificationService.getSubscriptionStatus()
      if (organizationId && !status.isSubscribed) {
        centralizedNotificationService.reconnect(organizationId)
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('focus', handleFocus)

    return () => {
      if (unsubscribe) {
        unsubscribe()
      }
      unsubscribeCustom()
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('focus', handleFocus)
    }
  }, [organizationId])

  const loadNotifications = async () => {
    try {
      setLoading(true)
      if (organizationId) {
        const notifications = await notificationService.getNotifications(organizationId, 20)
        setNotifications(notifications)
      }
    } catch (error) {
      console.error('Error loading notifications:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadUnreadCount = async () => {
    try {
      if (organizationId) {
        const count = await notificationService.getUnreadNotificationCount(organizationId)
        setUnreadCount(count)
      }
    } catch (error) {
      console.error('Error loading unread count:', error)
    }
  }

  const markAsRead = async (id: string) => {
    try {
      // Update database
      await notificationService.markAsRead(id)
      
      // Update local state immediately for instant feedback
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === id 
            ? { ...notification, is_read: true } 
            : notification
        )
      )
      setUnreadCount(prev => Math.max(0, prev - 1))
    } catch (error) {
      console.error('Error marking notification as read:', error)
    }
  }

  const markAllAsRead = async () => {
    try {
      if (organizationId) {
        // Update database
        await notificationService.markAllAsRead(organizationId)
        
        // Update local state immediately for instant feedback
        setNotifications(prev => 
          prev.map(notification => ({ ...notification, is_read: true }))
        )
        setUnreadCount(0)
        
        // Broadcast the change to other components
        centralizedNotificationService.broadcastEvent('notifications-marked-as-read', { organizationId })
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error)
    }
  }

  return {
    notifications,
    unreadCount,
    loading,
    markAsRead,
    markAllAsRead
  }
}