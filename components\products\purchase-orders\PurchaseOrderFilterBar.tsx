'use client'

import { useState, useEffect } from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { cn } from '@/lib/utils'
import { format } from 'date-fns'
import { CalendarIcon, SlidersHorizontal, X } from 'lucide-react'
import { PurchaseOrderFilters, INITIAL_PURCHASE_ORDER_FILTERS } from './types'

interface PurchaseOrderFilterBarProps {
  filters: PurchaseOrderFilters
  onFiltersChange: (filters: PurchaseOrderFilters) => void
  suppliers: string[]
}

export function PurchaseOrderFilterBar({ 
  filters, 
  onFiltersChange,
  suppliers 
}: PurchaseOrderFilterBarProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [localFilters, setLocalFilters] = useState<PurchaseOrderFilters>(filters)

  // Update local filters when props change
  useEffect(() => {
    setLocalFilters(filters)
  }, [filters])

  const handleApplyFilters = () => {
    onFiltersChange(localFilters)
    setIsExpanded(false)
  }

  const handleClearFilters = () => {
    setLocalFilters(INITIAL_PURCHASE_ORDER_FILTERS)
    onFiltersChange(INITIAL_PURCHASE_ORDER_FILTERS)
    setIsExpanded(false)
  }

  const activeFilterCount = [
    localFilters.search,
    localFilters.status !== 'all',
    localFilters.supplier,
    localFilters.dateRange.from,
    localFilters.dateRange.to
  ].filter(Boolean).length

  return (
    <div className="bg-white rounded-lg border border-gray-200">
      {/* Main search bar */}
      <div className="flex flex-col md:flex-row md:items-center gap-3">
        <div className="flex-1">
          <Input
            placeholder="Search PO number or supplier..."
            value={localFilters.search}
            onChange={(e) => setLocalFilters(prev => ({ 
              ...prev, 
              search: e.target.value 
            }))}
            className="text-xs h-9"
          />
        </div>
        
        <div className="flex items-center gap-2">
          {activeFilterCount > 0 && (
            <Button 
              variant="outline" 
              size="sm" 
              className="h-9 text-xs flex items-center gap-1.5"
              onClick={handleClearFilters}
            >
              <X className="h-3.5 w-3.5" />
              Clear
            </Button>
          )}
          
          <Button 
            variant="outline" 
            size="sm" 
            className="h-9 text-xs flex items-center gap-1.5"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            <SlidersHorizontal className="h-3.5 w-3.5" />
            Filter
            {activeFilterCount > 0 && (
              <span className="bg-blue-100 text-blue-800 text-xs font-medium px-1.5 py-0.5 rounded-full">
                {activeFilterCount}
              </span>
            )}
          </Button>
        </div>
      </div>
      
      {/* Expanded filters */}
      {isExpanded && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Status filter */}
            <div className="space-y-2">
              <label className="text-xs font-medium text-gray-700">
                Status
              </label>
              <Select
                value={localFilters.status}
                onValueChange={(value: any) => setLocalFilters(prev => ({ 
                  ...prev, 
                  status: value 
                }))}
              >
                <SelectTrigger className="text-xs h-9">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all" className="text-xs">All Statuses</SelectItem>
                  <SelectItem value="draft" className="text-xs">Draft</SelectItem>
                  <SelectItem value="open" className="text-xs">Open</SelectItem>
                  <SelectItem value="partially_received" className="text-xs">Partially Received</SelectItem>
                  <SelectItem value="received" className="text-xs">Received</SelectItem>
                  <SelectItem value="cancelled" className="text-xs">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {/* Supplier filter */}
            <div className="space-y-2">
              <label className="text-xs font-medium text-gray-700">
                Supplier
              </label>
              <Select
                value={localFilters.supplier}
                onValueChange={(value) => setLocalFilters(prev => ({ 
                  ...prev, 
                  supplier: value 
                }))}
              >
                <SelectTrigger className="text-xs h-9">
                  <SelectValue placeholder="Select supplier" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="" className="text-xs">All Suppliers</SelectItem>
                  {suppliers.map((supplier) => (
                    <SelectItem key={supplier} value={supplier} className="text-xs">
                      {supplier}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            {/* Date range from */}
            <div className="space-y-2">
              <label className="text-xs font-medium text-gray-700">
                Date From
              </label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal text-xs h-9",
                      !localFilters.dateRange.from && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {localFilters.dateRange.from ? (
                      format(localFilters.dateRange.from, "PPP")
                    ) : (
                      <span>Pick a date</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={localFilters.dateRange.from}
                    onSelect={(date) => setLocalFilters(prev => ({ 
                      ...prev, 
                      dateRange: { ...prev.dateRange, from: date } 
                    }))}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            
            {/* Date range to */}
            <div className="space-y-2">
              <label className="text-xs font-medium text-gray-700">
                Date To
              </label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal text-xs h-9",
                      !localFilters.dateRange.to && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {localFilters.dateRange.to ? (
                      format(localFilters.dateRange.to, "PPP")
                    ) : (
                      <span>Pick a date</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={localFilters.dateRange.to}
                    onSelect={(date) => setLocalFilters(prev => ({ 
                      ...prev, 
                      dateRange: { ...prev.dateRange, to: date } 
                    }))}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
          
          {/* Action buttons */}
          <div className="mt-4 flex justify-end gap-2">
            <Button 
              variant="outline" 
              size="sm" 
              className="h-8 text-xs"
              onClick={handleClearFilters}
            >
              Reset
            </Button>
            <Button 
              size="sm" 
              className="h-8 text-xs"
              onClick={handleApplyFilters}
            >
              Apply Filters
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}