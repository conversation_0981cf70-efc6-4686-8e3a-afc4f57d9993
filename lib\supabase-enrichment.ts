import { getSupabaseClient } from '@/lib/supabase'

/**
 * Comprehensive utility class for enriching records with related data
 * This avoids issues with Supabase schema cache and relationship syntax
 */
export class DataEnrichmentService {
  /**
   * Enrich sales records with customer data
   * @param supabase - Supabase client instance
   * @param sales - Array of sales records
   * @returns Promise with sales records including customer names
   */
  async enrichSalesWithCustomerData(supabase: any, sales: any[]) {
    // Extract unique customer IDs
    const customerIds = sales
      .filter(sale => sale.customer_id)
      .map(sale => sale.customer_id)
      .filter((id, index, self) => self.indexOf(id) === index) // Remove duplicates
    
    // Fetch customer data
    let customerMap: Record<string, string> = {}
    if (customerIds.length > 0) {
      const { data: customers, error: customersError } = await supabase
        .from('customers')
        .select('id, full_name')
        .in('id', customerIds)
      
      if (!customersError && customers) {
        customerMap = customers.reduce((map: Record<string, string>, customer: any) => {
          map[customer.id] = customer.full_name
          return map
        }, {} as Record<string, string>)
      }
    }
    
    // Enrich sales with customer names
    return sales.map(sale => ({
      ...sale,
      customer_name: (sale.customer_id && customerMap[sale.customer_id]) 
        ? customerMap[sale.customer_id] 
        : 'Unknown Customer'
    }))
  }

  /**
   * Enrich a single sale with customer data
   * @param supabase - Supabase client instance
   * @param sale - Sale record
   * @returns Promise with sale record including customer name
   */
  async enrichSaleWithCustomerData(supabase: any, sale: any) {
    // Fetch customer name
    let customerName = 'Unknown Customer'
    if (sale.customer_id) {
      const { data: customer, error: customerError } = await supabase
        .from('customers')
        .select('full_name')
        .eq('id', sale.customer_id)
        .single()
      
      if (!customerError && customer) {
        customerName = customer.full_name
      }
    }
    
    return {
      ...sale,
      customer_name: customerName
    }
  }

  /**
   * Enrich product records with category and variant data
   * @param supabase - Supabase client instance
   * @param products - Array of product records
   * @returns Promise with products including category names and variants
   */
  async enrichProductsWithRelatedData(supabase: any, products: any[]) {
    // Extract unique category IDs
    const categoryIds = products
      .filter(product => product.category_id)
      .map(product => product.category_id)
      .filter((id, index, self) => self.indexOf(id) === index) // Remove duplicates
    
    // Extract unique product IDs for variants
    const productIds = products
      .map(product => product.id)
      .filter((id, index, self) => self.indexOf(id) === index) // Remove duplicates
    
    // Fetch category data
    let categoryMap: Record<string, string> = {}
    if (categoryIds.length > 0) {
      const { data: categories, error: categoriesError } = await supabase
        .from('categories')
        .select('id, name')
        .in('id', categoryIds)
      
      if (!categoriesError && categories) {
        categoryMap = categories.reduce((map: Record<string, string>, category: any) => {
          map[category.id] = category.name
          return map
        }, {} as Record<string, string>)
      }
    }
    
    // Fetch variant data
    let variantMap: Record<string, any[]> = {}
    if (productIds.length > 0) {
      const { data: variants, error: variantsError } = await supabase
        .from('product_variants')
        .select('*')
        .in('product_id', productIds)
      
      if (!variantsError && variants) {
        // Group variants by product_id
        variantMap = variants.reduce((map: Record<string, any[]>, variant: any) => {
          if (!map[variant.product_id]) {
            map[variant.product_id] = []
          }
          map[variant.product_id].push(variant)
          return map
        }, {} as Record<string, any[]>)
      }
    }
    
    // Enrich products with category names and variants
    return products.map(product => ({
      ...product,
      category_name: (product.category_id && categoryMap[product.category_id]) 
        ? categoryMap[product.category_id] 
        : null,
      product_variants: variantMap[product.id] || []
    }))
  }

  /**
   * Enrich a single product with related data
   * @param supabase - Supabase client instance
   * @param product - Product record
   * @returns Promise with product record including category name and variants
   */
  async enrichProductWithRelatedData(supabase: any, product: any) {
    // Fetch category name
    let categoryName = null
    if (product.category_id) {
      const { data: category, error: categoryError } = await supabase
        .from('categories')
        .select('name')
        .eq('id', product.category_id)
        .single()
      
      if (!categoryError && category) {
        categoryName = category.name
      }
    }
    
    // Fetch variants
    let variants: any[] = []
    const { data: productVariants, error: variantsError } = await supabase
      .from('product_variants')
      .select('*')
      .eq('product_id', product.id)
    
    if (!variantsError && productVariants) {
      variants = productVariants
    }
    
    return {
      ...product,
      category_name: categoryName,
      product_variants: variants
    }
  }

  /**
   * Generic method to enrich records with related data
   * @param supabase - Supabase client instance
   * @param records - Array of records to enrich
   * @param relatedTable - Name of the related table
   * @param foreignKey - Foreign key field in the main records
   * @param relatedKey - Key field in the related table (default: 'id')
   * @param selectFields - Fields to select from the related table
   * @param resultMap - Function to transform related records into a map
   * @returns Promise with enriched records
   */
  async enrichRecordsWithRelatedData<T>(
    supabase: any,
    records: T[],
    relatedTable: string,
    foreignKey: string,
    relatedKey: string = 'id',
    selectFields: string,
    resultMap: (relatedRecords: any[]) => Record<string, any>
  ): Promise<T[]> {
    // Extract unique foreign key values
    const foreignKeyValues = records
      .filter(record => (record as any)[foreignKey])
      .map(record => (record as any)[foreignKey])
      .filter((value, index, self) => self.indexOf(value) === index) // Remove duplicates
    
    // Fetch related data
    let relatedMap: Record<string, any> = {}
    if (foreignKeyValues.length > 0) {
      const { data: relatedRecords, error: relatedError } = await supabase
        .from(relatedTable)
        .select(selectFields)
        .in(relatedKey, foreignKeyValues)
      
      if (!relatedError && relatedRecords) {
        relatedMap = resultMap(relatedRecords)
      }
    }
    
    // Enrich records with related data
    return records.map(record => ({
      ...record,
      ...relatedMap[(record as any)[foreignKey]] || {}
    }))
  }
}

// Export a singleton instance for convenience
export const dataEnrichmentService = new DataEnrichmentService()