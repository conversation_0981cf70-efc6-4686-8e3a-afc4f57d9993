-- Custom function to execute SQL from client
-- WARNING: This is for development only! Never use in production!
-- Run this in your Supabase SQL Editor

CREATE OR REPLACE FUNCTION exec_sql(sql text)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result json;
    rec record;
    rows_affected int;
BEGIN
    -- Only allow authenticated users
    IF auth.uid() IS NULL THEN
        RAISE EXCEPTION 'Access denied';
    END IF;
    
    -- Execute the SQL
    EXECUTE sql;
    
    -- Get the row count if applicable
    GET DIAGNOSTICS rows_affected = ROW_COUNT;
    
    -- Return success result
    result := json_build_object(
        'success', true,
        'rows_affected', rows_affected,
        'message', 'SQL executed successfully'
    );
    
    RETURN result;
    
EXCEPTION WHEN OTHERS THEN
    -- Return error result
    result := json_build_object(
        'success', false,
        'error', SQLERRM,
        'error_code', SQLSTATE
    );
    
    RETURN result;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION exec_sql(text) TO authenticated;

-- Create a safer version for SELECT queries only
CREATE OR REPLACE FUNCTION exec_select(sql text)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result json;
    query_result json;
BEGIN
    -- Only allow authenticated users
    IF auth.uid() IS NULL THEN
        RAISE EXCEPTION 'Access denied';
    END IF;
    
    -- Only allow SELECT statements
    IF upper(trim(sql)) NOT LIKE 'SELECT%' THEN
        RAISE EXCEPTION 'Only SELECT statements are allowed';
    END IF;
    
    -- Execute the SELECT query and return results as JSON
    EXECUTE 'SELECT array_to_json(array_agg(row_to_json(t))) FROM (' || sql || ') t'
    INTO query_result;
    
    -- Return success result with data
    result := json_build_object(
        'success', true,
        'data', query_result,
        'message', 'Query executed successfully'
    );
    
    RETURN result;
    
EXCEPTION WHEN OTHERS THEN
    -- Return error result
    result := json_build_object(
        'success', false,
        'error', SQLERRM,
        'error_code', SQLSTATE
    );
    
    RETURN result;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION exec_select(text) TO authenticated;