'use client'

import { useState, useMemo } from 'react'
// Removed Command imports - using simple div-based dropdown instead
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@/components/ui/popover'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { useCurrency } from '@/contexts/currency-context'
import { PlusCircle, X } from 'lucide-react'

interface ProductVariant {
  id: string
  sku: string
  variant_name: string
  size?: string
  color?: string
  material?: string
  style?: string
  cost_adjustment: number
  base_cost?: number
  packaging_cost?: number
  price: number
  stock_quantity: number
  is_active: boolean
}

interface SelectableProduct {
  id: string
  name: string
  base_sku: string
  supplier: string
  base_cost: number
  packaging_cost: number
  has_variants: boolean
  product_variants?: ProductVariant[]
}

interface SelectedProductValue {
  productId: string | null
  variantId?: string | null
  productName: string
  variantName?: string
  sku: string
  unitCost: number
  variantAttributes?: {
    size?: string
    color?: string
    material?: string
    style?: string
  }
}

interface EnhancedProductSelectorProps {
  products: SelectableProduct[]
  supplier: string
  value: SelectedProductValue | null
  onChange: (product: SelectedProductValue | null) => void
  onNewProduct: (productName: string, generatedSKU: string) => void
}

export function EnhancedProductSelector({
  products,
  supplier,
  value,
  onChange,
  onNewProduct
}: EnhancedProductSelectorProps) {
  const [open, setOpen] = useState(false)
  const [search, setSearch] = useState('')
  const { formatCurrency } = useCurrency()
  
  // Create orderable items (flatten products and variants into searchable items)
  const orderableItems = useMemo(() => {
    // Debug: Log filtering process
    console.log('EnhancedProductSelector - Creating orderable items:', {
      totalProducts: products.length,
      supplier,
      search
    });

    // If no supplier is selected, show no products but don't block the UI completely
    if (!supplier) {
      return [];
    }

    const items: Array<{
      id: string
      productId: string
      variantId?: string
      displayName: string
      sku: string
      unitCost: number
      isVariant: boolean
      variantAttributes?: any
    }> = [];

    products.forEach(product => {
      const productSupplier = product.supplier ? product.supplier.trim() : '';
      const selectedSupplier = supplier.trim();
      const isSupplierMatch = productSupplier.toLowerCase() === selectedSupplier.toLowerCase();

      if (!isSupplierMatch) return;

      if (!product.has_variants || !product.product_variants?.length) {
        // Simple product - add as single orderable item
        const totalCost = product.base_cost || 0;

        const item = {
          id: `product-${product.id}`,
          productId: product.id,
          displayName: product.name,
          sku: product.base_sku || '',
          unitCost: totalCost,
          isVariant: false
        };

        // Apply search filter
        const isSearchMatch = !search ||
                             item.displayName.toLowerCase().includes(search.toLowerCase()) ||
                             item.sku.toLowerCase().includes(search.toLowerCase());

        if (isSearchMatch) {
          items.push(item);
        }
      } else {
        // Variable product - add each variant as orderable item
        product.product_variants.forEach((variant: any) => {
          // For variants, use base_cost if available (new format), otherwise fall back to cost_adjustment (legacy)
          const unitCost = (variant.base_cost !== undefined && variant.base_cost !== null && variant.base_cost > 0)
            ? variant.base_cost
            : (variant.cost_adjustment || 0);

          // Create display name: "Product Name - Attribute1: Value1, Attribute2: Value2"
          const attributeString = Object.entries(variant.variant_attributes || {})
            .map(([key, value]) => `${key}: ${value}`)
            .join(', ');

          const displayName = attributeString
            ? `${product.name} - ${attributeString}`
            : `${product.name} - ${variant.variant_name || 'Variant'}`;

          const item = {
            id: `variant-${variant.id}`,
            productId: product.id,
            variantId: variant.id,
            displayName,
            sku: variant.sku || '',
            unitCost: unitCost,
            isVariant: true,
            variantAttributes: variant.variant_attributes
          };

          // Apply search filter
          const isSearchMatch = !search ||
                               item.displayName.toLowerCase().includes(search.toLowerCase()) ||
                               item.sku.toLowerCase().includes(search.toLowerCase());

          if (isSearchMatch) {
            items.push(item);
          }
        });
      }
    });

    console.log('EnhancedProductSelector - Orderable items:', items);
    return items;
  }, [products, supplier, search])
  
  const handleItemSelect = (item: any) => {
    console.log('EnhancedProductSelector - Item selected:', item);

    // Create the selected value from the orderable item
    const selectedValue: SelectedProductValue = {
      productId: item.productId,
      variantId: item.variantId || null,
      productName: item.displayName,
      variantName: item.isVariant ? item.displayName.split(' - ')[1] : undefined,
      sku: item.sku,
      unitCost: item.unitCost,
      variantAttributes: item.variantAttributes
    };

    onChange(selectedValue)
    setOpen(false) // Close dropdown after selecting item
  }

  const handleClear = () => {
    onChange(null)
    setOpen(false) // Close dropdown when clearing selection
  }
  
  const generateSKU = (productName: string) => {
    const namePrefix = productName
      .split(' ')
      .map(word => word.substring(0, 2).toUpperCase())
      .join('')
      .substring(0, 6)

    const date = new Date().toISOString().slice(0, 10).replace(/-/g, '')
    const random = Math.floor(Math.random() * 100).toString().padStart(2, '0')

    return `${namePrefix}-${date}-${random}`
  }

  const handleCreateNew = () => {
    if (search.trim()) {
      const productName = search.trim()
      const generatedSKU = generateSKU(productName)

      // Pass both product name and generated SKU
      onNewProduct(productName, generatedSKU)
      setSearch('')
      setOpen(false) // Close dropdown after creating new product
    }
  }
  
  return (
    <div className="space-y-2">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <div className="relative">
            <Input
              value={value?.productName || search}
              onChange={(e) => {
                // Allow typing to search
                setSearch(e.target.value)
                // If we had a selected product, clear it when user starts typing
                if (value?.productId) {
                  handleClear()
                }
              }}
              onClick={() => setOpen(true)}
              className="h-8 w-full pr-10 cursor-pointer text-xs border-gray-200 dark:border-gray-700 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 shadow-sm hover:shadow-md"
              placeholder={supplier ? "Search or select product..." : "Select a supplier first..."}
              // Removed disabled prop - allow interaction even without supplier
            />
            {value?.productId && (
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0"
                onClick={(e) => {
                  e.stopPropagation()
                  handleClear()
                  setSearch('')
                }}
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>
        </PopoverTrigger>
        <PopoverContent className="w-[400px] p-0 z-50" align="start">
          <div className="border rounded-md bg-background">
            <div className="p-2 border-b">
              <Input
                placeholder={supplier ? "Search products..." : "Select a supplier first..."}
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="text-xs h-8"
                // Add ref to focus on input when popover opens
                ref={(input) => {
                  if (input && open) {
                    // Focus the input when popover opens
                    setTimeout(() => input.focus(), 0);
                  }
                }}
              />
            </div>
            <div className="max-h-[200px] overflow-y-auto">
              {!supplier ? (
                <div className="p-4 text-center text-xs text-gray-500">
                  <p className="mb-2">Please select a supplier first to view available items</p>
                  <p className="text-gray-400">Items will be filtered by the selected supplier</p>
                </div>
              ) : orderableItems.length === 0 ? (
                <div className="p-2 text-center">
                  <p className="text-xs text-gray-500 mb-2">No items found for "{supplier}"</p>
                  {search.trim() && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="text-xs h-7"
                      onClick={handleCreateNew}
                    >
                      <PlusCircle className="h-3 w-3 mr-1" />
                      Create "{search.trim()}"
                    </Button>
                  )}
                </div>
              ) : (
                <div>
                  <div className="px-2 py-1 text-xs font-medium text-muted-foreground border-b">Available Items</div>
                  {orderableItems.map((item) => (
                    <div
                      key={item.id}
                      onClick={() => {
                        console.log('✅ Item clicked:', item.displayName);
                        handleItemSelect(item);
                      }}
                      className="flex items-center justify-between cursor-pointer text-sm py-4 px-4 hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:border-blue-200 dark:hover:border-blue-700 border-b border-gray-100 dark:border-gray-700 last:border-b-0 transition-all duration-200 group"
                    >
                      <div>
                        <div className="font-medium">{item.displayName}</div>
                        <div className="text-muted-foreground text-xs">
                          {item.sku}
                          {item.isVariant && (
                            <span className="ml-2 text-green-600">• Variant</span>
                          )}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-xs">
                          {formatCurrency(item.unitCost)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </PopoverContent>
      </Popover>

    {/* Variant selector no longer needed - variants are directly selectable */}
  </div>
  )
}