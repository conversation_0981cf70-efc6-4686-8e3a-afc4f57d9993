'use client'

import { useState, useEffect } from 'react'
import { AnalyticsTab } from '@/components/products/products-catalog/analytics-tab'
import { usePersistedState } from '@/lib/use-persisted-state'
import { type ProductFilters, INITIAL_PRODUCT_FILTERS } from '@/components/products/products-catalog/product-filters'
import { Button } from '@/components/ui/button'
import { Download } from 'lucide-react'
import { PageHeader } from '@/components/ui/page-header'

export default function AnalyticsPage() {
  const [isMobile, setIsMobile] = useState(false)
  
  // Shared state for filters (for contextual analytics) - with persistence
  const [globalFilters, setGlobalFilters] = usePersistedState<ProductFilters>(
    'products-filters', 
    INITIAL_PRODUCT_FILTERS
  )
  const [filteredProductIds, setFilteredProductIds] = useState<string[]>([])

  // Mobile detection
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Export to CSV function
  const exportToCSV = () => {
    // Dispatch event to trigger export in AnalyticsTab
    window.dispatchEvent(new CustomEvent('exportAnalyticsCSV'))
  }

  return (
    <div className="flex flex-col gap-4">
      {/* Page Header with Title and Actions */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <PageHeader 
            title="Product Analytics" 
            description="Insights into your product performance and inventory"
          />
        </div>
        
        <div className="flex items-center gap-2">
          {/* Export Button */}
          <Button 
            variant="outline" 
            size="sm" 
            className="flex items-center gap-1.5 h-8 px-3"
            onClick={exportToCSV}
          >
            <Download className="h-4 w-4" />
            <span>Export CSV</span>
          </Button>
        </div>
      </div>

      <AnalyticsTab 
        isMobile={isMobile} 
        contextualFilters={globalFilters}
        filteredProductIds={filteredProductIds}
      />
    </div>
  )
}