'use client'

import { useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { useToast } from '@/components/ui/use-toast'

interface NewCustomerModalProps {
  isOpen: boolean
  onClose: () => void
  onCreateCustomer?: (customerData: {
    full_name: string
    email: string
    phone?: string
    shipping_address?: string
    billing_address?: string
    notes?: string
  }) => void
}

export function NewCustomerModal({ isOpen, onClose, onCreateCustomer }: NewCustomerModalProps) {
  const { toast } = useToast()
  
  const [formData, setFormData] = useState({
    full_name: '',
    email: '',
    phone: '',
    shipping_address: '',
    billing_address: '',
    notes: ''
  })
  
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[name]
        return newErrors
      })
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}
    
    if (!formData.full_name.trim()) {
      newErrors.full_name = 'Full name is required'
    }
    
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async () => {
    if (!validateForm()) return
    
    setIsSubmitting(true)
    
    try {
      // If onCreateCustomer callback is provided, use it
      if (onCreateCustomer) {
        onCreateCustomer({
          full_name: formData.full_name,
          email: formData.email,
          phone: formData.phone || undefined,
          shipping_address: formData.shipping_address || undefined,
          billing_address: formData.billing_address || undefined,
          notes: formData.notes || undefined
        })
      } else {
        // Fallback to console log if no callback provided
        console.log('Creating customer:', formData)
        toast({
          title: "Customer Created",
          description: "The new customer has been created successfully."
        })
        
        // Reset form and close modal
        setFormData({
          full_name: '',
          email: '',
          phone: '',
          shipping_address: '',
          billing_address: '',
          notes: ''
        })
        onClose()
      }
    } catch (error: any) {
      console.error('Error creating customer:', error)
      toast({
        title: "Error",
        description: error.message || "Failed to create customer. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleCancel = () => {
    // Reset form
    setFormData({
      full_name: '',
      email: '',
      phone: '',
      shipping_address: '',
      billing_address: '',
      notes: ''
    })
    setErrors({})
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleCancel()}>
      <DialogContent className="max-w-3xl p-0 rounded-2xl shadow-xl border-0 bg-white dark:bg-gray-900 overflow-hidden">
        <DialogHeader className="px-6 pt-6 pb-4 border-b border-gray-200 dark:border-gray-700">
          <DialogTitle className="text-xl font-bold text-gray-900 dark:text-gray-100">
            New Customer
          </DialogTitle>
        </DialogHeader>
        
        <div className="px-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left Column */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Full Name *
                </label>
                <Input
                  name="full_name"
                  value={formData.full_name}
                  onChange={handleChange}
                  className={`h-9 ${errors.full_name ? 'border-red-500' : ''}`}
                  placeholder="Enter full name"
                />
                {errors.full_name && (
                  <p className="mt-1 text-xs text-red-500">{errors.full_name}</p>
                )}
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Email Address *
                </label>
                <Input
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleChange}
                  className={`h-9 ${errors.email ? 'border-red-500' : ''}`}
                  placeholder="Enter email address"
                />
                {errors.email && (
                  <p className="mt-1 text-xs text-red-500">{errors.email}</p>
                )}
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Phone Number
                </label>
                <Input
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  className="h-9"
                  placeholder="Enter phone number"
                />
              </div>
            </div>
            
            {/* Right Column */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Shipping Address
                </label>
                <Textarea
                  name="shipping_address"
                  value={formData.shipping_address}
                  onChange={handleChange}
                  className="min-h-[80px]"
                  placeholder="Enter shipping address"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Billing Address
                </label>
                <Textarea
                  name="billing_address"
                  value={formData.billing_address}
                  onChange={handleChange}
                  className="min-h-[80px]"
                  placeholder="Enter billing address"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Notes
                </label>
                <Textarea
                  name="notes"
                  value={formData.notes}
                  onChange={handleChange}
                  className="min-h-[80px]"
                  placeholder="Enter any additional notes"
                />
              </div>
            </div>
          </div>
        </div>
        
        <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-3">
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={isSubmitting}
            className="h-9 px-4 text-sm font-medium border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting}
            className="h-9 px-4 text-sm font-medium bg-blue-600 hover:bg-blue-700"
          >
            {isSubmitting ? 'Creating...' : 'Create Customer'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}