const authMiddleware = require('../middleware/auth');
const expensesController = require('../controllers/expenses');
const dashboardController = require('../controllers/dashboard');

class ApiRoutes {
  async handleRequest(req, res, pathname) {
    // Setup middleware
    if (!authMiddleware.setupApiMiddleware(req, res)) {
      return; // Request already handled (e.g., OPTIONS)
    }

    // Route to appropriate handler
    if (pathname === '/api/expenses') {
      await this.handleExpensesRoute(req, res);
    } else if (pathname === '/api/dashboard/metrics') {
      await this.handleDashboardMetricsRoute(req, res);
    } else if (pathname === '/api/dashboard/analytics') {
      await this.handleDashboardAnalyticsRoute(req, res);
    } else {
      this.handleNotFound(res);
    }
  }

  async handleExpensesRoute(req, res) {
    const method = req.method;

    if (method === 'GET') {
      await this.getExpenses(req, res);
    } else if (method === 'POST') {
      await this.addExpense(req, res);
    } else {
      this.handleMethodNotAllowed(res, ['GET', 'POST']);
    }
  }

  async getExpenses(req, res) {
    try {
      if (!authMiddleware.requireAuth(req, res)) {
        return;
      }

      const expenses = await expensesController.getExpenses(req.userToken);
      this.sendSuccess(res, expenses);
    } catch (error) {
      console.error('API Error - Get Expenses:', error);
      this.sendError(res, 500, `Database error: ${error.message}`);
    }
  }

  async addExpense(req, res) {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });

    req.on('end', async () => {
      try {
        if (!authMiddleware.requireAuth(req, res)) {
          return;
        }

        const expenseData = JSON.parse(body);
        
        // Enhanced validation for new fields
        if (!expenseData.amount || !expenseData.description || !expenseData.category || !expenseData.payment_method) {
          this.sendError(res, 400, 'Amount, description, category, and payment method are required');
          return;
        }

        if (isNaN(expenseData.amount) || expenseData.amount <= 0) {
          this.sendError(res, 400, 'Amount must be a positive number');
          return;
        }

        // Validate expense_date format if provided
        if (expenseData.expense_date) {
          const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
          if (!dateRegex.test(expenseData.expense_date)) {
            this.sendError(res, 400, 'Expense date must be in YYYY-MM-DD format');
            return;
          }
        }

        const newExpense = await expensesController.addExpense(expenseData, req.userToken);
        this.sendSuccess(res, newExpense, 201);
      } catch (error) {
        console.error('API Error - Add Expense:', error);
        this.sendError(res, 400, `Database error: ${error.message}`);
      }
    });
  }

  async handleDashboardMetricsRoute(req, res) {
    if (req.method !== 'GET') {
      this.handleMethodNotAllowed(res, ['GET']);
      return;
    }

    try {
      if (!authMiddleware.requireAuth(req, res)) {
        return;
      }

      const metrics = await dashboardController.getMetrics(req.userToken);
      this.sendSuccess(res, metrics);
    } catch (error) {
      console.error('API Error - Dashboard Metrics:', error);
      this.sendError(res, 500, `Database error: ${error.message}`);
    }
  }

  async handleDashboardAnalyticsRoute(req, res) {
    if (req.method !== 'GET') {
      this.handleMethodNotAllowed(res, ['GET']);
      return;
    }

    try {
      if (!authMiddleware.requireAuth(req, res)) {
        return;
      }

      const analytics = await dashboardController.getExpenseAnalytics(req.userToken);
      this.sendSuccess(res, analytics);
    } catch (error) {
      console.error('API Error - Dashboard Analytics:', error);
      this.sendError(res, 500, `Database error: ${error.message}`);
    }
  }

  // Helper methods
  sendSuccess(res, data, statusCode = 200) {
    res.writeHead(statusCode, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ 
      success: true, 
      data: data,
      timestamp: new Date().toISOString()
    }));
  }

  sendError(res, statusCode, message) {
    res.writeHead(statusCode, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ 
      success: false, 
      error: message,
      timestamp: new Date().toISOString()
    }));
  }

  handleNotFound(res) {
    this.sendError(res, 404, 'API endpoint not found');
  }

  handleMethodNotAllowed(res, allowedMethods) {
    res.setHeader('Allow', allowedMethods.join(', '));
    this.sendError(res, 405, `Method not allowed. Allowed methods: ${allowedMethods.join(', ')}`);
  }
}

module.exports = new ApiRoutes();