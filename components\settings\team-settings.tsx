'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { MoreHorizontal, Loader2 } from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'
import { EnhancedTeamTable } from './enhanced-team-table'
import { useTeamMembers, useInviteUserToTeam } from '@/hooks/use-team-members'
import { usePendingInvitations, useResendInvitation, useCancelInvitation } from '@/hooks/use-invitations'
import { format } from 'date-fns'

export function TeamSettings() {
  const { toast } = useToast()
  const { data: teamMembers, isLoading } = useTeamMembers()
  const { mutate: inviteUser, isPending: isInviting } = useInviteUserToTeam()
  const { data: pendingInvitations, isLoading: isLoadingInvitations } = usePendingInvitations()
  const { mutate: resendInvitation, isPending: isResending } = useResendInvitation()
  const { mutate: cancelInvitation, isPending: isCancelling } = useCancelInvitation()
  
  const [email, setEmail] = useState('')
  const [role, setRole] = useState<'admin' | 'staff'>('staff')

  const handleInvite = () => {
    if (!email) {
      toast({
        title: 'Email required',
        description: 'Please enter an email address to invite.',
        variant: 'destructive',
      })
      return
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      toast({
        title: 'Invalid email',
        description: 'Please enter a valid email address.',
        variant: 'destructive',
      })
      return
    }

    inviteUser(
      { email, role },
      {
        onSuccess: () => {
          toast({
            title: 'Invitation sent',
            description: `An invitation has been sent to ${email}.`,
          })
          setEmail('')
        },
        onError: (error) => {
          toast({
            title: 'Error sending invitation',
            description: error.message || 'Failed to send invitation. Please try again.',
            variant: 'destructive',
          })
        },
      }
    )
  }

  const handleResendInvitation = (invitationId: string) => {
    resendInvitation(invitationId, {
      onSuccess: () => {
        toast({
          title: 'Invitation resent',
          description: 'The invitation has been resent successfully.',
        })
      },
      onError: (error) => {
        toast({
          title: 'Error resending invitation',
          description: error.message || 'Failed to resend invitation. Please try again.',
          variant: 'destructive',
        })
      },
    })
  }

  const handleCancelInvitation = (invitationId: string) => {
    cancelInvitation(invitationId, {
      onSuccess: () => {
        toast({
          title: 'Invitation cancelled',
          description: 'The invitation has been cancelled successfully.',
        })
      },
      onError: (error) => {
        toast({
          title: 'Error cancelling invitation',
          description: error.message || 'Failed to cancel invitation. Please try again.',
          variant: 'destructive',
        })
      },
    })
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Team Management</CardTitle>
        <CardDescription>Invite and manage your team members.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Invite Section */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <Input
              type="email"
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled={isInviting}
            />
          </div>
          <div className="w-full sm:w-32">
            <Select value={role} onValueChange={(value: 'admin' | 'staff') => setRole(value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="admin">Admin</SelectItem>
                <SelectItem value="staff">Staff</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="w-full sm:w-32">
            <Button 
              className="w-full" 
              onClick={handleInvite}
              disabled={isInviting}
            >
              {isInviting ? 'Sending...' : 'Send Invite'}
            </Button>
          </div>
        </div>

        {/* Pending Invitations Section */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Pending Invitations</CardTitle>
            <CardDescription>Manage invitations sent to new team members.</CardDescription>
          </CardHeader>
          <CardContent>
            {isLoadingInvitations ? (
              <div className="flex items-center justify-center p-8">
                <Loader2 className="h-6 w-6 animate-spin" />
              </div>
            ) : pendingInvitations && pendingInvitations.length > 0 ? (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Email</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Date Invited</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {pendingInvitations.map((invitation) => (
                      <TableRow key={invitation.id}>
                        <TableCell className="font-medium">{invitation.email}</TableCell>
                        <TableCell>
                          <span className="capitalize">{invitation.role}</span>
                        </TableCell>
                        <TableCell>
                          {format(new Date(invitation.created_at), 'MMM d, yyyy')}
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem
                                onClick={() => handleResendInvitation(invitation.id)}
                                disabled={isResending}
                              >
                                {isResending ? 'Resending...' : 'Resend Invitation'}
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleCancelInvitation(invitation.id)}
                                disabled={isCancelling}
                                className="text-red-600"
                              >
                                {isCancelling ? 'Cancelling...' : 'Cancel Invitation'}
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                No pending invitations
              </div>
            )}
          </CardContent>
        </Card>

        {/* Users Table */}
        <div className="pt-4">
          <EnhancedTeamTable members={teamMembers || []} isLoading={isLoading} />
        </div>
      </CardContent>
    </Card>
  )
}