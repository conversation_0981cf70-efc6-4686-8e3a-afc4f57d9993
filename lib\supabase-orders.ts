import { getSupabaseClient } from '@/lib/supabase'
import { enrichSalesWithCustomerData, enrichSaleWithCustomerData } from '@/lib/supabase-utils'

// Define the type for our order data
export type OrderStatus = 'pending' | 'completed' | 'failed';

export interface OrderRow {
  id: string
  organization_id: string
  customer_id: string | null
  order_number: string
  order_date: string
  status: OrderStatus
  subtotal: number
  tax: number
  shipping: number
  discount: number
  total: number
  notes?: string
  created_at: string
  updated_at: string
  customer_name?: string
  customers?: {
    full_name: string
  }[]
}

export interface OrderItemRow {
  id: string
  order_id: string
  product_id: string
  variant_id: string | null
  quantity: number
  unit_price: number
  subtotal: number
  created_at: string
  product_name?: string
  variant_name?: string
}

export interface CreateOrderData {
  customer_id: string | null
  order_number: string
  order_date: string
  status: OrderStatus
  subtotal: number
  tax: number
  shipping: number
  discount: number
  total: number
  items: {
    product_id: string
    variant_id: string | null
    quantity: number
    unit_price: number
    subtotal: number
  }[]
}

/**
 * Function to get all orders for an organization
 * @param organizationId - The organization ID to fetch orders for
 * @returns Promise with array of orders
 */
export async function getAllOrdersForOrganization(organizationId: string) {
  const supabase = getSupabaseClient()
  
  try {
    const { data, error } = await supabase
      .from('sales')
      .select(`
        id,
        organization_id,
        customer_id,
        total_amount,
        payment_method,
        payment_status,
        sale_date,
        notes,
        created_at,
        updated_at
      `)
      .eq('organization_id', organizationId)
      .order('created_at', { ascending: false })
    
    if (error) {
      throw new Error(error.message)
    }
    
    // Enrich sales with customer data using our utility function
    const enrichedSales = await enrichSalesWithCustomerData(data)
    
    // Transform the data to match our interface
    return enrichedSales.map((sale: any) => ({
      id: sale.id,
      organization_id: sale.organization_id,
      customer_id: sale.customer_id,
      order_number: `ORD-${sale.id.substring(0, 8)}`, // Generate order number from sale ID
      order_date: sale.sale_date,
      status: mapDatabaseStatusToUIStatus(sale.payment_status) || 'Pending payment',
      subtotal: sale.total_amount, // For simplicity, using total_amount as subtotal
      tax: 0, // Not available in sales table
      shipping: 0, // Not available in sales table
      discount: 0, // Not available in sales table
      total: sale.total_amount,
      created_at: sale.created_at,
      updated_at: sale.updated_at,
      customer_name: sale.customer_name
    }))
  } catch (error) {
    console.error('Error fetching orders for organization:', error)
    throw error
  }
}

/**
 * Function to get an order by ID
 * @param orderId - The ID of the order to fetch
 * @returns Promise with the order data
 */
export async function getOrderById(orderId: string) {
  const supabase = getSupabaseClient()
  
  try {
    const { data, error } = await supabase
      .from('sales')
      .select(`
        id,
        organization_id,
        customer_id,
        total_amount,
        payment_method,
        payment_status,
        sale_date,
        notes,
        created_at,
        updated_at,
        sale_items(
          id,
          product_id,
          quantity,
          unit_price,
          total_price
        )
      `)
      .eq('id', orderId)
      .single()
    
    if (error) {
      throw new Error(error.message)
    }
    
    // Enrich sale with customer data using our utility function
    const enrichedSale = await enrichSaleWithCustomerData(data)
    
    // Transform the data to match our interface
    return {
      id: enrichedSale.id,
      organization_id: enrichedSale.organization_id,
      customer_id: enrichedSale.customer_id,
      order_number: `ORD-${enrichedSale.id.substring(0, 8)}`, // Generate order number from sale ID
      order_date: enrichedSale.sale_date,
      status: mapDatabaseStatusToUIStatus(enrichedSale.payment_status) || 'Pending payment',
      subtotal: enrichedSale.total_amount, // For simplicity, using total_amount as subtotal
      tax: 0, // Not available in sales table
      shipping: 0, // Not available in sales table
      discount: 0, // Not available in sales table
      total: enrichedSale.total_amount,
      created_at: enrichedSale.created_at,
      updated_at: enrichedSale.updated_at,
      customer_name: enrichedSale.customer_name,
      items: enrichedSale.sale_items?.map((item: any) => ({
        id: item.id,
        order_id: enrichedSale.id,
        product_id: item.product_id,
        variant_id: null, // Not available in sale_items table
        quantity: item.quantity,
        unit_price: item.unit_price,
        subtotal: item.total_price,
        product_name: 'Unknown Product', // We'll fetch product names separately if needed
        variant_name: null
      })) || []
    }
  } catch (error) {
    console.error('Error fetching order by ID:', error)
    throw error
  }
}

/**
 * Function to create a new order with its items and update inventory
 * @param organizationId - The organization ID creating the order
 * @param orderData - The order data to insert
 * @returns Promise with the created order ID
 */
export async function createOrder(organizationId: string, orderData: CreateOrderData) {
  const supabase = getSupabaseClient()
  
  try {
    // First create the sale record
    const { data: saleData, error: saleError } = await supabase
      .from('sales')
      .insert({
        organization_id: organizationId,
        customer_id: orderData.customer_id,
        total_amount: orderData.total,
        payment_method: 'pending',
        payment_status: mapUIStatusToDatabaseStatus(orderData.status),
        sale_date: orderData.order_date,
        notes: 'Created from order form'
      })
      .select()
      .single()
    
    if (saleError) {
      throw new Error(`Error creating order: ${saleError.message}`)
    }
    
    // Create sale items
    if (orderData.items && orderData.items.length > 0) {
      const saleItems = orderData.items.map(item => ({
        sale_id: saleData.id,
        product_id: item.product_id,
        variant_id: item.variant_id,
        quantity: item.quantity,
        unit_price: item.unit_price,
        // Remove total_price since it's a generated column
        organization_id: organizationId
      }))
      
      const { error: itemsError } = await supabase
        .from('sale_items')
        .insert(saleItems)
      
      if (itemsError) {
        throw new Error(`Error creating order items: ${itemsError.message}`)
      }
    }
    
    // Dispatch events to refresh related data
    // Refresh orders list
    window.dispatchEvent(new CustomEvent('orderCreated'))
    
    // Refresh products/inventory since stock levels may have changed
    window.dispatchEvent(new CustomEvent('inventoryUpdated'))
    
    // Refresh dashboard metrics
    window.dispatchEvent(new CustomEvent('dashboardRefresh'))
    
    // Refresh customers (since a new order was created for a customer)
    window.dispatchEvent(new CustomEvent('customerUpdated'))
    
    return saleData.id
  } catch (error) {
    console.error('Error creating order:', error)
    throw error
  }
}

/**
 * Function to update an existing order
 * @param orderId - The ID of the order to update
 * @param orderData - The order data to update
 * @returns Promise with the updated order
 */
export async function updateOrder(orderId: string, orderData: Partial<OrderRow>) {
  const supabase = getSupabaseClient()
  
  try {
    // Map OrderRow fields to sales table fields
    const updateData: any = {}
    if (orderData.customer_id !== undefined) updateData.customer_id = orderData.customer_id
    if (orderData.total !== undefined) updateData.total_amount = orderData.total
    if (orderData.status) updateData.payment_status = mapUIStatusToDatabaseStatus(orderData.status)
    if (orderData.order_date) updateData.sale_date = orderData.order_date
    if (orderData.notes) updateData.notes = orderData.notes
    
    const { data, error } = await supabase
      .from('sales')
      .update(updateData)
      .eq('id', orderId)
      .select()
      .single()
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data
  } catch (error) {
    console.error('Error updating order:', error)
    throw error
  }
}

/**
 * Function to delete an order
 * @param orderId - The ID of the order to delete
 * @returns Promise with the deletion result
 */
export async function deleteOrder(orderId: string) {
  const supabase = getSupabaseClient()
  
  try {
    const { error } = await supabase
      .from('sales')
      .delete()
      .eq('id', orderId)
    
    if (error) {
      throw new Error(error.message)
    }
    
    return { success: true }
  } catch (error) {
    console.error('Error deleting order:', error)
    throw error
  }
}

/**
 * Function to update the status of an order
 * @param orderId - The ID of the order to update
 * @param newStatus - The new status for the order
 * @returns Promise with the updated order data
 */
export async function updateOrderStatus(orderId: string, newStatus: OrderStatus) {
  const supabase = getSupabaseClient()
  
  try {
    const { data, error } = await supabase
      .from('sales')
      .update({ payment_status: newStatus })
      .eq('id', orderId)
      .select()
      .single()
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data
  } catch (error) {
    console.error('Error updating order status:', error)
    throw error
  }
}

/**
 * Maps database status values to UI status values
 * @param dbStatus - The status value from the database
 * @returns The corresponding UI status value
 */
function mapDatabaseStatusToUIStatus(dbStatus: string): string {
  switch (dbStatus) {
    case 'pending':
      return 'Pending payment'
    case 'completed':
      return 'Completed'
    case 'failed':
      return 'Failed'
    default:
      return dbStatus // Return as-is if no mapping is found
  }
}

/**
 * Maps UI status values to database status values
 * @param uiStatus - The status value from the UI
 * @returns The corresponding database status value
 */
export function mapUIStatusToDatabaseStatus(uiStatus: string): string {
  switch (uiStatus) {
    case 'Pending payment':
      return 'pending'
    case 'Processing':
      return 'pending' // Processing is still pending in database
    case 'On hold':
      return 'pending' // On hold is still pending in database
    case 'Completed':
      return 'completed'
    case 'Failed':
      return 'failed'
    case 'Cancelled':
      return 'failed' // Cancelled is considered failed in database
    case 'Refunded':
      return 'completed' // Refunded orders were completed first
    default:
      return uiStatus // Return as-is if no mapping is found
  }
}
