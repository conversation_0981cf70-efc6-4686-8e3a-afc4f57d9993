'use client'

import { useState, useEffect, useCallback, useRef } from 'react'

/**
 * Custom hook for persisting state to localStorage
 * @param key - The localStorage key
 * @param defaultValue - Default value if no stored value exists
 * @returns [value, setValue] tuple
 */
export function usePersistedState<T>(
  key: string,
  defaultValue: T
): [T, (value: T | ((prev: T) => T)) => void] {
  // Initialize state with default value (SSR safe)
  const [value, setValue] = useState<T>(defaultValue)
  const [isHydrated, setIsHydrated] = useState(false)
  const initializedRef = useRef(false)

  // Load persisted value on client-side hydration
  useEffect(() => {
    if (initializedRef.current) return
    
    try {
      if (typeof window !== 'undefined') {
        const item = window.localStorage.getItem(key)
        if (item !== null) {
          const parsedValue = JSON.parse(item)
          setValue(parsedValue)
        }
        setIsHydrated(true)
        initializedRef.current = true
      }
    } catch (error) {
      console.warn(`Error loading persisted state for key "${key}":`, error)
      setIsHydrated(true)
      initializedRef.current = true
    }
  }, [key])

  // Update localStorage whenever value changes (only after hydration)
  useEffect(() => {
    if (isHydrated && initializedRef.current && typeof window !== 'undefined') {
      try {
        window.localStorage.setItem(key, JSON.stringify(value))
      } catch (error) {
        console.warn(`Error persisting state for key "${key}":`, error)
      }
    }
  }, [key, value, isHydrated])

  const persistedSetValue = useCallback((newValue: T | ((prev: T) => T)) => {
    setValue(newValue)
  }, [])

  return [value, persistedSetValue]
}

/**
 * Custom hook for managing session storage
 * @param key - The sessionStorage key
 * @param defaultValue - Default value if no stored value exists
 * @returns [value, setValue] tuple
 */
export function useSessionState<T>(
  key: string,
  defaultValue: T
): [T, (value: T | ((prev: T) => T)) => void] {
  const [value, setValue] = useState<T>(defaultValue)
  const [isHydrated, setIsHydrated] = useState(false)
  const initializedRef = useRef(false)

  // Load persisted value on client-side hydration
  useEffect(() => {
    if (initializedRef.current) return
    
    try {
      if (typeof window !== 'undefined') {
        const item = window.sessionStorage.getItem(key)
        if (item !== null) {
          const parsedValue = JSON.parse(item)
          setValue(parsedValue)
        }
        setIsHydrated(true)
        initializedRef.current = true
      }
    } catch (error) {
      console.warn(`Error loading session state for key "${key}":`, error)
      setIsHydrated(true)
      initializedRef.current = true
    }
  }, [key])

  // Update sessionStorage whenever value changes (only after hydration)
  useEffect(() => {
    if (isHydrated && initializedRef.current && typeof window !== 'undefined') {
      try {
        window.sessionStorage.setItem(key, JSON.stringify(value))
      } catch (error) {
        console.warn(`Error persisting session state for key "${key}":`, error)
      }
    }
  }, [key, value, isHydrated])

  const sessionSetValue = useCallback((newValue: T | ((prev: T) => T)) => {
    setValue(newValue)
  }, [])

  return [value, sessionSetValue]
}

/**
 * Utility to clear persisted state
 */
export const clearPersistedState = (key: string) => {
  try {
    if (typeof window !== 'undefined') {
      window.localStorage.removeItem(key)
    }
  } catch (error) {
    console.warn(`Error clearing persisted state for key "${key}":`, error)
  }
}

/**
 * Utility to clear session state
 */
export const clearSessionState = (key: string) => {
  try {
    if (typeof window !== 'undefined') {
      window.sessionStorage.removeItem(key)
    }
  } catch (error) {
    console.warn(`Error clearing session state for key "${key}":`, error)
  }
}